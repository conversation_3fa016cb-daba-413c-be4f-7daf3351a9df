<!-- 报警分析 -->
<template>
  <div class="wrapper">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card shadow="always" class="analyze_card">
          <div class="title_view">
            <span
              style="
                font-weight: 900;
                color: var(--el-text-color-secondary);
                font-size: 18px;
              "
              >报警分析</span
            >
            <div>
              <el-date-picker
                v-model="search.date"
                type="month"
                placeholder="请选择日期"
                style="margin-right: 10px"
                @change="refreshData"
              />
              <el-button @click="thisMonth"> 本月 </el-button>
              <el-button type="primary" @click="exportData"> 导出 </el-button>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-row :gutter="20">
          <el-col :span="16">
            <el-card shadow="always" class="analyze_card">
              <div class="card_2">
                <div class="card_left">
                  <div class="card_letf_1">{{ state.showDate }}月报警情况</div>
                  <div class="card_letf_2">
                    {{ state.showDate }}月报警情况:{{
                      state.showDate
                    }}月共计报警{{ state.total }}次
                  </div>
                  <div class="card_left_3 flex">
                    <div>
                      <span>{{ state.showDate }}月共计报警(次)</span>
                      <div>{{ state.total }}</div>
                    </div>
                    <div>
                      <span>{{ state.showDate }}月紧急报警(次)</span>
                      <div>
                        {{ state.emergencyAlarm }}
                        <!-- <span>和{{ 8 }}月相同</span> -->
                      </div>
                    </div>
                  </div>
                  <!-- <div>紧急报警最多</div>
                  <div class="card_left_4">
                    {{ '沱江保护再生水厂' }}({{ 0 }}次)
                          </div> -->
                </div>
                <div class="card_right">
                  <div class="card_right_1">
                    <div>{{ state.showDate }}月报警类型分布</div>
                    <el-button key="" text>
                      <el-icon>
                        <MoreFilled />
                      </el-icon>
                    </el-button>
                  </div>
                  <div style="height: 230px; width: 100%">
                    <VChart :option="state.LXFB"></VChart>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>

          <el-col :span="8">
            <el-card shadow="always" class="analyze_card">
              <div>
                <div class="card_title">
                  <div>{{ state.showDate }}月报警等级分布</div>
                  <el-button key="" text>
                    <el-icon>
                      <MoreFilled />
                    </el-icon>
                  </el-button>
                </div>
                <div style="height: 230px; width: 100%">
                  <VChart :option="state.DJFB"></VChart>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="24">
            <el-row
              :gutter="20"
              :class="{ greater_than_three: !(state.tableList.length > 3) }"
            >
              <template v-for="(item, i) in state.tableList" :key="i">
                <el-col :span="arrangement()">
                  <cardTableV
                    :ref="(el) => setRef(el, i)"
                    :title="item.title"
                    :value="item.list"
                    :total="state.tableList.length"
                  ></cardTableV>
                </el-col>
              </template>
            </el-row>
          </el-col>
        </el-row>
      </el-col>

      <el-col :span="6">
        <el-row :gutter="20">
          <!-- <el-col :span="24">
            <el-card
              shadow="always"
              class="analyze_card"
            >
              <div class="card_title">
                <div>数据断线情况</div>
              </div>
              <div class="card_cutoff">
                {{ state.showDate }}月暂无数据断线情况
              </div>
              <FormTable
                class="card-table"
                title="asdas"
                :config="TableConfig"
              />
            </el-card>
                  </el-col> -->
          <el-col :span="24">
            <el-card shadow="always" class="analyze_card" style="height: 735px">
              <div class="card_title">
                <div>紧急报警排序</div>
              </div>
              <el-tabs
                v-model="tabs.activeName"
                class="demo-tabs"
                @tab-change="handleClick"
              >
                <template v-for="(item, i) in tabs.keys" :key="i">
                  <el-tab-pane :label="item" :name="item" />
                </template>
              </el-tabs>
              <div>
                <template v-for="(item, i) in state.tabsList" :key="i">
                  <div class="sort">
                    <div style="line-height: 30px; width: 250px">
                      <span style="margin-right: 10px">{{ i + 1 }}</span
                      ><span>{{ item.label }}</span>
                    </div>
                    <div style="width: 360px; height: 30px">
                      <el-progress :percentage="item.scale">
                        <el-button text style="width: 50px">
                          {{ item.scale }}次
                        </el-button>
                      </el-progress>
                    </div>
                  </div>
                </template>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { MoreFilled } from '@element-plus/icons-vue';
import { setBT, setBJPX } from './components/chart';
import {
  getAlarmAnalysis,
  GetAlarmRankByStation
} from '@/api/shuiwureports/zhandian';
import cardTableV from './components/analyzecardTable.vue';

const search = reactive({
  date: dayjs().format('YYYY-MM')
});

const selectRef = ref({}) as any;

const state = reactive({
  LXFB: setBT(),
  DJFB: setBT(),
  BJPX: setBJPX(),
  showDate: computed(() => dayjs(search.date).month() + 1),
  total: 0,
  emergencyAlarm: 0,
  tableList: [] as any[],
  tabsList: [] as any[]
});

const tabs = reactive({
  activeName: '水厂',
  keys: ['水厂', '泵站', '流量监测站', '压力监测站', '水质监测站'],
  data: [[], [], []] as any[]
});

// const TableConfig = reactive<ICardTable>({
//   defaultExpandAll: true,
//   indexVisible: true,
//   rowKey: 'id',
//   columns: [
//     { label: '规则名称', prop: 'title' },
//     { label: '站点变量ID', prop: 'stationAttrId' }
//   ],
//   operationWidth: '160px',
//   operations: [

//   ],
//   dataList: [],
//   pagination: {
//     hide: true
//   }
// })

const handleClick = (tab: any) => {
  initTabsData(tab);
};

function initEcharts(val) {
  const LXFB = {
    name: ['管网报警', '泵站报警', '水厂报警'],
    data: [0, 0, 0]
  };
  for (const i in val.alarmData) {
    switch (val.alarmData[i].title) {
      case '管网':
        LXFB.data[0] += val.alarmData[i].list.length || 0;
        break;
      case '泵站':
        LXFB.data[1] += val.alarmData[i].list.length || 0;
        break;
      case '水厂':
        LXFB.data[2] += val.alarmData[i].list.length || 0;
        break;
      default:
        break;
    }
  }
  state.LXFB = setBT(LXFB);

  const DJFB = {
    name: ['提醒报警', '重要报警', '紧急报警'],
    data: [0, 0, 0]
  };
  let data: any[] = [];
  val.alarmData.forEach((item) => {
    data = [...data, ...item.list];
  });
  data.forEach((item) => {
    DJFB.data[item.alarmLevel - 1] += 1;
  });

  state.emergencyAlarm = DJFB.data[2];
  state.DJFB = setBT(DJFB);
}

function initTabsData(type: string) {
  const params = {
    startTime: dayjs(search.date).valueOf(),
    endTime: dayjs(search.date).add(1, 'M').valueOf(),
    stationType: type
  };
  GetAlarmRankByStation(params).then((res) => {
    let sum = 0;
    (res.data.data || []).forEach((item) => {
      sum += item.count;
    });
    state.tabsList = (res.data.data || []).map((item) => {
      return {
        label: item.key,
        value: ((item.count / sum) * 100).toFixed(2),
        scale: item.count
      };
    });
  });
}

function arrangement() {
  switch (state.tableList.length) {
    case 0:
      return 24;
    case 1:
      return 24;
    case 2:
      return 12;
    case 3:
      return 8;
    default:
      return 8;
  }
}

// 动态设置ref
const setRef = (el, item) => {
  console.log(el, item);
  if (el) {
    selectRef.value[item] = el;
  }
};

function exportData() {
  state.tableList.forEach((item, i) => {
    selectRef.value[i].reftable.exportTable();
  });
}

function thisMonth() {
  search.date = dayjs().format('YYYY-MM');
  refreshData();
}

function refreshData() {
  const params = {
    startTime: dayjs(search.date).valueOf(),
    endTime: dayjs(search.date).add(1, 'M').valueOf()
  };
  getAlarmAnalysis(params).then((res) => {
    if (res.data.code === 200) {
      state.total = res.data.data.total || 0;
      state.tableList = res.data.data.alarmData || [];

      initEcharts(res.data.data);

      initTabsData(tabs.activeName);
    }
  });
}

onMounted(() => {
  refreshData();
  initTabsData('水厂');
});
</script>

<style lang="scss" scoped>
.el-col {
  margin-bottom: 15px;
}

.analyze_card {
  padding: -20px;

  :deep(.el-card__body) {
    padding: 10px;
  }

  .title_view {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.card_2 {
  display: flex;

  .card_left {
    flex: 1;

    div {
      margin-bottom: 10px;
    }

    .card_letf_1 {
      font-weight: 600;
      line-height: 32px;
      color: var(--el-text-color-secondary);
      font-size: 18px;
    }

    .card_letf_2 {
      color: #7e7e7e;
      margin-bottom: 30px;
    }

    .card_left_3 {
      width: 100%;

      div:first-child {
        margin-right: 20px;
      }

      div {
        span {
          color: #323232;
          font-weight: 500;
        }

        div {
          font-size: 30px;
          font-weight: 600;
          line-height: 40px;
          color: #4a83f7;

          span {
            font-size: 16px;
          }
        }
      }
    }

    .card_left_4 {
      font-size: 24px;
      font-weight: 600;
      line-height: 40px;
      color: #4a83f7;
    }
  }

  .card_right {
    flex: 1;

    .card_right_1 {
      display: flex;
      font-weight: 600;
      line-height: 32px;
      justify-content: space-between;
      color: var(--el-text-color-secondary);
      font-size: 18px;
    }
  }
}

.flex {
  display: flex;
}

.card_title {
  display: flex;
  font-weight: 600;
  line-height: 32px;
  justify-content: space-between;
  color: var(--el-text-color-secondary);
  font-size: 18px;
}

.card_cutoff {
  background: #f4f7fe;
  height: 70px;
  line-height: 70px;
  text-align: center;
  margin-bottom: 20px;
}

.sort {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
}

.el-row {
  height: 100%;
}

.greater_than_three {
  height: calc(100%);
  overflow-y: overlay;
  margin-right: -15px;
}
</style>
