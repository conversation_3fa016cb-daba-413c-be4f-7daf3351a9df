import {
  d as d2,
  e as e4,
  f as f2,
  f2 as f3,
  i as i2,
  m as m2,
  n as n2,
  n2 as n3,
  n3 as n4,
  o as o3,
  o2 as o4,
  o3 as o5,
  r as r6,
  r2 as r7,
  t as t4
} from "./chunk-YBV3KSLP.js";
import {
  t as t5
} from "./chunk-F4KVXA42.js";
import "./chunk-6ZZUUGXX.js";
import {
  g as g2,
  p
} from "./chunk-IVZBPDHM.js";
import {
  c as c2,
  m
} from "./chunk-CIOZM2QJ.js";
import "./chunk-MNYWPBDW.js";
import {
  u as u2
} from "./chunk-SKIEIN3S.js";
import {
  _
} from "./chunk-OISOH7BD.js";
import "./chunk-PSWIICDM.js";
import "./chunk-6ESVG4YL.js";
import {
  e2 as e3,
  f,
  n,
  o as o2,
  r as r5,
  t as t2,
  t2 as t3
} from "./chunk-UQUDWTCY.js";
import "./chunk-IKOX2HGY.js";
import {
  E,
  L,
  O,
  T,
  c,
  i,
  u,
  x
} from "./chunk-3KCCETWY.js";
import {
  D,
  E as E2
} from "./chunk-4M3AMTD4.js";
import "./chunk-ZIKXCGU7.js";
import "./chunk-XSQFM27N.js";
import "./chunk-QYOAH6AO.js";
import {
  e as e2
} from "./chunk-A7PY25IH.js";
import "./chunk-57ER3SHX.js";
import {
  g
} from "./chunk-ST2RRB55.js";
import "./chunk-SROTSYJS.js";
import "./chunk-J4KDDSED.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-P37TUI4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-FZ7BG3VX.js";
import {
  r as r4
} from "./chunk-NOZFLZZL.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-UVNYHPLJ.js";
import {
  U
} from "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import {
  l
} from "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import {
  d
} from "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import {
  r as r3
} from "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import "./chunk-NDCSRZLO.js";
import "./chunk-JN4FSB7Y.js";
import {
  r as r2
} from "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  e,
  o,
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/geometry/support/meshUtils/loadGLTFMesh.js
async function X(e5, t6, r8) {
  const o6 = new n4(Y(r8)), s = (await m2(o6, t6, r8, true)).model, i3 = s.lods.shift(), a = /* @__PURE__ */ new Map(), c3 = /* @__PURE__ */ new Map();
  s.textures.forEach((e6, t7) => a.set(t7, re(e6))), s.materials.forEach((e6, t7) => c3.set(t7, oe(e6, a)));
  const u3 = te(i3);
  for (const n5 of u3.parts) ne(u3, n5, c3);
  const { position: l2, normal: f4, tangent: m3, color: p2, texCoord0: d3 } = u3.vertexAttributes, x2 = { position: l2.typedBuffer, normal: r(f4) ? f4.typedBuffer : null, tangent: r(m3) ? m3.typedBuffer : null, uv: r(d3) ? d3.typedBuffer : null, color: r(p2) ? p2.typedBuffer : null }, T2 = _(x2, e5, r8);
  return { transform: T2.transform, components: u3.components, spatialReference: e5.spatialReference, vertexAttributes: new p({ position: T2.vertexAttributes.position, normal: T2.vertexAttributes.normal, tangent: T2.vertexAttributes.tangent, color: x2.color, uv: x2.uv }) };
}
function Y(e5) {
  const r8 = e5 == null ? void 0 : e5.resolveFile;
  return r8 ? { busy: false, request: async (e6, o6, s) => {
    const i3 = r8(e6), a = "image" === o6 ? "image" : "binary" === o6 ? "array-buffer" : "json";
    return (await U(i3, { responseType: a, signal: r(s) ? s.signal : null })).data;
  } } : null;
}
function Z(e5, t6) {
  if (t(e5)) return "-";
  const o6 = e5.typedBuffer;
  return `${r2(t6, o6.buffer, () => t6.size)}/${o6.byteOffset}/${o6.byteLength}`;
}
function ee(e5) {
  return r(e5) ? e5.toString() : "-";
}
function te(e5) {
  let t6 = 0;
  const has = { color: false, tangent: false, normal: false, texCoord0: false }, o6 = /* @__PURE__ */ new Map(), n5 = /* @__PURE__ */ new Map(), s = [];
  for (const i3 of e5.parts) {
    const { attributes: { position: e6, normal: a, color: c3, tangent: u3, texCoord0: l2 } } = i3, f4 = `
      ${Z(e6, o6)}/
      ${Z(a, o6)}/
      ${Z(c3, o6)}/
      ${Z(u3, o6)}/
      ${Z(l2, o6)}/
      ${ee(i3.transform)}
    `;
    let m3 = false;
    const p2 = r2(n5, f4, () => (m3 = true, { start: t6, length: e6.count }));
    m3 && (t6 += e6.count), a && (has.normal = true), c3 && (has.color = true), u3 && (has.tangent = true), l2 && (has.texCoord0 = true), s.push({ gltf: i3, writeVertices: m3, region: p2 });
  }
  return { vertexAttributes: { position: r7(T, t6), normal: has.normal ? r7(i, t6) : null, tangent: has.tangent ? r7(c, t6) : null, color: has.color ? r7(x, t6) : null, texCoord0: has.texCoord0 ? r7(u, t6) : null }, parts: s, components: [] };
}
function re(e5) {
  return new m({ data: (t5(e5.data), e5.data), wrap: ce(e5.parameters.wrap) });
}
function oe(t6, r8) {
  const o6 = new l(fe(t6.color, t6.opacity)), n5 = t6.emissiveFactor ? new l(me(t6.emissiveFactor)) : null;
  return new c2({ color: o6, colorTexture: e(o(t6.textureColor, (e5) => r8.get(e5))), normalTexture: e(o(t6.textureNormal, (e5) => r8.get(e5))), emissiveColor: n5, emissiveTexture: e(o(t6.textureEmissive, (e5) => r8.get(e5))), occlusionTexture: e(o(t6.textureOcclusion, (e5) => r8.get(e5))), alphaMode: ae(t6.alphaMode), alphaCutoff: t6.alphaCutoff, doubleSided: t6.doubleSided, metallic: t6.metallicFactor, roughness: t6.roughnessFactor, metallicRoughnessTexture: e(o(t6.textureMetallicRoughness, (e5) => r8.get(e5))), colorTextureTransform: t6.colorTextureTransform, normalTextureTransform: t6.normalTextureTransform, occlusionTextureTransform: t6.occlusionTextureTransform, emissiveTextureTransform: t6.emissiveTextureTransform, metallicRoughnessTextureTransform: t6.metallicRoughnessTextureTransform });
}
function ne(e5, t6, r8) {
  t6.writeVertices && se(e5, t6);
  const o6 = t6.gltf, n5 = ie(o6.indices || o6.attributes.position.count, o6.primitiveType), s = t6.region.start;
  if (s) for (let i3 = 0; i3 < n5.length; i3++) n5[i3] += s;
  e5.components.push(new g2({ faces: n5, material: r8.get(o6.material), trustSourceNormals: true }));
}
function se(e5, t6) {
  const { position: r8, normal: s, tangent: i3, color: a, texCoord0: l2 } = e5.vertexAttributes, f4 = t6.region.start, { attributes: m3, transform: p2 } = t6.gltf, d3 = m3.position.count;
  if (t2(r8.slice(f4, d3), m3.position, p2), r(m3.normal) && r(s)) {
    const e6 = g(e2(), p2), t7 = s.slice(f4, d3);
    r5(t7, m3.normal, e6), d(e6) && o2(t7, t7);
  } else r(s) && t3(s, 0, 0, 1, { dstIndex: f4, count: d3 });
  if (r(m3.tangent) && r(i3)) {
    const e6 = g(e2(), p2), t7 = i3.slice(f4, d3);
    r6(t7, m3.tangent, e6), d(e6) && f2(t7, t7);
  } else r(i3) && t4(i3, 0, 0, 1, 1, { dstIndex: f4, count: d3 });
  if (r(m3.texCoord0) && r(l2) ? n3(l2.slice(f4, d3), m3.texCoord0) : r(l2) && d2(l2, 0, 0, { dstIndex: f4, count: d3 }), r(m3.color) && r(a)) {
    const e6 = m3.color, t7 = a.slice(f4, d3);
    if (4 === e6.elementCount) e6 instanceof c ? o3(t7, e6, 255) : e6 instanceof x ? e4(t7, e6) : e6 instanceof L && n2(t7, e6, 8);
    else {
      t4(t7, 255, 255, 255, 255);
      const r9 = O.fromTypedArray(t7.typedBuffer, t7.typedBufferStride);
      e6 instanceof i ? f(r9, e6, 255) : e6 instanceof O ? e3(r9, e6) : e6 instanceof E && n(r9, e6, 8);
    }
  } else r(a) && t4(a.slice(f4, d3), 255, 255, 255, 255);
}
function ie(e5, t6) {
  switch (t6) {
    case E2.TRIANGLES:
      return o4(e5, u2);
    case E2.TRIANGLE_STRIP:
      return f3(e5);
    case E2.TRIANGLE_FAN:
      return i2(e5);
  }
}
function ae(e5) {
  switch (e5) {
    case "OPAQUE":
      return "opaque";
    case "MASK":
      return "mask";
    case "BLEND":
      return "blend";
  }
}
function ce(e5) {
  return { horizontal: ue(e5.s), vertical: ue(e5.t) };
}
function ue(e5) {
  switch (e5) {
    case D.CLAMP_TO_EDGE:
      return "clamp";
    case D.MIRRORED_REPEAT:
      return "mirror";
    case D.REPEAT:
      return "repeat";
  }
}
function le(e5) {
  return e5 ** (1 / o5) * 255;
}
function fe(e5, t6) {
  return r4(le(e5[0]), le(e5[1]), le(e5[2]), t6);
}
function me(e5) {
  return r3(le(e5[0]), le(e5[1]), le(e5[2]));
}
export {
  X as loadGLTFMesh
};
//# sourceMappingURL=loadGLTFMesh-OITPNWM7.js.map
