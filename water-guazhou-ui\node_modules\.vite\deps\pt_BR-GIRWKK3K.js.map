{"version": 3, "sources": ["../../@arcgis/core/chunks/pt_BR.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{g as e}from\"./_commonjsHelpers.js\";import{c as a}from\"./_commonjs-dynamic-modules.js\";function o(e,a){for(var o=0;o<a.length;o++){const r=a[o];if(\"string\"!=typeof r&&!Array.isArray(r))for(const a in r)if(\"default\"!==a&&!(a in e)){const o=Object.getOwnPropertyDescriptor(r,a);o&&Object.defineProperty(e,a,o.get?o:{enumerable:!0,get:()=>r[a]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}))}var r,t,i={},s={get exports(){return i},set exports(e){i=e}};r=s,void 0!==(t=function(e,a){Object.defineProperty(a,\"__esModule\",{value:!0}),a.default={_decimalSeparator:\",\",_thousandSeparator:\".\",_percentPrefix:null,_percentSuffix:\"%\",_date_millisecond:\"mm:ss SSS\",_date_second:\"HH:mm:ss\",_date_minute:\"HH:mm\",_date_hour:\"HH:mm\",_date_day:\"dd MMM\",_date_week:\"ww\",_date_month:\"MMM\",_date_year:\"yyyy\",_duration_millisecond:\"SSS\",_duration_second:\"ss\",_duration_minute:\"mm\",_duration_hour:\"hh\",_duration_day:\"dd\",_duration_week:\"ww\",_duration_month:\"MM\",_duration_year:\"yyyy\",_era_ad:\"DC\",_era_bc:\"AC\",A:\"\",P:\"\",AM:\"\",PM:\"\",\"A.M.\":\"\",\"P.M.\":\"\",January:\"Janeiro\",February:\"Fevereiro\",March:\"Março\",April:\"Abril\",May:\"Maio\",June:\"Junho\",July:\"Julho\",August:\"Agosto\",September:\"Setembro\",October:\"Outubro\",November:\"Novembro\",December:\"Dezembro\",Jan:\"Jan\",Feb:\"Fev\",Mar:\"Mar\",Apr:\"Abr\",\"May(short)\":\"Mai\",Jun:\"Jun\",Jul:\"Jul\",Aug:\"Ago\",Sep:\"Set\",Oct:\"Out\",Nov:\"Nov\",Dec:\"Dez\",Sunday:\"Domingo\",Monday:\"Segunda-feira\",Tuesday:\"Terça-feira\",Wednesday:\"Quarta-feira\",Thursday:\"Quinta-feira\",Friday:\"Sexta-feira\",Saturday:\"Sábado\",Sun:\"Dom\",Mon:\"Seg\",Tue:\"Ter\",Wed:\"Qua\",Thu:\"Qui\",Fri:\"Sex\",Sat:\"Sáb\",_dateOrd:function(e){return\"º\"},\"Zoom Out\":\"Reduzir Zoom\",Play:\"Play\",Stop:\"Parar\",Legend:\"Legenda\",\"Click, tap or press ENTER to toggle\":\"Clique, toque ou pressione ENTER para alternar\",Loading:\"Carregando\",Home:\"Início\",Chart:\"Gráfico\",\"Serial chart\":\"Gráfico Serial\",\"X/Y chart\":\"Gráfico XY\",\"Pie chart\":\"Gráfico de Pizza\",\"Gauge chart\":\"Gráfico Indicador\",\"Radar chart\":\"Gráfico de Radar\",\"Sankey diagram\":\"Diagrama Sankey\",\"Chord diagram\":\"Diagram Chord\",\"Flow diagram\":\"Diagrama Flow\",\"TreeMap chart\":\"Gráfico de Mapa de Árvore\",Series:\"Séries\",\"Candlestick Series\":\"Séries do Candlestick\",\"Column Series\":\"Séries de Colunas\",\"Line Series\":\"Séries de Linhas\",\"Pie Slice Series\":\"Séries de Fatias de Pizza\",\"X/Y Series\":\"Séries de XY\",Map:\"Mapa\",\"Press ENTER to zoom in\":\"Pressione ENTER para aumentar o zoom\",\"Press ENTER to zoom out\":\"Pressione ENTER para diminuir o zoom\",\"Use arrow keys to zoom in and out\":\"Use as setas para diminuir ou aumentar o zoom\",\"Use plus and minus keys on your keyboard to zoom in and out\":\"Use as teclas mais ou menos no seu teclado para diminuir ou aumentar o zoom\",Export:\"Exportar\",Image:\"Imagem\",Data:\"Dados\",Print:\"Imprimir\",\"Click, tap or press ENTER to open\":\"Clique, toque ou pressione ENTER para abrir\",\"Click, tap or press ENTER to print.\":\"Clique, toque ou pressione ENTER para imprimir\",\"Click, tap or press ENTER to export as %1.\":\"Clique, toque ou pressione ENTER para exportar como %1.\",'To save the image, right-click this link and choose \"Save picture as...\"':'Para salvar a imagem, clique no link com o botão da direira e escolha \"Salvar imagem como...\"','To save the image, right-click thumbnail on the left and choose \"Save picture as...\"':'Para salvar, clique na imagem à esquerda com o botão direito e escolha \"Salvar imagem como...\"',\"(Press ESC to close this message)\":\"(Pressione ESC para fechar esta mensagem)\",\"Image Export Complete\":\"A exportação da imagem foi completada\",\"Export operation took longer than expected. Something might have gone wrong.\":\"A exportação da imagem demorou mais do que o experado. Algo deve ter dado errado.\",\"Saved from\":\"Salvo de\",PNG:\"\",JPG:\"\",GIF:\"\",SVG:\"\",PDF:\"\",JSON:\"\",CSV:\"\",XLSX:\"\",\"Use TAB to select grip buttons or left and right arrows to change selection\":\"Use TAB para selecionar os botões ou setas para a direita ou esquerda para mudar a seleção\",\"Use left and right arrows to move selection\":\"Use as setas para a esquerda ou direita para mover a seleção\",\"Use left and right arrows to move left selection\":\"Use as setas para a esquerda ou direita para mover a seleção da esquerda\",\"Use left and right arrows to move right selection\":\"Use as setas para a esquerda ou direita para mover a seleção da direita\",\"Use TAB select grip buttons or up and down arrows to change selection\":\"Use TAB para selecionar os botões ou setas para cima ou para baixo para mudar a seleção\",\"Use up and down arrows to move selection\":\"Use as setas para cima ou para baixo para mover a seleção\",\"Use up and down arrows to move lower selection\":\"Use as setas para cima ou para baixo para mover a seleção de baixo\",\"Use up and down arrows to move upper selection\":\"Use as setas para cima ou para baixo para mover a seleção de cima\",\"From %1 to %2\":\"De %1 até %2\",\"From %1\":\"De %1\",\"To %1\":\"Até %1\",\"No parser available for file: %1\":\"Não há um interpretador para este arquivo: %1\",\"Error parsing file: %1\":\"Erro analizando o arquivo: %1\",\"Unable to load file: %1\":\"O arquivo não pôde ser carregado: %1\",\"Invalid date\":\"Data inválida\"}}(a,i))&&(r.exports=t);const n=o({__proto__:null,default:e(i)},[i]);export{n as p};\n"], "mappings": ";;;;;;;;;AAI6F,SAASA,GAAE,GAAE,GAAE;AAAC,WAAQA,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,UAAMC,KAAE,EAAED,EAAC;AAAE,QAAG,YAAU,OAAOC,MAAG,CAAC,MAAM,QAAQA,EAAC;AAAE,iBAAUC,MAAKD,GAAE,KAAG,cAAYC,MAAG,EAAEA,MAAK,IAAG;AAAC,cAAMF,KAAE,OAAO,yBAAyBC,IAAEC,EAAC;AAAE,QAAAF,MAAG,OAAO,eAAe,GAAEE,IAAEF,GAAE,MAAIA,KAAE,EAAC,YAAW,MAAG,KAAI,MAAIC,GAAEC,EAAC,EAAC,CAAC;AAAA,MAAC;AAAA;AAAA,EAAC;AAAC,SAAO,OAAO,OAAO,OAAO,eAAe,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;AAAC;AAAC,IAAID;AAAJ,IAAM;AAAN,IAAQ,IAAE,CAAC;AAAX,IAAa,IAAE,EAAC,IAAI,UAAS;AAAC,SAAO;AAAC,GAAE,IAAI,QAAQ,GAAE;AAAC,MAAE;AAAC,EAAC;AAAEA,KAAE,GAAE,YAAU,IAAE,SAAS,GAAE,GAAE;AAAC,SAAO,eAAe,GAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAE,EAAE,UAAQ,EAAC,mBAAkB,KAAI,oBAAmB,KAAI,gBAAe,MAAK,gBAAe,KAAI,mBAAkB,aAAY,cAAa,YAAW,cAAa,SAAQ,YAAW,SAAQ,WAAU,UAAS,YAAW,MAAK,aAAY,OAAM,YAAW,QAAO,uBAAsB,OAAM,kBAAiB,MAAK,kBAAiB,MAAK,gBAAe,MAAK,eAAc,MAAK,gBAAe,MAAK,iBAAgB,MAAK,gBAAe,QAAO,SAAQ,MAAK,SAAQ,MAAK,GAAE,IAAG,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,QAAO,IAAG,QAAO,IAAG,SAAQ,WAAU,UAAS,aAAY,OAAM,SAAQ,OAAM,SAAQ,KAAI,QAAO,MAAK,SAAQ,MAAK,SAAQ,QAAO,UAAS,WAAU,YAAW,SAAQ,WAAU,UAAS,YAAW,UAAS,YAAW,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,cAAa,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,QAAO,WAAU,QAAO,iBAAgB,SAAQ,eAAc,WAAU,gBAAe,UAAS,gBAAe,QAAO,eAAc,UAAS,UAAS,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,UAAS,SAASE,IAAE;AAAC,WAAM;AAAA,EAAG,GAAE,YAAW,gBAAe,MAAK,QAAO,MAAK,SAAQ,QAAO,WAAU,uCAAsC,kDAAiD,SAAQ,cAAa,MAAK,UAAS,OAAM,WAAU,gBAAe,kBAAiB,aAAY,cAAa,aAAY,oBAAmB,eAAc,qBAAoB,eAAc,oBAAmB,kBAAiB,mBAAkB,iBAAgB,iBAAgB,gBAAe,iBAAgB,iBAAgB,6BAA4B,QAAO,UAAS,sBAAqB,yBAAwB,iBAAgB,qBAAoB,eAAc,oBAAmB,oBAAmB,6BAA4B,cAAa,gBAAe,KAAI,QAAO,0BAAyB,wCAAuC,2BAA0B,wCAAuC,qCAAoC,iDAAgD,+DAA8D,+EAA8E,QAAO,YAAW,OAAM,UAAS,MAAK,SAAQ,OAAM,YAAW,qCAAoC,+CAA8C,uCAAsC,kDAAiD,8CAA6C,2DAA0D,4EAA2E,iGAAgG,wFAAuF,kGAAiG,qCAAoC,6CAA4C,yBAAwB,yCAAwC,gFAA+E,qFAAoF,cAAa,YAAW,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,MAAK,IAAG,KAAI,IAAG,MAAK,IAAG,+EAA8E,8FAA6F,+CAA8C,gEAA+D,oDAAmD,4EAA2E,qDAAoD,2EAA0E,yEAAwE,2FAA0F,4CAA2C,6DAA4D,kDAAiD,sEAAqE,kDAAiD,qEAAoE,iBAAgB,gBAAe,WAAU,SAAQ,SAAQ,UAAS,oCAAmC,iDAAgD,0BAAyB,iCAAgC,2BAA0B,wCAAuC,gBAAe,gBAAe;AAAC,EAAE,GAAE,CAAC,OAAKF,GAAE,UAAQ;AAAG,IAAM,IAAED,GAAE,EAAC,WAAU,MAAK,SAAQ,EAAE,CAAC,EAAC,GAAE,CAAC,CAAC,CAAC;", "names": ["o", "r", "a", "e"]}