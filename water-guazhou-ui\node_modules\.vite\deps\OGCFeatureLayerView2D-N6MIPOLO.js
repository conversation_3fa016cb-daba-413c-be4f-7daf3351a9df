import {
  D
} from "./chunk-RYDDWHB5.js";
import "./chunk-XR4QWT37.js";
import "./chunk-6FMMG4VO.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-XAC3PEBY.js";
import "./chunk-GCZ6JHKQ.js";
import "./chunk-ERH4WAJU.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-AEJDTXF3.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-N73MYEJE.js";
import "./chunk-C65HMCEM.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import "./chunk-DVCBUZVC.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-5JDQNIY4.js";
import "./chunk-J3EWJTCQ.js";
import "./chunk-HWB4LNSZ.js";
import "./chunk-JSZR3BUH.js";
import "./chunk-R6ZFHGHU.js";
import "./chunk-WAPZ634R.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-BMTNBZRF.js";
import "./chunk-YDRLAXYR.js";
import "./chunk-RURSJOSG.js";
import "./chunk-ZQY4DQCR.js";
import "./chunk-WZNPTIYX.js";
import "./chunk-22FAZXOH.js";
import "./chunk-DSTI5UIS.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-77E52HT5.js";
import "./chunk-SROTSYJS.js";
import "./chunk-YBNKNHCD.js";
import "./chunk-M5RPNIHK.js";
import "./chunk-KYTIKHPN.js";
import "./chunk-6JIS2R4B.js";
import "./chunk-7BSY2CUN.js";
import "./chunk-6RN6WNY4.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-7UNBPRRZ.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-IXMTA2RC.js";
import "./chunk-65QKNYBL.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-USWRDFDJ.js";
import "./chunk-XM4RHPJM.js";
import "./chunk-6T5FEO66.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-N4YJNWPS.js";
import "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-ORU3OGKZ.js";
import "./chunk-LHJ7PAFO.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-FRJZOKDL.js";
import "./chunk-DKEAXJKM.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-YJWWP4AU.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-EQYLMJEJ.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-L66EKMJP.js";
import "./chunk-3M3FTH72.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-52L4L36S.js";
import "./chunk-VTOVDEW2.js";
import "./chunk-UKESBP6X.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-TD7BKFJX.js";
import "./chunk-LNGMNZ4I.js";
import "./chunk-24NZLSKM.js";
import "./chunk-BNFBWJYJ.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-23VNIYCK.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-OVIRLBJH.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-4YKNTCHK.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-CGBA4LNQ.js";
import "./chunk-2RZLOKYO.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-HHMRK4FX.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/layers/OGCFeatureLayerView.js
var o = (o2) => {
  let t2 = class extends o2 {
    get availableFields() {
      return this.layer.fieldsIndex.fields.map((e2) => e2.name);
    }
  };
  return e([y()], t2.prototype, "layer", void 0), e([y({ readOnly: true })], t2.prototype, "availableFields", null), t2 = e([a("esri.views.layers.OGCFeatureLayerView")], t2), t2;
};

// node_modules/@arcgis/core/views/2d/layers/OGCFeatureLayerView2D.js
var t = class extends o(D) {
  supportsSpatialReference(r) {
    return this.layer.serviceSupportsSpatialReference(r);
  }
};
t = e([a("esri.views.2d.layers.OGCFeatureLayerView2D")], t);
var a2 = t;
export {
  a2 as default
};
//# sourceMappingURL=OGCFeatureLayerView2D-N6MIPOLO.js.map
