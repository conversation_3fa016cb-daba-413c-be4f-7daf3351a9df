import {
  g
} from "./chunk-L6LJUVNN.js";
import "./chunk-XX6IKIRW.js";
import {
  E as E3
} from "./chunk-UJKR53XD.js";
import "./chunk-I73RJE3H.js";
import "./chunk-OWSDEANX.js";
import "./chunk-FJ3BZQJK.js";
import {
  v,
  y as y3
} from "./chunk-ZIKXCGU7.js";
import {
  e as e4
} from "./chunk-XSQFM27N.js";
import "./chunk-A7PY25IH.js";
import {
  s as s4
} from "./chunk-5ZZCQR67.js";
import "./chunk-PCLDCFRI.js";
import "./chunk-ZJC3GHA7.js";
import {
  i as i3
} from "./chunk-RR74IWZB.js";
import {
  p as p4
} from "./chunk-KTB2COPC.js";
import {
  S
} from "./chunk-HTXGAKOK.js";
import {
  p as p5
} from "./chunk-5BWF7URZ.js";
import {
  _
} from "./chunk-TV6WKLTU.js";
import "./chunk-5VDORDNW.js";
import {
  c as c3
} from "./chunk-FHKOFAQ2.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-P37TUI4J.js";
import {
  t as t2
} from "./chunk-NGPCXWDX.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-67MHB3E3.js";
import "./chunk-MKG56XBH.js";
import "./chunk-FZ7BG3VX.js";
import {
  y as y2
} from "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import {
  c as c2,
  f as f2,
  p as p3
} from "./chunk-YJWWP4AU.js";
import "./chunk-EQYLMJEJ.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-VTOVDEW2.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-LNGMNZ4I.js";
import "./chunk-24NZLSKM.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import {
  n as n2
} from "./chunk-CTPXU2ZH.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import {
  i as i2
} from "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w3
} from "./chunk-XTO3XXZ3.js";
import {
  p as p2
} from "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import {
  w as w2
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  E,
  f2 as f
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import {
  l as l2
} from "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import {
  a as a3,
  h
} from "./chunk-EIGTETCG.js";
import {
  E as E2,
  c,
  e as e3,
  i,
  r as r4,
  u as u2,
  z
} from "./chunk-MQAXMQFG.js";
import {
  e as e2,
  n,
  r as r2
} from "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import {
  r as r3,
  u
} from "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import {
  s as s3
} from "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import {
  o
} from "./chunk-H4S5JNVJ.js";
import {
  l
} from "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2,
  y
} from "./chunk-JN4FSB7Y.js";
import {
  T,
  a
} from "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import {
  w
} from "./chunk-EKX3LLYN.js";
import {
  s as s2
} from "./chunk-4RZONHOY.js";
import {
  s
} from "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  p
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/voxel/voxelPlaneUtils.js
var h2 = n();
var i4 = e4();
var p6 = e4();
var e5 = e4();
var j2 = r2(0, 0, 1);
var k = r2(0, 1, 0);
var q = r2(1, 0, 0);
function v2(a8) {
  r4(h2, a8), z(h2, h2);
  const n3 = Math.atan2(h2[1], h2[0]), o2 = v(e4(), j2, -n3);
  E2(h2, h2, o2);
  const r5 = -1 * Math.atan2(h2[2], h2[0]);
  return [u(n3) + 270, u(r5) + 90];
}
function M(t7, n3) {
  return v(p6, j2, r3(t7 - 270)), v(e5, k, r3(n3 - 90)), y3(i4, p6, e5), r4(h2, q), E2(h2, h2, i4), z(h2, h2), [h2[0], h2[1], h2[2]];
}

// node_modules/@arcgis/core/layers/voxel/VoxelSlice.js
var m = class extends i2(l) {
  constructor(t7) {
    super(t7), this.enabled = true, this.label = "", this.normal = null, this.point = null;
  }
  get orientation() {
    if (!Array.isArray(this.normal) || 3 !== this.normal.length) return 0;
    const [t7, o2] = v2(this.normal);
    return s4.normalize(a(t7), 0, true);
  }
  set orientation(t7) {
    const o2 = M(t7, this.tilt);
    this._set("normal", o2), this._set("orientation", t7);
  }
  get tilt() {
    if (!Array.isArray(this.normal) || 3 !== this.normal.length) return 0;
    const [t7, o2] = v2(this.normal);
    return s4.normalize(a(o2), 0, true);
  }
  set tilt(t7) {
    const o2 = M(this.orientation, t7);
    this._set("normal", o2), this._set("tilt", t7);
  }
};
e([y({ type: Boolean, json: { write: true } })], m.prototype, "enabled", void 0), e([y({ type: String, json: { write: true } })], m.prototype, "label", void 0), e([y({ type: Number, json: { read: false }, clonable: false, range: { min: 0, max: 360 } }), s3((t7) => s4.normalize(a(t7), 0, true))], m.prototype, "orientation", null), e([y({ type: Number, json: { read: false }, clonable: false, range: { min: 0, max: 360 } }), s3((t7) => s4.normalize(a(t7), 0, true))], m.prototype, "tilt", null), e([y({ type: [Number], json: { write: true } })], m.prototype, "normal", void 0), e([y({ type: [Number], json: { write: true } })], m.prototype, "point", void 0), m = e([a2("esri.layers.voxel.VoxelSlice")], m);
var c4 = m;

// node_modules/@arcgis/core/layers/voxel/VoxelSection.js
var d = class extends i2(l) {
  constructor() {
    super(...arguments), this.enabled = true, this.href = null, this.id = null, this.label = "", this.normal = null, this.point = null, this.sizeInPixel = null, this.slices = null, this.timeId = 0, this.variableId = null;
  }
  get orientation() {
    if (!Array.isArray(this.normal) || 3 !== this.normal.length) return 0;
    const [e6, r5] = v2(this.normal);
    return s4.normalize(a(e6), 0, true);
  }
  get tilt() {
    if (!Array.isArray(this.normal) || 3 !== this.normal.length) return 0;
    const [e6, r5] = v2(this.normal);
    return s4.normalize(a(r5), 0, true);
  }
};
e([y({ type: Boolean, json: { default: true, write: true } })], d.prototype, "enabled", void 0), e([y({ type: String, json: { origins: { service: { read: p2 } }, write: { enabled: true, isRequired: true } } }), g({ origins: ["web-scene"], type: "resource", prefix: "sections", compress: true })], d.prototype, "href", void 0), e([y({ type: T, json: { write: { enabled: true, isRequired: true } } })], d.prototype, "id", void 0), e([y({ type: String, json: { write: true } })], d.prototype, "label", void 0), e([y({ type: Number, clonable: false, readOnly: true, range: { min: 0, max: 360 } })], d.prototype, "orientation", null), e([y({ type: Number, clonable: false, readOnly: true, range: { min: 0, max: 360 } })], d.prototype, "tilt", null), e([y({ type: [Number], json: { write: { enabled: true, isRequired: true } } })], d.prototype, "normal", void 0), e([y({ type: [Number], json: { write: { enabled: true, isRequired: true } } })], d.prototype, "point", void 0), e([y({ type: [T], json: { write: { enabled: true, isRequired: true } } })], d.prototype, "sizeInPixel", void 0), e([y({ type: [c4], json: { write: true } })], d.prototype, "slices", void 0), e([y({ type: T, json: { default: 0, write: true } })], d.prototype, "timeId", void 0), e([y({ type: T, json: { write: { enabled: true, isRequired: true } } })], d.prototype, "variableId", void 0), d = e([a2("esri.layers.voxel.VoxelSection")], d);
var u3 = d;

// node_modules/@arcgis/core/layers/voxel/VoxelSimpleShading.js
var t3 = class extends l {
  constructor() {
    super(...arguments), this.diffuseFactor = 0.5, this.specularFactor = 0.5;
  }
};
e([y({ type: Number, range: { min: 0, max: 1 }, json: { default: 0.5, write: true } })], t3.prototype, "diffuseFactor", void 0), e([y({ type: Number, range: { min: 0, max: 1 }, json: { default: 0.5, write: true } })], t3.prototype, "specularFactor", void 0), t3 = e([a2("esri.layers.voxel.VoxelSimpleShading")], t3);
var p7 = t3;

// node_modules/@arcgis/core/layers/voxel/VoxelFormat.js
var s5 = class extends l {
  constructor() {
    super(...arguments), this.continuity = null, this.hasNoData = false, this.noData = 0, this.offset = 0, this.scale = 1, this.type = null;
  }
};
e([y({ type: ["discrete", "continuous"], json: { write: true } })], s5.prototype, "continuity", void 0), e([y({ type: Boolean, json: { write: true } })], s5.prototype, "hasNoData", void 0), e([y({ type: Number, json: { write: true } })], s5.prototype, "noData", void 0), e([y({ type: Number, json: { write: true } })], s5.prototype, "offset", void 0), e([y({ type: Number, json: { write: true } })], s5.prototype, "scale", void 0), e([y({ type: String, json: { write: { enabled: true, isRequired: true } } })], s5.prototype, "type", void 0), s5 = e([a2("esri.layers.voxel.VoxelFormat")], s5);
var p8 = s5;

// node_modules/@arcgis/core/layers/voxel/VoxelVariable.js
var i5 = class extends l {
  constructor() {
    super(...arguments), this.id = null, this.description = "", this.name = null, this.originalFormat = null, this.renderingFormat = null, this.unit = "", this.volumeId = 0, this.type = null;
  }
};
e([y({ type: Number, json: { write: { enabled: true, isRequired: true } } })], i5.prototype, "id", void 0), e([y({ type: String, json: { write: true } })], i5.prototype, "description", void 0), e([y({ type: String, json: { write: { enabled: true, isRequired: true } } })], i5.prototype, "name", void 0), e([y({ type: p8, json: { write: true } })], i5.prototype, "originalFormat", void 0), e([y({ type: p8, json: { write: { enabled: true, isRequired: true } } })], i5.prototype, "renderingFormat", void 0), e([y({ type: String, json: { write: true } })], i5.prototype, "unit", void 0), e([y({ type: Number, json: { write: true } })], i5.prototype, "volumeId", void 0), e([y({ type: ["stc-hot-spot-results", "stc-cluster-outlier-results", "stc-estimated-bin", "generic-nearest-interpolated"], json: { write: true } })], i5.prototype, "type", void 0), i5 = e([a2("esri.layers.voxel.VoxelVariable")], i5);
var p9 = i5;

// node_modules/@arcgis/core/layers/voxel/VoxelIsosurface.js
var l3 = class extends i2(l) {
  constructor() {
    super(...arguments), this.color = l2.fromArray([0, 0, 0, 0]), this.value = 0, this.enabled = true, this.label = "", this.colorLocked = false;
  }
};
e([y({ type: l2, json: { type: [T], write: { enabled: true, isRequired: true } } })], l3.prototype, "color", void 0), e([y({ type: Number, json: { write: { enabled: true, isRequired: true } } })], l3.prototype, "value", void 0), e([y({ type: Boolean, json: { default: true, write: true } })], l3.prototype, "enabled", void 0), e([y({ type: String, json: { write: true } })], l3.prototype, "label", void 0), e([y({ type: Boolean, json: { default: false, write: true } })], l3.prototype, "colorLocked", void 0), l3 = e([a2("esri.layers.voxel.VoxelIsosurface")], l3);
var a4 = l3;

// node_modules/@arcgis/core/layers/voxel/VoxelColorStop.js
var c5 = class extends i2(l) {
  constructor() {
    super(...arguments), this.color = null, this.position = 0;
  }
};
e([y({ type: l2, json: { type: [T], write: { enabled: true, isRequired: true } } })], c5.prototype, "color", void 0), e([y({ type: Number, json: { write: { enabled: true, isRequired: true } } })], c5.prototype, "position", void 0), c5 = e([a2("esri.layers.voxel.VoxelColorStop")], c5);
var l4 = c5;

// node_modules/@arcgis/core/layers/voxel/VoxelOpacityStop.js
var p10 = class extends i2(l) {
  constructor() {
    super(...arguments), this.opacity = 1, this.position = 0;
  }
};
e([y({ type: Number, json: { name: "alpha", write: { enabled: true, isRequired: true } } })], p10.prototype, "opacity", void 0), e([y({ type: Number, json: { write: { enabled: true, isRequired: true } } })], p10.prototype, "position", void 0), p10 = e([a2("esri.layers.voxel.VoxelOpacityStop")], p10);
var i6 = p10;

// node_modules/@arcgis/core/layers/voxel/VoxelRangeFilter.js
var p11 = class extends i2(l) {
  constructor() {
    super(...arguments), this.enabled = false, this.range = null;
  }
};
e([y({ type: Boolean, json: { default: false, write: true } })], p11.prototype, "enabled", void 0), e([y({ type: [Number], json: { write: true } })], p11.prototype, "range", void 0), p11 = e([a2("esri.layers.voxel.VoxelRangeFilter")], p11);
var a5 = p11;

// node_modules/@arcgis/core/layers/voxel/VoxelTransferFunctionStyle.js
var u4;
!function(o2) {
  o2[o2.Color = 1] = "Color", o2[o2.Alpha = 2] = "Alpha", o2[o2.Both = 3] = "Both";
}(u4 || (u4 = {}));
var y4 = class extends i2(l) {
  constructor(o2) {
    super(o2), this.interpolation = null, this.stretchRange = null, this.rangeFilter = null, this._colorMapSize = 256, this.colorStops = new (j.ofType(l4))(), this.opacityStops = new (j.ofType(i6))();
  }
  set colorStops(o2) {
    this._set("colorStops", n2(o2, this._get("colorStops"), j.ofType(l4)));
  }
  set opacityStops(o2) {
    this._set("opacityStops", n2(o2, this._get("opacityStops"), j.ofType(i6)));
  }
  getPreviousNext(o2, t7, r5) {
    let e6 = o2;
    for (; --e6 > 0 && t7[e6].type !== r5 && t7[e6].type !== u4.Both; ) ;
    let s7 = o2;
    const i7 = t7.length;
    for (; ++s7 < i7 && t7[s7].type !== r5 && t7[s7].type !== u4.Both; ) ;
    return [e6, s7];
  }
  get rasterizedTransferFunction() {
    const o2 = [];
    if (this.colorStops.length < 2) return o2;
    const r5 = [], e6 = [], s7 = 1e-5;
    for (const t7 of this.colorStops) {
      if (!t7.color) return o2;
      e6.push({ color: { r: t7.color.r, g: t7.color.g, b: t7.color.b, a: Math.round(255 * (1 - t7.color.a)) }, position: t7.position, type: u4.Color });
    }
    if (0 === this.opacityStops.length) for (const t7 of e6) r5.push({ color: t7.color, position: t7.position });
    else {
      for (const t7 of this.opacityStops) {
        const o4 = a3(t7.position, 0, 1), r6 = Math.round(255 * a3(1 - t7.opacity, 0, 1));
        let i8 = false;
        for (const t8 of e6) if (t8.type === u4.Color && Math.abs(t8.position - o4) < s7) {
          t8.color.a = r6, t8.type = u4.Both, i8 = true;
          break;
        }
        i8 || e6.push({ color: { r: 0, g: 0, b: 0, a: r6 }, position: t7.position, type: u4.Alpha });
      }
      e6.sort((o4, t7) => o4.position < t7.position ? -1 : 1);
      const o3 = e6.length;
      for (let t7 = 0; t7 < o3; ++t7) {
        const r6 = e6[t7];
        if (r6.type !== u4.Both) if (r6.type === u4.Color) {
          const [s8, i8] = this.getPreviousNext(t7, e6, u4.Alpha);
          if (-1 !== s8 && i8 !== o3) {
            const o4 = (r6.position - e6[s8].position) / (e6[i8].position - e6[s8].position);
            r6.color.a = Math.round(h(e6[s8].color.a, e6[i8].color.a, o4));
          } else r6.color.a = -1 !== s8 ? e6[s8].color.a : e6[i8].color.a;
        } else {
          const [s8, i8] = this.getPreviousNext(t7, e6, u4.Color);
          if (-1 !== s8 && i8 !== o3) {
            const o4 = (r6.position - e6[s8].position) / (e6[i8].position - e6[s8].position), t8 = e6[s8].color, p18 = e6[i8].color;
            ["r", "g", "b"].forEach((e7) => {
              r6.color[e7] = Math.round(h(t8[e7], p18[e7], o4));
            });
          } else ["r", "g", "b"].forEach(-1 !== s8 ? (o4) => {
            r6.color[o4] = e6[s8][o4];
          } : (o4) => {
            r6.color[o4] = e6[i8][o4];
          });
        }
      }
      for (const t7 of e6) r5.push({ color: t7.color, position: t7.position });
    }
    r5[0].position = 0, r5[r5.length - 1].position = 1;
    let i7 = 0, l6 = 1;
    for (let c8 = 0; c8 < this._colorMapSize; ++c8) {
      const e7 = c8 / this._colorMapSize;
      for (; e7 > r5[l6].position; ) i7 = l6++;
      const s8 = (e7 - r5[i7].position) / (r5[l6].position - r5[i7].position), a8 = r5[i7].color, h3 = r5[l6].color, f5 = new l2();
      ["r", "g", "b"].forEach((o3) => {
        f5[o3] = Math.round(h(a8[o3], h3[o3], s8));
      }), f5.a = a3(1 - h(a8.a, h3.a, s8) / 255, 0, 1), o2.push(f5);
    }
    return o2;
  }
  getColorForContinuousDataValue(o2, t7) {
    const r5 = this.rasterizedTransferFunction;
    if (this.colorStops.length < 2 || !Array.isArray(this.stretchRange) || this.stretchRange.length < 2 || r5.length < 256) return null;
    let e6 = this.stretchRange[0], s7 = this.stretchRange[1];
    if (e6 > s7) {
      const o3 = e6;
      e6 = s7, s7 = o3;
    }
    o2 = a3(o2, e6, s7);
    const i7 = r5[Math.round((o2 - e6) / (s7 - e6) * (this._colorMapSize - 1))].clone();
    return t7 || (i7.a = 1), i7;
  }
};
e([y({ type: ["linear", "nearest"], json: { write: true } })], y4.prototype, "interpolation", void 0), e([y({ type: [Number], json: { write: { enabled: true, isRequired: true } } })], y4.prototype, "stretchRange", void 0), e([y({ type: j.ofType(l4), json: { write: { enabled: true, overridePolicy() {
  return { enabled: !!this.colorStops && this.colorStops.length > 0 };
} } } })], y4.prototype, "colorStops", null), e([y({ type: j.ofType(i6), json: { read: { source: "alphaStops" }, write: { enabled: true, target: "alphaStops", overridePolicy() {
  return { enabled: !!this.opacityStops && this.opacityStops.length > 0 };
} } } })], y4.prototype, "opacityStops", null), e([y({ type: a5, json: { write: true } })], y4.prototype, "rangeFilter", void 0), e([y({ type: [l2], clonable: false, json: { read: false } })], y4.prototype, "rasterizedTransferFunction", null), y4 = e([a2("esri.layers.voxel.VoxelTransferFunctionStyle")], y4);
var g2 = y4;

// node_modules/@arcgis/core/layers/voxel/VoxelUniqueValue.js
var l5 = class extends i2(l) {
  constructor() {
    super(...arguments), this.color = l2.fromArray([0, 0, 0, 0]), this.value = 0, this.enabled = true, this.label = "";
  }
};
e([y({ type: l2, json: { type: [T], write: { enabled: true, isRequired: true } } })], l5.prototype, "color", void 0), e([y({ type: T, json: { write: { enabled: true, isRequired: true } } })], l5.prototype, "value", void 0), e([y({ type: Boolean, json: { default: true, write: true } })], l5.prototype, "enabled", void 0), e([y({ type: String, json: { write: true } })], l5.prototype, "label", void 0), l5 = e([a2("esri.layers.voxel.VoxelUniqueValue")], l5);
var a6 = l5;

// node_modules/@arcgis/core/layers/voxel/VoxelVariableStyle.js
var p12;
var c6 = p12 = class extends l {
  constructor(e6) {
    super(e6), this.variableId = 0, this.label = "", this.transferFunction = null, this.uniqueValues = null, this.isosurfaces = null, this.uniqueValues = new (j.ofType(a6))(), this.isosurfaces = new (j.ofType(a4))();
  }
  clone() {
    return new p12({ variableId: this.variableId, label: this.label, transferFunction: p(this.transferFunction), uniqueValues: p(this.uniqueValues), isosurfaces: p(this.isosurfaces) });
  }
};
e([y({ type: T, json: { write: { enabled: true, isRequired: true } } })], c6.prototype, "variableId", void 0), e([y({ type: String, json: { write: true } })], c6.prototype, "label", void 0), e([y({ type: g2, json: { write: { enabled: true, overridePolicy() {
  return { enabled: !this.uniqueValues || this.uniqueValues.length < 1 };
} } } })], c6.prototype, "transferFunction", void 0), e([y({ type: j.ofType(a6), json: { write: { enabled: true, overridePolicy() {
  return { enabled: !!this.uniqueValues && this.uniqueValues.length > 0 };
} } } })], c6.prototype, "uniqueValues", void 0), e([y({ type: j.ofType(a4), json: { write: { enabled: true, overridePolicy() {
  const e6 = !this.uniqueValues || this.uniqueValues.length < 1, s7 = !!this.isosurfaces && this.isosurfaces.length > 0;
  return { enabled: e6 && s7 };
} } } })], c6.prototype, "isosurfaces", void 0), c6 = p12 = e([a2("esri.layers.voxel.VoxelVariableStyle")], c6);
var f3 = c6;

// node_modules/@arcgis/core/layers/voxel/VoxelIrregularSpacing.js
var t4 = class extends l {
  constructor() {
    super(...arguments), this.values = null;
  }
};
e([y({ type: [Number], json: { write: true } })], t4.prototype, "values", void 0), t4 = e([a2("esri.layers.voxel.VoxelIrregularSpacing")], t4);
var p13 = t4;

// node_modules/@arcgis/core/layers/voxel/VoxelRegularSpacing.js
var t5 = class extends l {
  constructor() {
    super(...arguments), this.scale = 1, this.offset = 0;
  }
};
e([y({ type: Number, json: { write: true } })], t5.prototype, "scale", void 0), e([y({ type: Number, json: { write: true } })], t5.prototype, "offset", void 0), t5 = e([a2("esri.layers.voxel.VoxelRegularSpacing")], t5);
var p14 = t5;

// node_modules/@arcgis/core/layers/voxel/VoxelDimension.js
var p15 = class extends l {
  constructor() {
    super(...arguments), this.irregularSpacing = null, this.isPositiveUp = true, this.isWrappedDateLine = false, this.label = null, this.name = null, this.quantity = null, this.regularSpacing = null, this.size = 0, this.unit = null;
  }
  get isRegular() {
    return (null == this.irregularSpacing || void 0 === this.irregularSpacing) && null !== this.regularSpacing;
  }
  getRange() {
    var _a;
    return this.isRegular ? [this.regularSpacing.offset, this.regularSpacing.offset + this.regularSpacing.scale * (this.size - 1)] : Array.isArray((_a = this.irregularSpacing) == null ? void 0 : _a.values) && this.irregularSpacing.values.length > 1 ? [this.irregularSpacing.values[0], this.irregularSpacing.values[this.irregularSpacing.values.length - 1]] : [0, 0];
  }
};
e([y({ type: p13, json: { write: true } })], p15.prototype, "irregularSpacing", void 0), e([y({ type: Boolean, json: { write: true } })], p15.prototype, "isPositiveUp", void 0), e([y({ type: Boolean, json: { write: true } })], p15.prototype, "isWrappedDateLine", void 0), e([y({ type: String, json: { write: true } })], p15.prototype, "label", void 0), e([y({ type: String, json: { write: true } })], p15.prototype, "name", void 0), e([y({ type: String, json: { write: true } })], p15.prototype, "quantity", void 0), e([y({ type: p14, json: { write: true } })], p15.prototype, "regularSpacing", void 0), e([y({ type: Number, json: { write: true } })], p15.prototype, "size", void 0), e([y({ type: String, json: { write: true } })], p15.prototype, "unit", void 0), e([y({ type: Boolean, json: { read: false } })], p15.prototype, "isRegular", null), p15 = e([a2("esri.layers.voxel.VoxelDimension")], p15);
var a7 = p15;

// node_modules/@arcgis/core/layers/voxel/VoxelVolume.js
var d2 = "esri.layers.voxel.VoxelVolume";
var y5 = s.getLogger(d2);
var f4 = class extends l {
  constructor(e6) {
    super(e6), this.id = 0, this.dimensions = null, this.spatialReference = f.WGS84;
  }
  get zDimension() {
    if (!this.dimensions) return -1;
    if (!Array.isArray(this.dimensions)) return -1;
    if (4 !== this.dimensions.length) return -1;
    for (let e6 = 2; e6 < 4; ++e6) if (this.dimensions[e6].size > 0) return e6;
    return -1;
  }
  get isValid() {
    return !!this.dimensions && (!!Array.isArray(this.dimensions) && (4 === this.dimensions.length && (!(this.dimensions[0].size < 1 || this.dimensions[1].size < 1) && !(-1 === this.zDimension || this.dimensions[this.zDimension].size < 1))));
  }
  get originInLayerSpace3D() {
    if (!this.isValid || "xyt" === this.volumeType) return [0, 0, 0];
    const e6 = this.dimensions[0].getRange(), i7 = this.dimensions[1].getRange(), s7 = this.dimensions[2], r5 = s7.isRegular ? s7.getRange() : [0, s7.size];
    return [e6[0], i7[0], r5[0]];
  }
  get voxelSizeInLayerSpaceSigned() {
    if (!this.isValid || "xyt" === this.volumeType) return [0, 0, 0];
    const e6 = this.dimensions[0].getRange(), i7 = this.dimensions[1].getRange(), s7 = this.dimensions[2], r5 = s7.isRegular ? s7.getRange() : [0, s7.size], t7 = [this.sizeInVoxels[0], this.sizeInVoxels[1], this.sizeInVoxels[2]];
    for (let n3 = 0; n3 < 3; ++n3) t7[n3] < 2 ? t7[n3] = 1 : t7[n3] -= 1;
    return s7.isRegular && !s7.isPositiveUp && (t7[2] *= -1), [(e6[1] - e6[0]) / t7[0], (i7[1] - i7[0]) / t7[1], (r5[1] - r5[0]) / t7[2]];
  }
  get volumeType() {
    if (this.isValid) {
      const e6 = this.dimensions[2].size > 0, i7 = this.dimensions[3].size > 0;
      if (!e6 && i7) return "xyt";
      if (e6 && i7) return "xyzt";
    }
    return "xyz";
  }
  get sizeInVoxels() {
    if (!this.isValid) return [0, 0, 0];
    const e6 = this.zDimension;
    return [this.dimensions[0].size, this.dimensions[1].size, this.dimensions[e6].size];
  }
  computeVoxelSpaceLocation(e6) {
    var _a;
    if (!this.isValid) return [0, 0, 0];
    if ("xyt" === this.volumeType) return y5.error("computeVoxelSpacePosition cannot be used with XYT volumes."), [0, 0, 0];
    if (!E(this.spatialReference, e6.spatialReference)) return y5.error("pos argument should have the same spatial reference as the VoxelLayer."), [0, 0, 0];
    const i7 = r2(e6.x, e6.y, e6.z ?? 0);
    e3(i7, i7, this.originInLayerSpace3D), i(i7, i7, this.voxelSizeInLayerSpaceSigned);
    const s7 = this.dimensions[this.zDimension];
    if (!s7.isRegular && Array.isArray((_a = s7.irregularSpacing) == null ? void 0 : _a.values) && s7.irregularSpacing.values.length > 1) {
      const r5 = e6.z ?? 0, t7 = s7.irregularSpacing.values, n3 = s7.isPositiveUp ? 1 : -1, o2 = t7.reduce((e7, i8) => Math.abs(n3 * i8 - r5) < Math.abs(n3 * e7 - r5) ? i8 : e7);
      for (let e7 = 0; e7 < t7.length; ++e7) if (t7[e7] === o2) {
        i7[2] = e7;
        break;
      }
    }
    return [i7[0], i7[1], i7[2]];
  }
  computeLayerSpaceLocation(e6) {
    var _a;
    if (!this.isValid) return new w2({ x: 0, y: 0, spatialReference: this.spatialReference });
    const i7 = e2(e6);
    if (c(i7, i7, this.voxelSizeInLayerSpaceSigned), u2(i7, i7, this.originInLayerSpace3D), "xyt" === this.volumeType) return new w2({ x: i7[0], y: i7[1], spatialReference: this.spatialReference });
    const s7 = this.dimensions[this.zDimension];
    return s7.isRegular || Array.isArray((_a = s7.irregularSpacing) == null ? void 0 : _a.values) && (e6[2] < 0 ? i7[2] = s7.irregularSpacing.values[0] : e6[2] < s7.irregularSpacing.values.length ? i7[2] = s7.irregularSpacing.values[e6[2]] : i7[2] = s7.irregularSpacing.values[s7.irregularSpacing.values.length - 1], s7.isPositiveUp || (i7[2] *= -1)), new w2({ x: i7[0], y: i7[1], z: i7[2], spatialReference: this.spatialReference });
  }
};
e([y({ type: Number, json: { write: { enabled: true, isRequired: true } } })], f4.prototype, "id", void 0), e([y({ type: [a7], json: { write: { enabled: true, isRequired: true } } })], f4.prototype, "dimensions", void 0), e([y({ type: f, json: { read: { enabled: false } } })], f4.prototype, "spatialReference", void 0), e([y({ type: Number, json: { read: false } })], f4.prototype, "zDimension", null), e([y({ type: [Boolean], json: { read: false } })], f4.prototype, "isValid", null), e([y({ type: [Number], json: { read: false } })], f4.prototype, "originInLayerSpace3D", null), e([y({ type: [Number], json: { read: false } })], f4.prototype, "voxelSizeInLayerSpaceSigned", null), e([y({ type: ["xyz", "xyzt", "xyt"], json: { read: { enabled: false } } })], f4.prototype, "volumeType", null), e([y({ type: [Number], json: { read: false } })], f4.prototype, "sizeInVoxels", null), f4 = e([a2(d2)], f4);
var S2 = f4;

// node_modules/@arcgis/core/layers/voxel/VoxelVolumeIndex.js
var t6;
var s6 = t6 = class extends l {
  constructor() {
    super(...arguments), this.apronWidth = 1, this.brickSize = [32, 32, 32], this.maxLodLevel = 0, this.nodeSize = [4, 4, 4];
  }
  isValid() {
    const e6 = new t6();
    return e6.apronWidth === this.apronWidth && e6.maxLodLevel === this.maxLodLevel && (!!this.brickSize && (!!this.nodeSize && (!(!Array.isArray(this.brickSize) || !Array.isArray(this.nodeSize)) && (3 === this.brickSize.length && 3 === this.nodeSize.length && (32 === this.brickSize[0] && 32 === this.brickSize[1] && 32 === this.brickSize[2] && (4 === this.nodeSize[0] && 4 === this.nodeSize[1] && 4 === this.nodeSize[2]))))));
  }
};
e([y({ type: Number, json: { write: { enabled: true, isRequired: true } } })], s6.prototype, "apronWidth", void 0), e([y({ type: [Number], json: { write: { enabled: true, isRequired: true } } })], s6.prototype, "brickSize", void 0), e([y({ type: Number, json: { write: { enabled: true, isRequired: true } } })], s6.prototype, "maxLodLevel", void 0), e([y({ type: [Number], json: { write: { enabled: true, isRequired: true } } })], s6.prototype, "nodeSize", void 0), s6 = t6 = e([a2("esri.layers.voxel.VoxelVolumeIndex")], s6);
var p16 = s6;

// node_modules/@arcgis/core/layers/voxel/VoxelDynamicSection.js
var m2 = class extends i2(l) {
  constructor(t7) {
    super(t7), this.enabled = true, this.label = "", this.normal = null, this.point = null;
  }
  get orientation() {
    if (!Array.isArray(this.normal) || 3 !== this.normal.length) return 0;
    const [t7, o2] = v2(this.normal);
    return s4.normalize(a(t7), 0, true);
  }
  set orientation(t7) {
    const o2 = M(t7, this.tilt);
    this._set("normal", o2), this._set("orientation", t7);
  }
  get tilt() {
    if (!Array.isArray(this.normal) || 3 !== this.normal.length) return 0;
    const [t7, o2] = v2(this.normal);
    return s4.normalize(a(o2), 0, true);
  }
  set tilt(t7) {
    const o2 = M(this.orientation, t7);
    this._set("normal", o2), this._set("tilt", t7);
  }
};
e([y({ type: Boolean, json: { default: true, write: true } })], m2.prototype, "enabled", void 0), e([y({ type: String, json: { write: true } })], m2.prototype, "label", void 0), e([y({ type: Number, json: { read: false }, clonable: false, range: { min: 0, max: 360 } }), s3((t7) => s4.normalize(a(t7), 0, true))], m2.prototype, "orientation", null), e([y({ type: Number, json: { read: false }, clonable: false, range: { min: 0, max: 360 } }), s3((t7) => s4.normalize(a(t7), 0, true))], m2.prototype, "tilt", null), e([y({ type: [Number], json: { write: true } })], m2.prototype, "normal", void 0), e([y({ type: [Number], json: { write: true } })], m2.prototype, "point", void 0), m2 = e([a2("esri.layers.voxel.VoxelDynamicSection")], m2);
var c7 = m2;

// node_modules/@arcgis/core/layers/voxel/VoxelVolumeStyle.js
var p17;
var m3 = p17 = class extends l {
  constructor(e6) {
    super(e6), this.volumeId = 0, this.verticalExaggeration = 1, this.exaggerationMode = "scale-height", this.verticalOffset = 0, this.slices = new (j.ofType(c4))(), this.dynamicSections = new (j.ofType(c7))();
  }
  set slices(e6) {
    this._set("slices", n2(e6, this._get("slices"), j.ofType(c4)));
  }
  set dynamicSections(e6) {
    this._set("dynamicSections", n2(e6, this._get("dynamicSections"), j.ofType(c7)));
  }
  clone() {
    return new p17({ volumeId: this.volumeId, verticalExaggeration: this.verticalExaggeration, exaggerationMode: this.exaggerationMode, verticalOffset: this.verticalOffset, slices: p(this.slices), dynamicSections: p(this.dynamicSections) });
  }
};
e([y({ type: T, json: { write: { enabled: true, isRequired: true } } })], m3.prototype, "volumeId", void 0), e([y({ type: Number, json: { default: 1, write: true } })], m3.prototype, "verticalExaggeration", void 0), e([y({ type: ["scale-position", "scale-height"], json: { default: "scale-height", write: true } })], m3.prototype, "exaggerationMode", void 0), e([y({ type: Number, json: { default: 0, write: true } })], m3.prototype, "verticalOffset", void 0), e([y({ type: j.ofType(c4), json: { write: { enabled: true, overridePolicy() {
  return { enabled: !!this.slices && this.slices.length > 0 };
} } } })], m3.prototype, "slices", null), e([y({ type: j.ofType(c7), json: { write: { enabled: true, overridePolicy() {
  return { enabled: !!this.dynamicSections && this.dynamicSections.length > 0 };
} } } })], m3.prototype, "dynamicSections", null), m3 = p17 = e([a2("esri.layers.voxel.VoxelVolumeStyle")], m3);
var d3 = m3;

// node_modules/@arcgis/core/layers/VoxelLayer.js
var R = "esri.layers.VoxelLayer";
var M2 = s.getLogger(R);
var z2 = class extends E3(p4(c3(_(t2(O(i3(b))))))) {
  constructor(e6) {
    super(e6), this.serviceRoot = "", this.operationalLayerType = "Voxel", this.legendEnabled = true, this.title = null, this.sections = null, this.currentVariableId = 0, this.volumeStyles = null, this.renderMode = "volume", this.variableStyles = null, this.enableSlices = true, this.enableSections = true, this.enableDynamicSections = true, this.enableIsosurfaces = true, this.shading = new p7(), this.opacity = 1, this.variables = new j(), this.volumes = new j(), this.index = null, this.minScale = 0, this.maxScale = 0, this.type = "voxel", this.version = { major: Number.NaN, minor: Number.NaN, versionString: "" }, this.fullExtent = null, this.popupEnabled = true, this.popupTemplate = null, this.test = null, this.volumeStyles = new (j.ofType(d3))(), this.variableStyles = new (j.ofType(f3))(), this.sections = new (j.ofType(u3))(), (e6 == null ? void 0 : e6.constantUpscaling) && (this.test = { constantUpscaling: true });
  }
  set url(e6) {
    this._set("url", S(e6, M2));
  }
  load(e6) {
    const t7 = r(e6) ? e6.signal : null, i7 = this.loadFromPortal({ supportedTypes: ["Scene Service"] }, e6).catch(w).then(() => this._fetchService(t7)).then(() => this.serviceRoot = this.url);
    return this.addResolvingPromise(i7), Promise.resolve(this);
  }
  read(e6, t7) {
    super.read(e6, t7);
    for (const i7 of this.volumes) i7.spatialReference = this.spatialReference;
  }
  readVersion(e6, t7) {
    return super.parseVersionString(e6);
  }
  validateLayer(e6) {
    if (e6.layerType && e6.layerType !== this.operationalLayerType) throw new s2("voxel-layer:layer-type-not-supported", "VoxelLayer does not support this layer type", { layerType: e6.layerType });
    if (isNaN(this.version.major) || isNaN(this.version.minor) || this.version.major < 3) throw new s2("layer:service-version-not-supported", "Service version is not supported.", { serviceVersion: this.version.versionString, supportedVersions: "3.x" });
    if (this.version.major > 3) throw new s2("layer:service-version-too-new", "Service version is too new.", { serviceVersion: this.version.versionString, supportedVersions: "3.x" });
  }
  readFullExtent(e6, t7, i7) {
    if (null != e6 && "object" == typeof e6) {
      const o2 = w3.fromJSON(e6, i7);
      if (0 === o2.zmin && 0 === o2.zmax && Array.isArray(t7.volumes)) {
        const e7 = S2.fromJSON(t7.volumes[0]);
        if (e7.isValid && "xyt" !== e7.volumeType) {
          const t8 = e7.dimensions[2];
          if (t8.isRegular) {
            let e8 = t8.regularSpacing.offset, i8 = t8.regularSpacing.offset + t8.regularSpacing.scale * (t8.size - 1);
            if (e8 > i8) {
              const t9 = e8;
              e8 = i8, i8 = t9;
            }
            o2.zmin = e8, o2.zmax = i8;
          }
        }
      }
      return o2;
    }
    return null;
  }
  get voxelFields() {
    const e6 = [new y2({ name: "Voxel.ServiceValue", alias: "Value", domain: null, editable: false, length: 128, type: "string" }), new y2({ name: "Voxel.ServiceVariableLabel", alias: "Variable", domain: null, editable: false, length: 128, type: "string" }), new y2({ name: "Voxel.Position", alias: "Voxel Position", domain: null, editable: false, length: 128, type: "string" })], t7 = this.getVolume(null);
    if (r(t7)) {
      if ("xyzt" === t7.volumeType || "xyt" === t7.volumeType) {
        const t8 = new y2({ name: "Voxel.ServiceLocalTime", alias: "Local Time", domain: null, editable: false, length: 128, type: "string" });
        e6.push(t8);
        const i7 = new y2({ name: "Voxel.ServiceNativeTime", alias: "Native Time", domain: null, editable: false, length: 128, type: "string" });
        e6.push(i7);
      }
      if ("xyt" !== t7.volumeType) {
        const t8 = new y2({ name: "Voxel.ServiceDepth", alias: "Depth", domain: null, editable: false, length: 128, type: "string" });
        e6.push(t8);
      }
    }
    return e6;
  }
  get defaultPopupTemplate() {
    return this.createPopupTemplate();
  }
  createPopupTemplate(e6) {
    const t7 = this.voxelFields, i7 = this.title;
    return p5({ fields: t7, title: i7 }, e6);
  }
  getConfiguration() {
    var _a, _b;
    const e6 = { layerType: this.operationalLayerType, version: this.version.versionString, name: this.title, spatialReference: this.spatialReference, fullExtent: this.fullExtent, volumes: this.volumes.toJSON(), variables: this.variables.toJSON(), index: (_a = this.index) == null ? void 0 : _a.toJSON(), sections: this.getSections(), style: { volumeStyles: this.getVolumeStyles(), currentVariableId: this.currentVariableId, renderMode: this.renderMode, variableStyles: this.getVariableStyles(), enableSections: this.enableSections, enableDynamicSections: this.enableDynamicSections, enableIsosurfaces: this.enableIsosurfaces, enableSlices: this.enableSlices, shading: this.shading } };
    return e6.index && ((_b = this.index) == null ? void 0 : _b.isValid()) ? JSON.stringify(e6) : "";
  }
  getVariableStyle(e6) {
    let t7 = -1;
    t7 = r(e6) ? e6 : this.currentVariableId;
    if (!this.variableStyles || -1 === t7) return null;
    const i7 = this.variableStyles.findIndex((e7) => e7.variableId === t7);
    return i7 < 0 ? null : this.variableStyles.getItemAt(i7);
  }
  getVariable(e6) {
    let t7 = -1;
    if (t7 = r(e6) ? e6 : this.currentVariableId, !this.variables || -1 === t7) return null;
    const i7 = this.variables.findIndex((e7) => e7.id === t7);
    return i7 < 0 ? null : this.variables.getItemAt(i7);
  }
  getVolume(e6) {
    const t7 = this.getVariable(e6);
    return r(t7) ? this.volumes.find(({ id: e7 }) => e7 === t7.volumeId) : null;
  }
  getVolumeStyle(e6) {
    const t7 = this.getVariable(e6);
    return r(t7) ? this.volumeStyles.find(({ volumeId: e7 }) => e7 === t7.volumeId) : null;
  }
  getColorForContinuousDataValue(e6, t7, i7) {
    var _a;
    const o2 = this.getVariable(e6);
    if (t(o2) || "continuous" !== ((_a = o2.renderingFormat) == null ? void 0 : _a.continuity)) return null;
    if (!this.variableStyles) return null;
    const r5 = this.variableStyles.findIndex((t8) => t8.variableId === e6);
    if (r5 < 0) return null;
    const n3 = this.variableStyles.getItemAt(r5);
    return n3.transferFunction ? n3.transferFunction.getColorForContinuousDataValue(t7, i7) : null;
  }
  getSections() {
    const e6 = [];
    for (const t7 of this.sections) e6.push(new u3({ enabled: t7.enabled, href: t7.href, id: t7.id, label: t7.label, normal: t7.normal, point: t7.point, sizeInPixel: t7.sizeInPixel, slices: t7.slices, timeId: t7.timeId, variableId: t7.variableId }));
    return e6;
  }
  getVariableStyles() {
    const e6 = [];
    for (const t7 of this.variableStyles) {
      const i7 = this._getVariable(t7);
      if (r(i7)) {
        const o2 = t7.clone();
        o2.isosurfaces.length > 4 && (o2.isosurfaces = o2.isosurfaces.slice(0, 3), M2.error("A maximum of 4 isosurfaces are supported for Voxel Layers."));
        for (const e7 of o2.isosurfaces) if (!e7.colorLocked) {
          const t8 = this.getColorForContinuousDataValue(o2.variableId, e7.value, false);
          null === t8 || t8.equals(e7.color) || (e7.color = t8);
        }
        if ("continuous" === i7.renderingFormat.continuity) (null === o2.transferFunction || o2.transferFunction.colorStops.length < 2) && M2.error(`VoxelVariableStyle for variable ${i7.id} is invalid. At least 2 color stops are required in the transferFunction for continuous Voxel Layer variables.`), null !== o2.transferFunction && (Array.isArray(o2.transferFunction.stretchRange) && 2 === o2.transferFunction.stretchRange.length || (M2.error(`VoxelVariableStyle for variable ${i7.id} is invalid. The stretchRange of the transferFunction for continuous Voxel Layer variables must be of the form [minimumDataValue, maximumDataValue].`), o2.transferFunction.stretchRange = [0, 1], o2.transferFunction.colorStops.removeAll()));
        else if ("discrete" === i7.renderingFormat.continuity) if (0 === t7.uniqueValues.length) M2.error(`VoxelVariableStyle for variable ${i7.id} is invalid. Unique values are required for discrete Voxel Layer variables.`);
        else for (const e7 of t7.uniqueValues) null !== e7.label && void 0 !== e7.label || null === e7.value || void 0 === e7.value || (e7.label = e7.value.toString());
        e6.push(o2);
      } else M2.error(`VoxelVariable ID=${t7.variableId} doesn't exist, VoxelVariableStyle for this VoxelVariable will be ignored.`);
    }
    return e6;
  }
  getVolumeStyles() {
    const e6 = [];
    for (const t7 of this.volumeStyles) {
      const i7 = this._getVolumeFromVolumeId(t7.volumeId);
      if (r(i7)) {
        const o2 = t7.clone();
        for (const e7 of o2.slices) this._isPlaneValid(e7, [0, 1, i7.zDimension], i7.dimensions) || (e7.enabled = false, e7.label = "invalid");
        for (const e7 of o2.dynamicSections) this._isPlaneValid(e7, [0, 1, i7.zDimension], i7.dimensions) || (e7.enabled = false, e7.label = "invalid");
        e6.push(o2);
      } else M2.error(`VoxelVolume ID=${t7.volumeId} doesn't exist, VoxelVolumeStyle for this VoxelVolume will be ignored.`);
    }
    return e6;
  }
  _getVariable(e6) {
    const t7 = e6.variableId;
    for (const i7 of this.variables) if (i7.id === t7) return i7;
    return null;
  }
  _getVolumeFromVolumeId(e6) {
    for (const t7 of this.volumes) if (t7.id === e6) return t7;
    return null;
  }
  _isPlaneValid(e6, t7, i7) {
    if (!e6.point) return false;
    if (!Array.isArray(e6.point) || 3 !== e6.point.length) return false;
    if (!e6.normal) return false;
    if (!Array.isArray(e6.normal) || 3 !== e6.normal.length) return false;
    for (let s7 = 0; s7 < 3; ++s7) {
      const o3 = e6.point[s7];
      if (o3 < 0 || o3 >= i7[t7[s7]].size) return false;
    }
    const o2 = r2(e6.normal[0], e6.normal[1], e6.normal[2]);
    z(o2, o2);
    const r5 = 1e-6;
    return !(Math.abs(o2[0]) + Math.abs(o2[1]) + Math.abs(o2[2]) < r5) && (e6.normal[0] = o2[0], e6.normal[1] = o2[1], e6.normal[2] = o2[2], true);
  }
};
e([y({ type: ["Voxel"] })], z2.prototype, "operationalLayerType", void 0), e([y(c2)], z2.prototype, "legendEnabled", void 0), e([y({ json: { write: true } })], z2.prototype, "title", void 0), e([y(f2)], z2.prototype, "url", null), e([y({ type: j.ofType(u3), json: { origins: { "web-scene": { name: "layerDefinition.sections", write: true } } } })], z2.prototype, "sections", void 0), e([y({ type: T, json: { origins: { "web-scene": { name: "layerDefinition.style.currentVariableId", write: { enabled: true, isRequired: true, ignoreOrigin: true } }, service: { name: "style.currentVariableId" } } } })], z2.prototype, "currentVariableId", void 0), e([y({ type: j.ofType(d3), json: { origins: { "web-scene": { name: "layerDefinition.style.volumeStyles", write: true }, service: { name: "style.volumeStyles" } } } })], z2.prototype, "volumeStyles", void 0), e([y({ type: ["volume", "surfaces"], json: { origins: { "web-scene": { name: "layerDefinition.style.renderMode", write: true }, service: { name: "style.renderMode" } } } })], z2.prototype, "renderMode", void 0), e([y({ type: j.ofType(f3), json: { origins: { "web-scene": { name: "layerDefinition.style.variableStyles", write: true }, service: { name: "style.variableStyles" } } } })], z2.prototype, "variableStyles", void 0), e([y({ type: Boolean, json: { origins: { "web-scene": { name: "layerDefinition.style.enableSlices", write: true }, service: { name: "style.enableSlices" } } } })], z2.prototype, "enableSlices", void 0), e([y({ type: Boolean, json: { origins: { "web-scene": { name: "layerDefinition.style.enableSections", write: true }, service: { name: "style.enableSections" } } } })], z2.prototype, "enableSections", void 0), e([y({ type: Boolean, json: { origins: { "web-scene": { name: "layerDefinition.style.enableDynamicSections", write: true }, service: { name: "style.enableDynamicSections" } } } })], z2.prototype, "enableDynamicSections", void 0), e([y({ type: Boolean, json: { origins: { "web-scene": { name: "layerDefinition.style.enableIsosurfaces", write: true }, service: { name: "style.enableIsosurfaces" } } } })], z2.prototype, "enableIsosurfaces", void 0), e([y({ type: p7, json: { origins: { "web-scene": { name: "layerDefinition.style.shading", write: true }, service: { name: "style.shading" } } } })], z2.prototype, "shading", void 0), e([y({ type: ["show", "hide"] })], z2.prototype, "listMode", void 0), e([y({ type: Number, range: { min: 0, max: 1 }, nonNullable: true, json: { read: false, write: false, origins: { "web-scene": { read: false, write: false }, "portal-item": { read: false, write: false } } } })], z2.prototype, "opacity", void 0), e([y({ type: j.ofType(p9) })], z2.prototype, "variables", void 0), e([y({ type: j.ofType(S2) })], z2.prototype, "volumes", void 0), e([y({ type: p16 })], z2.prototype, "index", void 0), e([y({ type: Number, json: { name: "layerDefinition.minScale", read: false, write: false, origins: { service: { read: false, write: false } } } })], z2.prototype, "minScale", void 0), e([y({ type: Number, json: { name: "layerDefinition.maxScale", read: false, write: false, origins: { service: { read: false, write: false } } } })], z2.prototype, "maxScale", void 0), e([y({ json: { read: false }, readOnly: true })], z2.prototype, "type", void 0), e([y({ readOnly: true, json: { name: "serviceVersion" } })], z2.prototype, "version", void 0), e([o("service", "version")], z2.prototype, "readVersion", null), e([y({ type: w3 })], z2.prototype, "fullExtent", void 0), e([o("service", "fullExtent", ["fullExtent"])], z2.prototype, "readFullExtent", null), e([y({ readOnly: true, clonable: false, json: { read: false } })], z2.prototype, "voxelFields", null), e([y(p3)], z2.prototype, "popupEnabled", void 0), e([y({ readOnly: true })], z2.prototype, "defaultPopupTemplate", null), z2 = e([a2(R)], z2);
var _2 = z2;
export {
  _2 as default
};
//# sourceMappingURL=VoxelLayer-P3BMPMLF.js.map
