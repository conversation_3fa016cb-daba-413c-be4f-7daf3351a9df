package org.thingsboard.server.service.security.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
public class FeePlatformLoginBody implements Serializable {

    @JsonProperty("code")
    private Integer code;
    @JsonProperty("msg")
    private String msg;
    @JsonProperty("data")
    private DataDTO data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JsonProperty("scope")
        private Object scopeX;
        @JsonProperty("openid")
        private Object openidX;
        @JsonProperty("access_token")
        private String accessToken;
        @JsonProperty("refresh_token")
        private Object refreshToken;
        @JsonProperty("expire_in")
        private Integer expireIn;
        @JsonProperty("refresh_expire_in")
        private Object refreshExpireIn;
        @JsonProperty("client_id")
        private String clientId;
    }
}
