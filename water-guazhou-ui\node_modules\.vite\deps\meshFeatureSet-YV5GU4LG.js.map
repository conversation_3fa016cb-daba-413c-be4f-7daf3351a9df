{"version": 3, "sources": ["../../@arcgis/core/geometry/support/meshUtils/centerAt.js", "../../@arcgis/core/geometry/support/meshUtils/loadExternal.js", "../../@arcgis/core/geometry/support/meshUtils/offset.js", "../../@arcgis/core/geometry/support/meshUtils/primitives.js", "../../@arcgis/core/geometry/support/meshUtils/rotate.js", "../../@arcgis/core/geometry/support/meshUtils/scale.js", "../../@arcgis/core/geometry/Mesh.js", "../../@arcgis/core/rest/support/meshFeatureSet.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../core/Logger.js\";import{isSome as t}from\"../../../core/maybe.js\";import{c as r}from\"../../../chunks/vec3f64.js\";import{projectPointToVector as i}from\"../../projection.js\";import{isGeographicMesh as o}from\"./geographicUtils.js\";import{ungeoreference as n,georeference as s}from\"./georeference.js\";const a=e.getLogger(\"esri.geometry.support.meshUtils.centerAt\");function c(e,r,i){if(!e.vertexAttributes||!e.vertexAttributes.position)return;const n=i?.origin??e.origin;if(t(e.transform))null!=i?.geographic&&i.geographic!==e.transform.geographic&&a.warn(`Specifying the 'geographic' parameter (${i.geographic}) different from the Mesh transform setting (${e.transform.geographic}) is not supported`),f(e.transform,r,n);else{o(e.spatialReference,i)?p(e,r,n):g(e,r,n)}}function f(e,t,r){const i=t.x-r.x,o=t.y-r.y,n=t.hasZ&&r.hasZ?t.z-r.z:0,s=e.origin;e.origin=[s[0]+i,s[1]+o,s[2]+n]}function p(e,t,r){const i=n(e.vertexAttributes,r,{geographic:!0}),{position:o,normal:a,tangent:c}=s(i,t,{geographic:!0});e.vertexAttributes.position=o,e.vertexAttributes.normal=a,e.vertexAttributes.tangent=c,e.vertexAttributesChanged()}function g(e,t,r){const o=h,n=l;if(i(t,n,e.spatialReference)){if(!i(r,o,e.spatialReference)){const t=e.origin;o[0]=t.x,o[1]=t.y,o[2]=t.z,a.error(`Failed to project specified origin (wkid:${r.spatialReference.wkid}) to mesh spatial reference (wkid:${e.spatialReference.wkid}).`)}m(e.vertexAttributes.position,n,o),e.vertexAttributesChanged()}else a.error(`Failed to project centerAt location (wkid:${t.spatialReference.wkid}) to mesh spatial reference (wkid:${e.spatialReference.wkid})`)}function m(e,t,r){if(e)for(let i=0;i<e.length;i+=3)for(let o=0;o<3;o++)e[i+o]+=t[o]-r[o]}const l=r(),h=r();export{c as centerAt};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../core/Error.js\";import{isSome as t,isNone as s}from\"../../../core/maybe.js\";import{whenOrAbort as r,eachAlwaysValues as o,isAborted as n,throwIfAborted as a}from\"../../../core/promiseUtils.js\";import{removeFile as i,makeRelative as l}from\"../../../core/urlUtils.js\";import c from\"../../Point.js\";async function u(e,s,o){const{loadGLTFMesh:n}=await r(import(\"./loadGLTFMesh.js\"),o),a=await m(s,o),i=n(new c({x:0,y:0,z:0,spatialReference:e.spatialReference}),a.url,{resolveFile:f(a),useTransform:!0,signal:t(o)?o.signal:null});i.then((()=>a.dispose()),(()=>a.dispose()));const{vertexAttributes:l,components:u}=await i;e.vertexAttributes=l,e.components=u}function f(e){const t=i(e.url);return s=>{const r=l(s,t,t),o=r?r.replace(/^ *\\.\\//,\"\"):null;return(o?e.files.get(o):null)??s}}async function m(e,t){return e instanceof Blob?y.fromBlob(e):\"string\"==typeof e?new y(e):Array.isArray(e)?p(e,t):w(e,t)}async function p(t,r){const i=new Map;let l=null;const c=await o(t.map((async e=>({name:e.name,source:await m(e instanceof Blob?e:e.source,r)})))),u=[];for(const e of c)e&&(n(r)?e.source.dispose():u.push(e));a(r);for(const{name:e,source:o}of u)(s(l)||/\\.(gltf|glb)/i.test(e))&&(l=o.url),i.set(e,o.url),o.files&&o.files.forEach(((e,t)=>i.set(t,e)));if(s(l))throw new e(\"mesh-load-external:missing-files\",\"Missing files to load external mesh source\");return new y(l,(()=>u.forEach((({source:e})=>e.dispose()))),i)}async function w(e,t){const{default:s}=await r(import(\"../../../request.js\"),t),o=\"string\"==typeof e.multipart[0]?await Promise.all(e.multipart.map((async e=>(await s(e,{responseType:\"array-buffer\"})).data))):e.multipart;return y.fromBlob(new Blob(o))}class y{constructor(e,t=(()=>{}),s=new Map){this.url=e,this.dispose=t,this.files=s}static fromBlob(e){const t=URL.createObjectURL(e);return new y(t,(()=>URL.revokeObjectURL(t)))}}export{u as loadExternal};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../../core/Logger.js\";import{isSome as e}from\"../../../core/maybe.js\";import{f as r}from\"../../../chunks/mat3.js\";import{c as o}from\"../../../chunks/mat3f64.js\";import{c as i}from\"../../../chunks/mat4f64.js\";import{a as s,t as n}from\"../../../chunks/vec3.js\";import{c as a}from\"../../../chunks/vec3f64.js\";import{computeTranslationToOriginAndRotation as f}from\"../../projection.js\";import{getSphericalPCPF as m}from\"../../spatialReferenceEllipsoidUtils.js\";import{isGeographicMesh as c}from\"./geographicUtils.js\";import{projectToPCPF as p,projectNormalToPCPF as g,projectTangentToPCPF as l,projectFromPCPF as u,projectNormalFromPCPF as h,projectTangentFromPCPF as j}from\"./projection.js\";function x(r,o,i){if(r.vertexAttributes&&r.vertexAttributes.position)if(e(r.transform))null!=i?.geographic&&i.geographic!==r.transform.geographic&&t.getLogger(\"esri.geometry.support.meshUtils.offset\").warn(`Specifying the 'geographic' parameter (${i.geographic}) different from the Mesh transform setting (${r.transform.geographic}) is not supported`),A(r.transform,o);else{c(r.spatialReference,i)?v(r,o):b(r,o)}}function A(t,e){const r=t.origin;t.origin=s(a(),r,e)}function v(t,o){const i=t.spatialReference,s=t.vertexAttributes.position,a=t.vertexAttributes.normal,c=t.vertexAttributes.tangent,x=new Float64Array(s.length),A=e(a)?new Float32Array(a.length):null,v=e(c)?new Float32Array(c.length):null,b=t.extent.center,F=d;f(i,[b.x,b.y,b.z],k,m(i)),r(w,k),n(F,o,w),p(s,i,x),e(a)&&e(A)&&g(a,s,x,i,A),e(c)&&e(v)&&l(c,s,x,i,v),y(x,F),u(x,s,i),e(a)&&e(A)&&h(A,s,x,i,a),e(c)&&e(v)&&j(v,s,x,i,c),t.vertexAttributesChanged()}function b(t,e){y(t.vertexAttributes.position,e),t.vertexAttributesChanged()}function y(t,e){if(t)for(let r=0;r<t.length;r+=3)for(let o=0;o<3;o++)t[r+o]+=e[o]}const d=a(),k=i(),w=o();export{x as offset};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t}from\"../../../core/maybe.js\";import{c as e}from\"../../../chunks/mat3f64.js\";import{t as n,n as r}from\"../../../chunks/vec3.js\";import{c as o}from\"../../../chunks/vec3f64.js\";import s from\"../MeshComponent.js\";import{MeshVertexAttributes as a}from\"../MeshVertexAttributes.js\";import{georeferenceByTransform as i}from\"./georeference.js\";function c(){const{faceDescriptions:t,faceVertexOffsets:e,uvScales:n}=d,r=4*t.length,o=new Float64Array(3*r),s=new Float32Array(3*r),a=new Float32Array(2*r),i=new Uint32Array(2*t.length*3);let c=0,l=0,f=0,u=0;for(let h=0;h<t.length;h++){const r=t[h],p=c/3;for(const t of e)i[u++]=p+t;const m=r.corners;for(let t=0;t<4;t++){const e=m[t];let i=0;a[f++]=.25*n[t][0]+r.uvOrigin[0],a[f++]=r.uvOrigin[1]-.25*n[t][1];for(let t=0;t<3;t++)0!==r.axis[t]?(o[c++]=.5*r.axis[t],s[l++]=r.axis[t]):(o[c++]=.5*e[i++],s[l++]=0)}}return{position:o,normal:s,uv:a,faces:i}}function l(e,n){const r=e.components[0],o=r.faces,a=M[n],i=6*a,c=new Array(6),l=new Array(o.length-6);let f=0,u=0;for(let t=0;t<o.length;t++)t>=i&&t<i+6?c[f++]=o[t]:l[u++]=o[t];if(t(e.vertexAttributes.uv)){const t=new Float32Array(e.vertexAttributes.uv),n=4*a*2,r=[0,1,1,1,1,0,0,0];for(let e=0;e<r.length;e++)t[n+e]=r[e];e.vertexAttributes.uv=t}return e.components=[new s({faces:c,material:r.material}),new s({faces:l})],e}function f(t=0){const e=Math.round(8*2**t),n=2*e,r=(e-1)*(n+1)+2*n,o=new Float64Array(3*r),s=new Float32Array(3*r),a=new Float32Array(2*r),i=new Uint32Array(3*((e-1)*n*2));let c=0,l=0,f=0,u=0;for(let h=0;h<=e;h++){const t=h/e*Math.PI+.5*Math.PI,r=Math.cos(t),p=Math.sin(t);F[2]=p;const m=0===h||h===e,w=m?n-1:n;for(let v=0;v<=w;v++){const t=v/w*2*Math.PI;F[0]=-Math.sin(t)*r,F[1]=Math.cos(t)*r;for(let e=0;e<3;e++)o[c]=.5*F[e],s[c]=F[e],++c;a[l++]=(v+(m?.5:0))/n,a[l++]=h/e,0!==h&&v!==n&&(h!==e&&(i[f++]=u,i[f++]=u+1,i[f++]=u-n),1!==h&&(i[f++]=u,i[f++]=u-n,i[f++]=u-n-1)),u++}}return{position:o,normal:s,uv:a,faces:i}}function u(t=0){const e=5,n=Math.round(16*2**t),r=(e-1)*(n+1)+2*n,o=new Float64Array(3*r),s=new Float32Array(3*r),a=new Float32Array(2*r),i=new Uint32Array(3*(4*n));let c=0,l=0,f=0,u=0,h=0;for(let p=0;p<=e;p++){const t=0===p||p===e,r=p<=1||p>=e-1,m=2===p||4===p,w=t?n-1:n;for(let v=0;v<=w;v++){const g=v/w*2*Math.PI,A=t?0:.5;F[0]=A*Math.sin(g),F[1]=A*-Math.cos(g),F[2]=p<=2?.5:-.5;for(let t=0;t<3;t++)o[c++]=F[t],s[l++]=r?2===t?p<=1?1:-1:0:2===t?0:F[t]/A;a[f++]=(v+(t?.5:0))/n,a[f++]=p<=1?1*p/3:p<=3?1*(p-2)/3+1/3:1*(p-4)/3+2/3,m||0===p||v===n||(p!==e&&(i[u++]=h,i[u++]=h+1,i[u++]=h-n),1!==p&&(i[u++]=h,i[u++]=h-n,i[u++]=h-n-1)),h++}}return{position:o,normal:s,uv:a,faces:i}}function h(t,e){const n=\"number\"==typeof e?e:null!=e?e.width:1,r=\"number\"==typeof e?e:null!=e?e.height:1;switch(t){case\"up\":case\"down\":return{width:n,depth:r};case\"north\":case\"south\":return{width:n,height:r};case\"east\":case\"west\":return{depth:n,height:r}}}function p(t){const e=g.facingAxisOrderSwap[t],n=g.position,r=g.normal,o=new Float64Array(n.length),s=new Float32Array(r.length);let a=0;for(let i=0;i<4;i++){const t=a;for(let i=0;i<3;i++){const c=e[i],l=Math.abs(c)-1,f=c>=0?1:-1;o[a]=n[t+l]*f,s[a]=r[t+l]*f,a++}}return{position:o,normal:s,uv:new Float32Array(g.uv),faces:new Uint32Array(g.faces),isPlane:!0}}const m=1,w=2,v=3,g={position:[-.5,-.5,0,.5,-.5,0,.5,.5,0,-.5,.5,0],normal:[0,0,1,0,0,1,0,0,1,0,0,1],uv:[0,1,1,1,1,0,0,0],faces:[0,1,2,0,2,3],facingAxisOrderSwap:{east:[v,m,w],west:[-v,-m,w],north:[-m,v,w],south:[m,-v,w],up:[m,w,v],down:[m,-w,-v]}};function A(t,e,n){t.isPlane||y(t),x(t,n?.size);const{vertexAttributes:r,transform:o}=i(t,e,n);return{vertexAttributes:new a({...r,uv:t.uv}),transform:o,components:[new s({faces:t.faces,material:n&&n.material||null})],spatialReference:e.spatialReference}}function y(t){for(let e=0;e<t.position.length;e+=3)t.position[e+2]+=.5}function x(t,e){if(null==e)return;const o=\"number\"==typeof e?[e,e,e]:[null!=e.width?e.width:1,null!=e.depth?e.depth:1,null!=e.height?e.height:1];O[0]=o[0],O[4]=o[1],O[8]=o[2];for(let r=0;r<t.position.length;r+=3){for(let e=0;e<3;e++)F[e]=t.position[r+e];n(F,F,O);for(let e=0;e<3;e++)t.position[r+e]=F[e]}if(o[0]!==o[1]||o[1]!==o[2]){O[0]=1/o[0],O[4]=1/o[1],O[8]=1/o[2];for(let e=0;e<t.normal.length;e+=3){for(let n=0;n<3;n++)F[n]=t.normal[e+n];n(F,F,O),r(F,F);for(let n=0;n<3;n++)t.normal[e+n]=F[n]}}}const d={faceDescriptions:[{axis:[0,-1,0],uvOrigin:[0,.625],corners:[[-1,-1],[1,-1],[1,1],[-1,1]]},{axis:[1,0,0],uvOrigin:[.25,.625],corners:[[-1,-1],[1,-1],[1,1],[-1,1]]},{axis:[0,1,0],uvOrigin:[.5,.625],corners:[[1,-1],[-1,-1],[-1,1],[1,1]]},{axis:[-1,0,0],uvOrigin:[.75,.625],corners:[[1,-1],[-1,-1],[-1,1],[1,1]]},{axis:[0,0,1],uvOrigin:[0,.375],corners:[[-1,-1],[1,-1],[1,1],[-1,1]]},{axis:[0,0,-1],uvOrigin:[0,.875],corners:[[-1,1],[1,1],[1,-1],[-1,-1]]}],uvScales:[[0,0],[1,0],[1,1],[0,1]],faceVertexOffsets:[0,1,2,0,2,3]},M={south:0,east:1,north:2,west:3,up:4,down:5},F=o(),O=e();export{M as boxFaceOrder,h as convertPlaneSizeParameter,A as convertUnitGeometry,c as createUnitSizeBox,u as createUnitSizeCylinder,p as createUnitSizePlane,f as createUnitSizeSphere,l as extractSingleFaceOfBox};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../../core/Logger.js\";import{isSome as e,isNone as r}from\"../../../core/maybe.js\";import{f as o}from\"../../../chunks/mat3.js\";import{c as i}from\"../../../chunks/mat3f64.js\";import{d as s}from\"../../../chunks/mat4.js\";import{c as n}from\"../../../chunks/mat4f64.js\";import{s as a,b as c,a as f,t as p,m}from\"../../../chunks/vec3.js\";import{c as g,Z as l}from\"../../../chunks/vec3f64.js\";import{projectPointToVector as u,computeTranslationToOriginAndRotation as h}from\"../../projection.js\";import{getSphericalPCPF as j}from\"../../spatialReferenceEllipsoidUtils.js\";import{create as v,compose as x,axis as A,angleRad as b}from\"../axisAngleDegrees.js\";import{isGeographicMesh as d}from\"./geographicUtils.js\";import{projectToPCPF as k,projectNormalToPCPF as y,projectTangentToPCPF as w,projectFromPCPF as R,projectNormalFromPCPF as F,projectTangentFromPCPF as L}from\"./projection.js\";const $=t.getLogger(\"esri.geometry.support.meshUtils.rotate\");function U(t,r,o){if(!t.vertexAttributes||!t.vertexAttributes.position||0===r[3])return;const i=t.spatialReference;if(e(t.transform)){null!=o?.geographic&&o.geographic!==t.transform.geographic&&$.warn(`Specifying the 'geographic' parameter (${o.geographic}) different from the Mesh transform setting (${t.transform.geographic}) is not supported`);const e=o?.origin??t.transform.getOriginPoint(i);z(t.transform,r,e)}else{const e=o?.origin??t.origin;d(t.spatialReference,o)?C(t,r,e):I(t,r,e)}}function z(t,e,r){const o=a(E,r.x,r.y,r.z),i=c(E,o,t.origin);t.applyLocalInverse(i,M),t.rotation=x(t.rotation,e,v()),t.applyLocalInverse(i,i),c(i,i,M),t.translation=f(g(),t.translation,i)}function C(t,r,i){const s=t.spatialReference,n=j(s),a=Z;u(i,a,n)||u(t.origin,a,n);const c=t.vertexAttributes.position,f=t.vertexAttributes.normal,m=t.vertexAttributes.tangent,g=new Float64Array(c.length),l=e(f)?new Float32Array(f.length):null,v=e(m)?new Float32Array(m.length):null;h(n,a,P,n),o(S,P);const x=O;p(A(O),A(r),S),x[3]=r[3],k(c,s,g),e(f)&&e(l)&&y(f,c,g,s,l),e(m)&&e(v)&&w(m,c,g,s,v),D(g,x,3,a),R(g,c,s),e(f)&&e(l)&&(D(l,x,3),F(l,c,g,s,f)),e(m)&&e(v)&&(D(v,x,4),L(v,c,g,s,m)),t.vertexAttributesChanged()}function I(t,e,r){const o=Z;if(!u(r,o,t.spatialReference)){const e=t.origin;o[0]=e.x,o[1]=e.y,o[2]=e.z,$.error(`Failed to project specified origin (wkid:${r.spatialReference.wkid}) to mesh spatial reference (wkid:${t.spatialReference.wkid}).`)}D(t.vertexAttributes.position,e,3,o),D(t.vertexAttributes.normal,e,3),D(t.vertexAttributes.tangent,e,4),t.vertexAttributesChanged()}function D(t,e,o,i=l){if(!r(t)){s(P,b(e),A(e));for(let e=0;e<t.length;e+=o){for(let r=0;r<3;r++)E[r]=t[e+r]-i[r];m(E,E,P);for(let r=0;r<3;r++)t[e+r]=E[r]+i[r]}}}const E=g(),M=g(),O=v(),P=n(),S=i(),Z=g();export{U as rotate};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../core/Logger.js\";import{isSome as t}from\"../../../core/maybe.js\";import{s as r,b as o,g as i,a as n}from\"../../../chunks/vec3.js\";import{c as s,Z as a}from\"../../../chunks/vec3f64.js\";import{projectPointToVector as c}from\"../../projection.js\";import{getSphericalPCPF as p}from\"../../spatialReferenceEllipsoidUtils.js\";import{isGeographicMesh as l}from\"./geographicUtils.js\";import{projectToPCPF as f,projectNormalToPCPF as g,projectTangentToPCPF as m,projectFromPCPF as u,projectNormalFromPCPF as h,projectTangentFromPCPF as v}from\"./projection.js\";const d=e.getLogger(\"esri.geometry.support.meshUtils.scale\");function j(e,r,o){if(!e.vertexAttributes||!e.vertexAttributes.position)return;const i=e.spatialReference;if(t(e.transform)){null!=o?.geographic&&o.geographic!==e.transform.geographic&&d.warn(`Specifying the 'geographic' parameter (${o.geographic}) different from the Mesh transform setting (${e.transform.geographic}) is not supported`);const t=o?.origin??e.transform.getOriginPoint(i);x(e.transform,r,t)}else{const t=l(e.spatialReference,o),i=o&&o.origin||e.origin;t?A(e,r,i):b(e,r,i)}}function x(e,t,a){const c=r(w,a.x,a.y,a.z),p=o(w,c,e.origin);e.applyLocalInverse(p,R);const l=i(s(),e.scale,t);e.scale=l,e.applyLocalInverse(p,p),o(p,p,R),e.translation=n(s(),e.translation,p)}function A(e,r,o){const i=e.spatialReference,n=p(i),s=k;c(o,s,n)||c(e.origin,s,n);const a=e.vertexAttributes.position,l=e.vertexAttributes.normal,d=e.vertexAttributes.tangent,j=new Float64Array(a.length),x=t(l)?new Float32Array(l.length):null,A=t(d)?new Float32Array(d.length):null;f(a,i,j),t(l)&&t(x)&&g(l,a,j,i,x),t(d)&&t(A)&&m(d,a,j,i,A),y(j,r,s),u(j,a,i),t(l)&&t(x)&&h(x,a,j,i,l),t(d)&&t(A)&&v(A,a,j,i,d),e.vertexAttributesChanged()}function b(e,t,r){const o=k;if(!c(r,o,e.spatialReference)){const t=e.origin;o[0]=t.x,o[1]=t.y,o[2]=t.z,d.error(`Failed to project specified origin (wkid:${r.spatialReference.wkid}) to mesh spatial reference (wkid:${e.spatialReference.wkid}).`)}y(e.vertexAttributes.position,t,o),e.vertexAttributesChanged()}function y(e,t,r=a){if(e)for(let o=0;o<e.length;o+=3){for(let t=0;t<3;t++)w[t]=e[o+t]-r[t];i(w,w,t);for(let t=0;t<3;t++)e[o+t]=w[t]+r[t]}}const w=s(),R=s(),k=s();export{j as scale};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import t from\"../core/Error.js\";import{HandleOwnerMixin as r}from\"../core/HandleOwner.js\";import o from\"../core/Loadable.js\";import n from\"../core/Logger.js\";import{isNone as s,isSome as i}from\"../core/maybe.js\";import{EsriPromiseMixin as a}from\"../core/Promise.js\";import{whenOrAbort as l}from\"../core/promiseUtils.js\";import{watch as c}from\"../core/reactiveUtils.js\";import{property as p}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{subclass as m}from\"../core/accessorSupport/decorators/subclass.js\";import{f as h,c as f}from\"../chunks/vec3f64.js\";import u from\"./Extent.js\";import d from\"./Geometry.js\";import g from\"./Point.js\";import x from\"./Polygon.js\";import{fromAxisAndAngle as y,compose as b,create as j}from\"./support/axisAngleDegrees.js\";import w from\"./support/MeshComponent.js\";import v from\"./support/MeshTransform.js\";import{MeshVertexAttributes as M}from\"./support/MeshVertexAttributes.js\";import{triangulate as P}from\"./support/triangulationUtils.js\";import{centerAt as L}from\"./support/meshUtils/centerAt.js\";import{loadExternal as R}from\"./support/meshUtils/loadExternal.js\";import{offset as C}from\"./support/meshUtils/offset.js\";import{convertUnitGeometry as A,createUnitSizeBox as z,extractSingleFaceOfBox as F,createUnitSizeSphere as O,createUnitSizeCylinder as U,convertPlaneSizeParameter as E,createUnitSizePlane as S}from\"./support/meshUtils/primitives.js\";import{rotate as T}from\"./support/meshUtils/rotate.js\";import{scale as _}from\"./support/meshUtils/scale.js\";var G;const I=\"esri.geometry.Mesh\";let B=G=class extends(r(o.LoadableMixin(a(d)))){constructor(e){super(e),this.components=null,this.transform=null,this.external=null,this.hasZ=!0,this.hasM=!1,this.vertexAttributes=new M,this.type=\"mesh\"}initialize(){(s(this.external)||this.vertexAttributes.position.length)&&(this.loadStatus=\"loaded\"),this.when((()=>{this.handles.add(c((()=>({vertexAttributes:this.vertexAttributes,components:this.components?.map((e=>e.clone()))})),(()=>this._set(\"external\",null)),{once:!0,sync:!0}))}))}get hasExtent(){return!this.loaded&&i(this.external)&&i(this.external.extent)||this.loaded&&this.vertexAttributes.position.length>0&&(!this.components||this.components.length>0)}get _boundingInfo(){const e=this.vertexAttributes.position,t=this.spatialReference;if(0===e.length||this.components&&0===this.components.length)return{extent:new u({xmin:0,ymin:0,zmin:0,xmax:0,ymax:0,zmax:0,spatialReference:t}),center:new g({x:0,y:0,z:0,spatialReference:t})};const r=i(this.transform)?this.transform.project(e,t):e;let o=1/0,n=1/0,s=1/0,a=-1/0,l=-1/0,c=-1/0,p=0,m=0,h=0;const f=r.length,d=1/(f/3);let x=0;for(;x<f;){const e=r[x++],t=r[x++],i=r[x++];o=Math.min(o,e),n=Math.min(n,t),s=Math.min(s,i),a=Math.max(a,e),l=Math.max(l,t),c=Math.max(c,i),p+=d*e,m+=d*t,h+=d*i}return{extent:new u({xmin:o,ymin:n,zmin:s,xmax:a,ymax:l,zmax:c,spatialReference:t}),center:new g({x:p,y:m,z:h,spatialReference:t})}}get anchor(){if(i(this.transform))return this.transform.getOriginPoint(this.spatialReference);const e=this._boundingInfo;return new g({x:e.center.x,y:e.center.y,z:e.extent.zmin,spatialReference:this.spatialReference})}get origin(){return i(this.transform)?this.transform.getOriginPoint(this.spatialReference):this._boundingInfo.center}get extent(){return!this.loaded&&i(this.external)&&i(this.external.extent)?this.external.extent.clone():this._boundingInfo.extent}addComponent(e){this.loaded?(this.components||(this.components=[]),this.components.push(w.from(e)),this.notifyChange(\"components\")):n.getLogger(this.declaredClass).error(\"addComponent()\",\"Mesh must be loaded before applying operations\")}removeComponent(e){if(this.loaded){if(this.components){const t=this.components.indexOf(e);if(-1!==t)return this.components.splice(t,1),void this.notifyChange(\"components\")}n.getLogger(this.declaredClass).error(\"removeComponent()\",\"Provided component is not part of the list of components\")}else n.getLogger(this.declaredClass).error(\"removeComponent()\",\"Mesh must be loaded before applying operations\")}rotate(e,t,r,o){return y(k.x,e,D),y(k.y,t,W),y(k.z,r,Z),b(D,W,D),b(D,Z,D),T(this,D,o),this}offset(e,t,r,o){return this.loaded?(H[0]=e,H[1]=t,H[2]=r,C(this,H,o),this):(n.getLogger(this.declaredClass).error(\"offset()\",\"Mesh must be loaded before applying operations\"),this)}scale(e,t){return this.loaded?(_(this,e,t),this):(n.getLogger(this.declaredClass).error(\"scale()\",\"Mesh must be loaded before applying operations\"),this)}centerAt(e,t){return this.loaded?(L(this,e,t),this):(n.getLogger(this.declaredClass).error(\"centerAt()\",\"Mesh must be loaded before applying operations\"),this)}load(e){return i(this.external)&&this.addResolvingPromise(R(this,this.external.source,e)),Promise.resolve(this)}updateExternalSource(e){this._set(\"external\",e)}clone(){let e=null;if(this.components){const t=new Map,r=new Map;e=this.components.map((e=>e.cloneWithDeduplication(t,r)))}const t={components:e,spatialReference:this.spatialReference,vertexAttributes:this.vertexAttributes.clone(),transform:i(this.transform)?this.transform.clone():null,external:i(this.external)?{source:this.external.source,extent:i(this.external.extent)?this.external.extent.clone():null}:null};return new G(t)}vertexAttributesChanged(){this.notifyChange(\"vertexAttributes\")}async toBinaryGLTF(e){const t=import(\"./support/meshUtils/exporters/gltf/gltfexport.js\"),r=this.load(),o=await Promise.all([t,r]),{toBinaryGLTF:n}=o[0];return n(this,e)}static createBox(e,t){if(!(e instanceof g))return n.getLogger(I).error(\".createBox()\",\"expected location to be a Point instance\"),null;const r=new G(A(z(),e,t));return t&&t.imageFace&&\"all\"!==t.imageFace?F(r,t.imageFace):r}static createSphere(e,t){return e instanceof g?new G(A(O(t&&t.densificationFactor||0),e,t)):(n.getLogger(I).error(\".createSphere()\",\"expected location to be a Point instance\"),null)}static createCylinder(e,t){return e instanceof g?new G(A(U(t&&t.densificationFactor||0),e,t)):(n.getLogger(I).error(\".createCylinder()\",\"expected location to be a Point instance\"),null)}static createPlane(e,t){if(!(e instanceof g))return n.getLogger(I).error(\".createPlane()\",\"expected location to be a Point instance\"),null;const r=t?.facing??\"up\",o=E(r,t?.size);return new G(A(S(r),e,{...t,size:o}))}static createFromPolygon(e,t){if(!(e instanceof x))return n.getLogger(I).error(\".createFromPolygon()\",\"expected polygon to be a Polygon instance\"),null;const r=P(e);return new G({vertexAttributes:new M({position:r.position}),components:[new w({faces:r.faces,shading:\"flat\",material:t?.material??null})],spatialReference:e.spatialReference})}static async createFromGLTF(e,r,o){if(!(e instanceof g))throw n.getLogger(I).error(\".createfromGLTF()\",\"expected location to be a Point instance\"),new t(\"invalid-input\",\"Expected location to be a Point instance\");const{loadGLTFMesh:s}=await l(import(\"./support/meshUtils/loadGLTFMesh.js\"),o);return new G(await s(e,r,o))}static createWithExternalSource(e,t,r){const o=r?.extent??null,n=r?.transform?.clone()??new v;n.origin=[e.x,e.y,e.z??0];const s=e.spatialReference;return new G({external:{source:t,extent:o},transform:n,spatialReference:s})}static createIncomplete(e,r){const o=r?.transform?.clone()??new v;o.origin=[e.x,e.y,e.z??0];const n=e.spatialReference,s=new G({transform:o,spatialReference:n});return s.addResolvingPromise(Promise.reject(new t(\"mesh-incomplete\",\"Mesh resources are not complete\"))),s}};e([p({type:[w],json:{write:!0}})],B.prototype,\"components\",void 0),e([p({type:v,json:{write:!0}})],B.prototype,\"transform\",void 0),e([p({constructOnly:!0})],B.prototype,\"external\",void 0),e([p({readOnly:!0})],B.prototype,\"hasExtent\",null),e([p({readOnly:!0})],B.prototype,\"_boundingInfo\",null),e([p({readOnly:!0})],B.prototype,\"anchor\",null),e([p({readOnly:!0})],B.prototype,\"origin\",null),e([p({readOnly:!0,json:{read:!1}})],B.prototype,\"extent\",null),e([p({readOnly:!0,json:{read:!1,write:!0,default:!0}})],B.prototype,\"hasZ\",void 0),e([p({readOnly:!0,json:{read:!1,write:!0,default:!1}})],B.prototype,\"hasM\",void 0),e([p({type:M,nonNullable:!0,json:{write:!0}})],B.prototype,\"vertexAttributes\",void 0),B=G=e([m(I)],B);const k={x:h(1,0,0),y:h(0,1,0),z:h(0,0,1)},D=j(),W=j(),Z=j(),H=f(),N=B;export{N as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../Graphic.js\";import{isSome as e}from\"../../core/maybe.js\";import r from\"../../geometry/Extent.js\";import n from\"../../geometry/Mesh.js\";import o from\"../../geometry/Point.js\";import s from\"../../geometry/SpatialReference.js\";import{fromAxisAndAngle as a}from\"../../geometry/support/axisAngleDegrees.js\";import i from\"../../geometry/support/MeshTransform.js\";import c from\"./FeatureSet.js\";function u(r,n,o){const a=o.features;o.features=[],delete o.geometryType;const i=c.fromJSON(o);if(i.geometryType=\"mesh\",!o.assetMaps)return i;const u=E(n,o.assetMaps),m=i.spatialReference??s.WGS84,p=o.globalIdFieldName,{outFields:g}=r,D=e(g)&&g.length>0?l(g.includes(\"*\")?null:new Set(g)):()=>({});for(const s of a){const r=f(s,p,m,n,u);e(r)&&i.features.push(new t({geometry:r,attributes:D(s)}))}return i}function l(t){return({attributes:e})=>{if(!e)return{};if(!t)return e;for(const r in e)t.has(r)||delete e[r];return e}}function f(t,e,o,s,a){const i=t.attributes[e],c=a.get(i);if(null==c||c.status===g.FAILED||null==c.url)return null;const u=m(t,o,s),l=r.fromJSON(t.geometry);l.spatialReference=o;const f=p(t.attributes,s,c.projectVertices);return c.status===g.PENDING?n.createIncomplete(u,{extent:l,transform:f}):n.createWithExternalSource(u,[{name:c.name,source:c.url}],{extent:l,transform:f})}function m({attributes:t},e,{transformFieldRoles:r}){return new o({x:t[r.originX],y:t[r.originY],z:t[r.originZ],spatialReference:e})}function p(t,{transformFieldRoles:e},r){return new i({translation:[t[e.translationX],-t[e.translationZ],t[e.translationY]],rotation:a([t[e.rotationX],t[e.rotationZ],t[e.rotationY]],t[e.rotationDeg]),scale:[t[e.scaleX],t[e.scaleY],t[e.scaleZ]],geographic:r})}var g;function E(t,e){const r=new Map;for(const n of e){const t=n.parentGlobalId;if(null==t)continue;const e=n.assetName,o=n.assetURL,s=n.conversionStatus;let a=r.get(t);if(null==a)switch(a={name:e,status:g.FAILED,url:o,projectVertices:D(n.flags).projectVertices},r.set(t,a),s){case\"COMPLETED\":case\"SUBMITTED\":a.status=g.COMPLETED;break;case\"INPROGRESS\":a.status=g.PENDING;break;default:a.status=g.FAILED}else console.warn(`Multiple asset parts not expected. Ignoring additional parts. conflicting assetname: ${n.assetName}`)}return r}function D(t){return{projectVertices:t.includes(\"PROJECT_VERTICES\")}}!function(t){t[t.FAILED=0]=\"FAILED\",t[t.PENDING=1]=\"PENDING\",t[t.COMPLETED=2]=\"COMPLETED\"}(g||(g={}));export{u as meshFeatureSetFromJSON};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI8T,IAAMA,KAAE,EAAE,UAAU,0CAA0C;AAAE,SAASC,GAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,CAACF,GAAE,oBAAkB,CAACA,GAAE,iBAAiB,SAAS;AAAO,QAAMG,MAAED,MAAA,gBAAAA,GAAG,WAAQF,GAAE;AAAO,MAAG,EAAEA,GAAE,SAAS,EAAE,UAAME,MAAA,gBAAAA,GAAG,eAAYA,GAAE,eAAaF,GAAE,UAAU,cAAYF,GAAE,KAAK,0CAA0CI,GAAE,UAAU,gDAAgDF,GAAE,UAAU,UAAU,oBAAoB,GAAEI,GAAEJ,GAAE,WAAUC,IAAEE,EAAC;AAAA,OAAM;AAAC,IAAAF,GAAED,GAAE,kBAAiBE,EAAC,IAAEG,GAAEL,IAAEC,IAAEE,EAAC,IAAEG,GAAEN,IAAEC,IAAEE,EAAC;AAAA,EAAC;AAAC;AAAC,SAASC,GAAEJ,IAAEO,IAAEN,IAAE;AAAC,QAAMC,KAAEK,GAAE,IAAEN,GAAE,GAAEO,KAAED,GAAE,IAAEN,GAAE,GAAEE,KAAEI,GAAE,QAAMN,GAAE,OAAKM,GAAE,IAAEN,GAAE,IAAE,GAAEQ,KAAET,GAAE;AAAO,EAAAA,GAAE,SAAO,CAACS,GAAE,CAAC,IAAEP,IAAEO,GAAE,CAAC,IAAED,IAAEC,GAAE,CAAC,IAAEN,EAAC;AAAC;AAAC,SAASE,GAAEL,IAAEO,IAAEN,IAAE;AAAC,QAAMC,KAAE,EAAEF,GAAE,kBAAiBC,IAAE,EAAC,YAAW,KAAE,CAAC,GAAE,EAAC,UAASO,IAAE,QAAOV,IAAE,SAAQC,GAAC,IAAEW,GAAER,IAAEK,IAAE,EAAC,YAAW,KAAE,CAAC;AAAE,EAAAP,GAAE,iBAAiB,WAASQ,IAAER,GAAE,iBAAiB,SAAOF,IAAEE,GAAE,iBAAiB,UAAQD,IAAEC,GAAE,wBAAwB;AAAC;AAAC,SAASM,GAAEN,IAAEO,IAAEN,IAAE;AAAC,QAAMO,KAAEG,IAAER,KAAES;AAAE,MAAG,GAAEL,IAAEJ,IAAEH,GAAE,gBAAgB,GAAE;AAAC,QAAG,CAAC,GAAEC,IAAEO,IAAER,GAAE,gBAAgB,GAAE;AAAC,YAAMO,KAAEP,GAAE;AAAO,MAAAQ,GAAE,CAAC,IAAED,GAAE,GAAEC,GAAE,CAAC,IAAED,GAAE,GAAEC,GAAE,CAAC,IAAED,GAAE,GAAET,GAAE,MAAM,4CAA4CG,GAAE,iBAAiB,IAAI,qCAAqCD,GAAE,iBAAiB,IAAI,IAAI;AAAA,IAAC;AAAC,IAAAa,GAAEb,GAAE,iBAAiB,UAASG,IAAEK,EAAC,GAAER,GAAE,wBAAwB;AAAA,EAAC,MAAM,CAAAF,GAAE,MAAM,6CAA6CS,GAAE,iBAAiB,IAAI,qCAAqCP,GAAE,iBAAiB,IAAI,GAAG;AAAC;AAAC,SAASa,GAAEb,IAAEO,IAAEN,IAAE;AAAC,MAAGD,GAAE,UAAQE,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAG,EAAE,UAAQM,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAR,GAAEE,KAAEM,EAAC,KAAGD,GAAEC,EAAC,IAAEP,GAAEO,EAAC;AAAC;AAAC,IAAMI,KAAE,EAAE;AAAV,IAAYD,KAAE,EAAE;;;ACAl4C,eAAeG,GAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAK,EAAC,cAAaC,GAAC,IAAE,MAAMC,GAAE,OAAO,4BAAmB,GAAEF,EAAC,GAAEG,KAAE,MAAMC,GAAEL,IAAEC,EAAC,GAAEK,KAAEJ,GAAE,IAAI,EAAE,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,kBAAiBH,GAAE,iBAAgB,CAAC,GAAEK,GAAE,KAAI,EAAC,aAAYG,GAAEH,EAAC,GAAE,cAAa,MAAG,QAAO,EAAEH,EAAC,IAAEA,GAAE,SAAO,KAAI,CAAC;AAAE,EAAAK,GAAE,KAAM,MAAIF,GAAE,QAAQ,GAAI,MAAIA,GAAE,QAAQ,CAAE;AAAE,QAAK,EAAC,kBAAiBI,IAAE,YAAWV,GAAC,IAAE,MAAMQ;AAAE,EAAAP,GAAE,mBAAiBS,IAAET,GAAE,aAAWD;AAAC;AAAC,SAASS,GAAER,IAAE;AAAC,QAAMU,KAAE,GAAEV,GAAE,GAAG;AAAE,SAAO,CAAAC,OAAG;AAAC,UAAMU,KAAE,EAAEV,IAAES,IAAEA,EAAC,GAAER,KAAES,KAAEA,GAAE,QAAQ,WAAU,EAAE,IAAE;AAAK,YAAOT,KAAEF,GAAE,MAAM,IAAIE,EAAC,IAAE,SAAOD;AAAA,EAAC;AAAC;AAAC,eAAeK,GAAEN,IAAEU,IAAE;AAAC,SAAOV,cAAa,OAAKI,GAAE,SAASJ,EAAC,IAAE,YAAU,OAAOA,KAAE,IAAII,GAAEJ,EAAC,IAAE,MAAM,QAAQA,EAAC,IAAEY,GAAEZ,IAAEU,EAAC,IAAEG,GAAEb,IAAEU,EAAC;AAAC;AAAC,eAAeE,GAAEF,IAAEC,IAAE;AAAC,QAAMJ,KAAE,oBAAI;AAAI,MAAIE,KAAE;AAAK,QAAMK,KAAE,MAAM,EAAEJ,GAAE,IAAK,OAAMV,QAAI,EAAC,MAAKA,GAAE,MAAK,QAAO,MAAMM,GAAEN,cAAa,OAAKA,KAAEA,GAAE,QAAOW,EAAC,EAAC,EAAG,CAAC,GAAEZ,KAAE,CAAC;AAAE,aAAUC,MAAKc,GAAE,CAAAd,OAAI,EAAEW,EAAC,IAAEX,GAAE,OAAO,QAAQ,IAAED,GAAE,KAAKC,EAAC;AAAG,IAAEW,EAAC;AAAE,aAAS,EAAC,MAAKX,IAAE,QAAOE,GAAC,KAAIH,GAAE,EAAC,EAAEU,EAAC,KAAG,gBAAgB,KAAKT,EAAC,OAAKS,KAAEP,GAAE,MAAKK,GAAE,IAAIP,IAAEE,GAAE,GAAG,GAAEA,GAAE,SAAOA,GAAE,MAAM,QAAS,CAACF,IAAEU,OAAIH,GAAE,IAAIG,IAAEV,EAAC,CAAE;AAAE,MAAG,EAAES,EAAC,EAAE,OAAM,IAAIR,GAAE,oCAAmC,4CAA4C;AAAE,SAAO,IAAIG,GAAEK,IAAG,MAAIV,GAAE,QAAS,CAAC,EAAC,QAAOC,GAAC,MAAIA,GAAE,QAAQ,CAAE,GAAGO,EAAC;AAAC;AAAC,eAAeM,GAAEb,IAAEU,IAAE;AAAC,QAAK,EAAC,SAAQT,GAAC,IAAE,MAAMG,GAAE,OAAO,+BAAqB,GAAEM,EAAC,GAAER,KAAE,YAAU,OAAOF,GAAE,UAAU,CAAC,IAAE,MAAM,QAAQ,IAAIA,GAAE,UAAU,IAAK,OAAMA,QAAI,MAAMC,GAAED,IAAE,EAAC,cAAa,eAAc,CAAC,GAAG,IAAK,CAAC,IAAEA,GAAE;AAAU,SAAOI,GAAE,SAAS,IAAI,KAAKF,EAAC,CAAC;AAAC;AAAC,IAAME,KAAN,MAAM,GAAC;AAAA,EAAC,YAAYJ,IAAEU,KAAG,MAAI;AAAA,EAAC,GAAGT,KAAE,oBAAI,OAAI;AAAC,SAAK,MAAID,IAAE,KAAK,UAAQU,IAAE,KAAK,QAAMT;AAAA,EAAC;AAAA,EAAC,OAAO,SAASD,IAAE;AAAC,UAAMU,KAAE,IAAI,gBAAgBV,EAAC;AAAE,WAAO,IAAI,GAAEU,IAAG,MAAI,IAAI,gBAAgBA,EAAC,CAAE;AAAA,EAAC;AAAC;;;ACAroC,SAASK,GAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAGF,GAAE,oBAAkBA,GAAE,iBAAiB,SAAS,KAAG,EAAEA,GAAE,SAAS,EAAE,UAAME,MAAA,gBAAAA,GAAG,eAAYA,GAAE,eAAaF,GAAE,UAAU,cAAY,EAAE,UAAU,wCAAwC,EAAE,KAAK,0CAA0CE,GAAE,UAAU,gDAAgDF,GAAE,UAAU,UAAU,oBAAoB,GAAE,EAAEA,GAAE,WAAUC,EAAC;AAAA,OAAM;AAAC,IAAAD,GAAEA,GAAE,kBAAiBE,EAAC,IAAEC,GAAEH,IAAEC,EAAC,IAAEG,GAAEJ,IAAEC,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEI,IAAEC,IAAE;AAAC,QAAMN,KAAEK,GAAE;AAAO,EAAAA,GAAE,SAAO,EAAE,EAAE,GAAEL,IAAEM,EAAC;AAAC;AAAC,SAASH,GAAEE,IAAEJ,IAAE;AAAC,QAAMC,KAAEG,GAAE,kBAAiBE,KAAEF,GAAE,iBAAiB,UAASG,KAAEH,GAAE,iBAAiB,QAAOI,KAAEJ,GAAE,iBAAiB,SAAQN,KAAE,IAAI,aAAaQ,GAAE,MAAM,GAAEG,KAAE,EAAEF,EAAC,IAAE,IAAI,aAAaA,GAAE,MAAM,IAAE,MAAKL,KAAE,EAAEM,EAAC,IAAE,IAAI,aAAaA,GAAE,MAAM,IAAE,MAAKL,KAAEC,GAAE,OAAO,QAAOM,KAAE;AAAE,KAAET,IAAE,CAACE,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC,GAAEQ,IAAE,EAAEV,EAAC,CAAC,GAAEM,GAAEK,IAAED,EAAC,GAAE,EAAED,IAAEV,IAAEY,EAAC,GAAE,EAAEN,IAAEL,IAAEH,EAAC,GAAE,EAAES,EAAC,KAAG,EAAEE,EAAC,KAAG,EAAEF,IAAED,IAAER,IAAEG,IAAEQ,EAAC,GAAE,EAAED,EAAC,KAAG,EAAEN,EAAC,KAAGS,GAAEH,IAAEF,IAAER,IAAEG,IAAEC,EAAC,GAAEW,GAAEf,IAAEY,EAAC,GAAE,EAAEZ,IAAEQ,IAAEL,EAAC,GAAE,EAAEM,EAAC,KAAG,EAAEE,EAAC,KAAG,EAAEA,IAAEH,IAAER,IAAEG,IAAEM,EAAC,GAAE,EAAEC,EAAC,KAAG,EAAEN,EAAC,KAAGY,GAAEZ,IAAEI,IAAER,IAAEG,IAAEO,EAAC,GAAEJ,GAAE,wBAAwB;AAAC;AAAC,SAASD,GAAEC,IAAEC,IAAE;AAAC,EAAAQ,GAAET,GAAE,iBAAiB,UAASC,EAAC,GAAED,GAAE,wBAAwB;AAAC;AAAC,SAASS,GAAET,IAAEC,IAAE;AAAC,MAAGD,GAAE,UAAQL,KAAE,GAAEA,KAAEK,GAAE,QAAOL,MAAG,EAAE,UAAQC,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAI,GAAEL,KAAEC,EAAC,KAAGK,GAAEL,EAAC;AAAC;AAAC,IAAM,IAAE,EAAE;AAAV,IAAYW,KAAEN,GAAE;AAAhB,IAAkBO,KAAEP,GAAE;;;ACA56C,SAASU,KAAG;AAAC,QAAK,EAAC,kBAAiBC,IAAE,mBAAkBC,IAAE,UAASC,GAAC,IAAEC,IAAEC,KAAE,IAAEJ,GAAE,QAAOK,KAAE,IAAI,aAAa,IAAED,EAAC,GAAEE,KAAE,IAAI,aAAa,IAAEF,EAAC,GAAEG,KAAE,IAAI,aAAa,IAAEH,EAAC,GAAEI,KAAE,IAAI,YAAY,IAAER,GAAE,SAAO,CAAC;AAAE,MAAID,KAAE,GAAEU,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,WAAQC,KAAE,GAAEA,KAAEZ,GAAE,QAAOY,MAAI;AAAC,UAAMR,KAAEJ,GAAEY,EAAC,GAAEC,KAAEd,KAAE;AAAE,eAAUC,MAAKC,GAAE,CAAAO,GAAEG,IAAG,IAAEE,KAAEb;AAAE,UAAMc,KAAEV,GAAE;AAAQ,aAAQJ,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAMC,KAAEa,GAAEd,EAAC;AAAE,UAAIQ,KAAE;AAAE,MAAAD,GAAEG,IAAG,IAAE,OAAIR,GAAEF,EAAC,EAAE,CAAC,IAAEI,GAAE,SAAS,CAAC,GAAEG,GAAEG,IAAG,IAAEN,GAAE,SAAS,CAAC,IAAE,OAAIF,GAAEF,EAAC,EAAE,CAAC;AAAE,eAAQA,KAAE,GAAEA,KAAE,GAAEA,KAAI,OAAII,GAAE,KAAKJ,EAAC,KAAGK,GAAEN,IAAG,IAAE,MAAGK,GAAE,KAAKJ,EAAC,GAAEM,GAAEG,IAAG,IAAEL,GAAE,KAAKJ,EAAC,MAAIK,GAAEN,IAAG,IAAE,MAAGE,GAAEO,IAAG,GAAEF,GAAEG,IAAG,IAAE;AAAA,IAAE;AAAA,EAAC;AAAC,SAAM,EAAC,UAASJ,IAAE,QAAOC,IAAE,IAAGC,IAAE,OAAMC,GAAC;AAAC;AAAC,SAASC,GAAER,IAAEC,IAAE;AAAC,QAAME,KAAEH,GAAE,WAAW,CAAC,GAAEI,KAAED,GAAE,OAAMG,KAAEQ,GAAEb,EAAC,GAAEM,KAAE,IAAED,IAAER,KAAE,IAAI,MAAM,CAAC,GAAEU,KAAE,IAAI,MAAMJ,GAAE,SAAO,CAAC;AAAE,MAAIK,KAAE,GAAEC,KAAE;AAAE,WAAQX,KAAE,GAAEA,KAAEK,GAAE,QAAOL,KAAI,CAAAA,MAAGQ,MAAGR,KAAEQ,KAAE,IAAET,GAAEW,IAAG,IAAEL,GAAEL,EAAC,IAAES,GAAEE,IAAG,IAAEN,GAAEL,EAAC;AAAE,MAAG,EAAEC,GAAE,iBAAiB,EAAE,GAAE;AAAC,UAAMD,KAAE,IAAI,aAAaC,GAAE,iBAAiB,EAAE,GAAEC,KAAE,IAAEK,KAAE,GAAEH,KAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAE,aAAQH,KAAE,GAAEA,KAAEG,GAAE,QAAOH,KAAI,CAAAD,GAAEE,KAAED,EAAC,IAAEG,GAAEH,EAAC;AAAE,IAAAA,GAAE,iBAAiB,KAAGD;AAAA,EAAC;AAAC,SAAOC,GAAE,aAAW,CAAC,IAAIe,GAAE,EAAC,OAAMjB,IAAE,UAASK,GAAE,SAAQ,CAAC,GAAE,IAAIY,GAAE,EAAC,OAAMP,GAAC,CAAC,CAAC,GAAER;AAAC;AAAC,SAASS,GAAEV,KAAE,GAAE;AAAC,QAAMC,KAAE,KAAK,MAAM,IAAE,KAAGD,EAAC,GAAEE,KAAE,IAAED,IAAEG,MAAGH,KAAE,MAAIC,KAAE,KAAG,IAAEA,IAAEG,KAAE,IAAI,aAAa,IAAED,EAAC,GAAEE,KAAE,IAAI,aAAa,IAAEF,EAAC,GAAEG,KAAE,IAAI,aAAa,IAAEH,EAAC,GAAEI,KAAE,IAAI,YAAY,MAAIP,KAAE,KAAGC,KAAE,EAAE;AAAE,MAAIH,KAAE,GAAEU,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,WAAQC,KAAE,GAAEA,MAAGX,IAAEW,MAAI;AAAC,UAAMZ,KAAEY,KAAEX,KAAE,KAAK,KAAG,MAAG,KAAK,IAAGG,KAAE,KAAK,IAAIJ,EAAC,GAAEa,KAAE,KAAK,IAAIb,EAAC;AAAE,MAAE,CAAC,IAAEa;AAAE,UAAMC,KAAE,MAAIF,MAAGA,OAAIX,IAAEgB,KAAEH,KAAEZ,KAAE,IAAEA;AAAE,aAAQgB,KAAE,GAAEA,MAAGD,IAAEC,MAAI;AAAC,YAAMlB,KAAEkB,KAAED,KAAE,IAAE,KAAK;AAAG,QAAE,CAAC,IAAE,CAAC,KAAK,IAAIjB,EAAC,IAAEI,IAAE,EAAE,CAAC,IAAE,KAAK,IAAIJ,EAAC,IAAEI;AAAE,eAAQH,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAI,GAAEN,EAAC,IAAE,MAAG,EAAEE,EAAC,GAAEK,GAAEP,EAAC,IAAE,EAAEE,EAAC,GAAE,EAAEF;AAAE,MAAAQ,GAAEE,IAAG,KAAGS,MAAGJ,KAAE,MAAG,MAAIZ,IAAEK,GAAEE,IAAG,IAAEG,KAAEX,IAAE,MAAIW,MAAGM,OAAIhB,OAAIU,OAAIX,OAAIO,GAAEE,IAAG,IAAEC,IAAEH,GAAEE,IAAG,IAAEC,KAAE,GAAEH,GAAEE,IAAG,IAAEC,KAAET,KAAG,MAAIU,OAAIJ,GAAEE,IAAG,IAAEC,IAAEH,GAAEE,IAAG,IAAEC,KAAET,IAAEM,GAAEE,IAAG,IAAEC,KAAET,KAAE,KAAIS;AAAA,IAAG;AAAA,EAAC;AAAC,SAAM,EAAC,UAASN,IAAE,QAAOC,IAAE,IAAGC,IAAE,OAAMC,GAAC;AAAC;AAAC,SAASG,GAAEX,KAAE,GAAE;AAAC,QAAMC,KAAE,GAAEC,KAAE,KAAK,MAAM,KAAG,KAAGF,EAAC,GAAEI,MAAGH,KAAE,MAAIC,KAAE,KAAG,IAAEA,IAAEG,KAAE,IAAI,aAAa,IAAED,EAAC,GAAEE,KAAE,IAAI,aAAa,IAAEF,EAAC,GAAEG,KAAE,IAAI,aAAa,IAAEH,EAAC,GAAEI,KAAE,IAAI,YAAY,KAAG,IAAEN,GAAE;AAAE,MAAIH,KAAE,GAAEU,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,WAAQC,KAAE,GAAEA,MAAGZ,IAAEY,MAAI;AAAC,UAAMb,KAAE,MAAIa,MAAGA,OAAIZ,IAAEG,KAAES,MAAG,KAAGA,MAAGZ,KAAE,GAAEa,KAAE,MAAID,MAAG,MAAIA,IAAEI,KAAEjB,KAAEE,KAAE,IAAEA;AAAE,aAAQgB,KAAE,GAAEA,MAAGD,IAAEC,MAAI;AAAC,YAAMF,KAAEE,KAAED,KAAE,IAAE,KAAK,IAAGE,KAAEnB,KAAE,IAAE;AAAG,QAAE,CAAC,IAAEmB,KAAE,KAAK,IAAIH,EAAC,GAAE,EAAE,CAAC,IAAEG,KAAE,CAAC,KAAK,IAAIH,EAAC,GAAE,EAAE,CAAC,IAAEH,MAAG,IAAE,MAAG;AAAI,eAAQb,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAK,GAAEN,IAAG,IAAE,EAAEC,EAAC,GAAEM,GAAEG,IAAG,IAAEL,KAAE,MAAIJ,KAAEa,MAAG,IAAE,IAAE,KAAG,IAAE,MAAIb,KAAE,IAAE,EAAEA,EAAC,IAAEmB;AAAE,MAAAZ,GAAEG,IAAG,KAAGQ,MAAGlB,KAAE,MAAG,MAAIE,IAAEK,GAAEG,IAAG,IAAEG,MAAG,IAAE,IAAEA,KAAE,IAAEA,MAAG,IAAE,KAAGA,KAAE,KAAG,IAAE,IAAE,IAAE,KAAGA,KAAE,KAAG,IAAE,IAAE,GAAEC,MAAG,MAAID,MAAGK,OAAIhB,OAAIW,OAAIZ,OAAIO,GAAEG,IAAG,IAAEC,IAAEJ,GAAEG,IAAG,IAAEC,KAAE,GAAEJ,GAAEG,IAAG,IAAEC,KAAEV,KAAG,MAAIW,OAAIL,GAAEG,IAAG,IAAEC,IAAEJ,GAAEG,IAAG,IAAEC,KAAEV,IAAEM,GAAEG,IAAG,IAAEC,KAAEV,KAAE,KAAIU;AAAA,IAAG;AAAA,EAAC;AAAC,SAAM,EAAC,UAASP,IAAE,QAAOC,IAAE,IAAGC,IAAE,OAAMC,GAAC;AAAC;AAAC,SAASI,GAAEZ,IAAEC,IAAE;AAAC,QAAMC,KAAE,YAAU,OAAOD,KAAEA,KAAE,QAAMA,KAAEA,GAAE,QAAM,GAAEG,KAAE,YAAU,OAAOH,KAAEA,KAAE,QAAMA,KAAEA,GAAE,SAAO;AAAE,UAAOD,IAAE;AAAA,IAAC,KAAI;AAAA,IAAK,KAAI;AAAO,aAAM,EAAC,OAAME,IAAE,OAAME,GAAC;AAAA,IAAE,KAAI;AAAA,IAAQ,KAAI;AAAQ,aAAM,EAAC,OAAMF,IAAE,QAAOE,GAAC;AAAA,IAAE,KAAI;AAAA,IAAO,KAAI;AAAO,aAAM,EAAC,OAAMF,IAAE,QAAOE,GAAC;AAAA,EAAC;AAAC;AAAC,SAASS,GAAEb,IAAE;AAAC,QAAMC,KAAEe,GAAE,oBAAoBhB,EAAC,GAAEE,KAAEc,GAAE,UAASZ,KAAEY,GAAE,QAAOX,KAAE,IAAI,aAAaH,GAAE,MAAM,GAAEI,KAAE,IAAI,aAAaF,GAAE,MAAM;AAAE,MAAIG,KAAE;AAAE,WAAQC,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,UAAMR,KAAEO;AAAE,aAAQC,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAMT,KAAEE,GAAEO,EAAC,GAAEC,KAAE,KAAK,IAAIV,EAAC,IAAE,GAAEW,KAAEX,MAAG,IAAE,IAAE;AAAG,MAAAM,GAAEE,EAAC,IAAEL,GAAEF,KAAES,EAAC,IAAEC,IAAEJ,GAAEC,EAAC,IAAEH,GAAEJ,KAAES,EAAC,IAAEC,IAAEH;AAAA,IAAG;AAAA,EAAC;AAAC,SAAM,EAAC,UAASF,IAAE,QAAOC,IAAE,IAAG,IAAI,aAAaU,GAAE,EAAE,GAAE,OAAM,IAAI,YAAYA,GAAE,KAAK,GAAE,SAAQ,KAAE;AAAC;AAAC,IAAMF,KAAE;AAAR,IAAUG,KAAE;AAAZ,IAAcC,KAAE;AAAhB,IAAkBF,KAAE,EAAC,UAAS,CAAC,MAAI,MAAI,GAAE,KAAG,MAAI,GAAE,KAAG,KAAG,GAAE,MAAI,KAAG,CAAC,GAAE,QAAO,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,OAAM,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,GAAE,qBAAoB,EAAC,MAAK,CAACE,IAAEJ,IAAEG,EAAC,GAAE,MAAK,CAAC,CAACC,IAAE,CAACJ,IAAEG,EAAC,GAAE,OAAM,CAAC,CAACH,IAAEI,IAAED,EAAC,GAAE,OAAM,CAACH,IAAE,CAACI,IAAED,EAAC,GAAE,IAAG,CAACH,IAAEG,IAAEC,EAAC,GAAE,MAAK,CAACJ,IAAE,CAACG,IAAE,CAACC,EAAC,EAAC,EAAC;AAAE,SAASC,GAAEnB,IAAEC,IAAEC,IAAE;AAAC,EAAAF,GAAE,WAASoB,GAAEpB,EAAC,GAAEqB,GAAErB,IAAEE,MAAA,gBAAAA,GAAG,IAAI;AAAE,QAAK,EAAC,kBAAiBE,IAAE,WAAUC,GAAC,IAAE,EAAEL,IAAEC,IAAEC,EAAC;AAAE,SAAM,EAAC,kBAAiB,IAAIW,GAAE,EAAC,GAAGT,IAAE,IAAGJ,GAAE,GAAE,CAAC,GAAE,WAAUK,IAAE,YAAW,CAAC,IAAIW,GAAE,EAAC,OAAMhB,GAAE,OAAM,UAASE,MAAGA,GAAE,YAAU,KAAI,CAAC,CAAC,GAAE,kBAAiBD,GAAE,iBAAgB;AAAC;AAAC,SAASmB,GAAEpB,IAAE;AAAC,WAAQC,KAAE,GAAEA,KAAED,GAAE,SAAS,QAAOC,MAAG,EAAE,CAAAD,GAAE,SAASC,KAAE,CAAC,KAAG;AAAE;AAAC,SAASoB,GAAErB,IAAEC,IAAE;AAAC,MAAG,QAAMA,GAAE;AAAO,QAAMI,KAAE,YAAU,OAAOJ,KAAE,CAACA,IAAEA,IAAEA,EAAC,IAAE,CAAC,QAAMA,GAAE,QAAMA,GAAE,QAAM,GAAE,QAAMA,GAAE,QAAMA,GAAE,QAAM,GAAE,QAAMA,GAAE,SAAOA,GAAE,SAAO,CAAC;AAAE,EAAAqB,GAAE,CAAC,IAAEjB,GAAE,CAAC,GAAEiB,GAAE,CAAC,IAAEjB,GAAE,CAAC,GAAEiB,GAAE,CAAC,IAAEjB,GAAE,CAAC;AAAE,WAAQD,KAAE,GAAEA,KAAEJ,GAAE,SAAS,QAAOI,MAAG,GAAE;AAAC,aAAQH,KAAE,GAAEA,KAAE,GAAEA,KAAI,GAAEA,EAAC,IAAED,GAAE,SAASI,KAAEH,EAAC;AAAE,MAAE,GAAE,GAAEqB,EAAC;AAAE,aAAQrB,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAD,GAAE,SAASI,KAAEH,EAAC,IAAE,EAAEA,EAAC;AAAA,EAAC;AAAC,MAAGI,GAAE,CAAC,MAAIA,GAAE,CAAC,KAAGA,GAAE,CAAC,MAAIA,GAAE,CAAC,GAAE;AAAC,IAAAiB,GAAE,CAAC,IAAE,IAAEjB,GAAE,CAAC,GAAEiB,GAAE,CAAC,IAAE,IAAEjB,GAAE,CAAC,GAAEiB,GAAE,CAAC,IAAE,IAAEjB,GAAE,CAAC;AAAE,aAAQJ,KAAE,GAAEA,KAAED,GAAE,OAAO,QAAOC,MAAG,GAAE;AAAC,eAAQC,KAAE,GAAEA,KAAE,GAAEA,KAAI,GAAEA,EAAC,IAAEF,GAAE,OAAOC,KAAEC,EAAC;AAAE,QAAE,GAAE,GAAEoB,EAAC,GAAE,EAAE,GAAE,CAAC;AAAE,eAAQpB,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAF,GAAE,OAAOC,KAAEC,EAAC,IAAE,EAAEA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAE,EAAC,kBAAiB,CAAC,EAAC,MAAK,CAAC,GAAE,IAAG,CAAC,GAAE,UAAS,CAAC,GAAE,KAAI,GAAE,SAAQ,CAAC,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,EAAC,GAAE,EAAC,MAAK,CAAC,GAAE,GAAE,CAAC,GAAE,UAAS,CAAC,MAAI,KAAI,GAAE,SAAQ,CAAC,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,EAAC,GAAE,EAAC,MAAK,CAAC,GAAE,GAAE,CAAC,GAAE,UAAS,CAAC,KAAG,KAAI,GAAE,SAAQ,CAAC,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAC,MAAK,CAAC,IAAG,GAAE,CAAC,GAAE,UAAS,CAAC,MAAI,KAAI,GAAE,SAAQ,CAAC,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,GAAE,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,EAAC,GAAE,EAAC,MAAK,CAAC,GAAE,GAAE,CAAC,GAAE,UAAS,CAAC,GAAE,KAAI,GAAE,SAAQ,CAAC,CAAC,IAAG,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,IAAG,CAAC,CAAC,EAAC,GAAE,EAAC,MAAK,CAAC,GAAE,GAAE,EAAE,GAAE,UAAS,CAAC,GAAE,KAAI,GAAE,SAAQ,CAAC,CAAC,IAAG,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,IAAG,EAAE,CAAC,EAAC,CAAC,GAAE,UAAS,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,GAAE,CAAC,CAAC,GAAE,mBAAkB,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,EAAC;AAAhhB,IAAkhBY,KAAE,EAAC,OAAM,GAAE,MAAK,GAAE,OAAM,GAAE,MAAK,GAAE,IAAG,GAAE,MAAK,EAAC;AAA9jB,IAAgkB,IAAE,EAAE;AAApkB,IAAskBO,KAAErB,GAAE;;;ACAp6H,IAAM,IAAE,EAAE,UAAU,wCAAwC;AAAE,SAAS,EAAEsB,IAAEC,IAAEC,IAAE;AAAC,MAAG,CAACF,GAAE,oBAAkB,CAACA,GAAE,iBAAiB,YAAU,MAAIC,GAAE,CAAC,EAAE;AAAO,QAAME,KAAEH,GAAE;AAAiB,MAAG,EAAEA,GAAE,SAAS,GAAE;AAAC,aAAME,MAAA,gBAAAA,GAAG,eAAYA,GAAE,eAAaF,GAAE,UAAU,cAAY,EAAE,KAAK,0CAA0CE,GAAE,UAAU,gDAAgDF,GAAE,UAAU,UAAU,oBAAoB;AAAE,UAAMI,MAAEF,MAAA,gBAAAA,GAAG,WAAQF,GAAE,UAAU,eAAeG,EAAC;AAAE,IAAAE,GAAEL,GAAE,WAAUC,IAAEG,EAAC;AAAA,EAAC,OAAK;AAAC,UAAMA,MAAEF,MAAA,gBAAAA,GAAG,WAAQF,GAAE;AAAO,IAAAC,GAAED,GAAE,kBAAiBE,EAAC,IAAE,EAAEF,IAAEC,IAAEG,EAAC,IAAE,EAAEJ,IAAEC,IAAEG,EAAC;AAAA,EAAC;AAAC;AAAC,SAASC,GAAEL,IAAEI,IAAEH,IAAE;AAAC,QAAMC,KAAE,EAAE,GAAED,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC,GAAEE,KAAEC,GAAE,GAAEF,IAAEF,GAAE,MAAM;AAAE,EAAAA,GAAE,kBAAkBG,IAAEG,EAAC,GAAEN,GAAE,WAASO,GAAEP,GAAE,UAASI,IAAEI,GAAE,CAAC,GAAER,GAAE,kBAAkBG,IAAEA,EAAC,GAAEC,GAAED,IAAEA,IAAEG,EAAC,GAAEN,GAAE,cAAY,EAAE,EAAE,GAAEA,GAAE,aAAYG,EAAC;AAAC;AAAC,SAAS,EAAEH,IAAEC,IAAEE,IAAE;AAAC,QAAMM,KAAET,GAAE,kBAAiBU,KAAE,EAAED,EAAC,GAAED,KAAE;AAAE,KAAEL,IAAEK,IAAEE,EAAC,KAAG,GAAEV,GAAE,QAAOQ,IAAEE,EAAC;AAAE,QAAMC,KAAEX,GAAE,iBAAiB,UAASY,KAAEZ,GAAE,iBAAiB,QAAOa,KAAEb,GAAE,iBAAiB,SAAQc,KAAE,IAAI,aAAaH,GAAE,MAAM,GAAEI,KAAE,EAAEH,EAAC,IAAE,IAAI,aAAaA,GAAE,MAAM,IAAE,MAAKL,KAAE,EAAEM,EAAC,IAAE,IAAI,aAAaA,GAAE,MAAM,IAAE;AAAK,KAAEH,IAAEF,IAAE,GAAEE,EAAC,GAAEF,GAAEQ,IAAE,CAAC;AAAE,QAAMC,KAAEC;AAAE,IAAEJ,GAAEI,EAAC,GAAEJ,GAAEb,EAAC,GAAEe,EAAC,GAAEC,GAAE,CAAC,IAAEhB,GAAE,CAAC,GAAE,EAAEU,IAAEF,IAAEK,EAAC,GAAE,EAAEF,EAAC,KAAG,EAAEG,EAAC,KAAG,EAAEH,IAAED,IAAEG,IAAEL,IAAEM,EAAC,GAAE,EAAEF,EAAC,KAAG,EAAEN,EAAC,KAAGY,GAAEN,IAAEF,IAAEG,IAAEL,IAAEF,EAAC,GAAE,EAAEO,IAAEG,IAAE,GAAET,EAAC,GAAE,EAAEM,IAAEH,IAAEF,EAAC,GAAE,EAAEG,EAAC,KAAG,EAAEG,EAAC,MAAI,EAAEA,IAAEE,IAAE,CAAC,GAAE,EAAEF,IAAEJ,IAAEG,IAAEL,IAAEG,EAAC,IAAG,EAAEC,EAAC,KAAG,EAAEN,EAAC,MAAI,EAAEA,IAAEU,IAAE,CAAC,GAAEG,GAAEb,IAAEI,IAAEG,IAAEL,IAAEI,EAAC,IAAGb,GAAE,wBAAwB;AAAC;AAAC,SAAS,EAAEA,IAAEI,IAAEH,IAAE;AAAC,QAAMC,KAAE;AAAE,MAAG,CAAC,GAAED,IAAEC,IAAEF,GAAE,gBAAgB,GAAE;AAAC,UAAMI,KAAEJ,GAAE;AAAO,IAAAE,GAAE,CAAC,IAAEE,GAAE,GAAEF,GAAE,CAAC,IAAEE,GAAE,GAAEF,GAAE,CAAC,IAAEE,GAAE,GAAE,EAAE,MAAM,4CAA4CH,GAAE,iBAAiB,IAAI,qCAAqCD,GAAE,iBAAiB,IAAI,IAAI;AAAA,EAAC;AAAC,IAAEA,GAAE,iBAAiB,UAASI,IAAE,GAAEF,EAAC,GAAE,EAAEF,GAAE,iBAAiB,QAAOI,IAAE,CAAC,GAAE,EAAEJ,GAAE,iBAAiB,SAAQI,IAAE,CAAC,GAAEJ,GAAE,wBAAwB;AAAC;AAAC,SAAS,EAAEA,IAAEI,IAAEF,IAAEC,KAAES,IAAE;AAAC,MAAG,CAAC,EAAEZ,EAAC,GAAE;AAAC,IAAAqB,GAAE,GAAE,EAAEjB,EAAC,GAAEU,GAAEV,EAAC,CAAC;AAAE,aAAQA,KAAE,GAAEA,KAAEJ,GAAE,QAAOI,MAAGF,IAAE;AAAC,eAAQD,KAAE,GAAEA,KAAE,GAAEA,KAAI,GAAEA,EAAC,IAAED,GAAEI,KAAEH,EAAC,IAAEE,GAAEF,EAAC;AAAE,QAAE,GAAE,GAAE,CAAC;AAAE,eAAQA,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAD,GAAEI,KAAEH,EAAC,IAAE,EAAEA,EAAC,IAAEE,GAAEF,EAAC;AAAA,IAAC;AAAA,EAAC;AAAC;AAAC,IAAM,IAAE,EAAE;AAAV,IAAYK,KAAE,EAAE;AAAhB,IAAkBY,KAAEV,GAAE;AAAtB,IAAwB,IAAEJ,GAAE;AAA5B,IAA8BY,KAAEZ,GAAE;AAAlC,IAAoC,IAAE,EAAE;;;ACA5mE,IAAMkB,KAAE,EAAE,UAAU,uCAAuC;AAAE,SAASC,GAAEC,IAAEC,IAAEC,IAAE;AAAC,MAAG,CAACF,GAAE,oBAAkB,CAACA,GAAE,iBAAiB,SAAS;AAAO,QAAMG,KAAEH,GAAE;AAAiB,MAAG,EAAEA,GAAE,SAAS,GAAE;AAAC,aAAME,MAAA,gBAAAA,GAAG,eAAYA,GAAE,eAAaF,GAAE,UAAU,cAAYF,GAAE,KAAK,0CAA0CI,GAAE,UAAU,gDAAgDF,GAAE,UAAU,UAAU,oBAAoB;AAAE,UAAMI,MAAEF,MAAA,gBAAAA,GAAG,WAAQF,GAAE,UAAU,eAAeG,EAAC;AAAE,IAAAE,GAAEL,GAAE,WAAUC,IAAEG,EAAC;AAAA,EAAC,OAAK;AAAC,UAAMA,KAAEH,GAAED,GAAE,kBAAiBE,EAAC,GAAEC,KAAED,MAAGA,GAAE,UAAQF,GAAE;AAAO,IAAAI,KAAEE,GAAEN,IAAEC,IAAEE,EAAC,IAAEI,GAAEP,IAAEC,IAAEE,EAAC;AAAA,EAAC;AAAC;AAAC,SAASE,GAAEL,IAAEI,IAAEI,IAAE;AAAC,QAAMC,KAAE,EAAEC,IAAEF,GAAE,GAAEA,GAAE,GAAEA,GAAE,CAAC,GAAEG,KAAEX,GAAEU,IAAED,IAAET,GAAE,MAAM;AAAE,EAAAA,GAAE,kBAAkBW,IAAEC,EAAC;AAAE,QAAMC,KAAE,EAAE,EAAE,GAAEb,GAAE,OAAMI,EAAC;AAAE,EAAAJ,GAAE,QAAMa,IAAEb,GAAE,kBAAkBW,IAAEA,EAAC,GAAEX,GAAEW,IAAEA,IAAEC,EAAC,GAAEZ,GAAE,cAAY,EAAE,EAAE,GAAEA,GAAE,aAAYW,EAAC;AAAC;AAAC,SAASL,GAAEN,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEH,GAAE,kBAAiBc,KAAE,EAAEX,EAAC,GAAEY,KAAEC;AAAE,KAAEd,IAAEa,IAAED,EAAC,KAAG,GAAEd,GAAE,QAAOe,IAAED,EAAC;AAAE,QAAMN,KAAER,GAAE,iBAAiB,UAASa,KAAEb,GAAE,iBAAiB,QAAOF,KAAEE,GAAE,iBAAiB,SAAQD,KAAE,IAAI,aAAaS,GAAE,MAAM,GAAEH,KAAE,EAAEQ,EAAC,IAAE,IAAI,aAAaA,GAAE,MAAM,IAAE,MAAKP,KAAE,EAAER,EAAC,IAAE,IAAI,aAAaA,GAAE,MAAM,IAAE;AAAK,IAAEU,IAAEL,IAAEJ,EAAC,GAAE,EAAEc,EAAC,KAAG,EAAER,EAAC,KAAG,EAAEQ,IAAEL,IAAET,IAAEI,IAAEE,EAAC,GAAE,EAAEP,EAAC,KAAG,EAAEQ,EAAC,KAAGU,GAAElB,IAAEU,IAAET,IAAEI,IAAEG,EAAC,GAAEW,GAAElB,IAAEE,IAAEc,EAAC,GAAE,EAAEhB,IAAES,IAAEL,EAAC,GAAE,EAAEU,EAAC,KAAG,EAAER,EAAC,KAAG,EAAEA,IAAEG,IAAET,IAAEI,IAAEU,EAAC,GAAE,EAAEf,EAAC,KAAG,EAAEQ,EAAC,KAAGY,GAAEZ,IAAEE,IAAET,IAAEI,IAAEL,EAAC,GAAEE,GAAE,wBAAwB;AAAC;AAAC,SAASO,GAAEP,IAAEI,IAAEH,IAAE;AAAC,QAAMC,KAAEc;AAAE,MAAG,CAAC,GAAEf,IAAEC,IAAEF,GAAE,gBAAgB,GAAE;AAAC,UAAMI,KAAEJ,GAAE;AAAO,IAAAE,GAAE,CAAC,IAAEE,GAAE,GAAEF,GAAE,CAAC,IAAEE,GAAE,GAAEF,GAAE,CAAC,IAAEE,GAAE,GAAEN,GAAE,MAAM,4CAA4CG,GAAE,iBAAiB,IAAI,qCAAqCD,GAAE,iBAAiB,IAAI,IAAI;AAAA,EAAC;AAAC,EAAAiB,GAAEjB,GAAE,iBAAiB,UAASI,IAAEF,EAAC,GAAEF,GAAE,wBAAwB;AAAC;AAAC,SAASiB,GAAEjB,IAAEI,IAAEH,KAAEkB,IAAE;AAAC,MAAGnB,GAAE,UAAQE,KAAE,GAAEA,KAAEF,GAAE,QAAOE,MAAG,GAAE;AAAC,aAAQE,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAM,GAAEN,EAAC,IAAEJ,GAAEE,KAAEE,EAAC,IAAEH,GAAEG,EAAC;AAAE,MAAEM,IAAEA,IAAEN,EAAC;AAAE,aAAQA,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAJ,GAAEE,KAAEE,EAAC,IAAEM,GAAEN,EAAC,IAAEH,GAAEG,EAAC;AAAA,EAAC;AAAC;AAAC,IAAMM,KAAE,EAAE;AAAV,IAAYE,KAAE,EAAE;AAAhB,IAAkBI,KAAE,EAAE;;;ACA9kB,IAAII;AAAE,IAAMC,KAAE;AAAqB,IAAI,IAAED,KAAE,cAAcE,GAAEC,GAAE,cAAc,EAAEC,EAAC,CAAC,CAAC,EAAE;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,aAAW,MAAK,KAAK,YAAU,MAAK,KAAK,WAAS,MAAK,KAAK,OAAK,MAAG,KAAK,OAAK,OAAG,KAAK,mBAAiB,IAAID,MAAE,KAAK,OAAK;AAAA,EAAM;AAAA,EAAC,aAAY;AAAC,KAAC,EAAE,KAAK,QAAQ,KAAG,KAAK,iBAAiB,SAAS,YAAU,KAAK,aAAW,WAAU,KAAK,KAAM,MAAI;AAAC,WAAK,QAAQ,IAAI,EAAG,MAAE;AAJh9D;AAIm9D,iBAAC,kBAAiB,KAAK,kBAAiB,aAAW,UAAK,eAAL,mBAAiB,IAAK,CAAAC,OAAGA,GAAE,MAAM,GAAG;AAAA,SAAK,MAAI,KAAK,KAAK,YAAW,IAAI,GAAG,EAAC,MAAK,MAAG,MAAK,KAAE,CAAC,CAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAM,CAAC,KAAK,UAAQ,EAAE,KAAK,QAAQ,KAAG,EAAE,KAAK,SAAS,MAAM,KAAG,KAAK,UAAQ,KAAK,iBAAiB,SAAS,SAAO,MAAI,CAAC,KAAK,cAAY,KAAK,WAAW,SAAO;AAAA,EAAE;AAAA,EAAC,IAAI,gBAAe;AAAC,UAAMA,KAAE,KAAK,iBAAiB,UAASC,KAAE,KAAK;AAAiB,QAAG,MAAID,GAAE,UAAQ,KAAK,cAAY,MAAI,KAAK,WAAW,OAAO,QAAM,EAAC,QAAO,IAAIE,GAAE,EAAC,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,MAAK,GAAE,kBAAiBD,GAAC,CAAC,GAAE,QAAO,IAAI,EAAE,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,kBAAiBA,GAAC,CAAC,EAAC;AAAE,UAAME,KAAE,EAAE,KAAK,SAAS,IAAE,KAAK,UAAU,QAAQH,IAAEC,EAAC,IAAED;AAAE,QAAII,KAAE,IAAE,GAAEC,KAAE,IAAE,GAAEC,KAAE,IAAE,GAAET,KAAE,KAAG,GAAEU,KAAE,KAAG,GAAEC,KAAE,KAAG,GAAET,KAAE,GAAED,KAAE,GAAEW,KAAE;AAAE,UAAMC,KAAEP,GAAE,QAAOQ,KAAE,KAAGD,KAAE;AAAG,QAAIE,KAAE;AAAE,WAAKA,KAAEF,MAAG;AAAC,YAAMV,KAAEG,GAAES,IAAG,GAAEX,KAAEE,GAAES,IAAG,GAAEC,KAAEV,GAAES,IAAG;AAAE,MAAAR,KAAE,KAAK,IAAIA,IAAEJ,EAAC,GAAEK,KAAE,KAAK,IAAIA,IAAEJ,EAAC,GAAEK,KAAE,KAAK,IAAIA,IAAEO,EAAC,GAAEhB,KAAE,KAAK,IAAIA,IAAEG,EAAC,GAAEO,KAAE,KAAK,IAAIA,IAAEN,EAAC,GAAEO,KAAE,KAAK,IAAIA,IAAEK,EAAC,GAAEd,MAAGY,KAAEX,IAAEF,MAAGa,KAAEV,IAAEQ,MAAGE,KAAEE;AAAA,IAAC;AAAC,WAAM,EAAC,QAAO,IAAIX,GAAE,EAAC,MAAKE,IAAE,MAAKC,IAAE,MAAKC,IAAE,MAAKT,IAAE,MAAKU,IAAE,MAAKC,IAAE,kBAAiBP,GAAC,CAAC,GAAE,QAAO,IAAI,EAAE,EAAC,GAAEF,IAAE,GAAED,IAAE,GAAEW,IAAE,kBAAiBR,GAAC,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,QAAG,EAAE,KAAK,SAAS,EAAE,QAAO,KAAK,UAAU,eAAe,KAAK,gBAAgB;AAAE,UAAMD,KAAE,KAAK;AAAc,WAAO,IAAI,EAAE,EAAC,GAAEA,GAAE,OAAO,GAAE,GAAEA,GAAE,OAAO,GAAE,GAAEA,GAAE,OAAO,MAAK,kBAAiB,KAAK,iBAAgB,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,EAAE,KAAK,SAAS,IAAE,KAAK,UAAU,eAAe,KAAK,gBAAgB,IAAE,KAAK,cAAc;AAAA,EAAM;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAM,CAAC,KAAK,UAAQ,EAAE,KAAK,QAAQ,KAAG,EAAE,KAAK,SAAS,MAAM,IAAE,KAAK,SAAS,OAAO,MAAM,IAAE,KAAK,cAAc;AAAA,EAAM;AAAA,EAAC,aAAaA,IAAE;AAAC,SAAK,UAAQ,KAAK,eAAa,KAAK,aAAW,CAAC,IAAG,KAAK,WAAW,KAAKc,GAAE,KAAKd,EAAC,CAAC,GAAE,KAAK,aAAa,YAAY,KAAG,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,kBAAiB,gDAAgD;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,QAAG,KAAK,QAAO;AAAC,UAAG,KAAK,YAAW;AAAC,cAAMC,KAAE,KAAK,WAAW,QAAQD,EAAC;AAAE,YAAG,OAAKC,GAAE,QAAO,KAAK,WAAW,OAAOA,IAAE,CAAC,GAAE,KAAK,KAAK,aAAa,YAAY;AAAA,MAAC;AAAC,QAAE,UAAU,KAAK,aAAa,EAAE,MAAM,qBAAoB,0DAA0D;AAAA,IAAC,MAAM,GAAE,UAAU,KAAK,aAAa,EAAE,MAAM,qBAAoB,gDAAgD;AAAA,EAAC;AAAA,EAAC,OAAOD,IAAEC,IAAEE,IAAEC,IAAE;AAAC,WAAO,EAAEW,GAAE,GAAEf,IAAEgB,EAAC,GAAE,EAAED,GAAE,GAAEd,IAAE,CAAC,GAAE,EAAEc,GAAE,GAAEZ,IAAEc,EAAC,GAAEC,GAAEF,IAAE,GAAEA,EAAC,GAAEE,GAAEF,IAAEC,IAAED,EAAC,GAAE,EAAE,MAAKA,IAAEZ,EAAC,GAAE;AAAA,EAAI;AAAA,EAAC,OAAOJ,IAAEC,IAAEE,IAAEC,IAAE;AAAC,WAAO,KAAK,UAAQ,EAAE,CAAC,IAAEJ,IAAE,EAAE,CAAC,IAAEC,IAAE,EAAE,CAAC,IAAEE,IAAES,GAAE,MAAK,GAAER,EAAC,GAAE,SAAO,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,YAAW,gDAAgD,GAAE;AAAA,EAAK;AAAA,EAAC,MAAMJ,IAAEC,IAAE;AAAC,WAAO,KAAK,UAAQkB,GAAE,MAAKnB,IAAEC,EAAC,GAAE,SAAO,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,WAAU,gDAAgD,GAAE;AAAA,EAAK;AAAA,EAAC,SAASD,IAAEC,IAAE;AAAC,WAAO,KAAK,UAAQO,GAAE,MAAKR,IAAEC,EAAC,GAAE,SAAO,EAAE,UAAU,KAAK,aAAa,EAAE,MAAM,cAAa,gDAAgD,GAAE;AAAA,EAAK;AAAA,EAAC,KAAKD,IAAE;AAAC,WAAO,EAAE,KAAK,QAAQ,KAAG,KAAK,oBAAoBoB,GAAE,MAAK,KAAK,SAAS,QAAOpB,EAAC,CAAC,GAAE,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,qBAAqBA,IAAE;AAAC,SAAK,KAAK,YAAWA,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,QAAIA,KAAE;AAAK,QAAG,KAAK,YAAW;AAAC,YAAMC,KAAE,oBAAI,OAAIE,KAAE,oBAAI;AAAI,MAAAH,KAAE,KAAK,WAAW,IAAK,CAAAA,OAAGA,GAAE,uBAAuBC,IAAEE,EAAC,CAAE;AAAA,IAAC;AAAC,UAAMF,KAAE,EAAC,YAAWD,IAAE,kBAAiB,KAAK,kBAAiB,kBAAiB,KAAK,iBAAiB,MAAM,GAAE,WAAU,EAAE,KAAK,SAAS,IAAE,KAAK,UAAU,MAAM,IAAE,MAAK,UAAS,EAAE,KAAK,QAAQ,IAAE,EAAC,QAAO,KAAK,SAAS,QAAO,QAAO,EAAE,KAAK,SAAS,MAAM,IAAE,KAAK,SAAS,OAAO,MAAM,IAAE,KAAI,IAAE,KAAI;AAAE,WAAO,IAAIL,GAAEM,EAAC;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,SAAK,aAAa,kBAAkB;AAAA,EAAC;AAAA,EAAC,MAAM,aAAaD,IAAE;AAAC,UAAMC,KAAE,OAAO,0BAAkD,GAAEE,KAAE,KAAK,KAAK,GAAEC,KAAE,MAAM,QAAQ,IAAI,CAACH,IAAEE,EAAC,CAAC,GAAE,EAAC,cAAaE,GAAC,IAAED,GAAE,CAAC;AAAE,WAAOC,GAAE,MAAKL,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,UAAUA,IAAEC,IAAE;AAAC,QAAG,EAAED,cAAa,GAAG,QAAO,EAAE,UAAUJ,EAAC,EAAE,MAAM,gBAAe,0CAA0C,GAAE;AAAK,UAAMO,KAAE,IAAIR,GAAE0B,GAAEb,GAAE,GAAER,IAAEC,EAAC,CAAC;AAAE,WAAOA,MAAGA,GAAE,aAAW,UAAQA,GAAE,YAAUM,GAAEJ,IAAEF,GAAE,SAAS,IAAEE;AAAA,EAAC;AAAA,EAAC,OAAO,aAAaH,IAAEC,IAAE;AAAC,WAAOD,cAAa,IAAE,IAAIL,GAAE0B,GAAEX,GAAET,MAAGA,GAAE,uBAAqB,CAAC,GAAED,IAAEC,EAAC,CAAC,KAAG,EAAE,UAAUL,EAAC,EAAE,MAAM,mBAAkB,0CAA0C,GAAE;AAAA,EAAK;AAAA,EAAC,OAAO,eAAeI,IAAEC,IAAE;AAAC,WAAOD,cAAa,IAAE,IAAIL,GAAE0B,GAAED,GAAEnB,MAAGA,GAAE,uBAAqB,CAAC,GAAED,IAAEC,EAAC,CAAC,KAAG,EAAE,UAAUL,EAAC,EAAE,MAAM,qBAAoB,0CAA0C,GAAE;AAAA,EAAK;AAAA,EAAC,OAAO,YAAYI,IAAEC,IAAE;AAAC,QAAG,EAAED,cAAa,GAAG,QAAO,EAAE,UAAUJ,EAAC,EAAE,MAAM,kBAAiB,0CAA0C,GAAE;AAAK,UAAMO,MAAEF,MAAA,gBAAAA,GAAG,WAAQ,MAAKG,KAAEK,GAAEN,IAAEF,MAAA,gBAAAA,GAAG,IAAI;AAAE,WAAO,IAAIN,GAAE0B,GAAEtB,GAAEI,EAAC,GAAEH,IAAE,EAAC,GAAGC,IAAE,MAAKG,GAAC,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,kBAAkBJ,IAAEC,IAAE;AAAC,QAAG,EAAED,cAAa,GAAG,QAAO,EAAE,UAAUJ,EAAC,EAAE,MAAM,wBAAuB,2CAA2C,GAAE;AAAK,UAAMO,KAAE,EAAEH,EAAC;AAAE,WAAO,IAAIL,GAAE,EAAC,kBAAiB,IAAII,GAAE,EAAC,UAASI,GAAE,SAAQ,CAAC,GAAE,YAAW,CAAC,IAAIW,GAAE,EAAC,OAAMX,GAAE,OAAM,SAAQ,QAAO,WAASF,MAAA,gBAAAA,GAAG,aAAU,KAAI,CAAC,CAAC,GAAE,kBAAiBD,GAAE,iBAAgB,CAAC;AAAA,EAAC;AAAA,EAAC,aAAa,eAAeA,IAAEG,IAAEC,IAAE;AAAC,QAAG,EAAEJ,cAAa,GAAG,OAAM,EAAE,UAAUJ,EAAC,EAAE,MAAM,qBAAoB,0CAA0C,GAAE,IAAIU,GAAE,iBAAgB,0CAA0C;AAAE,UAAK,EAAC,cAAaA,GAAC,IAAE,MAAMgB,GAAE,OAAO,4BAAqC,GAAElB,EAAC;AAAE,WAAO,IAAIT,GAAE,MAAMW,GAAEN,IAAEG,IAAEC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,yBAAyBJ,IAAEC,IAAEE,IAAE;AAJx5N;AAIy5N,UAAMC,MAAED,MAAA,gBAAAA,GAAG,WAAQ,MAAKE,OAAE,KAAAF,MAAA,gBAAAA,GAAG,cAAH,mBAAc,YAAS,IAAIoB;AAAE,IAAAlB,GAAE,SAAO,CAACL,GAAE,GAAEA,GAAE,GAAEA,GAAE,KAAG,CAAC;AAAE,UAAMM,KAAEN,GAAE;AAAiB,WAAO,IAAIL,GAAE,EAAC,UAAS,EAAC,QAAOM,IAAE,QAAOG,GAAC,GAAE,WAAUC,IAAE,kBAAiBC,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,iBAAiBN,IAAEG,IAAE;AAJ7mO;AAI8mO,UAAMC,OAAE,KAAAD,MAAA,gBAAAA,GAAG,cAAH,mBAAc,YAAS,IAAIoB;AAAE,IAAAnB,GAAE,SAAO,CAACJ,GAAE,GAAEA,GAAE,GAAEA,GAAE,KAAG,CAAC;AAAE,UAAMK,KAAEL,GAAE,kBAAiBM,KAAE,IAAIX,GAAE,EAAC,WAAUS,IAAE,kBAAiBC,GAAC,CAAC;AAAE,WAAOC,GAAE,oBAAoB,QAAQ,OAAO,IAAIA,GAAE,mBAAkB,iCAAiC,CAAC,CAAC,GAAEA;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAACQ,EAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKS,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,aAAY,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,UAAS,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,OAAG,OAAM,MAAG,SAAQ,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,OAAG,OAAM,MAAG,SAAQ,MAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKxB,IAAE,aAAY,MAAG,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,oBAAmB,MAAM,GAAE,IAAEJ,KAAE,EAAE,CAAC,EAAEC,EAAC,CAAC,GAAE,CAAC;AAAE,IAAMmB,KAAE,EAAC,GAAEZ,GAAE,GAAE,GAAE,CAAC,GAAE,GAAEA,GAAE,GAAE,GAAE,CAAC,GAAE,GAAEA,GAAE,GAAE,GAAE,CAAC,EAAC;AAAzC,IAA2Ca,KAAEnB,GAAE;AAA/C,IAAiD,IAAEA,GAAE;AAArD,IAAuDoB,KAAEpB,GAAE;AAA3D,IAA6D,IAAE,EAAE;AAAjE,IAAmE,IAAE;;;ACA7tP,SAAS2B,GAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAED,GAAE;AAAS,EAAAA,GAAE,WAAS,CAAC,GAAE,OAAOA,GAAE;AAAa,QAAME,KAAEC,GAAE,SAASH,EAAC;AAAE,MAAGE,GAAE,eAAa,QAAO,CAACF,GAAE,UAAU,QAAOE;AAAE,QAAML,KAAEO,GAAEL,IAAEC,GAAE,SAAS,GAAEK,KAAEH,GAAE,oBAAkBI,GAAE,OAAMC,KAAEP,GAAE,mBAAkB,EAAC,WAAUQ,GAAC,IAAEV,IAAEW,KAAE,EAAED,EAAC,KAAGA,GAAE,SAAO,IAAEE,GAAEF,GAAE,SAAS,GAAG,IAAE,OAAK,IAAI,IAAIA,EAAC,CAAC,IAAE,OAAK,CAAC;AAAG,aAAUG,MAAKV,IAAE;AAAC,UAAMH,KAAEQ,GAAEK,IAAEJ,IAAEF,IAAEN,IAAEF,EAAC;AAAE,MAAEC,EAAC,KAAGI,GAAE,SAAS,KAAK,IAAIM,GAAE,EAAC,UAASV,IAAE,YAAWW,GAAEE,EAAC,EAAC,CAAC,CAAC;AAAA,EAAC;AAAC,SAAOT;AAAC;AAAC,SAASQ,GAAEE,IAAE;AAAC,SAAM,CAAC,EAAC,YAAWC,GAAC,MAAI;AAAC,QAAG,CAACA,GAAE,QAAM,CAAC;AAAE,QAAG,CAACD,GAAE,QAAOC;AAAE,eAAUf,MAAKe,GAAE,CAAAD,GAAE,IAAId,EAAC,KAAG,OAAOe,GAAEf,EAAC;AAAE,WAAOe;AAAA,EAAC;AAAC;AAAC,SAASP,GAAEM,IAAEC,IAAEb,IAAEW,IAAEV,IAAE;AAAC,QAAMC,KAAEU,GAAE,WAAWC,EAAC,GAAEC,KAAEb,GAAE,IAAIC,EAAC;AAAE,MAAG,QAAMY,MAAGA,GAAE,WAASN,GAAE,UAAQ,QAAMM,GAAE,IAAI,QAAO;AAAK,QAAMjB,KAAEQ,GAAEO,IAAEZ,IAAEW,EAAC,GAAED,KAAEK,GAAE,SAASH,GAAE,QAAQ;AAAE,EAAAF,GAAE,mBAAiBV;AAAE,QAAMM,KAAEC,GAAEK,GAAE,YAAWD,IAAEG,GAAE,eAAe;AAAE,SAAOA,GAAE,WAASN,GAAE,UAAQ,EAAE,iBAAiBX,IAAE,EAAC,QAAOa,IAAE,WAAUJ,GAAC,CAAC,IAAE,EAAE,yBAAyBT,IAAE,CAAC,EAAC,MAAKiB,GAAE,MAAK,QAAOA,GAAE,IAAG,CAAC,GAAE,EAAC,QAAOJ,IAAE,WAAUJ,GAAC,CAAC;AAAC;AAAC,SAASD,GAAE,EAAC,YAAWO,GAAC,GAAEC,IAAE,EAAC,qBAAoBf,GAAC,GAAE;AAAC,SAAO,IAAI,EAAE,EAAC,GAAEc,GAAEd,GAAE,OAAO,GAAE,GAAEc,GAAEd,GAAE,OAAO,GAAE,GAAEc,GAAEd,GAAE,OAAO,GAAE,kBAAiBe,GAAC,CAAC;AAAC;AAAC,SAASN,GAAEK,IAAE,EAAC,qBAAoBC,GAAC,GAAEf,IAAE;AAAC,SAAO,IAAIkB,GAAE,EAAC,aAAY,CAACJ,GAAEC,GAAE,YAAY,GAAE,CAACD,GAAEC,GAAE,YAAY,GAAED,GAAEC,GAAE,YAAY,CAAC,GAAE,UAAS,EAAE,CAACD,GAAEC,GAAE,SAAS,GAAED,GAAEC,GAAE,SAAS,GAAED,GAAEC,GAAE,SAAS,CAAC,GAAED,GAAEC,GAAE,WAAW,CAAC,GAAE,OAAM,CAACD,GAAEC,GAAE,MAAM,GAAED,GAAEC,GAAE,MAAM,GAAED,GAAEC,GAAE,MAAM,CAAC,GAAE,YAAWf,GAAC,CAAC;AAAC;AAAC,IAAIU;AAAE,SAASJ,GAAEQ,IAAEC,IAAE;AAAC,QAAMf,KAAE,oBAAI;AAAI,aAAUC,MAAKc,IAAE;AAAC,UAAMD,KAAEb,GAAE;AAAe,QAAG,QAAMa,GAAE;AAAS,UAAMC,KAAEd,GAAE,WAAUC,KAAED,GAAE,UAASY,KAAEZ,GAAE;AAAiB,QAAIE,KAAEH,GAAE,IAAIc,EAAC;AAAE,QAAG,QAAMX,GAAE,SAAOA,KAAE,EAAC,MAAKY,IAAE,QAAOL,GAAE,QAAO,KAAIR,IAAE,iBAAgBS,GAAEV,GAAE,KAAK,EAAE,gBAAe,GAAED,GAAE,IAAIc,IAAEX,EAAC,GAAEU,IAAE;AAAA,MAAC,KAAI;AAAA,MAAY,KAAI;AAAY,QAAAV,GAAE,SAAOO,GAAE;AAAU;AAAA,MAAM,KAAI;AAAa,QAAAP,GAAE,SAAOO,GAAE;AAAQ;AAAA,MAAM;AAAQ,QAAAP,GAAE,SAAOO,GAAE;AAAA,IAAM;AAAA,QAAM,SAAQ,KAAK,wFAAwFT,GAAE,SAAS,EAAE;AAAA,EAAC;AAAC,SAAOD;AAAC;AAAC,SAASW,GAAEG,IAAE;AAAC,SAAM,EAAC,iBAAgBA,GAAE,SAAS,kBAAkB,EAAC;AAAC;AAAC,CAAC,SAASA,IAAE;AAAC,EAAAA,GAAEA,GAAE,SAAO,CAAC,IAAE,UAASA,GAAEA,GAAE,UAAQ,CAAC,IAAE,WAAUA,GAAEA,GAAE,YAAU,CAAC,IAAE;AAAW,EAAEJ,OAAIA,KAAE,CAAC,EAAE;", "names": ["a", "c", "e", "r", "i", "n", "f", "p", "g", "t", "o", "s", "x", "h", "l", "m", "u", "e", "s", "o", "n", "y", "a", "m", "i", "f", "l", "t", "r", "p", "w", "c", "x", "r", "o", "i", "v", "b", "t", "e", "s", "a", "c", "A", "F", "k", "w", "y", "L", "c", "t", "e", "n", "d", "r", "o", "s", "a", "i", "l", "f", "u", "h", "p", "m", "M", "g", "w", "v", "A", "y", "x", "O", "t", "r", "o", "i", "e", "z", "M", "v", "a", "s", "n", "c", "f", "m", "g", "l", "S", "x", "O", "k", "L", "p", "d", "j", "e", "r", "o", "i", "t", "x", "A", "b", "a", "c", "w", "p", "R", "l", "n", "s", "k", "y", "L", "f", "G", "I", "a", "m", "p", "e", "t", "w", "r", "o", "n", "s", "l", "c", "h", "f", "d", "x", "i", "g", "k", "D", "Z", "v", "j", "u", "A", "y", "L", "u", "r", "n", "o", "a", "i", "x", "E", "m", "f", "p", "g", "D", "l", "s", "t", "e", "c", "w", "L"]}