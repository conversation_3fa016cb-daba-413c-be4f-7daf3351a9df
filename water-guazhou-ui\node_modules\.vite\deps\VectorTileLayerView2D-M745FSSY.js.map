{"version": 3, "sources": ["../../@arcgis/core/views/2d/engine/vectorTiles/RectangleBinPack.js", "../../@arcgis/core/views/2d/engine/vectorTiles/GlyphMosaic.js", "../../@arcgis/core/views/2d/engine/vectorTiles/GlyphSource.js", "../../@arcgis/core/views/2d/engine/vectorTiles/SpriteMosaic.js", "../../@arcgis/core/views/2d/engine/vectorTiles/TileHandler.js", "../../@arcgis/core/views/2d/engine/vectorTiles/TileManager.js", "../../@arcgis/core/views/2d/engine/vectorTiles/decluttering/core.js", "../../@arcgis/core/views/2d/engine/vectorTiles/decluttering/util.js", "../../@arcgis/core/views/2d/engine/vectorTiles/RenderBucket.js", "../../@arcgis/core/views/2d/engine/vectorTiles/VectorTile.js", "../../@arcgis/core/views/2d/engine/vectorTiles/decluttering/jobs.js", "../../@arcgis/core/views/2d/engine/vectorTiles/decluttering/SymbolDeclutterer.js", "../../@arcgis/core/views/2d/engine/vectorTiles/decluttering/SymbolRepository.js", "../../@arcgis/core/views/2d/engine/vectorTiles/decluttering/SymbolFader.js", "../../@arcgis/core/views/2d/engine/webgl/RenderableTile.js", "../../@arcgis/core/views/2d/engine/vectorTiles/VectorTileContainer.js", "../../@arcgis/core/views/2d/layers/support/DebugOverlay.js", "../../@arcgis/core/views/2d/tiling/TileInfoViewPOT.js", "../../@arcgis/core/views/2d/layers/VectorTileLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport h from\"../webgl/Rect.js\";class e{constructor(e,t){this._width=0,this._height=0,this._free=[],this._width=e,this._height=t,this._free.push(new h(0,0,e,t))}get width(){return this._width}get height(){return this._height}allocate(e,t){if(e>this._width||t>this._height)return new h;let i=null,s=-1;for(let h=0;h<this._free.length;++h){const w=this._free[h];e<=w.width&&t<=w.height&&(null===i||w.y<=i.y&&w.x<=i.x)&&(i=w,s=h)}return null===i?new h:(this._free.splice(s,1),i.width<i.height?(i.width>e&&this._free.push(new h(i.x+e,i.y,i.width-e,t)),i.height>t&&this._free.push(new h(i.x,i.y+t,i.width,i.height-t))):(i.width>e&&this._free.push(new h(i.x+e,i.y,i.width-e,i.height)),i.height>t&&this._free.push(new h(i.x,i.y+t,e,i.height-t))),new h(i.x,i.y,e,t))}release(h){for(let e=0;e<this._free.length;++e){const t=this._free[e];if(t.y===h.y&&t.height===h.height&&t.x+t.width===h.x)t.width+=h.width;else if(t.x===h.x&&t.width===h.width&&t.y+t.height===h.y)t.height+=h.height;else if(h.y===t.y&&h.height===t.height&&h.x+h.width===t.x)t.x=h.x,t.width+=h.width;else{if(h.x!==t.x||h.width!==t.width||h.y+h.height!==t.y)continue;t.y=h.y,t.height+=h.height}this._free.splice(e,1),this.release(h)}this._free.push(h)}}export{e as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"./RectangleBinPack.js\";import e from\"../webgl/Rect.js\";import{PixelFormat as i,PixelType as s}from\"../../../webgl/enums.js\";import{Texture as h}from\"../../../webgl/Texture.js\";class r{constructor(e,i,s){this.width=0,this.height=0,this._dirties=[],this._glyphData=[],this._currentPage=0,this._glyphIndex={},this._textures=[],this._rangePromises=new Map,this.width=e,this.height=i,this._glyphSource=s,this._binPack=new t(e-4,i-4),this._glyphData.push(new Uint8Array(e*i)),this._dirties.push(!0),this._textures.push(void 0)}getGlyphItems(i,s){const h=[],r=this._glyphSource,n=new Set,a=1/256;for(const t of s){const e=Math.floor(t*a);n.add(e)}const o=[];return n.forEach((t=>{if(t<=256){const e=i+t;if(this._rangePromises.has(e))o.push(this._rangePromises.get(e));else{const s=r.getRange(i,t).then((()=>{this._rangePromises.delete(e)}),(()=>{this._rangePromises.delete(e)}));this._rangePromises.set(e,s),o.push(s)}}})),Promise.all(o).then((()=>{let n=this._glyphIndex[i];n||(n={},this._glyphIndex[i]=n);for(const a of s){const s=n[a];if(s){h[a]={sdf:!0,rect:s.rect,metrics:s.metrics,page:s.page,code:a};continue}const o=r.getGlyph(i,a);if(!o||!o.metrics)continue;const l=o.metrics;let c;if(0===l.width)c=new e(0,0,0,0);else{const e=3,i=l.width+2*e,s=l.height+2*e;let h=i%4?4-i%4:4,r=s%4?4-s%4:4;1===h&&(h=5),1===r&&(r=5),c=this._binPack.allocate(i+h,s+r),c.isEmpty&&(this._dirties[this._currentPage]||(this._glyphData[this._currentPage]=null),this._currentPage=this._glyphData.length,this._glyphData.push(new Uint8Array(this.width*this.height)),this._dirties.push(!0),this._textures.push(void 0),this._binPack=new t(this.width-4,this.height-4),c=this._binPack.allocate(i+h,s+r));const n=this._glyphData[this._currentPage],a=o.bitmap;let g,_;if(a)for(let t=0;t<s;t++){g=i*t,_=this.width*(c.y+t+1)+c.x;for(let t=0;t<i;t++)n[_+t+1]=a[g+t]}}n[a]={rect:c,metrics:l,tileIDs:null,page:this._currentPage},h[a]={sdf:!0,rect:c,metrics:l,page:this._currentPage,code:a},this._dirties[this._currentPage]=!0}return h}))}removeGlyphs(t){for(const e in this._glyphIndex){const i=this._glyphIndex[e];if(!i)continue;let s;for(const e in i)if(s=i[e],s.tileIDs.delete(t),0===s.tileIDs.size){const t=this._glyphData[s.page],h=s.rect;let r,n;for(let e=0;e<h.height;e++)for(r=this.width*(h.y+e)+h.x,n=0;n<h.width;n++)t[r+n]=0;delete i[e],this._dirties[s.page]=!0}}}bind(t,e,r,n=0){this._textures[r]||(this._textures[r]=new h(t,{pixelFormat:i.ALPHA,dataType:s.UNSIGNED_BYTE,width:this.width,height:this.height},new Uint8Array(this.width*this.height)));const a=this._textures[r];a.setSamplingMode(e),this._dirties[r]&&a.setData(this._glyphData[r]),t.bindTexture(a,n),this._dirties[r]=!1}dispose(){this._binPack=null;for(const t of this._textures)t&&t.dispose();this._textures.length=0}}export{r as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../request.js\";import t from\"../../../../core/pbf.js\";class s{constructor(e){if(this._metrics=[],this._bitmaps=[],e)for(;e.next();)switch(e.tag()){case 1:{const t=e.getMessage();for(;t.next();)switch(t.tag()){case 3:{const e=t.getMessage();let s,a,r,n,i,c,g;for(;e.next();)switch(e.tag()){case 1:s=e.getUInt32();break;case 2:a=e.getBytes();break;case 3:r=e.getUInt32();break;case 4:n=e.getUInt32();break;case 5:i=e.getSInt32();break;case 6:c=e.getSInt32();break;case 7:g=e.getUInt32();break;default:e.skip()}e.release(),s&&(this._metrics[s]={width:r,height:n,left:i,top:c,advance:g},this._bitmaps[s]=a);break}default:t.skip()}t.release();break}default:e.skip()}}getMetrics(e){return this._metrics[e]}getBitmap(e){return this._bitmaps[e]}}class a{constructor(){this._ranges=[]}getRange(e){return this._ranges[e]}addRange(e,t){this._ranges[e]=t}}class r{constructor(e){this._glyphInfo={},this._baseURL=e}getRange(a,r){const n=this._getFontStack(a);if(n.getRange(r))return Promise.resolve();const i=256*r,c=i+255;if(this._baseURL){const g=this._baseURL.replace(\"{fontstack}\",a).replace(\"{range}\",i+\"-\"+c);return e(g,{responseType:\"array-buffer\"}).then((e=>{n.addRange(r,new s(new t(new Uint8Array(e.data),new DataView(e.data))))})).catch((()=>{n.addRange(r,new s)}))}return n.addRange(r,new s),Promise.resolve()}getGlyph(e,t){const s=this._getFontStack(e);if(!s)return;const a=Math.floor(t/256);if(a>256)return;const r=s.getRange(a);return r?{metrics:r.getMetrics(t),bitmap:r.getBitmap(t)}:void 0}_getFontStack(e){let t=this._glyphInfo[e];return t||(t=this._glyphInfo[e]=new a),t}}export{r as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{rasterizeDash as t}from\"../../../../symbols/cim/rasterizingUtils.js\";import i from\"./RectangleBinPack.js\";import e from\"../webgl/Rect.js\";import{PixelFormat as s,PixelType as h,TextureWrapMode as a}from\"../../../webgl/enums.js\";import{Texture as r}from\"../../../webgl/Texture.js\";const o=\"dasharray-\";class n{constructor(t,e,s=0){this._size=[],this._mosaicsData=[],this._textures=[],this._dirties=[],this._maxItemSize=0,this._currentPage=0,this._pageWidth=0,this._pageHeight=0,this._mosaicRects={},this.pixelRatio=1,(t<=0||e<=0)&&console.error(\"Sprites mosaic defaultWidth and defaultHeight must be greater than zero!\"),this._pageWidth=t,this._pageHeight=e,s>0&&(this._maxItemSize=s),this._binPack=new i(t-4,e-4)}dispose(){this._binPack=null,this._mosaicRects={};for(const t of this._textures)t&&t.dispose();this._textures.length=0}getWidth(t){return t>=this._size.length?-1:this._size[t][0]}getHeight(t){return t>=this._size.length?-1:this._size[t][1]}getPageSize(t){return t>=this._size.length?null:this._size[t]}setSpriteSource(t){if(this.dispose(),this.pixelRatio=t.devicePixelRatio,0===this._mosaicsData.length){this._binPack=new i(this._pageWidth-4,this._pageHeight-4);const t=Math.floor(this._pageWidth),e=Math.floor(this._pageHeight),s=new Uint32Array(t*e);this._mosaicsData[0]=s,this._dirties.push(!0),this._size.push([this._pageWidth,this._pageHeight]),this._textures.push(void 0)}this._sprites=t}getSpriteItem(t,i=!1){let e,s,h=this._mosaicRects[t];if(h)return h;if(!this._sprites||\"loaded\"!==this._sprites.loadStatus)return null;if(t&&t.startsWith(o)?([e,s]=this._rasterizeDash(t),i=!0):e=this._sprites.getSpriteInfo(t),!e||!e.width||!e.height||e.width<0||e.height<0)return null;const a=e.width,r=e.height,[n,_,g]=this._allocateImage(a,r);return n.width<=0?null:(this._copy(n,e,_,g,i,s),h={rect:n,width:a,height:r,sdf:e.sdf,simplePattern:!1,pixelRatio:e.pixelRatio,page:_},this._mosaicRects[t]=h,h)}getSpriteItems(t){const i={};for(const e of t)i[e.name]=this.getSpriteItem(e.name,e.repeat);return i}getMosaicItemPosition(t,i){const e=this.getSpriteItem(t,i),s=e&&e.rect;if(!s)return null;s.width=e.width,s.height=e.height;const h=e.width,a=e.height,r=2;return{tl:[s.x+r,s.y+r],br:[s.x+r+h,s.y+r+a],page:e.page}}bind(t,i,e=0,o=0){if(e>=this._size.length||e>=this._mosaicsData.length)return;this._textures[e]||(this._textures[e]=new r(t,{pixelFormat:s.RGBA,dataType:h.UNSIGNED_BYTE,wrapMode:a.CLAMP_TO_EDGE,width:this._size[e][0],height:this._size[e][1]},new Uint8Array(this._mosaicsData[e].buffer)));const n=this._textures[e];n.setSamplingMode(i),this._dirties[e]&&n.setData(new Uint8Array(this._mosaicsData[e].buffer)),t.bindTexture(n,o),this._dirties[e]=!1}static _copyBits(t,i,e,s,h,a,r,o,n,_,g){let c=s*i+e,l=o*a+r;if(g){l-=a;for(let r=-1;r<=_;r++,c=((r+_)%_+s)*i+e,l+=a)for(let i=-1;i<=n;i++)h[l+i]=t[c+(i+n)%n]}else for(let p=0;p<_;p++){for(let i=0;i<n;i++)h[l+i]=t[c+i];c+=i,l+=a}}_copy(t,i,e,s,h,a){if(!this._sprites||\"loaded\"!==this._sprites.loadStatus||e>=this._mosaicsData.length)return;const r=new Uint32Array(a?a.buffer:this._sprites.image.buffer),o=this._mosaicsData[e];o&&r||console.error(\"Source or target images are uninitialized!\");const _=2,g=a?i.width:this._sprites.width;n._copyBits(r,g,i.x,i.y,o,s[0],t.x+_,t.y+_,i.width,i.height,h),this._dirties[e]=!0}_allocateImage(t,s){t+=2,s+=2;const h=Math.max(t,s);if(this._maxItemSize&&this._maxItemSize<h){const i=new e(0,0,t,s);return this._mosaicsData.push(new Uint32Array(t*s)),this._dirties.push(!0),this._size.push([t,s]),this._textures.push(void 0),[i,this._mosaicsData.length-1,[t,s]]}let a=t%4?4-t%4:4,r=s%4?4-s%4:4;1===a&&(a=5),1===r&&(r=5);const o=this._binPack.allocate(t+a,s+r);return o.width<=0?(this._dirties[this._currentPage]||(this._mosaicsData[this._currentPage]=null),this._currentPage=this._mosaicsData.length,this._mosaicsData.push(new Uint32Array(this._pageWidth*this._pageHeight)),this._dirties.push(!0),this._size.push([this._pageWidth,this._pageHeight]),this._textures.push(void 0),this._binPack=new i(this._pageWidth-4,this._pageHeight-4),this._allocateImage(t,s)):[o,this._currentPage,[this._pageWidth,this._pageHeight]]}_rasterizeDash(i){const e=/\\[(.*?)\\]/,s=i.match(e);if(!s)return null;const h=s[1].split(\",\").map(Number),a=i.slice(i.lastIndexOf(\"-\")+1),[r,o,n]=t(h,a);return[{x:0,y:0,width:o,height:n,sdf:!0,pixelRatio:1},new Uint8Array(r.buffer)]}}export{n as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{throwIfNotAbortError as e,isAbortError as t,eachAlways as s}from\"../../../../core/promiseUtils.js\";import{addQueryParameters as r}from\"../../../../core/urlUtils.js\";import{open as i}from\"../../../../core/workers/workers.js\";import o from\"./GlyphMosaic.js\";import a from\"./GlyphSource.js\";import l from\"./SpriteMosaic.js\";import c from\"../../tiling/TileKey.js\";class n{constructor(e,t,s){this._layer=e,this._styleRepository=t,this.devicePixelRatio=s,this._spriteMosaic=null,this._glyphMosaic=null,this._connection=null}destroy(){this._connection?.close(),this._connection=null,this._styleRepository=null,this._layer=null,this._spriteMosaic=null,this._glyphMosaic=null}get spriteMosaic(){return this._spriteSourcePromise.then((()=>this._spriteMosaic))}get glyphMosaic(){return this._glyphMosaic}async start(t){this._spriteSourcePromise=this._layer.loadSpriteSource(this.devicePixelRatio,t),this._spriteSourcePromise.then((e=>{this._spriteMosaic=new l(1024,1024,250),this._spriteMosaic.setSpriteSource(e)}));const s=this._layer.currentStyleInfo.glyphsUrl,c=new a(s?r(s,{...this._layer.customParameters,token:this._layer.apiKey}):null);this._glyphMosaic=new o(1024,1024,c),this._broadcastPromise=i(\"WorkerTileHandler\",{client:this,schedule:t.schedule,signal:t.signal}).then((s=>{if(this._connection=s,this._layer&&!this._connection.closed){const r=s.broadcast(\"setStyle\",this._layer.currentStyleInfo.style,t);Promise.all(r).catch((t=>e(t)))}}))}async updateStyle(e){return await this._broadcastPromise,this._broadcastPromise=Promise.all(this._connection.broadcast(\"updateStyle\",e)),this._broadcastPromise}setSpriteSource(e){const t=new l(1024,1024,250);return t.setSpriteSource(e),this._spriteMosaic=t,this._spriteSourcePromise=Promise.resolve(e),t}async setStyle(e,t){await this._broadcastPromise,this._styleRepository=e,this._spriteSourcePromise=this._layer.loadSpriteSource(this.devicePixelRatio,null),this._spriteSourcePromise.then((e=>{this._spriteMosaic=new l(1024,1024,250),this._spriteMosaic.setSpriteSource(e)}));const s=new a(this._layer.currentStyleInfo.glyphsUrl?r(this._layer.currentStyleInfo.glyphsUrl,{...this._layer.customParameters,token:this._layer.apiKey}):null);return this._glyphMosaic=new o(1024,1024,s),this._broadcastPromise=Promise.all(this._connection.broadcast(\"setStyle\",t)),this._broadcastPromise}fetchTileData(e,t){return this._getRefKeys(e,t).then((e=>{const s=this._layer.sourceNameToSource,r=[];for(const t in s)r.push(t);return this._getSourcesData(r,e,t)}))}parseTileData(e,t){const s=e&&e.data;if(!s)return Promise.resolve(null);const{sourceName2DataAndRefKey:r,transferList:i}=s;return 0===Object.keys(r).length?Promise.resolve(null):this._broadcastPromise.then((()=>this._connection.invoke(\"createTileAndParse\",{key:e.key.id,sourceName2DataAndRefKey:r,styleLayerUIDs:e.styleLayerUIDs},{...t,transferList:i})))}async getSprites(e){return await this._spriteSourcePromise,this._spriteMosaic.getSpriteItems(e)}getGlyphs(e){return this._glyphMosaic.getGlyphItems(e.font,e.codePoints)}async _getTilePayload(e,s,r){const i=c.pool.acquire(e.id),o=this._layer.sourceNameToSource[s],{level:a,row:l,col:n}=i;c.pool.release(i);try{return{protobuff:await o.requestTile(a,l,n,r),sourceName:s}}catch(h){if(t(h))throw h;return{protobuff:null,sourceName:s}}}_getRefKeys(e,t){const r=this._layer.sourceNameToSource,i=new Array;for(const s in r){const o=r[s].getRefKey(e,t);i.push(o)}return s(i)}_getSourcesData(e,t,r){const i=[];for(let s=0;s<t.length;s++)if(null==t[s].value||null==e[s])i.push(null);else{const o=this._getTilePayload(t[s].value,e[s],r);i.push(o)}return s(i).then((e=>{const s={},r=[];for(let i=0;i<e.length;i++){const o=e[i].value;if(o&&(o.protobuff&&o.protobuff.byteLength>0)){const e=t[i].value.id;s[o.sourceName]={refKey:e,protobuff:o.protobuff},r.push(o.protobuff)}}return{sourceName2DataAndRefKey:s,transferList:r}}))}}export{n as TileHandler};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../core/LRUCache.js\";import{isNone as i,isSome as t}from\"../../../../core/maybe.js\";import s from\"../../tiling/TileCoverage.js\";import l from\"../../tiling/TileKey.js\";const r=512,o=1e-6,n=(e,i)=>e+1/(1<<2*i);class a{constructor(i,t){this._tiles=new Map,this._tileCache=new e(40,(e=>e.dispose())),this._viewSize=[0,0],this._visibleTiles=new Map,this.acquireTile=i.acquireTile,this.releaseTile=i.releaseTile,this.tileInfoView=i.tileInfoView,this._container=t}destroy(){for(const[e,i]of this._tiles)i.dispose();this._tiles=null,this._tileCache.clear(),this._tileCache=null}update(e){this._updateCacheSize(e);const i=this.tileInfoView,t=i.getTileCoverage(e.state,0,\"smallest\"),{spans:r,lodInfo:o}=t,{level:n}=o,a=this._tiles,c=new Set,h=new Set;for(const{row:s,colFrom:_,colTo:f}of r)for(let e=_;e<=f;e++){const i=l.getId(n,s,o.normalizeCol(e),o.getWorldForColumn(e)),t=this._getOrAcquireTile(i);c.add(i),t.processed()?this._addToContainer(t):h.add(new l(i))}for(const[s,l]of a)l.isCoverage=c.has(s);for(const s of h)this._findPlaceholdersForMissingTiles(s,c);let d=!1;for(const[s,l]of a)l.neededForCoverage=c.has(s),l.neededForCoverage||l.isHoldingForFade&&i.intersects(t,l.key)&&c.add(s),l.isFading&&(d=!0);for(const[s,l]of this._tiles)c.has(s)||this._releaseTile(s);return s.pool.release(t),!d}clear(){this._tiles.clear(),this._tileCache.clear(),this._visibleTiles.clear()}clearCache(){this._tileCache.clear()}_findPlaceholdersForMissingTiles(e,i){const t=[];for(const[l,r]of this._tiles)this._addPlaceholderChild(t,r,e,i);const s=t.reduce(n,0);Math.abs(1-s)<o||this._addPlaceholderParent(e.id,i)}_addPlaceholderChild(e,i,t,s){i.key.level<=t.level||!i.hasData()||h(t,i.key)&&(this._addToContainer(i),s.add(i.id),e.push(i.key.level-t.level))}_addPlaceholderParent(e,i){const t=this._tiles;let s=e;for(;;){if(s=c(s),!s||i.has(s))return;const e=t.get(s);if(e&&e.hasData())return this._addToContainer(e),void i.add(e.id)}}_getOrAcquireTile(e){let i=this._tiles.get(e);return i||(i=this._tileCache.pop(e),i||(i=this.acquireTile(new l(e))),this._tiles.set(e,i),i)}_releaseTile(e){const i=this._tiles.get(e);this.releaseTile(i),this._removeFromContainer(i),this._tiles.delete(e),i.hasData()?this._tileCache.put(e,i,1):i.dispose()}_addToContainer(e){let s;const l=[],r=this._container;if(r.contains(e))return;const o=this._visibleTiles;for(const[t,n]of o)this._canConnectDirectly(e,n)&&l.push(n),i(s)&&this._canConnectDirectly(n,e)&&(s=n);if(t(s)){for(const i of l)s.childrenTiles.delete(i),e.childrenTiles.add(i),i.parentTile=e;s.childrenTiles.add(e),e.parentTile=s}else for(const i of l)e.childrenTiles.add(i),i.parentTile=e;o.set(e.id,e),r.addChild(e)}_removeFromContainer(e){if(this._visibleTiles.delete(e.id),this._container.removeChild(e),t(e.parentTile)){e.parentTile.childrenTiles.delete(e);for(const i of e.childrenTiles)t(e.parentTile)&&e.parentTile.childrenTiles.add(i)}for(const i of e.childrenTiles)i.parentTile=e.parentTile;e.parentTile=null,e.childrenTiles.clear()}_canConnectDirectly(e,i){const t=e.key;let{level:s,row:l,col:r,world:o}=i.key;const n=this._visibleTiles;for(;s>0;){if(s--,l>>=1,r>>=1,t.level===s&&t.row===l&&t.col===r&&t.world===o)return!0;if(n.has(`${s}/${l}/${r}/${o}`))return!1}return!1}_updateCacheSize(e){const i=e.state.size;if(i[0]===this._viewSize[0]&&i[1]===this._viewSize[1])return;const t=Math.ceil(i[0]/r)+1,s=Math.ceil(i[1]/r)+1;this._viewSize[0]=i[0],this._viewSize[1]=i[1],this._tileCache.maxSize=5*t*s}}function c(e){const[i,t,s,l]=e.split(\"/\"),r=parseInt(i,10);return 0===r?null:`${r-1}/${parseInt(t,10)>>1}/${parseInt(s,10)>>1}/${parseInt(l,10)}`}function h(e,i){const t=i.level-e.level;return e.row===i.row>>t&&e.col===i.col>>t&&e.world===i.world}export{a as TileManager};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(t){this.xTile=0,this.yTile=0,this.hash=0,this.priority=1,this.colliders=[],this.textVertexRanges=[],this.iconVertexRanges=[],this.tile=t}}class s{constructor(){this.tileSymbols=[],this.parts=[{startTime:0,startOpacity:0,targetOpacity:0,show:!1},{startTime:0,startOpacity:0,targetOpacity:0,show:!1}],this.show=!1}}export{t as VTLSymbol,s as VTLUniqueSymbol};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{BucketType as t}from\"../enums.js\";import{VTLSymbol as e}from\"./core.js\";function s(t,e,s,o,l,i){const r=s-l;if(r>=0)return(e>>r)+(o-(i<<r))*(t>>r);const n=-r;return e-(i-(o<<n))*(t>>n)<<n}class o{constructor(t,e,s){this._rows=Math.ceil(e/s),this._columns=Math.ceil(t/s),this._cellSize=s,this.cells=new Array(this._rows);for(let o=0;o<this._rows;o++){this.cells[o]=new Array(this._columns);for(let t=0;t<this._columns;t++)this.cells[o][t]=[]}}getCell(t,e){const s=Math.min(Math.max(Math.floor(e/this._cellSize),0),this._rows-1),o=Math.min(Math.max(Math.floor(t/this._cellSize),0),this._columns-1);return this.cells[s]&&this.cells[s][o]||null}getCellSpan(t,e,s,o){return[Math.min(Math.max(Math.floor(t/this._cellSize),0),this.columns-1),Math.min(Math.max(Math.floor(e/this._cellSize),0),this.rows-1),Math.min(Math.max(Math.floor(s/this._cellSize),0),this.columns-1),Math.min(Math.max(Math.floor(o/this._cellSize),0),this.rows-1)]}get cellSize(){return this._cellSize}get columns(){return this._columns}get rows(){return this._rows}}function l(t,s,o,l,i,r){const n=s[l++];for(let a=0;a<n;a++){const n=new e(r);n.xTile=s[l++],n.yTile=s[l++],n.hash=s[l++],n.priority=s[l++];const a=s[l++];for(let t=0;t<a;t++){const t=s[l++],e=s[l++],i=s[l++],r=s[l++],a=!!s[l++],c=s[l++],h=o[l++],f=o[l++],u=s[l++],m=s[l++];n.colliders.push({xTile:t,yTile:e,dxPixels:i,dyPixels:r,hard:a,partIndex:c,width:u,height:m,minLod:h,maxLod:f})}const c=t[l++];for(let e=0;e<c;e++)n.textVertexRanges.push([t[l++],t[l++]]);const h=t[l++];for(let e=0;e<h;e++)n.iconVertexRanges.push([t[l++],t[l++]]);i.push(n)}return l}function i(t,e,s){for(const[o,l]of t.symbols)r(t,e,s,l,o)}function r(e,s,o,l,i){const r=e.layerData.get(i);if(r.type===t.SYMBOL){for(const t of l){const s=t.unique;let l;if(t.selectedForRendering){const t=s.parts[0],i=t.startOpacity,r=t.targetOpacity;e.allSymbolsFadingOut=e.allSymbolsFadingOut&&0===r;const n=o?Math.floor(127*i)|r<<7:r?255:0;l=n<<24|n<<16|n<<8|n}else l=0;for(const[e,o]of t.iconVertexRanges)for(let t=e;t<e+o;t+=4)r.iconOpacity[t/4]=l;if(t.selectedForRendering){const t=s.parts[1],i=t.startOpacity,r=t.targetOpacity;e.allSymbolsFadingOut=e.allSymbolsFadingOut&&0===r;const n=o?Math.floor(127*i)|r<<7:r?255:0;l=n<<24|n<<16|n<<8|n}else l=0;for(const[e,o]of t.textVertexRanges)for(let t=e;t<e+o;t+=4)r.textOpacity[t/4]=l}r.lastOpacityUpdate=s,r.opacityChanged=!0}}export{o as GridIndex,l as deserializeSymbols,s as tileCoordChange,i as writeOpacityToBuffers};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as e,isSome as t,unwrap as r}from\"../../../../core/maybe.js\";import{BucketType as i}from\"./enums.js\";import{deserializeSymbols as s}from\"./decluttering/util.js\";import{BufferObject as n}from\"../../../webgl/BufferObject.js\";import{Usage as f}from\"../../../webgl/enums.js\";import{VertexArrayObject as a}from\"../../../webgl/VertexArrayObject.js\";class o{constructor(e,t){this.layerUIDs=[],this.isDestroyed=!1,this._data=e,this.memoryUsed=e.byteLength;let r=1;const i=new Uint32Array(e);this.layerUIDs=[];const s=i[r++];for(let n=0;n<s;n++)this.layerUIDs[n]=i[r++];this.bufferDataOffset=r,t&&(this.layer=t.getStyleLayerByUID(this.layerUIDs[0]))}get isPreparedForRendering(){return e(this._data)}get offset(){return this.bufferDataOffset}destroy(){this.isDestroyed||(this.doDestroy(),this.isDestroyed=!0)}prepareForRendering(t){e(this._data)||(this.doPrepareForRendering(t,this._data,this.bufferDataOffset),this._data=null)}}class l extends o{constructor(e,t){super(e,t),this.type=i.LINE,this.lineIndexStart=0,this.lineIndexCount=0;const r=new Uint32Array(e);let s=this.bufferDataOffset;this.lineIndexStart=r[s++],this.lineIndexCount=r[s++];const n=r[s++];if(n>0){const e=new Map;for(let t=0;t<n;t++){const t=r[s++],i=r[s++],n=r[s++];e.set(t,[i,n])}this.patternMap=e}this.bufferDataOffset=s}hasData(){return this.lineIndexCount>0}triangleCount(){return this.lineIndexCount/3}doDestroy(){t(this.lineVertexArrayObject)&&this.lineVertexArrayObject.dispose(),t(this.lineVertexBuffer)&&this.lineVertexBuffer.dispose(),t(this.lineIndexBuffer)&&this.lineIndexBuffer.dispose(),this.lineVertexArrayObject=null,this.lineVertexBuffer=null,this.lineIndexBuffer=null,this.memoryUsed=0}doPrepareForRendering(e,t,r){const i=new Uint32Array(t),s=new Int32Array(i.buffer),o=i[r++];this.lineVertexBuffer=n.createVertex(e,f.STATIC_DRAW,new Int32Array(s.buffer,4*r,o)),r+=o;const l=i[r++];this.lineIndexBuffer=n.createIndex(e,f.STATIC_DRAW,new Uint32Array(i.buffer,4*r,l)),r+=l;const u=this.layer.lineMaterial;this.lineVertexArrayObject=new a(e,u.getAttributeLocations(),u.getLayoutInfo(),{geometry:this.lineVertexBuffer},this.lineIndexBuffer)}}class u extends o{constructor(e,t){super(e,t),this.type=i.FILL,this.fillIndexStart=0,this.fillIndexCount=0,this.outlineIndexStart=0,this.outlineIndexCount=0;const r=new Uint32Array(e);let s=this.bufferDataOffset;this.fillIndexStart=r[s++],this.fillIndexCount=r[s++],this.outlineIndexStart=r[s++],this.outlineIndexCount=r[s++];const n=r[s++];if(n>0){const e=new Map;for(let t=0;t<n;t++){const t=r[s++],i=r[s++],n=r[s++];e.set(t,[i,n])}this.patternMap=e}this.bufferDataOffset=s}hasData(){return this.fillIndexCount>0||this.outlineIndexCount>0}triangleCount(){return(this.fillIndexCount+this.outlineIndexCount)/3}doDestroy(){t(this.fillVertexArrayObject)&&this.fillVertexArrayObject.dispose(),t(this.fillVertexBuffer)&&this.fillVertexBuffer.dispose(),t(this.fillIndexBuffer)&&this.fillIndexBuffer.dispose(),this.fillVertexArrayObject=null,this.fillVertexBuffer=null,this.fillIndexBuffer=null,t(this.outlineVertexArrayObject)&&this.outlineVertexArrayObject.dispose(),t(this.outlineVertexBuffer)&&this.outlineVertexBuffer.dispose(),t(this.outlineIndexBuffer)&&this.outlineIndexBuffer.dispose(),this.outlineVertexArrayObject=null,this.outlineVertexBuffer=null,this.outlineIndexBuffer=null,this.memoryUsed=0}doPrepareForRendering(e,t,r){const i=new Uint32Array(t),s=new Int32Array(i.buffer),o=i[r++];this.fillVertexBuffer=n.createVertex(e,f.STATIC_DRAW,new Int32Array(s.buffer,4*r,o)),r+=o;const l=i[r++];this.fillIndexBuffer=n.createIndex(e,f.STATIC_DRAW,new Uint32Array(i.buffer,4*r,l)),r+=l;const u=i[r++];this.outlineVertexBuffer=n.createVertex(e,f.STATIC_DRAW,new Int32Array(s.buffer,4*r,u)),r+=u;const h=i[r++];this.outlineIndexBuffer=n.createIndex(e,f.STATIC_DRAW,new Uint32Array(i.buffer,4*r,h)),r+=h;const c=this.layer,x=c.fillMaterial,y=c.outlineMaterial;this.fillVertexArrayObject=new a(e,x.getAttributeLocations(),x.getLayoutInfo(),{geometry:this.fillVertexBuffer},this.fillIndexBuffer),this.outlineVertexArrayObject=new a(e,y.getAttributeLocations(),y.getLayoutInfo(),{geometry:this.outlineVertexBuffer},this.outlineIndexBuffer)}}class h extends o{constructor(e,t,r){super(e,t),this.type=i.SYMBOL,this.iconPerPageElementsMap=new Map,this.glyphPerPageElementsMap=new Map,this.symbolInstances=[],this.isIconSDF=!1,this.opacityChanged=!1,this.lastOpacityUpdate=0,this.symbols=[];const n=new Uint32Array(e),f=new Int32Array(e),a=new Float32Array(e);let o=this.bufferDataOffset;this.isIconSDF=!!n[o++];const l=n[o++];for(let i=0;i<l;i++){const e=n[o++],t=n[o++],r=n[o++];this.iconPerPageElementsMap.set(e,[t,r])}const u=n[o++];for(let i=0;i<u;i++){const e=n[o++],t=n[o++],r=n[o++];this.glyphPerPageElementsMap.set(e,[t,r])}const h=n[o++],c=n[o++];this.iconOpacity=new Int32Array(h),this.textOpacity=new Int32Array(c),o=s(n,f,a,o,this.symbols,r),this.bufferDataOffset=o}hasData(){return this.iconPerPageElementsMap.size>0||this.glyphPerPageElementsMap.size>0}triangleCount(){let e=0;for(const[t,r]of this.iconPerPageElementsMap)e+=r[1];for(const[t,r]of this.glyphPerPageElementsMap)e+=r[1];return e/3}doDestroy(){t(this.iconVertexArrayObject)&&this.iconVertexArrayObject.dispose(),t(this.iconVertexBuffer)&&this.iconVertexBuffer.dispose(),t(this.iconOpacityBuffer)&&this.iconOpacityBuffer.dispose(),t(this.iconIndexBuffer)&&this.iconIndexBuffer.dispose(),this.iconVertexArrayObject=null,this.iconVertexBuffer=null,this.iconOpacityBuffer=null,this.iconIndexBuffer=null,t(this.textVertexArrayObject)&&this.textVertexArrayObject.dispose(),t(this.textVertexBuffer)&&this.textVertexBuffer.dispose(),t(this.textOpacityBuffer)&&this.textOpacityBuffer.dispose(),t(this.textIndexBuffer)&&this.textIndexBuffer.dispose(),this.textVertexArrayObject=null,this.textVertexBuffer=null,this.textOpacityBuffer=null,this.textIndexBuffer=null,this.memoryUsed=0}updateOpacityInfo(){if(!this.opacityChanged)return;this.opacityChanged=!1;const e=r(this.iconOpacity),t=r(this.iconOpacityBuffer);e.length>0&&e.byteLength===t.size&&t.setSubData(e,0,0,e.length);const i=r(this.textOpacity),s=r(this.textOpacityBuffer);i.length>0&&i.byteLength===s.size&&s.setSubData(i,0,0,i.length)}doPrepareForRendering(e,t,i){const s=new Uint32Array(t),o=new Int32Array(s.buffer),l=s[i++];this.iconVertexBuffer=n.createVertex(e,f.STATIC_DRAW,new Int32Array(o.buffer,4*i,l)),i+=l;const u=s[i++];this.iconIndexBuffer=n.createIndex(e,f.STATIC_DRAW,new Uint32Array(s.buffer,4*i,u)),i+=u;const h=s[i++];this.textVertexBuffer=n.createVertex(e,f.STATIC_DRAW,new Int32Array(o.buffer,4*i,h)),i+=h;const c=s[i++];this.textIndexBuffer=n.createIndex(e,f.STATIC_DRAW,new Uint32Array(s.buffer,4*i,c)),i+=c,this.iconOpacityBuffer=n.createVertex(e,f.STATIC_DRAW,r(this.iconOpacity).buffer),this.textOpacityBuffer=n.createVertex(e,f.STATIC_DRAW,r(this.textOpacity).buffer);const x=this.layer,y=x.iconMaterial,d=x.textMaterial;this.iconVertexArrayObject=new a(e,y.getAttributeLocations(),y.getLayoutInfo(),{geometry:this.iconVertexBuffer,opacity:this.iconOpacityBuffer},this.iconIndexBuffer),this.textVertexArrayObject=new a(e,d.getAttributeLocations(),d.getLayoutInfo(),{geometry:this.textVertexBuffer,opacity:this.textOpacityBuffer},this.textIndexBuffer)}}class c extends o{constructor(e,t){super(e,t),this.type=i.CIRCLE,this.circleIndexStart=0,this.circleIndexCount=0;const r=new Uint32Array(e);let s=this.bufferDataOffset;this.circleIndexStart=r[s++],this.circleIndexCount=r[s++],this.bufferDataOffset=s}hasData(){return this.circleIndexCount>0}triangleCount(){return this.circleIndexCount/3}doDestroy(){t(this.circleVertexArrayObject)&&this.circleVertexArrayObject.dispose(),t(this.circleVertexBuffer)&&this.circleVertexBuffer.dispose(),t(this.circleIndexBuffer)&&this.circleIndexBuffer.dispose(),this.circleVertexArrayObject=null,this.circleVertexBuffer=null,this.circleIndexBuffer=null,this.memoryUsed=0}doPrepareForRendering(e,t,r){const i=new Uint32Array(t),s=new Int32Array(i.buffer),o=i[r++];this.circleVertexBuffer=n.createVertex(e,f.STATIC_DRAW,new Int32Array(s.buffer,4*r,o)),r+=o;const l=i[r++];this.circleIndexBuffer=n.createIndex(e,f.STATIC_DRAW,new Uint32Array(i.buffer,4*r,l)),r+=l;const u=this.layer.circleMaterial;this.circleVertexArrayObject=new a(e,u.getAttributeLocations(),u.getLayoutInfo(),{geometry:this.circleVertexBuffer},this.circleIndexBuffer)}}export{c as CircleRenderBucket,u as FillRenderBucket,l as LineRenderBucket,o as RenderBucketBase,h as SymbolRenderBucket};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../../core/maybe.js\";import{g as t,h as s,r as a,d as r}from\"../../../../chunks/mat3.js\";import{c as i}from\"../../../../chunks/mat3f32.js\";import{BucketType as o}from\"./enums.js\";import{CircleRenderBucket as h,SymbolRenderBucket as n,LineRenderBucket as l,FillRenderBucket as y}from\"./RenderBucket.js\";import{FADE_DURATION as c}from\"./decluttering/config.js\";import{TiledDisplayObject as d}from\"../webgl/TiledDisplayObject.js\";class m extends d{constructor(e,t,s,a,r,i,o,h=null){super(e,t,s,a,r,i,4096,4096),this._memCache=h,this.type=\"vector-tile\",this._referenced=0,this._hasSymbolBuckets=!1,this._memoryUsedByLayerData=0,this.layerData=new Map,this.layerCount=0,this.status=\"loading\",this.allSymbolsFadingOut=!1,this.lastOpacityUpdate=0,this.symbols=new Map,this.isCoverage=!1,this.neededForCoverage=!1,this.decluttered=!1,this.invalidating=!1,this.parentTile=null,this.childrenTiles=new Set,this._processed=!1,this._referenced=1,this.styleRepository=o,this.id=e.id}get hasSymbolBuckets(){return this._hasSymbolBuckets}get isFading(){return this._hasSymbolBuckets&&performance.now()-this.lastOpacityUpdate<c}get isHoldingForFade(){return this._hasSymbolBuckets&&(!this.allSymbolsFadingOut||performance.now()-this.lastOpacityUpdate<c)}get wasRequested(){return\"errored\"===this.status||\"loaded\"===this.status||\"reloading\"===this.status}setData(e){this.changeDataImpl(e),this.requestRender(),this.ready(),this.invalidating=!1,this._processed=!0}deleteLayerData(t){let s=!1;for(const e of t)if(this.layerData.has(e)){const t=this.layerData.get(e);this._memoryUsedByLayerData-=t.memoryUsed,t.type===o.SYMBOL&&this.symbols.has(e)&&(this.symbols.delete(e),s=!0),t.destroy(),this.layerData.delete(e),this.layerCount--}e(this._memCache)&&this._memCache.updateSize(this.key.id,this,this._memoryUsedByLayerData),s&&this.emit(\"symbols-changed\"),this.requestRender()}processed(){return this._processed}hasData(){return this.layerCount>0}dispose(){\"unloaded\"!==this.status&&(u.delete(this),m._destroyRenderBuckets(this.layerData),this.layerData=null,this.layerCount=0,this._memoryUsedByLayerData=0,this.destroy(),this.status=\"unloaded\")}release(){return 0==--this._referenced&&(this.dispose(),this.stage=null,!0)}retain(){++this._referenced}get referenced(){return this._referenced}get memoryUsage(){return(this._memoryUsedByLayerData+256)/(this._referenced||1)}changeDataImpl(t){let s=!1;if(t){const{bucketsWithData:a,emptyBuckets:r}=t,i=this._createRenderBuckets(a);if(r&&r.byteLength>0){const e=new Uint32Array(r);for(const t of e)this._deleteLayerData(t)}for(const[e,t]of i)this._deleteLayerData(e),t.type===o.SYMBOL&&(this.symbols.set(e,t.symbols),s=!0),this._memoryUsedByLayerData+=t.memoryUsed,this.layerData.set(e,t),this.layerCount++;e(this._memCache)&&this._memCache.updateSize(this.key.id,this,this._memoryUsedByLayerData)}this._hasSymbolBuckets=!1;for(const[e,a]of this.layerData)a.type===o.SYMBOL&&(this._hasSymbolBuckets=!0);s&&this.emit(\"symbols-changed\")}attachWithContext(e){this.stage={context:e,trashDisplayObject(e){e.processDetach()},untrashDisplayObject:()=>!1}}setTransform(e){super.setTransform(e);const i=this.resolution/(e.resolution*e.pixelRatio),o=this.width/this.rangeX*i,h=this.height/this.rangeY*i,n=[0,0];e.toScreen(n,[this.x,this.y]);const l=this.transforms.tileUnitsToPixels;t(l),s(l,l,n),a(l,l,Math.PI*e.rotation/180),r(l,l,[o,h,1])}_createTransforms(){return{dvs:i(),tileMat3:i(),tileUnitsToPixels:i()}}static _destroyRenderBuckets(e){if(!e)return;const t=new Set;e.forEach((e=>{t.has(e)||(e.destroy(),t.add(e))})),e.clear()}_createRenderBuckets(e){const t=new Map,s=new Map;for(const a of e){const e=this._deserializeBucket(a,s);for(const s of e.layerUIDs)t.set(s,e)}return t}_deserializeBucket(e,t){let s=t.get(e);if(s)return s;switch(new Uint32Array(e)[0]){case o.FILL:s=new y(e,this.styleRepository);break;case o.LINE:s=new l(e,this.styleRepository);break;case o.SYMBOL:s=new n(e,this.styleRepository,this);break;case o.CIRCLE:s=new h(e,this.styleRepository)}return t.set(e,s),s}_deleteLayerData(e){if(!this.layerData.has(e))return;const t=this.layerData.get(e);this._memoryUsedByLayerData-=t.memoryUsed,t.destroy(),this.layerData.delete(e),this.layerCount--}}const u=new Map;function p(){u.forEach(((e,t)=>{console.log(`\\n${t.key}:`),e[0].forEach((e=>console.log(e))),console.log(\"========\"),e[1].forEach((e=>console.log(e)))}))}export{m as VectorTile,p as printAllocations};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{a as e}from\"../../../../../chunks/mat3f32.js\";import{COLLISION_GRID_CELL_SIZE as t}from\"./config.js\";import{GridIndex as n}from\"./util.js\";import{SymbolPlacement as o,RotationAlignment as r,TranslateAnchor as s}from\"../style/StyleDefinition.js\";function i(e,t,n,o,i,l){const{iconRotationAlignment:a,textRotationAlignment:c,iconTranslate:h,iconTranslateAnchor:u,textTranslate:d,textTranslateAnchor:y}=o;let x=0;for(const g of e.colliders){const[e,o]=0===g.partIndex?h:d,m=0===g.partIndex?u:y,f=g.minLod<=l&&l<=g.maxLod;x+=f?0:1,g.enabled=f,g.xScreen=g.xTile*i[0]+g.yTile*i[3]+i[6],g.yScreen=g.xTile*i[1]+g.yTile*i[4]+i[7],m===s.MAP?(g.xScreen+=n*e-t*o,g.yScreen+=t*e+n*o):(g.xScreen+=e,g.yScreen+=o),r.VIEWPORT===(0===g.partIndex?a:c)?(g.dxScreen=g.dxPixels,g.dyScreen=g.dyPixels):(g.dxScreen=n*(g.dxPixels+g.width/2)-t*(g.dyPixels+g.height/2)-g.width/2,g.dyScreen=t*(g.dxPixels+g.width/2)+n*(g.dyPixels+g.height/2)-g.height/2)}e.colliders.length>0&&x===e.colliders.length&&(e.unique.show=!1)}class l{constructor(o,r,s,i,l,a){this._symbols=o,this._styleRepository=i,this._zoom=l,this._currentLayerCursor=0,this._currentSymbolCursor=0,this._styleProps=new Map,this._allNeededMatrices=new Map,this._gridIndex=new n(r,s,t),this._si=Math.sin(Math.PI*a/180),this._co=Math.cos(Math.PI*a/180);for(const t of o)for(const n of t.symbols)this._allNeededMatrices.has(n.tile)||this._allNeededMatrices.set(n.tile,e(n.tile.transforms.tileUnitsToPixels))}work(e){const t=this._gridIndex;function n(e){const n=e.xScreen+e.dxScreen,o=e.yScreen+e.dyScreen,r=n+e.width,s=o+e.height,[i,l,a,c]=t.getCellSpan(n,o,r,s);for(let h=l;h<=c;h++)for(let e=i;e<=a;e++){const i=t.cells[h][e];for(const e of i){const t=e.xScreen+e.dxScreen,i=e.yScreen+e.dyScreen,l=t+e.width,a=i+e.height;if(!(r<t||n>l||s<i||o>a))return!0}}return!1}const o=performance.now();for(;this._currentLayerCursor<this._symbols.length;this._currentLayerCursor++,this._currentSymbolCursor=0){const t=this._symbols[this._currentLayerCursor],r=this._getProperties(t.styleLayerUID);for(;this._currentSymbolCursor<t.symbols.length;this._currentSymbolCursor++){if(this._currentSymbolCursor%100==99&&performance.now()-o>e)return!1;const s=t.symbols[this._currentSymbolCursor];if(!s.unique.show)continue;i(s,this._si,this._co,r,this._allNeededMatrices.get(s.tile),this._zoom);const l=s.unique;if(!l.show)continue;const{iconAllowOverlap:a,iconIgnorePlacement:c,textAllowOverlap:h,textIgnorePlacement:u}=r;for(const e of s.colliders){if(!e.enabled)continue;const t=l.parts[e.partIndex];if(!t.show)continue;!(e.partIndex?h:a)&&n(e)&&(e.hard?l.show=!1:t.show=!1)}if(l.show)for(const e of s.colliders){if(!e.enabled)continue;if(e.partIndex?u:c)continue;if(!l.parts[e.partIndex].show)continue;const t=e.xScreen+e.dxScreen,n=e.yScreen+e.dyScreen,o=t+e.width,r=n+e.height,[s,i,a,h]=this._gridIndex.getCellSpan(t,n,o,r);for(let l=i;l<=h;l++)for(let t=s;t<=a;t++){this._gridIndex.cells[l][t].push(e)}}}}return!0}_getProperties(e){const t=this._styleProps.get(e);if(t)return t;const n=this._zoom,s=this._styleRepository.getStyleLayerByUID(e),i=s.getLayoutValue(\"symbol-placement\",n)!==o.POINT;let l=s.getLayoutValue(\"icon-rotation-alignment\",n);l===r.AUTO&&(l=i?r.MAP:r.VIEWPORT);let a=s.getLayoutValue(\"text-rotation-alignment\",n);a===r.AUTO&&(a=i?r.MAP:r.VIEWPORT);const c=s.getPaintValue(\"icon-translate\",n),h=s.getPaintValue(\"icon-translate-anchor\",n),u=s.getPaintValue(\"text-translate\",n),d=s.getPaintValue(\"text-translate-anchor\",n),y={iconAllowOverlap:s.getLayoutValue(\"icon-allow-overlap\",n),iconIgnorePlacement:s.getLayoutValue(\"icon-ignore-placement\",n),textAllowOverlap:s.getLayoutValue(\"text-allow-overlap\",n),textIgnorePlacement:s.getLayoutValue(\"text-ignore-placement\",n),iconRotationAlignment:l,textRotationAlignment:a,iconTranslateAnchor:h,iconTranslate:c,textTranslateAnchor:d,textTranslate:u};return this._styleProps.set(e,y),y}}export{l as CollisionJob};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as o}from\"../../../../../core/maybe.js\";import{FADE_DURATION as e}from\"./config.js\";function t(o,e){if(o.priority-e.priority)return o.priority-e.priority;const t=o.tile.key,i=e.tile.key;return t.world-i.world?t.world-i.world:t.level-i.level?t.level-i.level:t.row-i.row?t.row-i.row:t.col-i.col?t.col-i.col:o.xTile-e.xTile?o.xTile-e.xTile:o.yTile-e.yTile}class i{get running(){return this._running}constructor(o,e,t,i,s,r){this._visibleTiles=o,this._symbolRepository=e,this._createCollisionJob=t,this._assignTileSymbolsOpacity=i,this._symbolLayerSorter=s,this._isLayerVisible=r,this._selectionJob=null,this._selectionJobCompleted=!1,this._collisionJob=null,this._collisionJobCompleted=!1,this._opacityJob=null,this._opacityJobCompleted=!1,this._running=!0}setScreenSize(o,e){this._screenWidth===o&&this._screenHeight===e||this.restart(),this._screenWidth=o,this._screenHeight=e}restart(){this._selectionJob=null,this._selectionJobCompleted=!1,this._collisionJob=null,this._collisionJobCompleted=!1,this._opacityJob=null,this._opacityJobCompleted=!1,this._running=!0}continue(o){if(this._selectionJob||(this._selectionJob=this._createSelectionJob()),!this._selectionJobCompleted){const e=performance.now();if(!this._selectionJob.work(o))return!1;if(this._selectionJobCompleted=!0,0===(o=Math.max(0,o-(performance.now()-e))))return!1}if(this._collisionJob||(this._collisionJob=this._createCollisionJob(this._selectionJob.sortedSymbols,this._screenWidth,this._screenHeight)),!this._collisionJobCompleted){const e=performance.now();if(!this._collisionJob.work(o))return!1;if(this._collisionJobCompleted=!0,0===(o=Math.max(0,o-(performance.now()-e))))return!1}if(this._opacityJob||(this._opacityJob=this._createOpacityJob()),!this._opacityJobCompleted){const e=performance.now();if(!this._opacityJob.work(o))return!1;if(this._opacityJobCompleted=!0,0===(o=Math.max(0,o-(performance.now()-e))))return!1}return this._running=!1,!0}_createSelectionJob(){const o=this._symbolRepository.uniqueSymbols;for(let t=0;t<o.length;t++){const e=o[t];for(let o=0;o<e.uniqueSymbols.length;o++){const t=e.uniqueSymbols[o];for(const o of t.tileSymbols)o.selectedForRendering=!1}}const e=[];let i=0,s=0;const r=this._isLayerVisible;function n(n){let l;const c=performance.now();for(;s<o.length;s++,i=0){const t=o[s],h=t.styleLayerUID;if(!r(h)){e[s]||(e[s]={styleLayerUID:h,symbols:[]});continue}e[s]=e[s]||{styleLayerUID:h,symbols:[]};const a=e[s];for(;i<t.uniqueSymbols.length;i++){if(l=t.uniqueSymbols[i],i%100==99&&performance.now()-c>n)return!1;let o=null,e=!1,s=!1;for(const t of l.tileSymbols)if(!s||!e){const i=t.tile;(!o||i.isCoverage||i.neededForCoverage&&!e)&&(o=t,(i.neededForCoverage||i.isCoverage)&&(s=!0),i.isCoverage&&(e=!0))}if(o.selectedForRendering=!0,s){a.symbols.push(o),l.show=!0;for(const o of l.parts)o.show=!0}else l.show=!1}}for(const o of e)o.symbols.sort(t);return!0}const l=this._symbolLayerSorter;return{work:n,get sortedSymbols(){return e.sort(l)}}}_createOpacityJob(){const e=this._assignTileSymbolsOpacity,t=this._visibleTiles;let i=0;function r(o,t){const i=o.symbols;for(const[e,r]of i)s(r,t);e(o,t);for(const e of o.childrenTiles)r(e,t)}return{work(e){const s=performance.now();for(;i<t.length;i++){if(performance.now()-s>e)return!1;const n=t[i];if(o(n.parentTile))continue;r(n,performance.now())}return!0}}}}function s(o,t){for(const i of o){const o=i.unique;for(const i of o.parts){const s=i.targetOpacity>.5?1:-1;i.startOpacity+=s*((t-i.startTime)/e),i.startOpacity=Math.min(Math.max(i.startOpacity,0),1),i.startTime=t,i.targetOpacity=o.show&&i.show?1:0}}}export{i as SymbolDeclutterer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as e,isSome as s}from\"../../../../../core/maybe.js\";import{GridIndex as o,tileCoordChange as t}from\"./util.js\";const l=32,i=8,n=64;class r{constructor(e,s,o){this.tileCoordRange=e,this._visibleTiles=s,this._createUnique=o,this._tiles=new Map,this._uniqueSymbolsReferences=new Map}get uniqueSymbols(){return e(this._uniqueSymbolLayerArray)&&(this._uniqueSymbolLayerArray=this._createUniqueSymbolLayerArray()),this._uniqueSymbolLayerArray}add(e,s){this._uniqueSymbolLayerArray=null;let t=this._tiles.get(e.id);t||(t={symbols:new Map},this._tiles.set(e.id,t));const r=new Map;if(s)for(const o of s)t.symbols.has(o)&&(r.set(o,t.symbols.get(o)),t.symbols.delete(o));else for(const[o,l]of e.layerData)t.symbols.has(o)&&(r.set(o,t.symbols.get(o)),t.symbols.delete(o));this._removeSymbols(r);const y=e.symbols,a=new Map;for(const[f,c]of y){let e=c.length;if(e>=l){let s=this.tileCoordRange;do{s/=2,e/=4}while(e>i&&s>n);const l=new o(this.tileCoordRange,this.tileCoordRange,s);a.set(f,{flat:c,index:l}),t.symbols.set(f,{flat:c,index:l});for(const e of c)l.getCell(e.xTile,e.yTile).push(e)}else a.set(f,{flat:c}),t.symbols.set(f,{flat:c})}this._addSymbols(e.key,y)}deleteStyleLayers(e){this._uniqueSymbolLayerArray=null;for(const[s,o]of this._tiles){const t=new Map;for(const s of e)o.symbols.has(s)&&(t.set(s,o.symbols.get(s)),o.symbols.delete(s));this._removeSymbols(t),0===o.symbols.size&&this._tiles.delete(s)}}removeTile(e){this._uniqueSymbolLayerArray=null;const s=this._tiles.get(e.id);if(!s)return;const o=new Map;for(const[t,l]of e.symbols)s.symbols.has(t)&&(o.set(t,s.symbols.get(t)),s.symbols.delete(t));this._removeSymbols(o),0===s.symbols.size&&this._tiles.delete(e.id)}_removeSymbols(e){for(const[s,{flat:o}]of e)for(const e of o){const o=e.unique,t=o.tileSymbols,l=t.length-1;for(let s=0;s<l;s++)if(t[s]===e){t[s]=t[l];break}if(t.length=l,0===l){const e=this._uniqueSymbolsReferences.get(s);e.delete(o),0===e.size&&this._uniqueSymbolsReferences.delete(s)}e.unique=null}}_addSymbols(s,o){if(0===o.size)return;const t=this._visibleTiles;for(const e of t)e.parentTile||e.key.world!==s.world||e.key.level===s.level&&!e.key.equals(s)||this._matchSymbols(e,s,o);for(const[l,i]of o)for(const s of i)if(e(s.unique)){const e=this._createUnique();s.unique=e,e.tileSymbols.push(s);let o=this._uniqueSymbolsReferences.get(l);o||(o=new Set,this._uniqueSymbolsReferences.set(l,o)),o.add(e)}}_matchSymbols(e,o,l){if(e.key.level>o.level){const s=e.key.level-o.level;if(e.key.row>>s!==o.row||e.key.col>>s!==o.col)return}if(o.level>e.key.level){const s=o.level-e.key.level;if(o.row>>s!==e.key.row||o.col>>s!==e.key.col)return}if(o.equals(e.key)){for(const s of e.childrenTiles)this._matchSymbols(s,o,l);return}const i=new Map;for(const[n,r]of l){const l=[];for(const s of r){const i=t(this.tileCoordRange,s.xTile,o.level,o.col,e.key.level,e.key.col),n=t(this.tileCoordRange,s.yTile,o.level,o.row,e.key.level,e.key.row);i>=0&&i<this.tileCoordRange&&n>=0&&n<this.tileCoordRange&&l.push({symbol:s,xTransformed:i,yTransformed:n})}const y=[],a=e.key.level<o.level?1:1<<e.key.level-o.level,f=this._tiles.get(e.id).symbols.get(n);if(f){const e=f.flat;for(const o of l){let t,l=!1;const i=o.xTransformed,n=o.yTransformed;t=s(f.index)?f.index.getCell(i,n):e;const r=o.symbol,c=r.hash;for(const e of t)if(c===e.hash&&Math.abs(i-e.xTile)<=a&&Math.abs(n-e.yTile)<=a){const s=e.unique;r.unique=s,s.tileSymbols.push(r),l=!0;break}l||y.push(r)}}y.length>0&&i.set(n,y)}for(const s of e.childrenTiles)this._matchSymbols(s,o,i)}_createUniqueSymbolLayerArray(){const e=this._uniqueSymbolsReferences,s=new Array(e.size);let o,t=0;for(const[l,i]of e){const e=new Array(i.size);o=0;for(const s of i)e[o++]=s;s[t]={styleLayerUID:l,uniqueSymbols:e},t++}return s}}export{r as SymbolRepository};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../../../../core/Evented.js\";import{isSome as e}from\"../../../../../core/maybe.js\";import{DECLUTTER_BUDGET as i,FADE_DURATION as s}from\"./config.js\";import{VTLUniqueSymbol as o}from\"./core.js\";import{CollisionJob as r}from\"./jobs.js\";import{SymbolDeclutterer as l}from\"./SymbolDeclutterer.js\";import{SymbolRepository as a}from\"./SymbolRepository.js\";import{writeOpacityToBuffers as h}from\"./util.js\";import{Visibility as c}from\"../style/StyleDefinition.js\";const n=.5,_=1e-6;class m extends t{constructor(t,e){super(),this.styleRepository=t,this._tileToHandle=new Map,this._viewState={scale:0,rotation:0,center:[0,0],size:[0,0]},this._declutterViewState={scale:0,rotation:0,center:[0,0],size:[0,0]},this._completed=!1,this._symbolRepository=new a(4096,e,(()=>new o)),this._symbolDeclutterer=new l(e,this._symbolRepository,((t,e,i)=>new r(t,e,i,this.styleRepository,this._zoom,this._viewState.rotation)),((t,e)=>{t.allSymbolsFadingOut=!0,t.lastOpacityUpdate=e,h(t,e,!0),t.decluttered=!0,t.requestRender()}),((t,e)=>this.styleRepository.getStyleLayerByUID(t.styleLayerUID).z-this.styleRepository.getStyleLayerByUID(e.styleLayerUID).z),(t=>{const e=this.styleRepository.getStyleLayerByUID(t);if(this._zoom+_<e.minzoom||this._zoom-_>=e.maxzoom)return!1;const i=e.getLayoutProperty(\"visibility\");return!i||i.getValue()!==c.NONE}))}addTile(t){t.decluttered=!1,this._tileToHandle.set(t,t.on(\"symbols-changed\",(()=>{this._symbolRepository.add(t),this.restartDeclutter()}))),this._symbolRepository.add(t),this.restartDeclutter()}removeTile(t){const e=this._tileToHandle.get(t);e&&(this._symbolRepository.removeTile(t),this.restartDeclutter(),e.remove(),this._tileToHandle.delete(t))}update(t,e){return this._zoom=t,this._viewState={scale:e.scale,rotation:e.rotation,center:[e.center[0],e.center[1]],size:[e.size[0],e.size[1]]},this._continueDeclutter(),this._completed}restartDeclutter(){this._completed=!1,this._symbolDeclutterer.restart(),this._notifyUnstable()}clear(){this._completed=!1,this._symbolRepository=null,this._symbolDeclutterer.restart(),this._tileToHandle.forEach((t=>t.remove())),this._tileToHandle.clear()}get stale(){return this._zoom!==this._declutterZoom||this._viewState.size[0]!==this._declutterViewState.size[0]||this._viewState.size[1]!==this._declutterViewState.size[1]||this._viewState.scale!==this._declutterViewState.scale||this._viewState.rotation!==this._declutterViewState.rotation}deleteStyleLayers(t){this._symbolRepository.deleteStyleLayers(t)}_continueDeclutter(){this._completed&&!this.stale||(this._symbolDeclutterer.running||(this._declutterZoom=this._zoom,this._declutterViewState.center[0]=this._viewState.center[0],this._declutterViewState.center[1]=this._viewState.center[1],this._declutterViewState.rotation=this._viewState.rotation,this._declutterViewState.scale=this._viewState.scale,this._declutterViewState.size[0]=this._viewState.size[0],this._declutterViewState.size[1]=this._viewState.size[1],this._symbolDeclutterer.restart()),this._symbolDeclutterer.setScreenSize(this._viewState.size[0],this._viewState.size[1]),this._completed=this._symbolDeclutterer.continue(i),this._completed&&this._scheduleNotifyStable())}_scheduleNotifyStable(){e(this._stableNotificationHandle)&&clearTimeout(this._stableNotificationHandle),this._stableNotificationHandle=setTimeout((()=>{this._stableNotificationHandle=null,this.emit(\"fade-complete\")}),(1+n)*s)}_notifyUnstable(){e(this._stableNotificationHandle)&&(clearTimeout(this._stableNotificationHandle),this._stableNotificationHandle=null),this.emit(\"fade-start\")}}export{m as SymbolFader};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{c as s}from\"../../../../chunks/mat3f32.js\";import{TiledDisplayObject as r}from\"./TiledDisplayObject.js\";class t extends r{_createTransforms(){return{dvs:s(),tileMat3:s()}}}export{t as RenderableTile};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e,isNone as t,unwrap as s}from\"../../../../core/maybe.js\";import{createResolver as r}from\"../../../../core/promiseUtils.js\";import{create as i}from\"../../../../geometry/support/aaBoundingRect.js\";import{SymbolFader as o}from\"./decluttering/SymbolFader.js\";import{Visibility as l,StyleLayerType as n}from\"./style/StyleDefinition.js\";import{WGLDrawPhase as a}from\"../webgl/enums.js\";import{RenderableTile as d}from\"../webgl/RenderableTile.js\";import h from\"../webgl/TileContainer.js\";import c from\"../../tiling/TileCoverage.js\";import y from\"../../tiling/TileKey.js\";import{StencilOperation as p,CompareFunction as u,BlendFactor as m}from\"../../../webgl/enums.js\";const f=1e-6;function _(e,t){if(e){const s=e.getLayoutProperty(\"visibility\");if(!s||s.getValue()!==l.NONE&&(void 0===e.minzoom||e.minzoom<t+f)&&(void 0===e.maxzoom||e.maxzoom>=t-f))return!0}return!1}class b extends h{constructor(e){super(e),this._backgroundTiles=[],this._pointToCallbacks=new Map}destroy(){this.removeAllChildren(),this._spriteMosaic?.dispose(),this._spriteMosaic=null,this._glyphMosaic?.dispose(),this._glyphMosaic=null,e(this._symbolFader)&&(this._symbolFader.clear(),this._symbolFader=null),this._styleRepository=null,this._backgroundTiles=[],this._pointToCallbacks.clear()}setStyleResources(e,r,i){if(this._spriteMosaic=e,this._glyphMosaic=r,this._styleRepository=i,t(this._symbolFader)){const e=new o(this._styleRepository,this.children);e.on(\"fade-start\",(()=>{this.emit(\"fade-start\"),this.requestRender()})),e.on(\"fade-complete\",(()=>{this.emit(\"fade-complete\"),this.requestRender()})),this._symbolFader=e}s(this._symbolFader).styleRepository=i}setSpriteMosaic(e){this._spriteMosaic?.dispose(),this._spriteMosaic=e}deleteStyleLayers(t){e(this._symbolFader)&&this._symbolFader.deleteStyleLayers(t)}async hitTest(e){const t=r();return this._pointToCallbacks.set(e,t),this.requestRender(),t.promise}enterTileInvalidation(){for(const e of this.children)e.invalidating=!0}createRenderParams(e){return{...super.createRenderParams(e),renderPass:null,styleLayer:null,styleLayerUID:-1,glyphMosaic:this._glyphMosaic,spriteMosaic:this._spriteMosaic,hasClipping:!!this._clippingInfos}}doRender(e){!this.visible||e.drawPhase!==a.MAP&&e.drawPhase!==a.DEBUG||void 0===this._spriteMosaic||super.doRender(e)}addChild(t){return super.addChild(t),e(this._symbolFader)?this._symbolFader.addTile(t):t.decluttered=!0,this.requestRender(),t}removeChild(t){return e(this._symbolFader)&&this._symbolFader.removeTile(t),this.requestRender(),super.removeChild(t)}renderChildren(e){const{drawPhase:t}=e;if(t!==a.DEBUG){if(this._doRender(e),this._pointToCallbacks.size>0){e.drawPhase=a.HITTEST;const s=e.painter.effects.hittestVTL;s.bind(e),this._doRender(e),s.draw(e,this._pointToCallbacks),s.unbind(e),e.drawPhase=t}}else super.renderChildren(e)}removeAllChildren(){for(let t=0;t<this.children.length;t++){const s=this.children[t];e(this._symbolFader)&&this._symbolFader.removeTile(s),s.dispose()}super.removeAllChildren()}getStencilTarget(){return this.children.filter((e=>e.neededForCoverage&&e.hasData()))}restartDeclutter(){e(this._symbolFader)&&this._symbolFader.restartDeclutter()}_doRender(e){const{context:t}=e,s=this._styleRepository;if(!s)return;const r=s.layers;let i=!0;e.drawPhase===a.HITTEST&&(i=!1),s.backgroundBucketIds.length>0&&(e.renderPass=\"background\",this._renderBackgroundLayers(e,s.backgroundBucketIds)),super.renderChildren(e),e.drawPhase===a.MAP&&this._fade(e.displayLevel,e.state);const o=this.children.filter((e=>e.visible&&e.hasData()));if(!o||0===o.length)return t.bindVAO(),t.setStencilTestEnabled(!0),void t.setBlendingEnabled(!0);for(const l of o)l.triangleCount=0;t.setStencilWriteMask(0),t.setColorMask(!0,!0,!0,!0),t.setStencilOp(p.KEEP,p.KEEP,p.REPLACE),t.setStencilTestEnabled(!0),t.setBlendingEnabled(!1),t.setDepthTestEnabled(!0),t.setDepthWriteEnabled(!0),t.setDepthFunction(u.LEQUAL),t.setClearDepth(1),t.clear(t.gl.DEPTH_BUFFER_BIT),e.renderPass=\"opaque\";for(let l=r.length-1;l>=0;l--)this._renderStyleLayer(r[l],e,o);t.setDepthWriteEnabled(!1),t.setBlendingEnabled(i),t.setBlendFunctionSeparate(m.ONE,m.ONE_MINUS_SRC_ALPHA,m.ONE,m.ONE_MINUS_SRC_ALPHA),e.renderPass=\"translucent\";for(let l=0;l<r.length;l++)this._renderStyleLayer(r[l],e,o);t.bindVAO(),t.setStencilTestEnabled(!0),t.setBlendingEnabled(!0)}_fade(t,s){e(this._symbolFader)&&(this._symbolFader.update(t,s)||this.requestRender())}_renderStyleLayer(e,t,s){const{painter:r,renderPass:i}=t;if(void 0===e)return;const o=e.getLayoutProperty(\"visibility\");if(o&&o.getValue()===l.NONE)return;let a;switch(e.type){case n.BACKGROUND:return;case n.FILL:if(\"opaque\"!==i&&\"translucent\"!==t.renderPass)return;a=\"vtlFill\";break;case n.LINE:if(\"translucent\"!==i)return;a=\"vtlLine\";break;case n.CIRCLE:if(\"translucent\"!==i)return;a=\"vtlCircle\";break;case n.SYMBOL:if(\"translucent\"!==i)return;a=\"vtlSymbol\"}if(s=e.type===n.SYMBOL?s.filter((e=>e.decluttered)):s.filter((e=>e.neededForCoverage)),\"vtlSymbol\"!==a){const r=t.displayLevel;if(0===s.length||void 0!==e.minzoom&&e.minzoom>=r+f||void 0!==e.maxzoom&&e.maxzoom<r-f)return}const d=e.uid;t.styleLayerUID=d,t.styleLayer=e;for(const l of s)if(l.layerData.has(d)){r.renderObjects(t,s,a);break}}_renderBackgroundLayers(t,s){const{context:r,displayLevel:o,painter:l,state:h}=t,m=this._styleRepository;let f=!1;for(const e of s){if(m.getLayerById(e).type===n.BACKGROUND&&_(m.getLayerById(e),o)){f=!0;break}}if(!f)return;const b=this._tileInfoView.getTileCoverage(t.state,0,\"smallest\"),{spans:g,lodInfo:T}=b,{level:E}=T,C=i(),L=[];if(this._renderPasses){const s=this._renderPasses[0];e(this._clippingInfos)&&(s.brushes[0].prepareState(t),s.brushes[0].drawMany(t,this._clippingInfos))}const v=this._backgroundTiles;let R,S=0;for(const{row:e,colFrom:n,colTo:a}of g)for(let t=n;t<=a;t++){if(S<v.length)R=v[S],R.key.set(E,e,T.normalizeCol(t),T.getWorldForColumn(t)),this._tileInfoView.getTileBounds(C,R.key,!1),R.x=C[0],R.y=C[3],R.resolution=this._tileInfoView.getTileResolution(E);else{const s=new y(E,e,T.normalizeCol(t),T.getWorldForColumn(t)),r=this._tileInfoView.getTileBounds(i(),s),o=this._tileInfoView.getTileResolution(E);R=new d(s,o,r[0],r[3],512,512,4096,4096),v.push(R)}R.setTransform(h),L.push(R),S++}r.setStencilWriteMask(0),r.setColorMask(!0,!0,!0,!0),r.setStencilOp(p.KEEP,p.KEEP,p.REPLACE),r.setStencilFunction(u.EQUAL,0,255);let F=!0;t.drawPhase===a.HITTEST&&(F=!1),r.setStencilTestEnabled(F);for(const e of s){const s=m.getLayerById(e);s.type===n.BACKGROUND&&_(s,o)&&(t.styleLayerUID=s.uid,t.styleLayer=s,l.renderObjects(t,L,\"vtlBackground\"))}c.pool.release(b)}}export{b as VectorTileContainer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{dispose<PERSON>aybe as e}from\"../../../../core/maybe.js\";import{g as t,h as r,d as s}from\"../../../../chunks/mat3.js\";import{c as a}from\"../../../../chunks/mat3f32.js\";import{BufferObject as o}from\"../../../webgl/BufferObject.js\";import\"../../../webgl/FramebufferObject.js\";import\"../../../../core/arrayUtils.js\";import\"../../../../core/has.js\";import\"../../../webgl/checkWebGLError.js\";import\"../../../webgl/context-util.js\";import{DataType as i,BlendFactor as n,PrimitiveType as m,Usage as f}from\"../../../webgl/enums.js\";import\"../../../../chunks/builtins.js\";import\"../../../webgl/Texture.js\";import{VertexArrayObject as h}from\"../../../webgl/VertexArrayObject.js\";import{DisplayObject as c}from\"../../engine/DisplayObject.js\";import{VertexElementDescriptor as _}from\"../../../webgl/VertexElementDescriptor.js\";const u={geometry:[new _(\"a_PositionAndFlags\",3,i.SHORT,0,6)]},d=new Map;d.set(\"a_PositionAndFlags\",0);const g={vsPath:\"debug/overlay\",fsPath:\"debug/overlay\",attributes:d};class l extends c{constructor(e){super(),this._conf=e}static makeFlags(e,t){return e|t<<2}_createTransforms(){return{dvs:a()}}doRender(e){this._updateTransforms(e),this._ensureResources(e);const{context:t}=e;t.useProgram(this._program),this._program.setUniformMatrix3fv(\"u_dvsMat3\",this.transforms.dvs),this._program.setUniform4fv(\"u_colors\",this._conf.getColors(e)),this._program.setUniform1fv(\"u_opacities\",this._conf.getOpacities(e));const{vertexData:r,indexData:s}=this._conf.getMesh(e);this._vertexBuffer.setData(r),this._indexBuffer.setData(s),t.bindVAO(this._vertexArray),t.setBlendingEnabled(!0),t.setBlendFunction(n.ONE,n.ONE_MINUS_SRC_ALPHA),t.setDepthTestEnabled(!1),t.setStencilTestEnabled(!1),t.setColorMask(!0,!0,!0,!0),t.drawElements(m.TRIANGLES,s.length,i.UNSIGNED_INT,0)}onDetach(){this._vertexArray=e(this._vertexArray)}_updateTransforms(e){t(this.transforms.dvs),r(this.transforms.dvs,this.transforms.dvs,[-1,1]),s(this.transforms.dvs,this.transforms.dvs,[2/e.state.size[0],-2/e.state.size[1],1])}_ensureResources(e){const{context:t}=e;this._program||(this._program=e.painter.materialManager.getProgram(g)),this._vertexBuffer||(this._vertexBuffer=o.createVertex(t,f.STREAM_DRAW)),this._indexBuffer||(this._indexBuffer=o.createIndex(t,f.STREAM_DRAW)),this._vertexArray||(this._vertexArray=new h(t,d,u,{geometry:this._vertexBuffer},this._indexBuffer))}}export{l as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../layers/support/TileInfo.js\";import l from\"./TileInfoView.js\";import s from\"./TileKey.js\";class t extends l{constructor(){super(...arguments),this._fullCacheLodInfos=null,this._levelByScale={}}getTileParentId(e){const l=s.pool.acquire(e),t=0===l.level?null:s.getId(l.level-1,l.row>>1,l.col>>1,l.world);return s.pool.release(l),t}getTileCoverage(e,l,s){const t=super.getTileCoverage(e,l,s);if(!t)return t;const o=1<<t.lodInfo.level;return t.spans=t.spans.filter((e=>e.row>=0&&e.row<o)),t}scaleToLevel(e){if(this._fullCacheLodInfos||this._initializeFullCacheLODs(this._lodInfos),this._levelByScale[e])return this._levelByScale[e];{const l=this._fullCacheLodInfos;if(e>l[0].scale)return l[0].level;let s,t;for(let o=0;o<l.length-1;o++)if(t=l[o+1],e>t.scale)return s=l[o],s.level+(s.scale-e)/(s.scale-t.scale);return l[l.length-1].level}}_initializeFullCacheLODs(l){let s;if(0===l[0].level)s=l.map((e=>({level:e.level,resolution:e.resolution,scale:e.scale})));else{const l=this.tileInfo.size[0],t=this.tileInfo.spatialReference;s=e.create({size:l,spatialReference:t}).lods.map((e=>({level:e.level,resolution:e.resolution,scale:e.scale})))}for(let e=0;e<s.length;e++)this._levelByScale[s[e].scale]=s[e].level;this._fullCacheLodInfos=s}}export{t as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import t from\"../../../Graphic.js\";import{clone as i}from\"../../../core/lang.js\";import s from\"../../../core/Logger.js\";import{isNone as r,destroyMaybe as a}from\"../../../core/maybe.js\";import{isAbortError as l}from\"../../../core/promiseUtils.js\";import{watch as n,initial as o}from\"../../../core/reactiveUtils.js\";import{property as h}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import{subclass as u}from\"../../../core/accessorSupport/decorators/subclass.js\";import{diff as c,hasDiff as y}from\"../../../core/accessorSupport/diffUtils.js\";import{create as p}from\"../../../geometry/support/aaBoundingRect.js\";import{equals as d}from\"../../../geometry/support/spatialReferenceUtils.js\";import{StyleUpdateType as _}from\"../engine/vectorTiles/enums.js\";import{TileHandler as f}from\"../engine/vectorTiles/TileHandler.js\";import{TileManager as g}from\"../engine/vectorTiles/TileManager.js\";import{VectorTile as m}from\"../engine/vectorTiles/VectorTile.js\";import{VectorTileContainer as C}from\"../engine/vectorTiles/VectorTileContainer.js\";import{StyleLayerType as T}from\"../engine/vectorTiles/style/StyleDefinition.js\";import v from\"../engine/vectorTiles/style/StyleRepository.js\";import{LayerView2DMixin as R}from\"./LayerView2D.js\";import H from\"./support/DebugOverlay.js\";import w from\"../tiling/TileInfoViewPOT.js\";import S from\"../tiling/TileQueue.js\";import D from\"../../layers/LayerView.js\";let Q=class extends(R(D)){constructor(){super(...arguments),this._styleChanges=[],this._fetchQueue=null,this._parseQueue=null,this._tileHandlerPromise=null,this._isTileHandlerReady=!1,this._collisionOverlay=null,this.fading=!1,this._getCollidersMesh=e=>{const{pixelRatio:t}=e.state;let i=0;const s=[],r=[];for(const a of this._vectorTileContainer.children)if(a.symbols)for(const[e,l]of a.symbols)for(const a of l)for(const e of a.colliders){const l=(e.xScreen+e.dxScreen)*t,n=(e.yScreen+e.dyScreen)*t,o=e.width*t,h=e.height*t,u=a.unique.parts[e.partIndex].targetOpacity>.5;if(!u&&\"all\"!==this.layer.showCollisionBoxes)continue;const c=3,y=1,p=3,d=0,_=u?2:0,f=u?3:0,g=H.makeFlags(_,f);s.push(l,n,g,l+o,n,g,l,n+h,g,l+o,n+h,g),r.push(i+0,i+1,i+2,i+1,i+3,i+2),i+=4;const m=u?c:y,C=u?p:d,T=H.makeFlags(m,C);s.push(l,n,T,l+o,n,T,l,n+1,T,l+o,n+1,T),r.push(i+0,i+1,i+2,i+1,i+3,i+2),i+=4,s.push(l,n+h-1,T,l+o,n+h-1,T,l,n+h,T,l+o,n+h,T),r.push(i+0,i+1,i+2,i+1,i+3,i+2),i+=4,s.push(l,n,T,l+1,n,T,l,n+h,T,l+1,n+h,T),r.push(i+0,i+1,i+2,i+1,i+3,i+2),i+=4,s.push(l+o-1,n,T,l+o,n,T,l+o-1,n+h,T,l+o,n+h,T),r.push(i+0,i+1,i+2,i+1,i+3,i+2),i+=4}return{vertexData:new Int16Array(s),indexData:new Uint32Array(r)}},this._getCollidersColors=()=>[1,.5,0,1,1,0,0,1,0,1,.5,1,0,.5,0,1],this._getCollidersOpacities=()=>[.05,.01,.15,.2]}async hitTest(e,i){if(!this._tileHandlerPromise)return null;await this._tileHandlerPromise;const s=await this._vectorTileContainer.hitTest(i);if(!s||0===s.length)return null;const r=s[0]-1,a=this._styleRepository,l=a.getStyleLayerByUID(r);if(!l)return null;const n=a.getStyleLayerIndex(l.id);return[{type:\"graphic\",mapPoint:e,layer:this.layer,graphic:new t({attributes:{layerId:n,layerName:l.id,layerUID:r},layer:this.layer,sourceLayer:this.layer})}]}update(e){if(this._tileHandlerPromise&&this._isTileHandlerReady)return e.pixelRatio!==this._tileHandler.devicePixelRatio?(this._start(),void(this._tileHandler.devicePixelRatio=e.pixelRatio)):void(this._styleChanges.length>0?this._tileHandlerPromise=this._applyStyleChanges():(this._fetchQueue.pause(),this._parseQueue.pause(),this._fetchQueue.state=e.state,this._parseQueue.state=e.state,this._tileManager.update(e)||this.requestUpdate(),this._parseQueue.resume(),this._fetchQueue.resume()))}attach(){const{style:e}=this.layer.currentStyleInfo;this._styleRepository=new v(e),this._tileInfoView=new w(this.layer.tileInfo,this.layer.fullExtent),this._vectorTileContainer=new C(this._tileInfoView),this._tileHandler=new f(this.layer,this._styleRepository,window.devicePixelRatio||1),this.container.addChild(this._vectorTileContainer),this._start(),this.addAttachHandles([this._vectorTileContainer.on(\"fade-start\",(()=>{this.fading=!0,this.notifyChange(\"updating\"),this.requestUpdate()})),this._vectorTileContainer.on(\"fade-complete\",(()=>{this._collisionOverlay?.requestRender(),this.fading=!1,this.notifyChange(\"updating\"),this.requestUpdate()})),n((()=>this.layer.showCollisionBoxes),(e=>{\"none\"!==e?this._collisionOverlay||(this._collisionOverlay=new H({getMesh:this._getCollidersMesh,getColors:this._getCollidersColors,getOpacities:this._getCollidersOpacities}),this.container.addChild(this._collisionOverlay)):this._collisionOverlay&&(this.container.removeChild(this._collisionOverlay),this._collisionOverlay=null),this.container.requestRender()}),o),this.layer.on(\"paint-change\",(e=>{if(e.isDataDriven)this._styleChanges.push({type:_.PAINTER_CHANGED,data:e}),this.notifyChange(\"updating\"),this.requestUpdate();else{const t=this._styleRepository,i=t.getLayerById(e.layer);if(!i)return;const s=i.type===T.SYMBOL;t.setPaintProperties(e.layer,e.paint),s&&this._vectorTileContainer.restartDeclutter(),this._vectorTileContainer.requestRender()}})),this.layer.on(\"layout-change\",(e=>{const t=this._styleRepository,i=t.getLayerById(e.layer);if(!i)return;const s=c(i.layout,e.layout);if(!r(s)){if(y(s,\"visibility\")&&1===L(s))return t.setLayoutProperties(e.layer,e.layout),i.type===T.SYMBOL&&this._vectorTileContainer.restartDeclutter(),void this._vectorTileContainer.requestRender();this._styleChanges.push({type:_.LAYOUT_CHANGED,data:e}),this.notifyChange(\"updating\"),this.requestUpdate()}})),this.layer.on(\"style-layer-visibility-change\",(e=>{const t=this._styleRepository,i=t.getLayerById(e.layer);i&&(t.setStyleLayerVisibility(e.layer,e.visibility),i.type===T.SYMBOL&&this._vectorTileContainer.restartDeclutter(),this._vectorTileContainer.requestRender())})),this.layer.on(\"style-layer-change\",(e=>{this._styleChanges.push({type:_.LAYER_CHANGED,data:e}),this.notifyChange(\"updating\"),this.requestUpdate()})),this.layer.on(\"delete-style-layer\",(e=>{this._styleChanges.push({type:_.LAYER_REMOVED,data:e}),this.notifyChange(\"updating\"),this.requestUpdate()})),this.layer.on(\"load-style\",(()=>this._loadStyle())),this.layer.on(\"spriteSource-change\",(e=>{this._newSpriteSource=e.spriteSource,this._styleChanges.push({type:_.SPRITES_CHANGED,data:null});const t=this._styleRepository.layers;for(const i of t)switch(i.type){case T.SYMBOL:i.getLayoutProperty(\"icon-image\")&&this._styleChanges.push({type:_.LAYOUT_CHANGED,data:{layer:i.id,layout:i.layout}});break;case T.LINE:i.getPaintProperty(\"line-pattern\")&&this._styleChanges.push({type:_.PAINTER_CHANGED,data:{layer:i.id,paint:i.paint,isDataDriven:i.isPainterDataDriven()}});break;case T.FILL:i.getLayoutProperty(\"fill-pattern\")&&this._styleChanges.push({type:_.PAINTER_CHANGED,data:{layer:i.id,paint:i.paint,isDataDriven:i.isPainterDataDriven()}})}this.notifyChange(\"updating\"),this.requestUpdate()}))])}detach(){this._stop(),this.container.removeAllChildren(),this._vectorTileContainer=a(this._vectorTileContainer),this._tileHandler=a(this._tileHandler)}moveStart(){this.requestUpdate()}viewChange(){this.requestUpdate()}moveEnd(){this._collisionOverlay&&this._vectorTileContainer.restartDeclutter(),this.requestUpdate()}supportsSpatialReference(e){return d(this.layer.tileInfo?.spatialReference,e)}canResume(){let e=super.canResume();const{currentStyleInfo:t}=this.layer;if(e&&t?.layerDefinition){const i=this.view.scale,{minScale:s,maxScale:r}=t.layerDefinition;t&&t.layerDefinition&&(s&&s<i&&(e=!1),r&&r>i&&(e=!1))}return e}isUpdating(){const e=this._vectorTileContainer.children;return!this._isTileHandlerReady||!this._fetchQueue||!this._parseQueue||this._fetchQueue.updating||this._parseQueue.updating||e.length>0&&e.some((e=>e.invalidating))||this.fading}acquireTile(e){const t=this._createVectorTile(e);return this._tileHandlerPromise?.then((()=>{this._fetchQueue.push(t.key).then((e=>this._parseQueue.push({key:t.key,data:e}))).then((e=>{t.once(\"attach\",(()=>this.requestUpdate())),t.setData(e),this.requestUpdate(),this.notifyChange(\"updating\")})).catch((e=>{this.notifyChange(\"updating\"),l(e)||s.getLogger(this.declaredClass).error(e)}))})),t}releaseTile(e){const t=e.key.id;this._fetchQueue.abort(t),this._parseQueue.abort(t),this.requestUpdate()}_start(){if(this._stop(),this._tileManager=new g({acquireTile:e=>this.acquireTile(e),releaseTile:e=>this.releaseTile(e),tileInfoView:this._tileInfoView},this._vectorTileContainer),!this.layer.currentStyleInfo)return;const e=new AbortController,t=this._tileHandler.start({signal:e.signal}).then((()=>{this._fetchQueue=new S({tileInfoView:this._tileInfoView,process:(e,t)=>this._getTileData(e,t),concurrency:15}),this._parseQueue=new S({tileInfoView:this._tileInfoView,process:(e,t)=>this._parseTileData(e,t),concurrency:8}),this.requestUpdate(),this._isTileHandlerReady=!0}));this._tileHandler.spriteMosaic.then((e=>{this._vectorTileContainer.setStyleResources(e,this._tileHandler.glyphMosaic,this._styleRepository),this.requestUpdate()})),this._tileHandlerAbortController=e,this._tileHandlerPromise=t}_stop(){if(!this._tileHandlerAbortController||!this._vectorTileContainer)return;const e=this._tileHandlerAbortController;e&&e.abort(),this._tileHandlerPromise=null,this._isTileHandlerReady=!1,this._fetchQueue=a(this._fetchQueue),this._parseQueue=a(this._parseQueue),this._tileManager=a(this._tileManager),this._vectorTileContainer.removeAllChildren()}async _getTileData(e,t){const i=await this._tileHandler.fetchTileData(e,t);return this.notifyChange(\"updating\"),i}async _parseTileData(e,t){return this._tileHandler.parseTileData(e,t)}async _applyStyleChanges(){this._isTileHandlerReady=!1,this._fetchQueue.pause(),this._parseQueue.pause(),this._fetchQueue.clear(),this._parseQueue.clear(),this._tileManager.clearCache();const e=this._styleChanges;try{await this._tileHandler.updateStyle(e)}catch(n){s.getLogger(this.declaredClass).error(\"error applying vector-tiles style update\",n.message),this._fetchQueue.resume(),this._parseQueue.resume(),this._isTileHandlerReady=!0}const t=this._styleRepository,i=[];e.forEach((e=>{if(e.type!==_.LAYER_REMOVED)return;const s=e.data,r=t.getLayerById(s.layer);r&&i.push(r.uid)}));const r=[];let a;e.forEach((e=>{const i=e.type,s=e.data;switch(i){case _.PAINTER_CHANGED:t.setPaintProperties(s.layer,s.paint),a=s.layer;break;case _.LAYOUT_CHANGED:t.setLayoutProperties(s.layer,s.layout),a=s.layer;break;case _.LAYER_REMOVED:return void t.deleteStyleLayer(s.layer);case _.LAYER_CHANGED:t.setStyleLayer(s.layer,s.index),a=s.layer.id;break;case _.SPRITES_CHANGED:this._vectorTileContainer.setSpriteMosaic(this._tileHandler.setSpriteSource(this._newSpriteSource)),this._newSpriteSource=null,a=null}const l=t.getLayerById(a);l&&r.push(l.uid)}));const l=this._vectorTileContainer.children;if(i.length>0){this._vectorTileContainer.deleteStyleLayers(i);for(const e of l)e.deleteLayerData(i)}if(this._fetchQueue.resume(),this._parseQueue.resume(),r.length>0){const e=[];for(const t of l){const i=this._fetchQueue.push(t.key).then((e=>this._parseQueue.push({key:t.key,data:e,styleLayerUIDs:r}))).then((e=>t.setData(e)));e.push(i)}await Promise.all(e)}this._styleChanges=[],this._isTileHandlerReady=!0,this.notifyChange(\"updating\"),this.requestUpdate()}async _loadStyle(){const{style:e}=this.layer.currentStyleInfo,t=i(e);this._isTileHandlerReady=!1,this._fetchQueue.pause(),this._parseQueue.pause(),this._fetchQueue.clear(),this._parseQueue.clear(),this.notifyChange(\"updating\"),this._styleRepository=new v(t),this._vectorTileContainer.destroy(),this._tileManager.clear(),this._tileHandlerAbortController.abort(),this._tileHandlerAbortController=new AbortController;const{signal:s}=this._tileHandlerAbortController;try{this._tileHandlerPromise=this._tileHandler.setStyle(this._styleRepository,t),await this._tileHandlerPromise}catch(a){if(!l(a))throw a}if(s.aborted)return this._fetchQueue.resume(),this._parseQueue.resume(),this._isTileHandlerReady=!0,this.notifyChange(\"updating\"),void this.requestUpdate();const r=await this._tileHandler.spriteMosaic;this._vectorTileContainer.setStyleResources(r,this._tileHandler.glyphMosaic,this._styleRepository),this._fetchQueue.resume(),this._parseQueue.resume(),this._isTileHandlerReady=!0,this.notifyChange(\"updating\"),this.requestUpdate()}_createVectorTile(e){const t=this._tileInfoView.getTileBounds(p(),e),i=this._tileInfoView.getTileResolution(e.level);return new m(e,i,t[0],t[3],512,512,this._styleRepository)}};function L(e){if(r(e))return 0;switch(e.type){case\"partial\":return Object.keys(e.diff).length;case\"complete\":return Math.max(Object.keys(e.oldValue).length,Object.keys(e.newValue).length);case\"collection\":return Object.keys(e.added).length+Object.keys(e.changed).length+Object.keys(e.removed).length}}e([h()],Q.prototype,\"_fetchQueue\",void 0),e([h()],Q.prototype,\"_parseQueue\",void 0),e([h()],Q.prototype,\"_isTileHandlerReady\",void 0),e([h()],Q.prototype,\"fading\",void 0),Q=e([u(\"esri.views.2d.layers.VectorTileLayerView2D\")],Q);const I=Q;export{I as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIgC,IAAMA,KAAN,MAAO;AAAA,EAAC,YAAYA,IAAEC,IAAE;AAAC,SAAK,SAAO,GAAE,KAAK,UAAQ,GAAE,KAAK,QAAM,CAAC,GAAE,KAAK,SAAOD,IAAE,KAAK,UAAQC,IAAE,KAAK,MAAM,KAAK,IAAIA,GAAE,GAAE,GAAED,IAAEC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK;AAAA,EAAM;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,SAASD,IAAEC,IAAE;AAAC,QAAGD,KAAE,KAAK,UAAQC,KAAE,KAAK,QAAQ,QAAO,IAAIA;AAAE,QAAIC,KAAE,MAAKC,KAAE;AAAG,aAAQC,KAAE,GAAEA,KAAE,KAAK,MAAM,QAAO,EAAEA,IAAE;AAAC,YAAM,IAAE,KAAK,MAAMA,EAAC;AAAE,MAAAJ,MAAG,EAAE,SAAOC,MAAG,EAAE,WAAS,SAAOC,MAAG,EAAE,KAAGA,GAAE,KAAG,EAAE,KAAGA,GAAE,OAAKA,KAAE,GAAEC,KAAEC;AAAA,IAAE;AAAC,WAAO,SAAOF,KAAE,IAAID,QAAG,KAAK,MAAM,OAAOE,IAAE,CAAC,GAAED,GAAE,QAAMA,GAAE,UAAQA,GAAE,QAAMF,MAAG,KAAK,MAAM,KAAK,IAAIC,GAAEC,GAAE,IAAEF,IAAEE,GAAE,GAAEA,GAAE,QAAMF,IAAEC,EAAC,CAAC,GAAEC,GAAE,SAAOD,MAAG,KAAK,MAAM,KAAK,IAAIA,GAAEC,GAAE,GAAEA,GAAE,IAAED,IAAEC,GAAE,OAAMA,GAAE,SAAOD,EAAC,CAAC,MAAIC,GAAE,QAAMF,MAAG,KAAK,MAAM,KAAK,IAAIC,GAAEC,GAAE,IAAEF,IAAEE,GAAE,GAAEA,GAAE,QAAMF,IAAEE,GAAE,MAAM,CAAC,GAAEA,GAAE,SAAOD,MAAG,KAAK,MAAM,KAAK,IAAIA,GAAEC,GAAE,GAAEA,GAAE,IAAED,IAAED,IAAEE,GAAE,SAAOD,EAAC,CAAC,IAAG,IAAIA,GAAEC,GAAE,GAAEA,GAAE,GAAEF,IAAEC,EAAC;AAAA,EAAE;AAAA,EAAC,QAAQG,IAAE;AAAC,aAAQJ,KAAE,GAAEA,KAAE,KAAK,MAAM,QAAO,EAAEA,IAAE;AAAC,YAAMC,KAAE,KAAK,MAAMD,EAAC;AAAE,UAAGC,GAAE,MAAIG,GAAE,KAAGH,GAAE,WAASG,GAAE,UAAQH,GAAE,IAAEA,GAAE,UAAQG,GAAE,EAAE,CAAAH,GAAE,SAAOG,GAAE;AAAA,eAAcH,GAAE,MAAIG,GAAE,KAAGH,GAAE,UAAQG,GAAE,SAAOH,GAAE,IAAEA,GAAE,WAASG,GAAE,EAAE,CAAAH,GAAE,UAAQG,GAAE;AAAA,eAAeA,GAAE,MAAIH,GAAE,KAAGG,GAAE,WAASH,GAAE,UAAQG,GAAE,IAAEA,GAAE,UAAQH,GAAE,EAAE,CAAAA,GAAE,IAAEG,GAAE,GAAEH,GAAE,SAAOG,GAAE;AAAA,WAAU;AAAC,YAAGA,GAAE,MAAIH,GAAE,KAAGG,GAAE,UAAQH,GAAE,SAAOG,GAAE,IAAEA,GAAE,WAASH,GAAE,EAAE;AAAS,QAAAA,GAAE,IAAEG,GAAE,GAAEH,GAAE,UAAQG,GAAE;AAAA,MAAM;AAAC,WAAK,MAAM,OAAOJ,IAAE,CAAC,GAAE,KAAK,QAAQI,EAAC;AAAA,IAAC;AAAC,SAAK,MAAM,KAAKA,EAAC;AAAA,EAAC;AAAC;;;ACA3/B,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAE;AAAC,SAAK,QAAM,GAAE,KAAK,SAAO,GAAE,KAAK,WAAS,CAAC,GAAE,KAAK,aAAW,CAAC,GAAE,KAAK,eAAa,GAAE,KAAK,cAAY,CAAC,GAAE,KAAK,YAAU,CAAC,GAAE,KAAK,iBAAe,oBAAI,OAAI,KAAK,QAAMF,IAAE,KAAK,SAAOC,IAAE,KAAK,eAAaC,IAAE,KAAK,WAAS,IAAIF,GAAEA,KAAE,GAAEC,KAAE,CAAC,GAAE,KAAK,WAAW,KAAK,IAAI,WAAWD,KAAEC,EAAC,CAAC,GAAE,KAAK,SAAS,KAAK,IAAE,GAAE,KAAK,UAAU,KAAK,MAAM;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEC,IAAE;AAAC,UAAMC,KAAE,CAAC,GAAEJ,MAAE,KAAK,cAAaK,KAAE,oBAAI,OAAIC,KAAE,IAAE;AAAI,eAAUC,MAAKJ,IAAE;AAAC,YAAMF,KAAE,KAAK,MAAMM,KAAED,EAAC;AAAE,MAAAD,GAAE,IAAIJ,EAAC;AAAA,IAAC;AAAC,UAAMO,KAAE,CAAC;AAAE,WAAOH,GAAE,QAAS,CAAAE,OAAG;AAAC,UAAGA,MAAG,KAAI;AAAC,cAAMN,KAAEC,KAAEK;AAAE,YAAG,KAAK,eAAe,IAAIN,EAAC,EAAE,CAAAO,GAAE,KAAK,KAAK,eAAe,IAAIP,EAAC,CAAC;AAAA,aAAM;AAAC,gBAAME,KAAEH,IAAE,SAASE,IAAEK,EAAC,EAAE,KAAM,MAAI;AAAC,iBAAK,eAAe,OAAON,EAAC;AAAA,UAAC,GAAI,MAAI;AAAC,iBAAK,eAAe,OAAOA,EAAC;AAAA,UAAC,CAAE;AAAE,eAAK,eAAe,IAAIA,IAAEE,EAAC,GAAEK,GAAE,KAAKL,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE,GAAE,QAAQ,IAAIK,EAAC,EAAE,KAAM,MAAI;AAAC,UAAIH,MAAE,KAAK,YAAYH,EAAC;AAAE,MAAAG,QAAIA,MAAE,CAAC,GAAE,KAAK,YAAYH,EAAC,IAAEG;AAAG,iBAAUC,MAAKH,IAAE;AAAC,cAAMA,KAAEE,IAAEC,EAAC;AAAE,YAAGH,IAAE;AAAC,UAAAC,GAAEE,EAAC,IAAE,EAAC,KAAI,MAAG,MAAKH,GAAE,MAAK,SAAQA,GAAE,SAAQ,MAAKA,GAAE,MAAK,MAAKG,GAAC;AAAE;AAAA,QAAQ;AAAC,cAAME,KAAER,IAAE,SAASE,IAAEI,EAAC;AAAE,YAAG,CAACE,MAAG,CAACA,GAAE,QAAQ;AAAS,cAAMC,MAAED,GAAE;AAAQ,YAAIE;AAAE,YAAG,MAAID,IAAE,MAAM,CAAAC,KAAE,IAAIH,GAAE,GAAE,GAAE,GAAE,CAAC;AAAA,aAAM;AAAC,gBAAMN,KAAE,GAAEC,KAAEO,IAAE,QAAM,IAAER,IAAEE,KAAEM,IAAE,SAAO,IAAER;AAAE,cAAIG,KAAEF,KAAE,IAAE,IAAEA,KAAE,IAAE,GAAEF,MAAEG,KAAE,IAAE,IAAEA,KAAE,IAAE;AAAE,gBAAIC,OAAIA,KAAE,IAAG,MAAIJ,QAAIA,MAAE,IAAGU,KAAE,KAAK,SAAS,SAASR,KAAEE,IAAED,KAAEH,GAAC,GAAEU,GAAE,YAAU,KAAK,SAAS,KAAK,YAAY,MAAI,KAAK,WAAW,KAAK,YAAY,IAAE,OAAM,KAAK,eAAa,KAAK,WAAW,QAAO,KAAK,WAAW,KAAK,IAAI,WAAW,KAAK,QAAM,KAAK,MAAM,CAAC,GAAE,KAAK,SAAS,KAAK,IAAE,GAAE,KAAK,UAAU,KAAK,MAAM,GAAE,KAAK,WAAS,IAAIT,GAAE,KAAK,QAAM,GAAE,KAAK,SAAO,CAAC,GAAES,KAAE,KAAK,SAAS,SAASR,KAAEE,IAAED,KAAEH,GAAC;AAAG,gBAAMK,MAAE,KAAK,WAAW,KAAK,YAAY,GAAEC,KAAEE,GAAE;AAAO,cAAIG,IAAEC;AAAE,cAAGN,GAAE,UAAQC,KAAE,GAAEA,KAAEJ,IAAEI,MAAI;AAAC,YAAAI,KAAET,KAAEK,IAAEK,KAAE,KAAK,SAAOF,GAAE,IAAEH,KAAE,KAAGG,GAAE;AAAE,qBAAQH,MAAE,GAAEA,MAAEL,IAAEK,MAAI,CAAAF,IAAEO,KAAEL,MAAE,CAAC,IAAED,GAAEK,KAAEJ,GAAC;AAAA,UAAC;AAAA,QAAC;AAAC,QAAAF,IAAEC,EAAC,IAAE,EAAC,MAAKI,IAAE,SAAQD,KAAE,SAAQ,MAAK,MAAK,KAAK,aAAY,GAAEL,GAAEE,EAAC,IAAE,EAAC,KAAI,MAAG,MAAKI,IAAE,SAAQD,KAAE,MAAK,KAAK,cAAa,MAAKH,GAAC,GAAE,KAAK,SAAS,KAAK,YAAY,IAAE;AAAA,MAAE;AAAC,aAAOF;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,aAAaG,IAAE;AAAC,eAAUN,MAAK,KAAK,aAAY;AAAC,YAAMC,KAAE,KAAK,YAAYD,EAAC;AAAE,UAAG,CAACC,GAAE;AAAS,UAAIC;AAAE,iBAAUF,OAAKC,GAAE,KAAGC,KAAED,GAAED,GAAC,GAAEE,GAAE,QAAQ,OAAOI,EAAC,GAAE,MAAIJ,GAAE,QAAQ,MAAK;AAAC,cAAMI,MAAE,KAAK,WAAWJ,GAAE,IAAI,GAAEC,KAAED,GAAE;AAAK,YAAIH,KAAEK;AAAE,iBAAQJ,MAAE,GAAEA,MAAEG,GAAE,QAAOH,MAAI,MAAID,MAAE,KAAK,SAAOI,GAAE,IAAEH,OAAGG,GAAE,GAAEC,KAAE,GAAEA,KAAED,GAAE,OAAMC,KAAI,CAAAE,IAAEP,MAAEK,EAAC,IAAE;AAAE,eAAOH,GAAED,GAAC,GAAE,KAAK,SAASE,GAAE,IAAI,IAAE;AAAA,MAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,KAAKI,IAAEN,IAAED,KAAEK,KAAE,GAAE;AAAC,SAAK,UAAUL,GAAC,MAAI,KAAK,UAAUA,GAAC,IAAE,IAAIa,GAAEN,IAAE,EAAC,aAAY,EAAE,OAAM,UAAS,EAAE,eAAc,OAAM,KAAK,OAAM,QAAO,KAAK,OAAM,GAAE,IAAI,WAAW,KAAK,QAAM,KAAK,MAAM,CAAC;AAAG,UAAMD,KAAE,KAAK,UAAUN,GAAC;AAAE,IAAAM,GAAE,gBAAgBL,EAAC,GAAE,KAAK,SAASD,GAAC,KAAGM,GAAE,QAAQ,KAAK,WAAWN,GAAC,CAAC,GAAEO,GAAE,YAAYD,IAAED,EAAC,GAAE,KAAK,SAASL,GAAC,IAAE;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,SAAK,WAAS;AAAK,eAAUO,MAAK,KAAK,UAAU,CAAAA,MAAGA,GAAE,QAAQ;AAAE,SAAK,UAAU,SAAO;AAAA,EAAC;AAAC;;;ACAtoF,IAAMO,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAE;AAAC,QAAG,KAAK,WAAS,CAAC,GAAE,KAAK,WAAS,CAAC,GAAEA,GAAE,QAAKA,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,MAAC,KAAK,GAAE;AAAC,cAAMC,KAAED,GAAE,WAAW;AAAE,eAAKC,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,UAAC,KAAK,GAAE;AAAC,kBAAMD,MAAEC,GAAE,WAAW;AAAE,gBAAIF,IAAEG,IAAEC,KAAEC,IAAEC,IAAEC,IAAEC;AAAE,mBAAKP,IAAE,KAAK,IAAG,SAAOA,IAAE,IAAI,GAAE;AAAA,cAAC,KAAK;AAAE,gBAAAD,KAAEC,IAAE,UAAU;AAAE;AAAA,cAAM,KAAK;AAAE,gBAAAE,KAAEF,IAAE,SAAS;AAAE;AAAA,cAAM,KAAK;AAAE,gBAAAG,MAAEH,IAAE,UAAU;AAAE;AAAA,cAAM,KAAK;AAAE,gBAAAI,KAAEJ,IAAE,UAAU;AAAE;AAAA,cAAM,KAAK;AAAE,gBAAAK,KAAEL,IAAE,UAAU;AAAE;AAAA,cAAM,KAAK;AAAE,gBAAAM,KAAEN,IAAE,UAAU;AAAE;AAAA,cAAM,KAAK;AAAE,gBAAAO,KAAEP,IAAE,UAAU;AAAE;AAAA,cAAM;AAAQ,gBAAAA,IAAE,KAAK;AAAA,YAAC;AAAC,YAAAA,IAAE,QAAQ,GAAED,OAAI,KAAK,SAASA,EAAC,IAAE,EAAC,OAAMI,KAAE,QAAOC,IAAE,MAAKC,IAAE,KAAIC,IAAE,SAAQC,GAAC,GAAE,KAAK,SAASR,EAAC,IAAEG;AAAG;AAAA,UAAK;AAAA,UAAC;AAAQ,YAAAD,GAAE,KAAK;AAAA,QAAC;AAAC,QAAAA,GAAE,QAAQ;AAAE;AAAA,MAAK;AAAA,MAAC;AAAQ,QAAAD,GAAE,KAAK;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,WAAO,KAAK,SAASA,EAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAE;AAAC,WAAO,KAAK,SAASA,EAAC;AAAA,EAAC;AAAC;AAAC,IAAME,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,UAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,SAASF,IAAE;AAAC,WAAO,KAAK,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,SAASA,IAAEC,IAAE;AAAC,SAAK,QAAQD,EAAC,IAAEC;AAAA,EAAC;AAAC;AAAC,IAAME,KAAN,MAAO;AAAA,EAAC,YAAYH,IAAE;AAAC,SAAK,aAAW,CAAC,GAAE,KAAK,WAASA;AAAA,EAAC;AAAA,EAAC,SAASE,IAAEC,KAAE;AAAC,UAAMC,KAAE,KAAK,cAAcF,EAAC;AAAE,QAAGE,GAAE,SAASD,GAAC,EAAE,QAAO,QAAQ,QAAQ;AAAE,UAAME,KAAE,MAAIF,KAAEG,KAAED,KAAE;AAAI,QAAG,KAAK,UAAS;AAAC,YAAME,KAAE,KAAK,SAAS,QAAQ,eAAcL,EAAC,EAAE,QAAQ,WAAUG,KAAE,MAAIC,EAAC;AAAE,aAAO,EAAEC,IAAE,EAAC,cAAa,eAAc,CAAC,EAAE,KAAM,CAAAP,OAAG;AAAC,QAAAI,GAAE,SAASD,KAAE,IAAIJ,GAAE,IAAIK,GAAE,IAAI,WAAWJ,GAAE,IAAI,GAAE,IAAI,SAASA,GAAE,IAAI,CAAC,CAAC,CAAC;AAAA,MAAC,CAAE,EAAE,MAAO,MAAI;AAAC,QAAAI,GAAE,SAASD,KAAE,IAAIJ,IAAC;AAAA,MAAC,CAAE;AAAA,IAAC;AAAC,WAAOK,GAAE,SAASD,KAAE,IAAIJ,IAAC,GAAE,QAAQ,QAAQ;AAAA,EAAC;AAAA,EAAC,SAASC,IAAEC,IAAE;AAAC,UAAMF,KAAE,KAAK,cAAcC,EAAC;AAAE,QAAG,CAACD,GAAE;AAAO,UAAMG,KAAE,KAAK,MAAMD,KAAE,GAAG;AAAE,QAAGC,KAAE,IAAI;AAAO,UAAMC,MAAEJ,GAAE,SAASG,EAAC;AAAE,WAAOC,MAAE,EAAC,SAAQA,IAAE,WAAWF,EAAC,GAAE,QAAOE,IAAE,UAAUF,EAAC,EAAC,IAAE;AAAA,EAAM;AAAA,EAAC,cAAcD,IAAE;AAAC,QAAIC,KAAE,KAAK,WAAWD,EAAC;AAAE,WAAOC,OAAIA,KAAE,KAAK,WAAWD,EAAC,IAAE,IAAIE,OAAGD;AAAA,EAAC;AAAC;;;ACAjyC,IAAM,IAAE;AAAa,IAAMO,KAAN,MAAM,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAEC,KAAE,GAAE;AAAC,SAAK,QAAM,CAAC,GAAE,KAAK,eAAa,CAAC,GAAE,KAAK,YAAU,CAAC,GAAE,KAAK,WAAS,CAAC,GAAE,KAAK,eAAa,GAAE,KAAK,eAAa,GAAE,KAAK,aAAW,GAAE,KAAK,cAAY,GAAE,KAAK,eAAa,CAAC,GAAE,KAAK,aAAW,IAAGF,MAAG,KAAGC,MAAG,MAAI,QAAQ,MAAM,0EAA0E,GAAE,KAAK,aAAWD,IAAE,KAAK,cAAYC,IAAEC,KAAE,MAAI,KAAK,eAAaA,KAAG,KAAK,WAAS,IAAID,GAAED,KAAE,GAAEC,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,WAAS,MAAK,KAAK,eAAa,CAAC;AAAE,eAAUD,MAAK,KAAK,UAAU,CAAAA,MAAGA,GAAE,QAAQ;AAAE,SAAK,UAAU,SAAO;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,WAAOA,MAAG,KAAK,MAAM,SAAO,KAAG,KAAK,MAAMA,EAAC,EAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAE;AAAC,WAAOA,MAAG,KAAK,MAAM,SAAO,KAAG,KAAK,MAAMA,EAAC,EAAE,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,WAAOA,MAAG,KAAK,MAAM,SAAO,OAAK,KAAK,MAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,QAAG,KAAK,QAAQ,GAAE,KAAK,aAAWA,GAAE,kBAAiB,MAAI,KAAK,aAAa,QAAO;AAAC,WAAK,WAAS,IAAIC,GAAE,KAAK,aAAW,GAAE,KAAK,cAAY,CAAC;AAAE,YAAMD,MAAE,KAAK,MAAM,KAAK,UAAU,GAAEC,KAAE,KAAK,MAAM,KAAK,WAAW,GAAEC,KAAE,IAAI,YAAYF,MAAEC,EAAC;AAAE,WAAK,aAAa,CAAC,IAAEC,IAAE,KAAK,SAAS,KAAK,IAAE,GAAE,KAAK,MAAM,KAAK,CAAC,KAAK,YAAW,KAAK,WAAW,CAAC,GAAE,KAAK,UAAU,KAAK,MAAM;AAAA,IAAC;AAAC,SAAK,WAASF;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEG,KAAE,OAAG;AAAC,QAAIF,IAAEC,IAAEE,KAAE,KAAK,aAAaJ,EAAC;AAAE,QAAGI,GAAE,QAAOA;AAAE,QAAG,CAAC,KAAK,YAAU,aAAW,KAAK,SAAS,WAAW,QAAO;AAAK,QAAGJ,MAAGA,GAAE,WAAW,CAAC,KAAG,CAACC,IAAEC,EAAC,IAAE,KAAK,eAAeF,EAAC,GAAEG,KAAE,QAAIF,KAAE,KAAK,SAAS,cAAcD,EAAC,GAAE,CAACC,MAAG,CAACA,GAAE,SAAO,CAACA,GAAE,UAAQA,GAAE,QAAM,KAAGA,GAAE,SAAO,EAAE,QAAO;AAAK,UAAMI,KAAEJ,GAAE,OAAMK,MAAEL,GAAE,QAAO,CAACF,IAAEQ,IAAEC,EAAC,IAAE,KAAK,eAAeH,IAAEC,GAAC;AAAE,WAAOP,GAAE,SAAO,IAAE,QAAM,KAAK,MAAMA,IAAEE,IAAEM,IAAEC,IAAEL,IAAED,EAAC,GAAEE,KAAE,EAAC,MAAKL,IAAE,OAAMM,IAAE,QAAOC,KAAE,KAAIL,GAAE,KAAI,eAAc,OAAG,YAAWA,GAAE,YAAW,MAAKM,GAAC,GAAE,KAAK,aAAaP,EAAC,IAAEI,IAAEA;AAAA,EAAE;AAAA,EAAC,eAAeJ,IAAE;AAAC,UAAMG,KAAE,CAAC;AAAE,eAAUF,MAAKD,GAAE,CAAAG,GAAEF,GAAE,IAAI,IAAE,KAAK,cAAcA,GAAE,MAAKA,GAAE,MAAM;AAAE,WAAOE;AAAA,EAAC;AAAA,EAAC,sBAAsBH,IAAEG,IAAE;AAAC,UAAMF,KAAE,KAAK,cAAcD,IAAEG,EAAC,GAAED,KAAED,MAAGA,GAAE;AAAK,QAAG,CAACC,GAAE,QAAO;AAAK,IAAAA,GAAE,QAAMD,GAAE,OAAMC,GAAE,SAAOD,GAAE;AAAO,UAAMG,KAAEH,GAAE,OAAMI,KAAEJ,GAAE,QAAOK,MAAE;AAAE,WAAM,EAAC,IAAG,CAACJ,GAAE,IAAEI,KAAEJ,GAAE,IAAEI,GAAC,GAAE,IAAG,CAACJ,GAAE,IAAEI,MAAEF,IAAEF,GAAE,IAAEI,MAAED,EAAC,GAAE,MAAKJ,GAAE,KAAI;AAAA,EAAC;AAAA,EAAC,KAAKD,IAAEG,IAAEF,KAAE,GAAEQ,KAAE,GAAE;AAAC,QAAGR,MAAG,KAAK,MAAM,UAAQA,MAAG,KAAK,aAAa,OAAO;AAAO,SAAK,UAAUA,EAAC,MAAI,KAAK,UAAUA,EAAC,IAAE,IAAIS,GAAEV,IAAE,EAAC,aAAY,EAAE,MAAK,UAAS,EAAE,eAAc,UAASW,GAAE,eAAc,OAAM,KAAK,MAAMV,EAAC,EAAE,CAAC,GAAE,QAAO,KAAK,MAAMA,EAAC,EAAE,CAAC,EAAC,GAAE,IAAI,WAAW,KAAK,aAAaA,EAAC,EAAE,MAAM,CAAC;AAAG,UAAMF,KAAE,KAAK,UAAUE,EAAC;AAAE,IAAAF,GAAE,gBAAgBI,EAAC,GAAE,KAAK,SAASF,EAAC,KAAGF,GAAE,QAAQ,IAAI,WAAW,KAAK,aAAaE,EAAC,EAAE,MAAM,CAAC,GAAED,GAAE,YAAYD,IAAEU,EAAC,GAAE,KAAK,SAASR,EAAC,IAAE;AAAA,EAAE;AAAA,EAAC,OAAO,UAAUD,IAAEG,IAAEF,IAAEC,IAAEE,IAAEC,IAAEC,KAAEG,IAAEV,IAAEQ,IAAEC,IAAE;AAAC,QAAII,KAAEV,KAAEC,KAAEF,IAAEY,MAAEJ,KAAEJ,KAAEC;AAAE,QAAGE,IAAE;AAAC,MAAAK,OAAGR;AAAE,eAAQC,MAAE,IAAGA,OAAGC,IAAED,OAAIM,OAAIN,MAAEC,MAAGA,KAAEL,MAAGC,KAAEF,IAAEY,OAAGR,GAAE,UAAQF,KAAE,IAAGA,MAAGJ,IAAEI,KAAI,CAAAC,GAAES,MAAEV,EAAC,IAAEH,GAAEY,MAAGT,KAAEJ,MAAGA,EAAC;AAAA,IAAC,MAAM,UAAQe,KAAE,GAAEA,KAAEP,IAAEO,MAAI;AAAC,eAAQX,KAAE,GAAEA,KAAEJ,IAAEI,KAAI,CAAAC,GAAES,MAAEV,EAAC,IAAEH,GAAEY,KAAET,EAAC;AAAE,MAAAS,MAAGT,IAAEU,OAAGR;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAML,IAAEG,IAAEF,IAAEC,IAAEE,IAAEC,IAAE;AAAC,QAAG,CAAC,KAAK,YAAU,aAAW,KAAK,SAAS,cAAYJ,MAAG,KAAK,aAAa,OAAO;AAAO,UAAMK,MAAE,IAAI,YAAYD,KAAEA,GAAE,SAAO,KAAK,SAAS,MAAM,MAAM,GAAEI,KAAE,KAAK,aAAaR,EAAC;AAAE,IAAAQ,MAAGH,OAAG,QAAQ,MAAM,4CAA4C;AAAE,UAAMC,KAAE,GAAEC,KAAEH,KAAEF,GAAE,QAAM,KAAK,SAAS;AAAM,OAAE,UAAUG,KAAEE,IAAEL,GAAE,GAAEA,GAAE,GAAEM,IAAEP,GAAE,CAAC,GAAEF,GAAE,IAAEO,IAAEP,GAAE,IAAEO,IAAEJ,GAAE,OAAMA,GAAE,QAAOC,EAAC,GAAE,KAAK,SAASH,EAAC,IAAE;AAAA,EAAE;AAAA,EAAC,eAAeD,IAAEE,IAAE;AAAC,IAAAF,MAAG,GAAEE,MAAG;AAAE,UAAME,KAAE,KAAK,IAAIJ,IAAEE,EAAC;AAAE,QAAG,KAAK,gBAAc,KAAK,eAAaE,IAAE;AAAC,YAAMD,KAAE,IAAIH,GAAE,GAAE,GAAEA,IAAEE,EAAC;AAAE,aAAO,KAAK,aAAa,KAAK,IAAI,YAAYF,KAAEE,EAAC,CAAC,GAAE,KAAK,SAAS,KAAK,IAAE,GAAE,KAAK,MAAM,KAAK,CAACF,IAAEE,EAAC,CAAC,GAAE,KAAK,UAAU,KAAK,MAAM,GAAE,CAACC,IAAE,KAAK,aAAa,SAAO,GAAE,CAACH,IAAEE,EAAC,CAAC;AAAA,IAAC;AAAC,QAAIG,KAAEL,KAAE,IAAE,IAAEA,KAAE,IAAE,GAAEM,MAAEJ,KAAE,IAAE,IAAEA,KAAE,IAAE;AAAE,UAAIG,OAAIA,KAAE,IAAG,MAAIC,QAAIA,MAAE;AAAG,UAAMG,KAAE,KAAK,SAAS,SAAST,KAAEK,IAAEH,KAAEI,GAAC;AAAE,WAAOG,GAAE,SAAO,KAAG,KAAK,SAAS,KAAK,YAAY,MAAI,KAAK,aAAa,KAAK,YAAY,IAAE,OAAM,KAAK,eAAa,KAAK,aAAa,QAAO,KAAK,aAAa,KAAK,IAAI,YAAY,KAAK,aAAW,KAAK,WAAW,CAAC,GAAE,KAAK,SAAS,KAAK,IAAE,GAAE,KAAK,MAAM,KAAK,CAAC,KAAK,YAAW,KAAK,WAAW,CAAC,GAAE,KAAK,UAAU,KAAK,MAAM,GAAE,KAAK,WAAS,IAAIR,GAAE,KAAK,aAAW,GAAE,KAAK,cAAY,CAAC,GAAE,KAAK,eAAeD,IAAEE,EAAC,KAAG,CAACO,IAAE,KAAK,cAAa,CAAC,KAAK,YAAW,KAAK,WAAW,CAAC;AAAA,EAAC;AAAA,EAAC,eAAeN,IAAE;AAAC,UAAMF,KAAE,aAAYC,KAAEC,GAAE,MAAMF,EAAC;AAAE,QAAG,CAACC,GAAE,QAAO;AAAK,UAAME,KAAEF,GAAE,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,MAAM,GAAEG,KAAEF,GAAE,MAAMA,GAAE,YAAY,GAAG,IAAE,CAAC,GAAE,CAACG,KAAEG,IAAEV,EAAC,IAAEE,GAAEG,IAAEC,EAAC;AAAE,WAAM,CAAC,EAAC,GAAE,GAAE,GAAE,GAAE,OAAMI,IAAE,QAAOV,IAAE,KAAI,MAAG,YAAW,EAAC,GAAE,IAAI,WAAWO,IAAE,MAAM,CAAC;AAAA,EAAC;AAAC;;;ACA55H,IAAMS,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAE;AAAC,SAAK,SAAOF,IAAE,KAAK,mBAAiBC,IAAE,KAAK,mBAAiBC,IAAE,KAAK,gBAAc,MAAK,KAAK,eAAa,MAAK,KAAK,cAAY;AAAA,EAAI;AAAA,EAAC,UAAS;AAJthB;AAIuhB,eAAK,gBAAL,mBAAkB,SAAQ,KAAK,cAAY,MAAK,KAAK,mBAAiB,MAAK,KAAK,SAAO,MAAK,KAAK,gBAAc,MAAK,KAAK,eAAa;AAAA,EAAI;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK,qBAAqB,KAAM,MAAI,KAAK,aAAc;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK;AAAA,EAAY;AAAA,EAAC,MAAM,MAAMD,IAAE;AAAC,SAAK,uBAAqB,KAAK,OAAO,iBAAiB,KAAK,kBAAiBA,EAAC,GAAE,KAAK,qBAAqB,KAAM,CAAAD,OAAG;AAAC,WAAK,gBAAc,IAAID,GAAE,MAAK,MAAK,GAAG,GAAE,KAAK,cAAc,gBAAgBC,EAAC;AAAA,IAAC,CAAE;AAAE,UAAME,KAAE,KAAK,OAAO,iBAAiB,WAAUC,KAAE,IAAIC,GAAEF,KAAE,GAAEA,IAAE,EAAC,GAAG,KAAK,OAAO,kBAAiB,OAAM,KAAK,OAAO,OAAM,CAAC,IAAE,IAAI;AAAE,SAAK,eAAa,IAAIE,GAAE,MAAK,MAAKD,EAAC,GAAE,KAAK,oBAAkBE,GAAE,qBAAoB,EAAC,QAAO,MAAK,UAASJ,GAAE,UAAS,QAAOA,GAAE,OAAM,CAAC,EAAE,KAAM,CAAAC,OAAG;AAAC,UAAG,KAAK,cAAYA,IAAE,KAAK,UAAQ,CAAC,KAAK,YAAY,QAAO;AAAC,cAAME,MAAEF,GAAE,UAAU,YAAW,KAAK,OAAO,iBAAiB,OAAMD,EAAC;AAAE,gBAAQ,IAAIG,GAAC,EAAE,MAAO,CAAAH,QAAG,EAAEA,GAAC,CAAE;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,MAAM,YAAYD,IAAE;AAAC,WAAO,MAAM,KAAK,mBAAkB,KAAK,oBAAkB,QAAQ,IAAI,KAAK,YAAY,UAAU,eAAcA,EAAC,CAAC,GAAE,KAAK;AAAA,EAAiB;AAAA,EAAC,gBAAgBA,IAAE;AAAC,UAAMC,KAAE,IAAIF,GAAE,MAAK,MAAK,GAAG;AAAE,WAAOE,GAAE,gBAAgBD,EAAC,GAAE,KAAK,gBAAcC,IAAE,KAAK,uBAAqB,QAAQ,QAAQD,EAAC,GAAEC;AAAA,EAAC;AAAA,EAAC,MAAM,SAASD,IAAEC,IAAE;AAAC,UAAM,KAAK,mBAAkB,KAAK,mBAAiBD,IAAE,KAAK,uBAAqB,KAAK,OAAO,iBAAiB,KAAK,kBAAiB,IAAI,GAAE,KAAK,qBAAqB,KAAM,CAAAA,QAAG;AAAC,WAAK,gBAAc,IAAID,GAAE,MAAK,MAAK,GAAG,GAAE,KAAK,cAAc,gBAAgBC,GAAC;AAAA,IAAC,CAAE;AAAE,UAAME,KAAE,IAAIE,GAAE,KAAK,OAAO,iBAAiB,YAAU,GAAE,KAAK,OAAO,iBAAiB,WAAU,EAAC,GAAG,KAAK,OAAO,kBAAiB,OAAM,KAAK,OAAO,OAAM,CAAC,IAAE,IAAI;AAAE,WAAO,KAAK,eAAa,IAAIA,GAAE,MAAK,MAAKF,EAAC,GAAE,KAAK,oBAAkB,QAAQ,IAAI,KAAK,YAAY,UAAU,YAAWD,EAAC,CAAC,GAAE,KAAK;AAAA,EAAiB;AAAA,EAAC,cAAcD,IAAEC,IAAE;AAAC,WAAO,KAAK,YAAYD,IAAEC,EAAC,EAAE,KAAM,CAAAD,QAAG;AAAC,YAAME,KAAE,KAAK,OAAO,oBAAmBE,MAAE,CAAC;AAAE,iBAAUH,OAAKC,GAAE,CAAAE,IAAE,KAAKH,GAAC;AAAE,aAAO,KAAK,gBAAgBG,KAAEJ,KAAEC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAEC,IAAE;AAAC,UAAMC,KAAEF,MAAGA,GAAE;AAAK,QAAG,CAACE,GAAE,QAAO,QAAQ,QAAQ,IAAI;AAAE,UAAK,EAAC,0BAAyBE,KAAE,cAAaE,GAAC,IAAEJ;AAAE,WAAO,MAAI,OAAO,KAAKE,GAAC,EAAE,SAAO,QAAQ,QAAQ,IAAI,IAAE,KAAK,kBAAkB,KAAM,MAAI,KAAK,YAAY,OAAO,sBAAqB,EAAC,KAAIJ,GAAE,IAAI,IAAG,0BAAyBI,KAAE,gBAAeJ,GAAE,eAAc,GAAE,EAAC,GAAGC,IAAE,cAAaK,GAAC,CAAC,CAAE;AAAA,EAAC;AAAA,EAAC,MAAM,WAAWN,IAAE;AAAC,WAAO,MAAM,KAAK,sBAAqB,KAAK,cAAc,eAAeA,EAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAE;AAAC,WAAO,KAAK,aAAa,cAAcA,GAAE,MAAKA,GAAE,UAAU;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBA,IAAEE,IAAEE,KAAE;AAAC,UAAME,KAAEN,GAAE,KAAK,QAAQA,GAAE,EAAE,GAAEO,KAAE,KAAK,OAAO,mBAAmBL,EAAC,GAAE,EAAC,OAAMM,IAAE,KAAIC,KAAE,KAAIV,GAAC,IAAEO;AAAE,IAAAN,GAAE,KAAK,QAAQM,EAAC;AAAE,QAAG;AAAC,aAAM,EAAC,WAAU,MAAMC,GAAE,YAAYC,IAAEC,KAAEV,IAAEK,GAAC,GAAE,YAAWF,GAAC;AAAA,IAAC,SAAOQ,IAAE;AAAC,UAAG,EAAEA,EAAC,EAAE,OAAMA;AAAE,aAAM,EAAC,WAAU,MAAK,YAAWR,GAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,YAAYF,IAAEC,IAAE;AAAC,UAAMG,MAAE,KAAK,OAAO,oBAAmBE,KAAE,IAAI;AAAM,eAAUJ,MAAKE,KAAE;AAAC,YAAMG,KAAEH,IAAEF,EAAC,EAAE,UAAUF,IAAEC,EAAC;AAAE,MAAAK,GAAE,KAAKC,EAAC;AAAA,IAAC;AAAC,WAAO,EAAED,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBN,IAAEC,IAAEG,KAAE;AAAC,UAAME,KAAE,CAAC;AAAE,aAAQJ,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,KAAG,QAAMD,GAAEC,EAAC,EAAE,SAAO,QAAMF,GAAEE,EAAC,EAAE,CAAAI,GAAE,KAAK,IAAI;AAAA,SAAM;AAAC,YAAMC,KAAE,KAAK,gBAAgBN,GAAEC,EAAC,EAAE,OAAMF,GAAEE,EAAC,GAAEE,GAAC;AAAE,MAAAE,GAAE,KAAKC,EAAC;AAAA,IAAC;AAAC,WAAO,EAAED,EAAC,EAAE,KAAM,CAAAN,QAAG;AAAC,YAAME,KAAE,CAAC,GAAEE,MAAE,CAAC;AAAE,eAAQE,KAAE,GAAEA,KAAEN,IAAE,QAAOM,MAAI;AAAC,cAAMC,KAAEP,IAAEM,EAAC,EAAE;AAAM,YAAGC,OAAIA,GAAE,aAAWA,GAAE,UAAU,aAAW,IAAG;AAAC,gBAAMP,MAAEC,GAAEK,EAAC,EAAE,MAAM;AAAG,UAAAJ,GAAEK,GAAE,UAAU,IAAE,EAAC,QAAOP,KAAE,WAAUO,GAAE,UAAS,GAAEH,IAAE,KAAKG,GAAE,SAAS;AAAA,QAAC;AAAA,MAAC;AAAC,aAAM,EAAC,0BAAyBL,IAAE,cAAaE,IAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;;;ACA1lH,IAAMO,KAAE;AAAR,IAAYC,KAAE;AAAd,IAAmBC,KAAE,CAACC,IAAEC,OAAID,KAAE,KAAG,KAAG,IAAEC;AAAG,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYD,IAAEE,IAAE;AAAC,SAAK,SAAO,oBAAI,OAAI,KAAK,aAAW,IAAIH,GAAE,IAAI,CAAAA,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,YAAU,CAAC,GAAE,CAAC,GAAE,KAAK,gBAAc,oBAAI,OAAI,KAAK,cAAYC,GAAE,aAAY,KAAK,cAAYA,GAAE,aAAY,KAAK,eAAaA,GAAE,cAAa,KAAK,aAAWE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,eAAS,CAACH,IAAEC,EAAC,KAAI,KAAK,OAAO,CAAAA,GAAE,QAAQ;AAAE,SAAK,SAAO,MAAK,KAAK,WAAW,MAAM,GAAE,KAAK,aAAW;AAAA,EAAI;AAAA,EAAC,OAAOD,IAAE;AAAC,SAAK,iBAAiBA,EAAC;AAAE,UAAMC,KAAE,KAAK,cAAaE,KAAEF,GAAE,gBAAgBD,GAAE,OAAM,GAAE,UAAU,GAAE,EAAC,OAAMH,KAAE,SAAQC,GAAC,IAAEK,IAAE,EAAC,OAAMJ,GAAC,IAAED,IAAEI,KAAE,KAAK,QAAOE,KAAE,oBAAI,OAAIC,KAAE,oBAAI;AAAI,eAAS,EAAC,KAAIC,IAAE,SAAQC,IAAE,OAAMC,GAAC,KAAIX,IAAE,UAAQG,MAAEO,IAAEP,OAAGQ,IAAER,OAAI;AAAC,YAAMC,KAAED,GAAE,MAAMD,IAAEO,IAAER,GAAE,aAAaE,GAAC,GAAEF,GAAE,kBAAkBE,GAAC,CAAC,GAAEG,MAAE,KAAK,kBAAkBF,EAAC;AAAE,MAAAG,GAAE,IAAIH,EAAC,GAAEE,IAAE,UAAU,IAAE,KAAK,gBAAgBA,GAAC,IAAEE,GAAE,IAAI,IAAIL,GAAEC,EAAC,CAAC;AAAA,IAAC;AAAC,eAAS,CAACK,IAAEG,GAAC,KAAIP,GAAE,CAAAO,IAAE,aAAWL,GAAE,IAAIE,EAAC;AAAE,eAAUA,MAAKD,GAAE,MAAK,iCAAiCC,IAAEF,EAAC;AAAE,QAAIM,KAAE;AAAG,eAAS,CAACJ,IAAEG,GAAC,KAAIP,GAAE,CAAAO,IAAE,oBAAkBL,GAAE,IAAIE,EAAC,GAAEG,IAAE,qBAAmBA,IAAE,oBAAkBR,GAAE,WAAWE,IAAEM,IAAE,GAAG,KAAGL,GAAE,IAAIE,EAAC,GAAEG,IAAE,aAAWC,KAAE;AAAI,eAAS,CAACJ,IAAEG,GAAC,KAAI,KAAK,OAAO,CAAAL,GAAE,IAAIE,EAAC,KAAG,KAAK,aAAaA,EAAC;AAAE,WAAOG,GAAE,KAAK,QAAQN,EAAC,GAAE,CAACO;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,OAAO,MAAM,GAAE,KAAK,WAAW,MAAM,GAAE,KAAK,cAAc,MAAM;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,WAAW,MAAM;AAAA,EAAC;AAAA,EAAC,iCAAiCV,IAAEC,IAAE;AAAC,UAAME,KAAE,CAAC;AAAE,eAAS,CAACM,KAAEZ,GAAC,KAAI,KAAK,OAAO,MAAK,qBAAqBM,IAAEN,KAAEG,IAAEC,EAAC;AAAE,UAAMK,KAAEH,GAAE,OAAOJ,IAAE,CAAC;AAAE,SAAK,IAAI,IAAEO,EAAC,IAAER,MAAG,KAAK,sBAAsBE,GAAE,IAAGC,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAqBD,IAAEC,IAAEE,IAAEG,IAAE;AAAC,IAAAL,GAAE,IAAI,SAAOE,GAAE,SAAO,CAACF,GAAE,QAAQ,KAAGI,GAAEF,IAAEF,GAAE,GAAG,MAAI,KAAK,gBAAgBA,EAAC,GAAEK,GAAE,IAAIL,GAAE,EAAE,GAAED,GAAE,KAAKC,GAAE,IAAI,QAAME,GAAE,KAAK;AAAA,EAAE;AAAA,EAAC,sBAAsBH,IAAEC,IAAE;AAAC,UAAME,KAAE,KAAK;AAAO,QAAIG,KAAEN;AAAE,eAAO;AAAC,UAAGM,KAAEF,GAAEE,EAAC,GAAE,CAACA,MAAGL,GAAE,IAAIK,EAAC,EAAE;AAAO,YAAMN,MAAEG,GAAE,IAAIG,EAAC;AAAE,UAAGN,OAAGA,IAAE,QAAQ,EAAE,QAAO,KAAK,gBAAgBA,GAAC,GAAE,KAAKC,GAAE,IAAID,IAAE,EAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAE;AAAC,QAAIC,KAAE,KAAK,OAAO,IAAID,EAAC;AAAE,WAAOC,OAAIA,KAAE,KAAK,WAAW,IAAID,EAAC,GAAEC,OAAIA,KAAE,KAAK,YAAY,IAAID,GAAEA,EAAC,CAAC,IAAG,KAAK,OAAO,IAAIA,IAAEC,EAAC,GAAEA;AAAA,EAAE;AAAA,EAAC,aAAaD,IAAE;AAAC,UAAMC,KAAE,KAAK,OAAO,IAAID,EAAC;AAAE,SAAK,YAAYC,EAAC,GAAE,KAAK,qBAAqBA,EAAC,GAAE,KAAK,OAAO,OAAOD,EAAC,GAAEC,GAAE,QAAQ,IAAE,KAAK,WAAW,IAAID,IAAEC,IAAE,CAAC,IAAEA,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,gBAAgBD,IAAE;AAAC,QAAIM;AAAE,UAAMG,MAAE,CAAC,GAAEZ,MAAE,KAAK;AAAW,QAAGA,IAAE,SAASG,EAAC,EAAE;AAAO,UAAMF,KAAE,KAAK;AAAc,eAAS,CAACK,IAAEJ,EAAC,KAAID,GAAE,MAAK,oBAAoBE,IAAED,EAAC,KAAGU,IAAE,KAAKV,EAAC,GAAE,EAAEO,EAAC,KAAG,KAAK,oBAAoBP,IAAEC,EAAC,MAAIM,KAAEP;AAAG,QAAG,EAAEO,EAAC,GAAE;AAAC,iBAAUL,MAAKQ,IAAE,CAAAH,GAAE,cAAc,OAAOL,EAAC,GAAED,GAAE,cAAc,IAAIC,EAAC,GAAEA,GAAE,aAAWD;AAAE,MAAAM,GAAE,cAAc,IAAIN,EAAC,GAAEA,GAAE,aAAWM;AAAA,IAAC,MAAM,YAAUL,MAAKQ,IAAE,CAAAT,GAAE,cAAc,IAAIC,EAAC,GAAEA,GAAE,aAAWD;AAAE,IAAAF,GAAE,IAAIE,GAAE,IAAGA,EAAC,GAAEH,IAAE,SAASG,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAqBA,IAAE;AAAC,QAAG,KAAK,cAAc,OAAOA,GAAE,EAAE,GAAE,KAAK,WAAW,YAAYA,EAAC,GAAE,EAAEA,GAAE,UAAU,GAAE;AAAC,MAAAA,GAAE,WAAW,cAAc,OAAOA,EAAC;AAAE,iBAAUC,MAAKD,GAAE,cAAc,GAAEA,GAAE,UAAU,KAAGA,GAAE,WAAW,cAAc,IAAIC,EAAC;AAAA,IAAC;AAAC,eAAUA,MAAKD,GAAE,cAAc,CAAAC,GAAE,aAAWD,GAAE;AAAW,IAAAA,GAAE,aAAW,MAAKA,GAAE,cAAc,MAAM;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAEC,IAAE;AAAC,UAAME,KAAEH,GAAE;AAAI,QAAG,EAAC,OAAMM,IAAE,KAAIG,KAAE,KAAIZ,KAAE,OAAMC,GAAC,IAAEG,GAAE;AAAI,UAAMF,KAAE,KAAK;AAAc,WAAKO,KAAE,KAAG;AAAC,UAAGA,MAAIG,QAAI,GAAEZ,QAAI,GAAEM,GAAE,UAAQG,MAAGH,GAAE,QAAMM,OAAGN,GAAE,QAAMN,OAAGM,GAAE,UAAQL,GAAE,QAAM;AAAG,UAAGC,GAAE,IAAI,GAAGO,EAAC,IAAIG,GAAC,IAAIZ,GAAC,IAAIC,EAAC,EAAE,EAAE,QAAM;AAAA,IAAE;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,iBAAiBE,IAAE;AAAC,UAAMC,KAAED,GAAE,MAAM;AAAK,QAAGC,GAAE,CAAC,MAAI,KAAK,UAAU,CAAC,KAAGA,GAAE,CAAC,MAAI,KAAK,UAAU,CAAC,EAAE;AAAO,UAAME,KAAE,KAAK,KAAKF,GAAE,CAAC,IAAEJ,EAAC,IAAE,GAAES,KAAE,KAAK,KAAKL,GAAE,CAAC,IAAEJ,EAAC,IAAE;AAAE,SAAK,UAAU,CAAC,IAAEI,GAAE,CAAC,GAAE,KAAK,UAAU,CAAC,IAAEA,GAAE,CAAC,GAAE,KAAK,WAAW,UAAQ,IAAEE,KAAEG;AAAA,EAAC;AAAC;AAAC,SAASF,GAAEJ,IAAE;AAAC,QAAK,CAACC,IAAEE,IAAEG,IAAEG,GAAC,IAAET,GAAE,MAAM,GAAG,GAAEH,MAAE,SAASI,IAAE,EAAE;AAAE,SAAO,MAAIJ,MAAE,OAAK,GAAGA,MAAE,CAAC,IAAI,SAASM,IAAE,EAAE,KAAG,CAAC,IAAI,SAASG,IAAE,EAAE,KAAG,CAAC,IAAI,SAASG,KAAE,EAAE,CAAC;AAAE;AAAC,SAASJ,GAAEL,IAAEC,IAAE;AAAC,QAAME,KAAEF,GAAE,QAAMD,GAAE;AAAM,SAAOA,GAAE,QAAMC,GAAE,OAAKE,MAAGH,GAAE,QAAMC,GAAE,OAAKE,MAAGH,GAAE,UAAQC,GAAE;AAAK;;;ACA9nH,IAAMU,KAAN,MAAO;AAAA,EAAC,YAAYA,IAAE;AAAC,SAAK,QAAM,GAAE,KAAK,QAAM,GAAE,KAAK,OAAK,GAAE,KAAK,WAAS,GAAE,KAAK,YAAU,CAAC,GAAE,KAAK,mBAAiB,CAAC,GAAE,KAAK,mBAAiB,CAAC,GAAE,KAAK,OAAKA;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,cAAY,CAAC,GAAE,KAAK,QAAM,CAAC,EAAC,WAAU,GAAE,cAAa,GAAE,eAAc,GAAE,MAAK,MAAE,GAAE,EAAC,WAAU,GAAE,cAAa,GAAE,eAAc,GAAE,MAAK,MAAE,CAAC,GAAE,KAAK,OAAK;AAAA,EAAE;AAAC;;;ACA7P,SAASC,GAAEC,IAAEC,IAAEF,IAAEG,IAAEC,KAAEC,IAAE;AAAC,QAAMC,MAAEN,KAAEI;AAAE,MAAGE,OAAG,EAAE,SAAOJ,MAAGI,QAAIH,MAAGE,MAAGC,SAAKL,MAAGK;AAAG,QAAMC,KAAE,CAACD;AAAE,SAAOJ,MAAGG,MAAGF,MAAGI,QAAKN,MAAGM,OAAIA;AAAC;AAAC,IAAMJ,KAAN,MAAO;AAAA,EAAC,YAAYF,IAAEC,IAAEF,IAAE;AAAC,SAAK,QAAM,KAAK,KAAKE,KAAEF,EAAC,GAAE,KAAK,WAAS,KAAK,KAAKC,KAAED,EAAC,GAAE,KAAK,YAAUA,IAAE,KAAK,QAAM,IAAI,MAAM,KAAK,KAAK;AAAE,aAAQG,KAAE,GAAEA,KAAE,KAAK,OAAMA,MAAI;AAAC,WAAK,MAAMA,EAAC,IAAE,IAAI,MAAM,KAAK,QAAQ;AAAE,eAAQF,MAAE,GAAEA,MAAE,KAAK,UAASA,MAAI,MAAK,MAAME,EAAC,EAAEF,GAAC,IAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAEC,IAAE;AAAC,UAAMF,KAAE,KAAK,IAAI,KAAK,IAAI,KAAK,MAAME,KAAE,KAAK,SAAS,GAAE,CAAC,GAAE,KAAK,QAAM,CAAC,GAAEC,KAAE,KAAK,IAAI,KAAK,IAAI,KAAK,MAAMF,KAAE,KAAK,SAAS,GAAE,CAAC,GAAE,KAAK,WAAS,CAAC;AAAE,WAAO,KAAK,MAAMD,EAAC,KAAG,KAAK,MAAMA,EAAC,EAAEG,EAAC,KAAG;AAAA,EAAI;AAAA,EAAC,YAAYF,IAAEC,IAAEF,IAAEG,IAAE;AAAC,WAAM,CAAC,KAAK,IAAI,KAAK,IAAI,KAAK,MAAMF,KAAE,KAAK,SAAS,GAAE,CAAC,GAAE,KAAK,UAAQ,CAAC,GAAE,KAAK,IAAI,KAAK,IAAI,KAAK,MAAMC,KAAE,KAAK,SAAS,GAAE,CAAC,GAAE,KAAK,OAAK,CAAC,GAAE,KAAK,IAAI,KAAK,IAAI,KAAK,MAAMF,KAAE,KAAK,SAAS,GAAE,CAAC,GAAE,KAAK,UAAQ,CAAC,GAAE,KAAK,IAAI,KAAK,IAAI,KAAK,MAAMG,KAAE,KAAK,SAAS,GAAE,CAAC,GAAE,KAAK,OAAK,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK;AAAA,EAAS;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK;AAAA,EAAK;AAAC;AAAC,SAASC,GAAEH,IAAED,IAAEG,IAAEC,KAAEC,IAAEC,KAAE;AAAC,QAAMC,KAAEP,GAAEI,KAAG;AAAE,WAAQI,KAAE,GAAEA,KAAED,IAAEC,MAAI;AAAC,UAAMD,MAAE,IAAIN,GAAEK,GAAC;AAAE,IAAAC,IAAE,QAAMP,GAAEI,KAAG,GAAEG,IAAE,QAAMP,GAAEI,KAAG,GAAEG,IAAE,OAAKP,GAAEI,KAAG,GAAEG,IAAE,WAASP,GAAEI,KAAG;AAAE,UAAMI,KAAER,GAAEI,KAAG;AAAE,aAAQH,MAAE,GAAEA,MAAEO,IAAEP,OAAI;AAAC,YAAMA,MAAED,GAAEI,KAAG,GAAEF,KAAEF,GAAEI,KAAG,GAAEC,KAAEL,GAAEI,KAAG,GAAEE,MAAEN,GAAEI,KAAG,GAAEI,KAAE,CAAC,CAACR,GAAEI,KAAG,GAAEK,KAAET,GAAEI,KAAG,GAAEM,KAAEP,GAAEC,KAAG,GAAEO,KAAER,GAAEC,KAAG,GAAEQ,KAAEZ,GAAEI,KAAG,GAAES,KAAEb,GAAEI,KAAG;AAAE,MAAAG,IAAE,UAAU,KAAK,EAAC,OAAMN,KAAE,OAAMC,IAAE,UAASG,IAAE,UAASC,KAAE,MAAKE,IAAE,WAAUC,IAAE,OAAMG,IAAE,QAAOC,IAAE,QAAOH,IAAE,QAAOC,GAAC,CAAC;AAAA,IAAC;AAAC,UAAMF,KAAER,GAAEG,KAAG;AAAE,aAAQF,KAAE,GAAEA,KAAEO,IAAEP,KAAI,CAAAK,IAAE,iBAAiB,KAAK,CAACN,GAAEG,KAAG,GAAEH,GAAEG,KAAG,CAAC,CAAC;AAAE,UAAMM,KAAET,GAAEG,KAAG;AAAE,aAAQF,KAAE,GAAEA,KAAEQ,IAAER,KAAI,CAAAK,IAAE,iBAAiB,KAAK,CAACN,GAAEG,KAAG,GAAEH,GAAEG,KAAG,CAAC,CAAC;AAAE,IAAAC,GAAE,KAAKE,GAAC;AAAA,EAAC;AAAC,SAAOH;AAAC;AAAC,SAASC,GAAEJ,IAAEC,IAAEF,IAAE;AAAC,aAAS,CAACG,IAAEC,GAAC,KAAIH,GAAE,QAAQ,CAAAK,IAAEL,IAAEC,IAAEF,IAAEI,KAAED,EAAC;AAAC;AAAC,SAASG,IAAEJ,IAAEF,IAAEG,IAAEC,KAAEC,IAAE;AAAC,QAAMC,MAAEJ,GAAE,UAAU,IAAIG,EAAC;AAAE,MAAGC,IAAE,SAAOQ,GAAE,QAAO;AAAC,eAAUb,MAAKG,KAAE;AAAC,YAAMJ,KAAEC,GAAE;AAAO,UAAIG;AAAE,UAAGH,GAAE,sBAAqB;AAAC,cAAMA,MAAED,GAAE,MAAM,CAAC,GAAEK,KAAEJ,IAAE,cAAaK,MAAEL,IAAE;AAAc,QAAAC,GAAE,sBAAoBA,GAAE,uBAAqB,MAAII;AAAE,cAAMC,KAAEJ,KAAE,KAAK,MAAM,MAAIE,EAAC,IAAEC,OAAG,IAAEA,MAAE,MAAI;AAAE,QAAAF,MAAEG,MAAG,KAAGA,MAAG,KAAGA,MAAG,IAAEA;AAAA,MAAC,MAAM,CAAAH,MAAE;AAAE,iBAAS,CAACF,KAAEC,EAAC,KAAIF,GAAE,iBAAiB,UAAQA,MAAEC,KAAED,MAAEC,MAAEC,IAAEF,OAAG,EAAE,CAAAK,IAAE,YAAYL,MAAE,CAAC,IAAEG;AAAE,UAAGH,GAAE,sBAAqB;AAAC,cAAMA,MAAED,GAAE,MAAM,CAAC,GAAEK,KAAEJ,IAAE,cAAaK,MAAEL,IAAE;AAAc,QAAAC,GAAE,sBAAoBA,GAAE,uBAAqB,MAAII;AAAE,cAAMC,KAAEJ,KAAE,KAAK,MAAM,MAAIE,EAAC,IAAEC,OAAG,IAAEA,MAAE,MAAI;AAAE,QAAAF,MAAEG,MAAG,KAAGA,MAAG,KAAGA,MAAG,IAAEA;AAAA,MAAC,MAAM,CAAAH,MAAE;AAAE,iBAAS,CAACF,KAAEC,EAAC,KAAIF,GAAE,iBAAiB,UAAQA,MAAEC,KAAED,MAAEC,MAAEC,IAAEF,OAAG,EAAE,CAAAK,IAAE,YAAYL,MAAE,CAAC,IAAEG;AAAA,IAAC;AAAC,IAAAE,IAAE,oBAAkBN,IAAEM,IAAE,iBAAe;AAAA,EAAE;AAAC;;;ACA99D,IAAMS,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,SAAK,YAAU,CAAC,GAAE,KAAK,cAAY,OAAG,KAAK,QAAMD,IAAE,KAAK,aAAWA,GAAE;AAAW,QAAIE,MAAE;AAAE,UAAMC,KAAE,IAAI,YAAYH,EAAC;AAAE,SAAK,YAAU,CAAC;AAAE,UAAMI,KAAED,GAAED,KAAG;AAAE,aAAQG,KAAE,GAAEA,KAAED,IAAEC,KAAI,MAAK,UAAUA,EAAC,IAAEF,GAAED,KAAG;AAAE,SAAK,mBAAiBA,KAAED,OAAI,KAAK,QAAMA,GAAE,mBAAmB,KAAK,UAAU,CAAC,CAAC;AAAA,EAAE;AAAA,EAAC,IAAI,yBAAwB;AAAC,WAAO,EAAE,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAgB;AAAA,EAAC,UAAS;AAAC,SAAK,gBAAc,KAAK,UAAU,GAAE,KAAK,cAAY;AAAA,EAAG;AAAA,EAAC,oBAAoBA,IAAE;AAAC,MAAE,KAAK,KAAK,MAAI,KAAK,sBAAsBA,IAAE,KAAK,OAAM,KAAK,gBAAgB,GAAE,KAAK,QAAM;AAAA,EAAK;AAAC;AAAC,IAAMK,KAAN,cAAgBP,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,UAAMD,IAAEC,EAAC,GAAE,KAAK,OAAKM,GAAE,MAAK,KAAK,iBAAe,GAAE,KAAK,iBAAe;AAAE,UAAML,MAAE,IAAI,YAAYF,EAAC;AAAE,QAAII,KAAE,KAAK;AAAiB,SAAK,iBAAeF,IAAEE,IAAG,GAAE,KAAK,iBAAeF,IAAEE,IAAG;AAAE,UAAMC,KAAEH,IAAEE,IAAG;AAAE,QAAGC,KAAE,GAAE;AAAC,YAAML,MAAE,oBAAI;AAAI,eAAQC,MAAE,GAAEA,MAAEI,IAAEJ,OAAI;AAAC,cAAMA,MAAEC,IAAEE,IAAG,GAAED,KAAED,IAAEE,IAAG,GAAEC,MAAEH,IAAEE,IAAG;AAAE,QAAAJ,IAAE,IAAIC,KAAE,CAACE,IAAEE,GAAC,CAAC;AAAA,MAAC;AAAC,WAAK,aAAWL;AAAA,IAAC;AAAC,SAAK,mBAAiBI;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,iBAAe;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,WAAO,KAAK,iBAAe;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,MAAE,KAAK,qBAAqB,KAAG,KAAK,sBAAsB,QAAQ,GAAE,EAAE,KAAK,gBAAgB,KAAG,KAAK,iBAAiB,QAAQ,GAAE,EAAE,KAAK,eAAe,KAAG,KAAK,gBAAgB,QAAQ,GAAE,KAAK,wBAAsB,MAAK,KAAK,mBAAiB,MAAK,KAAK,kBAAgB,MAAK,KAAK,aAAW;AAAA,EAAC;AAAA,EAAC,sBAAsBJ,IAAEC,IAAEC,KAAE;AAAC,UAAMC,KAAE,IAAI,YAAYF,EAAC,GAAEG,KAAE,IAAI,WAAWD,GAAE,MAAM,GAAEJ,KAAEI,GAAED,KAAG;AAAE,SAAK,mBAAiBK,GAAE,aAAaP,IAAE,EAAE,aAAY,IAAI,WAAWI,GAAE,QAAO,IAAEF,KAAEH,EAAC,CAAC,GAAEG,OAAGH;AAAE,UAAMO,MAAEH,GAAED,KAAG;AAAE,SAAK,kBAAgBK,GAAE,YAAYP,IAAE,EAAE,aAAY,IAAI,YAAYG,GAAE,QAAO,IAAED,KAAEI,GAAC,CAAC,GAAEJ,OAAGI;AAAE,UAAME,KAAE,KAAK,MAAM;AAAa,SAAK,wBAAsB,IAAIC,GAAET,IAAEQ,GAAE,sBAAsB,GAAEA,GAAE,cAAc,GAAE,EAAC,UAAS,KAAK,iBAAgB,GAAE,KAAK,eAAe;AAAA,EAAC;AAAC;AAAC,IAAMA,KAAN,cAAgBT,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,UAAMD,IAAEC,EAAC,GAAE,KAAK,OAAKM,GAAE,MAAK,KAAK,iBAAe,GAAE,KAAK,iBAAe,GAAE,KAAK,oBAAkB,GAAE,KAAK,oBAAkB;AAAE,UAAML,MAAE,IAAI,YAAYF,EAAC;AAAE,QAAII,KAAE,KAAK;AAAiB,SAAK,iBAAeF,IAAEE,IAAG,GAAE,KAAK,iBAAeF,IAAEE,IAAG,GAAE,KAAK,oBAAkBF,IAAEE,IAAG,GAAE,KAAK,oBAAkBF,IAAEE,IAAG;AAAE,UAAMC,KAAEH,IAAEE,IAAG;AAAE,QAAGC,KAAE,GAAE;AAAC,YAAML,MAAE,oBAAI;AAAI,eAAQC,MAAE,GAAEA,MAAEI,IAAEJ,OAAI;AAAC,cAAMA,MAAEC,IAAEE,IAAG,GAAED,KAAED,IAAEE,IAAG,GAAEC,MAAEH,IAAEE,IAAG;AAAE,QAAAJ,IAAE,IAAIC,KAAE,CAACE,IAAEE,GAAC,CAAC;AAAA,MAAC;AAAC,WAAK,aAAWL;AAAA,IAAC;AAAC,SAAK,mBAAiBI;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,iBAAe,KAAG,KAAK,oBAAkB;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,YAAO,KAAK,iBAAe,KAAK,qBAAmB;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,MAAE,KAAK,qBAAqB,KAAG,KAAK,sBAAsB,QAAQ,GAAE,EAAE,KAAK,gBAAgB,KAAG,KAAK,iBAAiB,QAAQ,GAAE,EAAE,KAAK,eAAe,KAAG,KAAK,gBAAgB,QAAQ,GAAE,KAAK,wBAAsB,MAAK,KAAK,mBAAiB,MAAK,KAAK,kBAAgB,MAAK,EAAE,KAAK,wBAAwB,KAAG,KAAK,yBAAyB,QAAQ,GAAE,EAAE,KAAK,mBAAmB,KAAG,KAAK,oBAAoB,QAAQ,GAAE,EAAE,KAAK,kBAAkB,KAAG,KAAK,mBAAmB,QAAQ,GAAE,KAAK,2BAAyB,MAAK,KAAK,sBAAoB,MAAK,KAAK,qBAAmB,MAAK,KAAK,aAAW;AAAA,EAAC;AAAA,EAAC,sBAAsBJ,IAAEC,IAAEC,KAAE;AAAC,UAAMC,KAAE,IAAI,YAAYF,EAAC,GAAEG,KAAE,IAAI,WAAWD,GAAE,MAAM,GAAEJ,KAAEI,GAAED,KAAG;AAAE,SAAK,mBAAiBK,GAAE,aAAaP,IAAE,EAAE,aAAY,IAAI,WAAWI,GAAE,QAAO,IAAEF,KAAEH,EAAC,CAAC,GAAEG,OAAGH;AAAE,UAAMO,MAAEH,GAAED,KAAG;AAAE,SAAK,kBAAgBK,GAAE,YAAYP,IAAE,EAAE,aAAY,IAAI,YAAYG,GAAE,QAAO,IAAED,KAAEI,GAAC,CAAC,GAAEJ,OAAGI;AAAE,UAAME,KAAEL,GAAED,KAAG;AAAE,SAAK,sBAAoBK,GAAE,aAAaP,IAAE,EAAE,aAAY,IAAI,WAAWI,GAAE,QAAO,IAAEF,KAAEM,EAAC,CAAC,GAAEN,OAAGM;AAAE,UAAME,KAAEP,GAAED,KAAG;AAAE,SAAK,qBAAmBK,GAAE,YAAYP,IAAE,EAAE,aAAY,IAAI,YAAYG,GAAE,QAAO,IAAED,KAAEQ,EAAC,CAAC,GAAER,OAAGQ;AAAE,UAAMC,KAAE,KAAK,OAAM,IAAEA,GAAE,cAAaC,KAAED,GAAE;AAAgB,SAAK,wBAAsB,IAAIF,GAAET,IAAE,EAAE,sBAAsB,GAAE,EAAE,cAAc,GAAE,EAAC,UAAS,KAAK,iBAAgB,GAAE,KAAK,eAAe,GAAE,KAAK,2BAAyB,IAAIS,GAAET,IAAEY,GAAE,sBAAsB,GAAEA,GAAE,cAAc,GAAE,EAAC,UAAS,KAAK,oBAAmB,GAAE,KAAK,kBAAkB;AAAA,EAAC;AAAC;AAAC,IAAMF,KAAN,cAAgBX,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAEC,KAAE;AAAC,UAAMF,IAAEC,EAAC,GAAE,KAAK,OAAKM,GAAE,QAAO,KAAK,yBAAuB,oBAAI,OAAI,KAAK,0BAAwB,oBAAI,OAAI,KAAK,kBAAgB,CAAC,GAAE,KAAK,YAAU,OAAG,KAAK,iBAAe,OAAG,KAAK,oBAAkB,GAAE,KAAK,UAAQ,CAAC;AAAE,UAAMF,KAAE,IAAI,YAAYL,EAAC,GAAES,KAAE,IAAI,WAAWT,EAAC,GAAEa,KAAE,IAAI,aAAab,EAAC;AAAE,QAAID,KAAE,KAAK;AAAiB,SAAK,YAAU,CAAC,CAACM,GAAEN,IAAG;AAAE,UAAMO,MAAED,GAAEN,IAAG;AAAE,aAAQI,KAAE,GAAEA,KAAEG,KAAEH,MAAI;AAAC,YAAMH,MAAEK,GAAEN,IAAG,GAAEE,MAAEI,GAAEN,IAAG,GAAEG,MAAEG,GAAEN,IAAG;AAAE,WAAK,uBAAuB,IAAIC,KAAE,CAACC,KAAEC,GAAC,CAAC;AAAA,IAAC;AAAC,UAAMM,KAAEH,GAAEN,IAAG;AAAE,aAAQI,KAAE,GAAEA,KAAEK,IAAEL,MAAI;AAAC,YAAMH,MAAEK,GAAEN,IAAG,GAAEE,MAAEI,GAAEN,IAAG,GAAEG,MAAEG,GAAEN,IAAG;AAAE,WAAK,wBAAwB,IAAIC,KAAE,CAACC,KAAEC,GAAC,CAAC;AAAA,IAAC;AAAC,UAAMQ,KAAEL,GAAEN,IAAG,GAAEY,KAAEN,GAAEN,IAAG;AAAE,SAAK,cAAY,IAAI,WAAWW,EAAC,GAAE,KAAK,cAAY,IAAI,WAAWC,EAAC,GAAEZ,KAAEO,GAAED,IAAEI,IAAEI,IAAEd,IAAE,KAAK,SAAQG,GAAC,GAAE,KAAK,mBAAiBH;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,uBAAuB,OAAK,KAAG,KAAK,wBAAwB,OAAK;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,QAAIC,KAAE;AAAE,eAAS,CAACC,IAAEC,GAAC,KAAI,KAAK,uBAAuB,CAAAF,MAAGE,IAAE,CAAC;AAAE,eAAS,CAACD,IAAEC,GAAC,KAAI,KAAK,wBAAwB,CAAAF,MAAGE,IAAE,CAAC;AAAE,WAAOF,KAAE;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,MAAE,KAAK,qBAAqB,KAAG,KAAK,sBAAsB,QAAQ,GAAE,EAAE,KAAK,gBAAgB,KAAG,KAAK,iBAAiB,QAAQ,GAAE,EAAE,KAAK,iBAAiB,KAAG,KAAK,kBAAkB,QAAQ,GAAE,EAAE,KAAK,eAAe,KAAG,KAAK,gBAAgB,QAAQ,GAAE,KAAK,wBAAsB,MAAK,KAAK,mBAAiB,MAAK,KAAK,oBAAkB,MAAK,KAAK,kBAAgB,MAAK,EAAE,KAAK,qBAAqB,KAAG,KAAK,sBAAsB,QAAQ,GAAE,EAAE,KAAK,gBAAgB,KAAG,KAAK,iBAAiB,QAAQ,GAAE,EAAE,KAAK,iBAAiB,KAAG,KAAK,kBAAkB,QAAQ,GAAE,EAAE,KAAK,eAAe,KAAG,KAAK,gBAAgB,QAAQ,GAAE,KAAK,wBAAsB,MAAK,KAAK,mBAAiB,MAAK,KAAK,oBAAkB,MAAK,KAAK,kBAAgB,MAAK,KAAK,aAAW;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,QAAG,CAAC,KAAK,eAAe;AAAO,SAAK,iBAAe;AAAG,UAAMA,KAAE,EAAE,KAAK,WAAW,GAAEC,KAAE,EAAE,KAAK,iBAAiB;AAAE,IAAAD,GAAE,SAAO,KAAGA,GAAE,eAAaC,GAAE,QAAMA,GAAE,WAAWD,IAAE,GAAE,GAAEA,GAAE,MAAM;AAAE,UAAMG,KAAE,EAAE,KAAK,WAAW,GAAEC,KAAE,EAAE,KAAK,iBAAiB;AAAE,IAAAD,GAAE,SAAO,KAAGA,GAAE,eAAaC,GAAE,QAAMA,GAAE,WAAWD,IAAE,GAAE,GAAEA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,sBAAsBH,IAAEC,IAAEE,IAAE;AAAC,UAAMC,KAAE,IAAI,YAAYH,EAAC,GAAEF,KAAE,IAAI,WAAWK,GAAE,MAAM,GAAEE,MAAEF,GAAED,IAAG;AAAE,SAAK,mBAAiBI,GAAE,aAAaP,IAAE,EAAE,aAAY,IAAI,WAAWD,GAAE,QAAO,IAAEI,IAAEG,GAAC,CAAC,GAAEH,MAAGG;AAAE,UAAME,KAAEJ,GAAED,IAAG;AAAE,SAAK,kBAAgBI,GAAE,YAAYP,IAAE,EAAE,aAAY,IAAI,YAAYI,GAAE,QAAO,IAAED,IAAEK,EAAC,CAAC,GAAEL,MAAGK;AAAE,UAAME,KAAEN,GAAED,IAAG;AAAE,SAAK,mBAAiBI,GAAE,aAAaP,IAAE,EAAE,aAAY,IAAI,WAAWD,GAAE,QAAO,IAAEI,IAAEO,EAAC,CAAC,GAAEP,MAAGO;AAAE,UAAMC,KAAEP,GAAED,IAAG;AAAE,SAAK,kBAAgBI,GAAE,YAAYP,IAAE,EAAE,aAAY,IAAI,YAAYI,GAAE,QAAO,IAAED,IAAEQ,EAAC,CAAC,GAAER,MAAGQ,IAAE,KAAK,oBAAkBJ,GAAE,aAAaP,IAAE,EAAE,aAAY,EAAE,KAAK,WAAW,EAAE,MAAM,GAAE,KAAK,oBAAkBO,GAAE,aAAaP,IAAE,EAAE,aAAY,EAAE,KAAK,WAAW,EAAE,MAAM;AAAE,UAAM,IAAE,KAAK,OAAMY,KAAE,EAAE,cAAaE,KAAE,EAAE;AAAa,SAAK,wBAAsB,IAAIL,GAAET,IAAEY,GAAE,sBAAsB,GAAEA,GAAE,cAAc,GAAE,EAAC,UAAS,KAAK,kBAAiB,SAAQ,KAAK,kBAAiB,GAAE,KAAK,eAAe,GAAE,KAAK,wBAAsB,IAAIH,GAAET,IAAEc,GAAE,sBAAsB,GAAEA,GAAE,cAAc,GAAE,EAAC,UAAS,KAAK,kBAAiB,SAAQ,KAAK,kBAAiB,GAAE,KAAK,eAAe;AAAA,EAAC;AAAC;AAAC,IAAMH,KAAN,cAAgBZ,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,UAAMD,IAAEC,EAAC,GAAE,KAAK,OAAKM,GAAE,QAAO,KAAK,mBAAiB,GAAE,KAAK,mBAAiB;AAAE,UAAML,MAAE,IAAI,YAAYF,EAAC;AAAE,QAAII,KAAE,KAAK;AAAiB,SAAK,mBAAiBF,IAAEE,IAAG,GAAE,KAAK,mBAAiBF,IAAEE,IAAG,GAAE,KAAK,mBAAiBA;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,mBAAiB;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,WAAO,KAAK,mBAAiB;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,MAAE,KAAK,uBAAuB,KAAG,KAAK,wBAAwB,QAAQ,GAAE,EAAE,KAAK,kBAAkB,KAAG,KAAK,mBAAmB,QAAQ,GAAE,EAAE,KAAK,iBAAiB,KAAG,KAAK,kBAAkB,QAAQ,GAAE,KAAK,0BAAwB,MAAK,KAAK,qBAAmB,MAAK,KAAK,oBAAkB,MAAK,KAAK,aAAW;AAAA,EAAC;AAAA,EAAC,sBAAsBJ,IAAEC,IAAEC,KAAE;AAAC,UAAMC,KAAE,IAAI,YAAYF,EAAC,GAAEG,KAAE,IAAI,WAAWD,GAAE,MAAM,GAAEJ,KAAEI,GAAED,KAAG;AAAE,SAAK,qBAAmBK,GAAE,aAAaP,IAAE,EAAE,aAAY,IAAI,WAAWI,GAAE,QAAO,IAAEF,KAAEH,EAAC,CAAC,GAAEG,OAAGH;AAAE,UAAMO,MAAEH,GAAED,KAAG;AAAE,SAAK,oBAAkBK,GAAE,YAAYP,IAAE,EAAE,aAAY,IAAI,YAAYG,GAAE,QAAO,IAAED,KAAEI,GAAC,CAAC,GAAEJ,OAAGI;AAAE,UAAME,KAAE,KAAK,MAAM;AAAe,SAAK,0BAAwB,IAAIC,GAAET,IAAEQ,GAAE,sBAAsB,GAAEA,GAAE,cAAc,GAAE,EAAC,UAAS,KAAK,mBAAkB,GAAE,KAAK,iBAAiB;AAAA,EAAC;AAAC;;;ACAptP,IAAMO,KAAN,MAAM,WAAUC,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAEC,IAAEJ,KAAEK,IAAEC,IAAEC,KAAE,MAAK;AAAC,UAAMN,IAAEC,IAAEC,IAAEC,IAAEJ,KAAEK,IAAE,MAAK,IAAI,GAAE,KAAK,YAAUE,IAAE,KAAK,OAAK,eAAc,KAAK,cAAY,GAAE,KAAK,oBAAkB,OAAG,KAAK,yBAAuB,GAAE,KAAK,YAAU,oBAAI,OAAI,KAAK,aAAW,GAAE,KAAK,SAAO,WAAU,KAAK,sBAAoB,OAAG,KAAK,oBAAkB,GAAE,KAAK,UAAQ,oBAAI,OAAI,KAAK,aAAW,OAAG,KAAK,oBAAkB,OAAG,KAAK,cAAY,OAAG,KAAK,eAAa,OAAG,KAAK,aAAW,MAAK,KAAK,gBAAc,oBAAI,OAAI,KAAK,aAAW,OAAG,KAAK,cAAY,GAAE,KAAK,kBAAgBD,IAAE,KAAK,KAAGL,GAAE;AAAA,EAAE;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK;AAAA,EAAiB;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,qBAAmB,YAAY,IAAI,IAAE,KAAK,oBAAkBA;AAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK,sBAAoB,CAAC,KAAK,uBAAqB,YAAY,IAAI,IAAE,KAAK,oBAAkBA;AAAA,EAAE;AAAA,EAAC,IAAI,eAAc;AAAC,WAAM,cAAY,KAAK,UAAQ,aAAW,KAAK,UAAQ,gBAAc,KAAK;AAAA,EAAM;AAAA,EAAC,QAAQA,IAAE;AAAC,SAAK,eAAeA,EAAC,GAAE,KAAK,cAAc,GAAE,KAAK,MAAM,GAAE,KAAK,eAAa,OAAG,KAAK,aAAW;AAAA,EAAE;AAAA,EAAC,gBAAgBC,IAAE;AAAC,QAAIC,KAAE;AAAG,eAAUF,MAAKC,GAAE,KAAG,KAAK,UAAU,IAAID,EAAC,GAAE;AAAC,YAAMC,MAAE,KAAK,UAAU,IAAID,EAAC;AAAE,WAAK,0BAAwBC,IAAE,YAAWA,IAAE,SAAOM,GAAE,UAAQ,KAAK,QAAQ,IAAIP,EAAC,MAAI,KAAK,QAAQ,OAAOA,EAAC,GAAEE,KAAE,OAAID,IAAE,QAAQ,GAAE,KAAK,UAAU,OAAOD,EAAC,GAAE,KAAK;AAAA,IAAY;AAAC,MAAE,KAAK,SAAS,KAAG,KAAK,UAAU,WAAW,KAAK,IAAI,IAAG,MAAK,KAAK,sBAAsB,GAAEE,MAAG,KAAK,KAAK,iBAAiB,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK;AAAA,EAAU;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK,aAAW;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,mBAAa,KAAK,WAASM,GAAE,OAAO,IAAI,GAAE,GAAE,sBAAsB,KAAK,SAAS,GAAE,KAAK,YAAU,MAAK,KAAK,aAAW,GAAE,KAAK,yBAAuB,GAAE,KAAK,QAAQ,GAAE,KAAK,SAAO;AAAA,EAAW;AAAA,EAAC,UAAS;AAAC,WAAO,KAAG,EAAE,KAAK,gBAAc,KAAK,QAAQ,GAAE,KAAK,QAAM,MAAK;AAAA,EAAG;AAAA,EAAC,SAAQ;AAAC,MAAE,KAAK;AAAA,EAAW;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK;AAAA,EAAW;AAAA,EAAC,IAAI,cAAa;AAAC,YAAO,KAAK,yBAAuB,QAAM,KAAK,eAAa;AAAA,EAAE;AAAA,EAAC,eAAeP,IAAE;AAAC,QAAIC,KAAE;AAAG,QAAGD,IAAE;AAAC,YAAK,EAAC,iBAAgBE,IAAE,cAAaJ,IAAC,IAAEE,IAAEG,KAAE,KAAK,qBAAqBD,EAAC;AAAE,UAAGJ,OAAGA,IAAE,aAAW,GAAE;AAAC,cAAMC,KAAE,IAAI,YAAYD,GAAC;AAAE,mBAAUE,OAAKD,GAAE,MAAK,iBAAiBC,GAAC;AAAA,MAAC;AAAC,iBAAS,CAACD,IAAEC,GAAC,KAAIG,GAAE,MAAK,iBAAiBJ,EAAC,GAAEC,IAAE,SAAOM,GAAE,WAAS,KAAK,QAAQ,IAAIP,IAAEC,IAAE,OAAO,GAAEC,KAAE,OAAI,KAAK,0BAAwBD,IAAE,YAAW,KAAK,UAAU,IAAID,IAAEC,GAAC,GAAE,KAAK;AAAa,QAAE,KAAK,SAAS,KAAG,KAAK,UAAU,WAAW,KAAK,IAAI,IAAG,MAAK,KAAK,sBAAsB;AAAA,IAAC;AAAC,SAAK,oBAAkB;AAAG,eAAS,CAACD,IAAEG,EAAC,KAAI,KAAK,UAAU,CAAAA,GAAE,SAAOI,GAAE,WAAS,KAAK,oBAAkB;AAAI,IAAAL,MAAG,KAAK,KAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,kBAAkBF,IAAE;AAAC,SAAK,QAAM,EAAC,SAAQA,IAAE,mBAAmBA,KAAE;AAAC,MAAAA,IAAE,cAAc;AAAA,IAAC,GAAE,sBAAqB,MAAI,MAAE;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAM,aAAaA,EAAC;AAAE,UAAMI,KAAE,KAAK,cAAYJ,GAAE,aAAWA,GAAE,aAAYK,KAAE,KAAK,QAAM,KAAK,SAAOD,IAAEE,KAAE,KAAK,SAAO,KAAK,SAAOF,IAAEK,KAAE,CAAC,GAAE,CAAC;AAAE,IAAAT,GAAE,SAASS,IAAE,CAAC,KAAK,GAAE,KAAK,CAAC,CAAC;AAAE,UAAMC,MAAE,KAAK,WAAW;AAAkB,IAAAX,GAAEW,GAAC,GAAE,EAAEA,KAAEA,KAAED,EAAC,GAAEH,GAAEI,KAAEA,KAAE,KAAK,KAAGV,GAAE,WAAS,GAAG,GAAE,EAAEU,KAAEA,KAAE,CAACL,IAAEC,IAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAM,EAAC,KAAIN,GAAE,GAAE,UAASA,GAAE,GAAE,mBAAkBA,GAAE,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,sBAAsBA,IAAE;AAAC,QAAG,CAACA,GAAE;AAAO,UAAMC,KAAE,oBAAI;AAAI,IAAAD,GAAE,QAAS,CAAAA,QAAG;AAAC,MAAAC,GAAE,IAAID,GAAC,MAAIA,IAAE,QAAQ,GAAEC,GAAE,IAAID,GAAC;AAAA,IAAE,CAAE,GAAEA,GAAE,MAAM;AAAA,EAAC;AAAA,EAAC,qBAAqBA,IAAE;AAAC,UAAMC,KAAE,oBAAI,OAAIC,KAAE,oBAAI;AAAI,eAAUC,MAAKH,IAAE;AAAC,YAAMA,MAAE,KAAK,mBAAmBG,IAAED,EAAC;AAAE,iBAAUA,MAAKF,IAAE,UAAU,CAAAC,GAAE,IAAIC,IAAEF,GAAC;AAAA,IAAC;AAAC,WAAOC;AAAA,EAAC;AAAA,EAAC,mBAAmBD,IAAEC,IAAE;AAAC,QAAIC,KAAED,GAAE,IAAID,EAAC;AAAE,QAAGE,GAAE,QAAOA;AAAE,YAAO,IAAI,YAAYF,EAAC,EAAE,CAAC,GAAE;AAAA,MAAC,KAAKO,GAAE;AAAK,QAAAL,KAAE,IAAIM,GAAER,IAAE,KAAK,eAAe;AAAE;AAAA,MAAM,KAAKO,GAAE;AAAK,QAAAL,KAAE,IAAIQ,GAAEV,IAAE,KAAK,eAAe;AAAE;AAAA,MAAM,KAAKO,GAAE;AAAO,QAAAL,KAAE,IAAII,GAAEN,IAAE,KAAK,iBAAgB,IAAI;AAAE;AAAA,MAAM,KAAKO,GAAE;AAAO,QAAAL,KAAE,IAAIS,GAAEX,IAAE,KAAK,eAAe;AAAA,IAAC;AAAC,WAAOC,GAAE,IAAID,IAAEE,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,iBAAiBF,IAAE;AAAC,QAAG,CAAC,KAAK,UAAU,IAAIA,EAAC,EAAE;AAAO,UAAMC,KAAE,KAAK,UAAU,IAAID,EAAC;AAAE,SAAK,0BAAwBC,GAAE,YAAWA,GAAE,QAAQ,GAAE,KAAK,UAAU,OAAOD,EAAC,GAAE,KAAK;AAAA,EAAY;AAAC;AAAC,IAAMQ,KAAE,oBAAI;;;ACA34H,SAASI,GAAEC,IAAEC,IAAEC,IAAEC,IAAEJ,IAAEK,KAAE;AAAC,QAAK,EAAC,uBAAsBC,IAAE,uBAAsBC,IAAE,eAAcC,IAAE,qBAAoBC,IAAE,eAAcC,IAAE,qBAAoBC,GAAC,IAAEP;AAAE,MAAI,IAAE;AAAE,aAAUQ,MAAKX,GAAE,WAAU;AAAC,UAAK,CAACA,KAAEG,EAAC,IAAE,MAAIQ,GAAE,YAAUJ,KAAEE,IAAEG,KAAE,MAAID,GAAE,YAAUH,KAAEE,IAAEG,KAAEF,GAAE,UAAQP,OAAGA,OAAGO,GAAE;AAAO,SAAGE,KAAE,IAAE,GAAEF,GAAE,UAAQE,IAAEF,GAAE,UAAQA,GAAE,QAAMZ,GAAE,CAAC,IAAEY,GAAE,QAAMZ,GAAE,CAAC,IAAEA,GAAE,CAAC,GAAEY,GAAE,UAAQA,GAAE,QAAMZ,GAAE,CAAC,IAAEY,GAAE,QAAMZ,GAAE,CAAC,IAAEA,GAAE,CAAC,GAAEa,OAAIE,GAAE,OAAKH,GAAE,WAAST,KAAEF,MAAEC,KAAEE,IAAEQ,GAAE,WAASV,KAAED,MAAEE,KAAEC,OAAIQ,GAAE,WAASX,KAAEW,GAAE,WAASR,KAAGC,GAAE,cAAY,MAAIO,GAAE,YAAUN,KAAEC,OAAIK,GAAE,WAASA,GAAE,UAASA,GAAE,WAASA,GAAE,aAAWA,GAAE,WAAST,MAAGS,GAAE,WAASA,GAAE,QAAM,KAAGV,MAAGU,GAAE,WAASA,GAAE,SAAO,KAAGA,GAAE,QAAM,GAAEA,GAAE,WAASV,MAAGU,GAAE,WAASA,GAAE,QAAM,KAAGT,MAAGS,GAAE,WAASA,GAAE,SAAO,KAAGA,GAAE,SAAO;AAAA,EAAE;AAAC,EAAAX,GAAE,UAAU,SAAO,KAAG,MAAIA,GAAE,UAAU,WAASA,GAAE,OAAO,OAAK;AAAG;AAAC,IAAMI,KAAN,MAAO;AAAA,EAAC,YAAYD,IAAEW,KAAEC,IAAEhB,IAAEK,KAAEC,IAAE;AAAC,SAAK,WAASF,IAAE,KAAK,mBAAiBJ,IAAE,KAAK,QAAMK,KAAE,KAAK,sBAAoB,GAAE,KAAK,uBAAqB,GAAE,KAAK,cAAY,oBAAI,OAAI,KAAK,qBAAmB,oBAAI,OAAI,KAAK,aAAW,IAAID,GAAEW,KAAEC,IAAEd,EAAC,GAAE,KAAK,MAAI,KAAK,IAAI,KAAK,KAAGI,KAAE,GAAG,GAAE,KAAK,MAAI,KAAK,IAAI,KAAK,KAAGA,KAAE,GAAG;AAAE,eAAUJ,MAAKE,GAAE,YAAUD,MAAKD,GAAE,QAAQ,MAAK,mBAAmB,IAAIC,GAAE,IAAI,KAAG,KAAK,mBAAmB,IAAIA,GAAE,MAAKY,GAAEZ,GAAE,KAAK,WAAW,iBAAiB,CAAC;AAAA,EAAC;AAAA,EAAC,KAAKF,IAAE;AAAC,UAAMC,KAAE,KAAK;AAAW,aAASC,GAAEF,KAAE;AAAC,YAAME,MAAEF,IAAE,UAAQA,IAAE,UAASG,KAAEH,IAAE,UAAQA,IAAE,UAASc,MAAEZ,MAAEF,IAAE,OAAMe,KAAEZ,KAAEH,IAAE,QAAO,CAACD,IAAEK,KAAEC,IAAEC,EAAC,IAAEL,GAAE,YAAYC,KAAEC,IAAEW,KAAEC,EAAC;AAAE,eAAQR,KAAEH,KAAEG,MAAGD,IAAEC,KAAI,UAAQP,MAAED,IAAEC,OAAGK,IAAEL,OAAI;AAAC,cAAMD,KAAEE,GAAE,MAAMM,EAAC,EAAEP,GAAC;AAAE,mBAAUA,OAAKD,IAAE;AAAC,gBAAME,MAAED,IAAE,UAAQA,IAAE,UAASD,KAAEC,IAAE,UAAQA,IAAE,UAASI,MAAEH,MAAED,IAAE,OAAMK,KAAEN,KAAEC,IAAE;AAAO,cAAG,EAAEc,MAAEb,OAAGC,MAAEE,OAAGW,KAAEhB,MAAGI,KAAEE,IAAG,QAAM;AAAA,QAAE;AAAA,MAAC;AAAC,aAAM;AAAA,IAAE;AAAC,UAAMF,KAAE,YAAY,IAAI;AAAE,WAAK,KAAK,sBAAoB,KAAK,SAAS,QAAO,KAAK,uBAAsB,KAAK,uBAAqB,GAAE;AAAC,YAAMF,MAAE,KAAK,SAAS,KAAK,mBAAmB,GAAEa,MAAE,KAAK,eAAeb,IAAE,aAAa;AAAE,aAAK,KAAK,uBAAqBA,IAAE,QAAQ,QAAO,KAAK,wBAAuB;AAAC,YAAG,KAAK,uBAAqB,OAAK,MAAI,YAAY,IAAI,IAAEE,KAAEH,GAAE,QAAM;AAAG,cAAMe,KAAEd,IAAE,QAAQ,KAAK,oBAAoB;AAAE,YAAG,CAACc,GAAE,OAAO,KAAK;AAAS,QAAAhB,GAAEgB,IAAE,KAAK,KAAI,KAAK,KAAID,KAAE,KAAK,mBAAmB,IAAIC,GAAE,IAAI,GAAE,KAAK,KAAK;AAAE,cAAMX,MAAEW,GAAE;AAAO,YAAG,CAACX,IAAE,KAAK;AAAS,cAAK,EAAC,kBAAiBC,IAAE,qBAAoBC,IAAE,kBAAiBC,IAAE,qBAAoBC,GAAC,IAAEM;AAAE,mBAAUd,OAAKe,GAAE,WAAU;AAAC,cAAG,CAACf,IAAE,QAAQ;AAAS,gBAAMC,MAAEG,IAAE,MAAMJ,IAAE,SAAS;AAAE,cAAG,CAACC,IAAE,KAAK;AAAS,YAAED,IAAE,YAAUO,KAAEF,OAAIH,GAAEF,GAAC,MAAIA,IAAE,OAAKI,IAAE,OAAK,QAAGH,IAAE,OAAK;AAAA,QAAG;AAAC,YAAGG,IAAE,KAAK,YAAUJ,OAAKe,GAAE,WAAU;AAAC,cAAG,CAACf,IAAE,QAAQ;AAAS,cAAGA,IAAE,YAAUQ,KAAEF,GAAE;AAAS,cAAG,CAACF,IAAE,MAAMJ,IAAE,SAAS,EAAE,KAAK;AAAS,gBAAMC,MAAED,IAAE,UAAQA,IAAE,UAASE,MAAEF,IAAE,UAAQA,IAAE,UAASG,KAAEF,MAAED,IAAE,OAAMc,MAAEZ,MAAEF,IAAE,QAAO,CAACe,IAAEhB,IAAEM,IAAEE,EAAC,IAAE,KAAK,WAAW,YAAYN,KAAEC,KAAEC,IAAEW,GAAC;AAAE,mBAAQV,MAAEL,IAAEK,OAAGG,IAAEH,MAAI,UAAQH,MAAEc,IAAEd,OAAGI,IAAEJ,OAAI;AAAC,iBAAK,WAAW,MAAMG,GAAC,EAAEH,GAAC,EAAE,KAAKD,GAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,eAAeA,IAAE;AAAC,UAAMC,KAAE,KAAK,YAAY,IAAID,EAAC;AAAE,QAAGC,GAAE,QAAOA;AAAE,UAAMC,KAAE,KAAK,OAAMa,KAAE,KAAK,iBAAiB,mBAAmBf,EAAC,GAAED,KAAEgB,GAAE,eAAe,oBAAmBb,EAAC,MAAIA,GAAE;AAAM,QAAIE,MAAEW,GAAE,eAAe,2BAA0Bb,EAAC;AAAE,IAAAE,QAAIA,GAAE,SAAOA,MAAEL,KAAEK,GAAE,MAAIA,GAAE;AAAU,QAAIC,KAAEU,GAAE,eAAe,2BAA0Bb,EAAC;AAAE,IAAAG,OAAID,GAAE,SAAOC,KAAEN,KAAEK,GAAE,MAAIA,GAAE;AAAU,UAAME,KAAES,GAAE,cAAc,kBAAiBb,EAAC,GAAEK,KAAEQ,GAAE,cAAc,yBAAwBb,EAAC,GAAEM,KAAEO,GAAE,cAAc,kBAAiBb,EAAC,GAAEO,KAAEM,GAAE,cAAc,yBAAwBb,EAAC,GAAEQ,KAAE,EAAC,kBAAiBK,GAAE,eAAe,sBAAqBb,EAAC,GAAE,qBAAoBa,GAAE,eAAe,yBAAwBb,EAAC,GAAE,kBAAiBa,GAAE,eAAe,sBAAqBb,EAAC,GAAE,qBAAoBa,GAAE,eAAe,yBAAwBb,EAAC,GAAE,uBAAsBE,KAAE,uBAAsBC,IAAE,qBAAoBE,IAAE,eAAcD,IAAE,qBAAoBG,IAAE,eAAcD,GAAC;AAAE,WAAO,KAAK,YAAY,IAAIR,IAAEU,EAAC,GAAEA;AAAA,EAAC;AAAC;;;ACAjsH,SAASM,GAAEC,IAAEC,IAAE;AAAC,MAAGD,GAAE,WAASC,GAAE,SAAS,QAAOD,GAAE,WAASC,GAAE;AAAS,QAAMF,KAAEC,GAAE,KAAK,KAAIE,KAAED,GAAE,KAAK;AAAI,SAAOF,GAAE,QAAMG,GAAE,QAAMH,GAAE,QAAMG,GAAE,QAAMH,GAAE,QAAMG,GAAE,QAAMH,GAAE,QAAMG,GAAE,QAAMH,GAAE,MAAIG,GAAE,MAAIH,GAAE,MAAIG,GAAE,MAAIH,GAAE,MAAIG,GAAE,MAAIH,GAAE,MAAIG,GAAE,MAAIF,GAAE,QAAMC,GAAE,QAAMD,GAAE,QAAMC,GAAE,QAAMD,GAAE,QAAMC,GAAE;AAAK;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,YAAYF,IAAEC,IAAEF,IAAEG,IAAEC,IAAEC,KAAE;AAAC,SAAK,gBAAcJ,IAAE,KAAK,oBAAkBC,IAAE,KAAK,sBAAoBF,IAAE,KAAK,4BAA0BG,IAAE,KAAK,qBAAmBC,IAAE,KAAK,kBAAgBC,KAAE,KAAK,gBAAc,MAAK,KAAK,yBAAuB,OAAG,KAAK,gBAAc,MAAK,KAAK,yBAAuB,OAAG,KAAK,cAAY,MAAK,KAAK,uBAAqB,OAAG,KAAK,WAAS;AAAA,EAAE;AAAA,EAAC,cAAcJ,IAAEC,IAAE;AAAC,SAAK,iBAAeD,MAAG,KAAK,kBAAgBC,MAAG,KAAK,QAAQ,GAAE,KAAK,eAAaD,IAAE,KAAK,gBAAcC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,gBAAc,MAAK,KAAK,yBAAuB,OAAG,KAAK,gBAAc,MAAK,KAAK,yBAAuB,OAAG,KAAK,cAAY,MAAK,KAAK,uBAAqB,OAAG,KAAK,WAAS;AAAA,EAAE;AAAA,EAAC,SAASD,IAAE;AAAC,QAAG,KAAK,kBAAgB,KAAK,gBAAc,KAAK,oBAAoB,IAAG,CAAC,KAAK,wBAAuB;AAAC,YAAMC,KAAE,YAAY,IAAI;AAAE,UAAG,CAAC,KAAK,cAAc,KAAKD,EAAC,EAAE,QAAM;AAAG,UAAG,KAAK,yBAAuB,MAAG,OAAKA,KAAE,KAAK,IAAI,GAAEA,MAAG,YAAY,IAAI,IAAEC,GAAE,GAAG,QAAM;AAAA,IAAE;AAAC,QAAG,KAAK,kBAAgB,KAAK,gBAAc,KAAK,oBAAoB,KAAK,cAAc,eAAc,KAAK,cAAa,KAAK,aAAa,IAAG,CAAC,KAAK,wBAAuB;AAAC,YAAMA,KAAE,YAAY,IAAI;AAAE,UAAG,CAAC,KAAK,cAAc,KAAKD,EAAC,EAAE,QAAM;AAAG,UAAG,KAAK,yBAAuB,MAAG,OAAKA,KAAE,KAAK,IAAI,GAAEA,MAAG,YAAY,IAAI,IAAEC,GAAE,GAAG,QAAM;AAAA,IAAE;AAAC,QAAG,KAAK,gBAAc,KAAK,cAAY,KAAK,kBAAkB,IAAG,CAAC,KAAK,sBAAqB;AAAC,YAAMA,KAAE,YAAY,IAAI;AAAE,UAAG,CAAC,KAAK,YAAY,KAAKD,EAAC,EAAE,QAAM;AAAG,UAAG,KAAK,uBAAqB,MAAG,OAAKA,KAAE,KAAK,IAAI,GAAEA,MAAG,YAAY,IAAI,IAAEC,GAAE,GAAG,QAAM;AAAA,IAAE;AAAC,WAAO,KAAK,WAAS,OAAG;AAAA,EAAE;AAAA,EAAC,sBAAqB;AAAC,UAAMD,KAAE,KAAK,kBAAkB;AAAc,aAAQD,KAAE,GAAEA,KAAEC,GAAE,QAAOD,MAAI;AAAC,YAAME,MAAED,GAAED,EAAC;AAAE,eAAQC,KAAE,GAAEA,KAAEC,IAAE,cAAc,QAAOD,MAAI;AAAC,cAAMD,MAAEE,IAAE,cAAcD,EAAC;AAAE,mBAAUA,MAAKD,IAAE,YAAY,CAAAC,GAAE,uBAAqB;AAAA,MAAE;AAAA,IAAC;AAAC,UAAMC,KAAE,CAAC;AAAE,QAAIC,KAAE,GAAEC,KAAE;AAAE,UAAMC,MAAE,KAAK;AAAgB,aAASC,GAAEA,KAAE;AAAC,UAAIC;AAAE,YAAMC,KAAE,YAAY,IAAI;AAAE,aAAKJ,KAAEH,GAAE,QAAOG,MAAID,KAAE,GAAE;AAAC,cAAMH,KAAEC,GAAEG,EAAC,GAAEK,KAAET,GAAE;AAAc,YAAG,CAACK,IAAEI,EAAC,GAAE;AAAC,UAAAP,GAAEE,EAAC,MAAIF,GAAEE,EAAC,IAAE,EAAC,eAAcK,IAAE,SAAQ,CAAC,EAAC;AAAG;AAAA,QAAQ;AAAC,QAAAP,GAAEE,EAAC,IAAEF,GAAEE,EAAC,KAAG,EAAC,eAAcK,IAAE,SAAQ,CAAC,EAAC;AAAE,cAAMC,KAAER,GAAEE,EAAC;AAAE,eAAKD,KAAEH,GAAE,cAAc,QAAOG,MAAI;AAAC,cAAGI,MAAEP,GAAE,cAAcG,EAAC,GAAEA,KAAE,OAAK,MAAI,YAAY,IAAI,IAAEK,KAAEF,IAAE,QAAM;AAAG,cAAIL,KAAE,MAAKC,MAAE,OAAGE,KAAE;AAAG,qBAAUJ,OAAKO,IAAE,YAAY,KAAG,CAACH,MAAG,CAACF,KAAE;AAAC,kBAAMC,KAAEH,IAAE;AAAK,aAAC,CAACC,MAAGE,GAAE,cAAYA,GAAE,qBAAmB,CAACD,SAAKD,KAAED,MAAGG,GAAE,qBAAmBA,GAAE,gBAAcC,KAAE,OAAID,GAAE,eAAaD,MAAE;AAAA,UAAI;AAAC,cAAGD,GAAE,uBAAqB,MAAGG,IAAE;AAAC,YAAAM,GAAE,QAAQ,KAAKT,EAAC,GAAEM,IAAE,OAAK;AAAG,uBAAUN,MAAKM,IAAE,MAAM,CAAAN,GAAE,OAAK;AAAA,UAAE,MAAM,CAAAM,IAAE,OAAK;AAAA,QAAE;AAAA,MAAC;AAAC,iBAAUN,MAAKC,GAAE,CAAAD,GAAE,QAAQ,KAAKD,EAAC;AAAE,aAAM;AAAA,IAAE;AAAC,UAAMO,MAAE,KAAK;AAAmB,WAAM,EAAC,MAAKD,IAAE,IAAI,gBAAe;AAAC,aAAOJ,GAAE,KAAKK,GAAC;AAAA,IAAC,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,UAAML,KAAE,KAAK,2BAA0BF,KAAE,KAAK;AAAc,QAAIG,KAAE;AAAE,aAASE,IAAEJ,IAAED,KAAE;AAAC,YAAMG,KAAEF,GAAE;AAAQ,iBAAS,CAACC,KAAEG,GAAC,KAAIF,GAAE,CAAAC,GAAEC,KAAEL,GAAC;AAAE,MAAAE,GAAED,IAAED,GAAC;AAAE,iBAAUE,OAAKD,GAAE,cAAc,CAAAI,IAAEH,KAAEF,GAAC;AAAA,IAAC;AAAC,WAAM,EAAC,KAAKE,KAAE;AAAC,YAAME,KAAE,YAAY,IAAI;AAAE,aAAKD,KAAEH,GAAE,QAAOG,MAAI;AAAC,YAAG,YAAY,IAAI,IAAEC,KAAEF,IAAE,QAAM;AAAG,cAAMI,KAAEN,GAAEG,EAAC;AAAE,YAAG,EAAEG,GAAE,UAAU,EAAE;AAAS,QAAAD,IAAEC,IAAE,YAAY,IAAI,CAAC;AAAA,MAAC;AAAC,aAAM;AAAA,IAAE,EAAC;AAAA,EAAC;AAAC;AAAC,SAASF,GAAEH,IAAED,IAAE;AAAC,aAAUG,MAAKF,IAAE;AAAC,UAAMA,KAAEE,GAAE;AAAO,eAAUA,MAAKF,GAAE,OAAM;AAAC,YAAMG,KAAED,GAAE,gBAAc,MAAG,IAAE;AAAG,MAAAA,GAAE,gBAAcC,OAAIJ,KAAEG,GAAE,aAAWD,KAAGC,GAAE,eAAa,KAAK,IAAI,KAAK,IAAIA,GAAE,cAAa,CAAC,GAAE,CAAC,GAAEA,GAAE,YAAUH,IAAEG,GAAE,gBAAcF,GAAE,QAAME,GAAE,OAAK,IAAE;AAAA,IAAC;AAAA,EAAC;AAAC;;;ACAj4G,IAAMQ,KAAE;AAAR,IAAWC,KAAE;AAAb,IAAeC,KAAE;AAAG,IAAMC,MAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAE;AAAC,SAAK,iBAAeF,IAAE,KAAK,gBAAcC,IAAE,KAAK,gBAAcC,IAAE,KAAK,SAAO,oBAAI,OAAI,KAAK,2BAAyB,oBAAI;AAAA,EAAG;AAAA,EAAC,IAAI,gBAAe;AAAC,WAAO,EAAE,KAAK,uBAAuB,MAAI,KAAK,0BAAwB,KAAK,8BAA8B,IAAG,KAAK;AAAA,EAAuB;AAAA,EAAC,IAAIF,IAAEC,IAAE;AAAC,SAAK,0BAAwB;AAAK,QAAIE,KAAE,KAAK,OAAO,IAAIH,GAAE,EAAE;AAAE,IAAAG,OAAIA,KAAE,EAAC,SAAQ,oBAAI,MAAG,GAAE,KAAK,OAAO,IAAIH,GAAE,IAAGG,EAAC;AAAG,UAAMJ,MAAE,oBAAI;AAAI,QAAGE,GAAE,YAAUC,MAAKD,GAAE,CAAAE,GAAE,QAAQ,IAAID,EAAC,MAAIH,IAAE,IAAIG,IAAEC,GAAE,QAAQ,IAAID,EAAC,CAAC,GAAEC,GAAE,QAAQ,OAAOD,EAAC;AAAA,QAAQ,YAAS,CAACA,IAAEN,GAAC,KAAII,GAAE,UAAU,CAAAG,GAAE,QAAQ,IAAID,EAAC,MAAIH,IAAE,IAAIG,IAAEC,GAAE,QAAQ,IAAID,EAAC,CAAC,GAAEC,GAAE,QAAQ,OAAOD,EAAC;AAAG,SAAK,eAAeH,GAAC;AAAE,UAAMK,KAAEJ,GAAE,SAAQK,KAAE,oBAAI;AAAI,eAAS,CAACC,IAAEC,EAAC,KAAIH,IAAE;AAAC,UAAIJ,MAAEO,GAAE;AAAO,UAAGP,OAAGJ,IAAE;AAAC,YAAIK,KAAE,KAAK;AAAe,WAAE;AAAC,UAAAA,MAAG,GAAED,OAAG;AAAA,QAAC,SAAOA,MAAEH,MAAGI,KAAEH;AAAG,cAAMF,MAAE,IAAIM,GAAE,KAAK,gBAAe,KAAK,gBAAeD,EAAC;AAAE,QAAAI,GAAE,IAAIC,IAAE,EAAC,MAAKC,IAAE,OAAMX,IAAC,CAAC,GAAEO,GAAE,QAAQ,IAAIG,IAAE,EAAC,MAAKC,IAAE,OAAMX,IAAC,CAAC;AAAE,mBAAUI,OAAKO,GAAE,CAAAX,IAAE,QAAQI,IAAE,OAAMA,IAAE,KAAK,EAAE,KAAKA,GAAC;AAAA,MAAC,MAAM,CAAAK,GAAE,IAAIC,IAAE,EAAC,MAAKC,GAAC,CAAC,GAAEJ,GAAE,QAAQ,IAAIG,IAAE,EAAC,MAAKC,GAAC,CAAC;AAAA,IAAC;AAAC,SAAK,YAAYP,GAAE,KAAII,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBJ,IAAE;AAAC,SAAK,0BAAwB;AAAK,eAAS,CAACC,IAAEC,EAAC,KAAI,KAAK,QAAO;AAAC,YAAMC,KAAE,oBAAI;AAAI,iBAAUF,MAAKD,GAAE,CAAAE,GAAE,QAAQ,IAAID,EAAC,MAAIE,GAAE,IAAIF,IAAEC,GAAE,QAAQ,IAAID,EAAC,CAAC,GAAEC,GAAE,QAAQ,OAAOD,EAAC;AAAG,WAAK,eAAeE,EAAC,GAAE,MAAID,GAAE,QAAQ,QAAM,KAAK,OAAO,OAAOD,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,WAAWD,IAAE;AAAC,SAAK,0BAAwB;AAAK,UAAMC,KAAE,KAAK,OAAO,IAAID,GAAE,EAAE;AAAE,QAAG,CAACC,GAAE;AAAO,UAAMC,KAAE,oBAAI;AAAI,eAAS,CAACC,IAAEP,GAAC,KAAII,GAAE,QAAQ,CAAAC,GAAE,QAAQ,IAAIE,EAAC,MAAID,GAAE,IAAIC,IAAEF,GAAE,QAAQ,IAAIE,EAAC,CAAC,GAAEF,GAAE,QAAQ,OAAOE,EAAC;AAAG,SAAK,eAAeD,EAAC,GAAE,MAAID,GAAE,QAAQ,QAAM,KAAK,OAAO,OAAOD,GAAE,EAAE;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAE;AAAC,eAAS,CAACC,IAAE,EAAC,MAAKC,GAAC,CAAC,KAAIF,GAAE,YAAUA,OAAKE,IAAE;AAAC,YAAMA,KAAEF,IAAE,QAAOG,KAAED,GAAE,aAAYN,MAAEO,GAAE,SAAO;AAAE,eAAQF,KAAE,GAAEA,KAAEL,KAAEK,KAAI,KAAGE,GAAEF,EAAC,MAAID,KAAE;AAAC,QAAAG,GAAEF,EAAC,IAAEE,GAAEP,GAAC;AAAE;AAAA,MAAK;AAAC,UAAGO,GAAE,SAAOP,KAAE,MAAIA,KAAE;AAAC,cAAMI,MAAE,KAAK,yBAAyB,IAAIC,EAAC;AAAE,QAAAD,IAAE,OAAOE,EAAC,GAAE,MAAIF,IAAE,QAAM,KAAK,yBAAyB,OAAOC,EAAC;AAAA,MAAC;AAAC,MAAAD,IAAE,SAAO;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,QAAG,MAAIA,GAAE,KAAK;AAAO,UAAMC,KAAE,KAAK;AAAc,eAAUH,MAAKG,GAAE,CAAAH,GAAE,cAAYA,GAAE,IAAI,UAAQC,GAAE,SAAOD,GAAE,IAAI,UAAQC,GAAE,SAAO,CAACD,GAAE,IAAI,OAAOC,EAAC,KAAG,KAAK,cAAcD,IAAEC,IAAEC,EAAC;AAAE,eAAS,CAACN,KAAEC,EAAC,KAAIK,GAAE,YAAUD,MAAKJ,GAAE,KAAG,EAAEI,GAAE,MAAM,GAAE;AAAC,YAAMD,KAAE,KAAK,cAAc;AAAE,MAAAC,GAAE,SAAOD,IAAEA,GAAE,YAAY,KAAKC,EAAC;AAAE,UAAIC,KAAE,KAAK,yBAAyB,IAAIN,GAAC;AAAE,MAAAM,OAAIA,KAAE,oBAAI,OAAI,KAAK,yBAAyB,IAAIN,KAAEM,EAAC,IAAGA,GAAE,IAAIF,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEE,IAAEN,KAAE;AAAC,QAAGI,GAAE,IAAI,QAAME,GAAE,OAAM;AAAC,YAAMD,KAAED,GAAE,IAAI,QAAME,GAAE;AAAM,UAAGF,GAAE,IAAI,OAAKC,OAAIC,GAAE,OAAKF,GAAE,IAAI,OAAKC,OAAIC,GAAE,IAAI;AAAA,IAAM;AAAC,QAAGA,GAAE,QAAMF,GAAE,IAAI,OAAM;AAAC,YAAMC,KAAEC,GAAE,QAAMF,GAAE,IAAI;AAAM,UAAGE,GAAE,OAAKD,OAAID,GAAE,IAAI,OAAKE,GAAE,OAAKD,OAAID,GAAE,IAAI,IAAI;AAAA,IAAM;AAAC,QAAGE,GAAE,OAAOF,GAAE,GAAG,GAAE;AAAC,iBAAUC,MAAKD,GAAE,cAAc,MAAK,cAAcC,IAAEC,IAAEN,GAAC;AAAE;AAAA,IAAM;AAAC,UAAMC,KAAE,oBAAI;AAAI,eAAS,CAACC,IAAEC,GAAC,KAAIH,KAAE;AAAC,YAAMA,MAAE,CAAC;AAAE,iBAAUK,MAAKF,KAAE;AAAC,cAAMF,KAAEI,GAAE,KAAK,gBAAeA,GAAE,OAAMC,GAAE,OAAMA,GAAE,KAAIF,GAAE,IAAI,OAAMA,GAAE,IAAI,GAAG,GAAEF,MAAEG,GAAE,KAAK,gBAAeA,GAAE,OAAMC,GAAE,OAAMA,GAAE,KAAIF,GAAE,IAAI,OAAMA,GAAE,IAAI,GAAG;AAAE,QAAAH,MAAG,KAAGA,KAAE,KAAK,kBAAgBC,OAAG,KAAGA,MAAE,KAAK,kBAAgBF,IAAE,KAAK,EAAC,QAAOK,IAAE,cAAaJ,IAAE,cAAaC,IAAC,CAAC;AAAA,MAAC;AAAC,YAAMM,KAAE,CAAC,GAAEC,KAAEL,GAAE,IAAI,QAAME,GAAE,QAAM,IAAE,KAAGF,GAAE,IAAI,QAAME,GAAE,OAAMI,KAAE,KAAK,OAAO,IAAIN,GAAE,EAAE,EAAE,QAAQ,IAAIF,EAAC;AAAE,UAAGQ,IAAE;AAAC,cAAMN,MAAEM,GAAE;AAAK,mBAAUJ,MAAKN,KAAE;AAAC,cAAIO,IAAEP,MAAE;AAAG,gBAAMC,KAAEK,GAAE,cAAaJ,MAAEI,GAAE;AAAa,UAAAC,KAAE,EAAEG,GAAE,KAAK,IAAEA,GAAE,MAAM,QAAQT,IAAEC,GAAC,IAAEE;AAAE,gBAAMD,MAAEG,GAAE,QAAOK,KAAER,IAAE;AAAK,qBAAUC,OAAKG,GAAE,KAAGI,OAAIP,IAAE,QAAM,KAAK,IAAIH,KAAEG,IAAE,KAAK,KAAGK,MAAG,KAAK,IAAIP,MAAEE,IAAE,KAAK,KAAGK,IAAE;AAAC,kBAAMJ,KAAED,IAAE;AAAO,YAAAD,IAAE,SAAOE,IAAEA,GAAE,YAAY,KAAKF,GAAC,GAAEH,MAAE;AAAG;AAAA,UAAK;AAAC,UAAAA,OAAGQ,GAAE,KAAKL,GAAC;AAAA,QAAC;AAAA,MAAC;AAAC,MAAAK,GAAE,SAAO,KAAGP,GAAE,IAAIC,IAAEM,EAAC;AAAA,IAAC;AAAC,eAAUH,MAAKD,GAAE,cAAc,MAAK,cAAcC,IAAEC,IAAEL,EAAC;AAAA,EAAC;AAAA,EAAC,gCAA+B;AAAC,UAAMG,KAAE,KAAK,0BAAyBC,KAAE,IAAI,MAAMD,GAAE,IAAI;AAAE,QAAIE,IAAEC,KAAE;AAAE,eAAS,CAACP,KAAEC,EAAC,KAAIG,IAAE;AAAC,YAAMA,MAAE,IAAI,MAAMH,GAAE,IAAI;AAAE,MAAAK,KAAE;AAAE,iBAAUD,MAAKJ,GAAE,CAAAG,IAAEE,IAAG,IAAED;AAAE,MAAAA,GAAEE,EAAC,IAAE,EAAC,eAAcP,KAAE,eAAcI,IAAC,GAAEG;AAAA,IAAG;AAAC,WAAOF;AAAA,EAAC;AAAC;;;ACAjsG,IAAMO,KAAE;AAAR,IAAW,IAAE;AAAK,IAAMC,KAAN,cAAgB,EAAC;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,UAAM,GAAE,KAAK,kBAAgBD,IAAE,KAAK,gBAAc,oBAAI,OAAI,KAAK,aAAW,EAAC,OAAM,GAAE,UAAS,GAAE,QAAO,CAAC,GAAE,CAAC,GAAE,MAAK,CAAC,GAAE,CAAC,EAAC,GAAE,KAAK,sBAAoB,EAAC,OAAM,GAAE,UAAS,GAAE,QAAO,CAAC,GAAE,CAAC,GAAE,MAAK,CAAC,GAAE,CAAC,EAAC,GAAE,KAAK,aAAW,OAAG,KAAK,oBAAkB,IAAIE,IAAE,MAAKD,IAAG,MAAI,IAAIE,IAAE,GAAE,KAAK,qBAAmB,IAAIC,GAAEH,IAAE,KAAK,mBAAmB,CAACD,KAAEC,KAAEG,OAAI,IAAIC,GAAEL,KAAEC,KAAEG,IAAE,KAAK,iBAAgB,KAAK,OAAM,KAAK,WAAW,QAAQ,GAAI,CAACJ,KAAEC,QAAI;AAAC,MAAAD,IAAE,sBAAoB,MAAGA,IAAE,oBAAkBC,KAAEG,GAAEJ,KAAEC,KAAE,IAAE,GAAED,IAAE,cAAY,MAAGA,IAAE,cAAc;AAAA,IAAC,GAAI,CAACA,KAAEC,QAAI,KAAK,gBAAgB,mBAAmBD,IAAE,aAAa,EAAE,IAAE,KAAK,gBAAgB,mBAAmBC,IAAE,aAAa,EAAE,GAAI,CAAAD,QAAG;AAAC,YAAMC,MAAE,KAAK,gBAAgB,mBAAmBD,GAAC;AAAE,UAAG,KAAK,QAAM,IAAEC,IAAE,WAAS,KAAK,QAAM,KAAGA,IAAE,QAAQ,QAAM;AAAG,YAAMG,KAAEH,IAAE,kBAAkB,YAAY;AAAE,aAAM,CAACG,MAAGA,GAAE,SAAS,MAAI,EAAE;AAAA,IAAI,CAAE;AAAA,EAAC;AAAA,EAAC,QAAQJ,IAAE;AAAC,IAAAA,GAAE,cAAY,OAAG,KAAK,cAAc,IAAIA,IAAEA,GAAE,GAAG,mBAAmB,MAAI;AAAC,WAAK,kBAAkB,IAAIA,EAAC,GAAE,KAAK,iBAAiB;AAAA,IAAC,CAAE,CAAC,GAAE,KAAK,kBAAkB,IAAIA,EAAC,GAAE,KAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,UAAMC,KAAE,KAAK,cAAc,IAAID,EAAC;AAAE,IAAAC,OAAI,KAAK,kBAAkB,WAAWD,EAAC,GAAE,KAAK,iBAAiB,GAAEC,GAAE,OAAO,GAAE,KAAK,cAAc,OAAOD,EAAC;AAAA,EAAE;AAAA,EAAC,OAAOA,IAAEC,IAAE;AAAC,WAAO,KAAK,QAAMD,IAAE,KAAK,aAAW,EAAC,OAAMC,GAAE,OAAM,UAASA,GAAE,UAAS,QAAO,CAACA,GAAE,OAAO,CAAC,GAAEA,GAAE,OAAO,CAAC,CAAC,GAAE,MAAK,CAACA,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAK,CAAC,CAAC,EAAC,GAAE,KAAK,mBAAmB,GAAE,KAAK;AAAA,EAAU;AAAA,EAAC,mBAAkB;AAAC,SAAK,aAAW,OAAG,KAAK,mBAAmB,QAAQ,GAAE,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,aAAW,OAAG,KAAK,oBAAkB,MAAK,KAAK,mBAAmB,QAAQ,GAAE,KAAK,cAAc,QAAS,CAAAD,OAAGA,GAAE,OAAO,CAAE,GAAE,KAAK,cAAc,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,UAAQ,KAAK,kBAAgB,KAAK,WAAW,KAAK,CAAC,MAAI,KAAK,oBAAoB,KAAK,CAAC,KAAG,KAAK,WAAW,KAAK,CAAC,MAAI,KAAK,oBAAoB,KAAK,CAAC,KAAG,KAAK,WAAW,UAAQ,KAAK,oBAAoB,SAAO,KAAK,WAAW,aAAW,KAAK,oBAAoB;AAAA,EAAQ;AAAA,EAAC,kBAAkBA,IAAE;AAAC,SAAK,kBAAkB,kBAAkBA,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,SAAK,cAAY,CAAC,KAAK,UAAQ,KAAK,mBAAmB,YAAU,KAAK,iBAAe,KAAK,OAAM,KAAK,oBAAoB,OAAO,CAAC,IAAE,KAAK,WAAW,OAAO,CAAC,GAAE,KAAK,oBAAoB,OAAO,CAAC,IAAE,KAAK,WAAW,OAAO,CAAC,GAAE,KAAK,oBAAoB,WAAS,KAAK,WAAW,UAAS,KAAK,oBAAoB,QAAM,KAAK,WAAW,OAAM,KAAK,oBAAoB,KAAK,CAAC,IAAE,KAAK,WAAW,KAAK,CAAC,GAAE,KAAK,oBAAoB,KAAK,CAAC,IAAE,KAAK,WAAW,KAAK,CAAC,GAAE,KAAK,mBAAmB,QAAQ,IAAG,KAAK,mBAAmB,cAAc,KAAK,WAAW,KAAK,CAAC,GAAE,KAAK,WAAW,KAAK,CAAC,CAAC,GAAE,KAAK,aAAW,KAAK,mBAAmB,SAAS,CAAC,GAAE,KAAK,cAAY,KAAK,sBAAsB;AAAA,EAAE;AAAA,EAAC,wBAAuB;AAAC,MAAE,KAAK,yBAAyB,KAAG,aAAa,KAAK,yBAAyB,GAAE,KAAK,4BAA0B,WAAY,MAAI;AAAC,WAAK,4BAA0B,MAAK,KAAK,KAAK,eAAe;AAAA,IAAC,IAAI,IAAEF,MAAGG,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,MAAE,KAAK,yBAAyB,MAAI,aAAa,KAAK,yBAAyB,GAAE,KAAK,4BAA0B,OAAM,KAAK,KAAK,YAAY;AAAA,EAAC;AAAC;;;ACAx3G,IAAMK,KAAN,cAAgBC,GAAC;AAAA,EAAC,oBAAmB;AAAC,WAAM,EAAC,KAAIC,GAAE,GAAE,UAASA,GAAE,EAAC;AAAA,EAAC;AAAC;;;ACAqf,IAAMC,KAAE;AAAK,SAASC,GAAEC,IAAEC,IAAE;AAAC,MAAGD,IAAE;AAAC,UAAME,KAAEF,GAAE,kBAAkB,YAAY;AAAE,QAAG,CAACE,MAAGA,GAAE,SAAS,MAAI,EAAE,SAAO,WAASF,GAAE,WAASA,GAAE,UAAQC,KAAEH,QAAK,WAASE,GAAE,WAASA,GAAE,WAASC,KAAEH,IAAG,QAAM;AAAA,EAAE;AAAC,SAAM;AAAE;AAAC,IAAMK,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYJ,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,mBAAiB,CAAC,GAAE,KAAK,oBAAkB,oBAAI;AAAA,EAAG;AAAA,EAAC,UAAS;AAJz9B,YAAAK;AAI09B,SAAK,kBAAkB,IAAE,UAAK,kBAAL,mBAAoB,WAAU,KAAK,gBAAc,OAAKA,MAAA,KAAK,iBAAL,gBAAAA,IAAmB,WAAU,KAAK,eAAa,MAAK,EAAE,KAAK,YAAY,MAAI,KAAK,aAAa,MAAM,GAAE,KAAK,eAAa,OAAM,KAAK,mBAAiB,MAAK,KAAK,mBAAiB,CAAC,GAAE,KAAK,kBAAkB,MAAM;AAAA,EAAC;AAAA,EAAC,kBAAkBL,IAAEM,KAAEF,IAAE;AAAC,QAAG,KAAK,gBAAcJ,IAAE,KAAK,eAAaM,KAAE,KAAK,mBAAiBF,IAAE,EAAE,KAAK,YAAY,GAAE;AAAC,YAAMJ,MAAE,IAAIO,GAAE,KAAK,kBAAiB,KAAK,QAAQ;AAAE,MAAAP,IAAE,GAAG,cAAc,MAAI;AAAC,aAAK,KAAK,YAAY,GAAE,KAAK,cAAc;AAAA,MAAC,CAAE,GAAEA,IAAE,GAAG,iBAAiB,MAAI;AAAC,aAAK,KAAK,eAAe,GAAE,KAAK,cAAc;AAAA,MAAC,CAAE,GAAE,KAAK,eAAaA;AAAA,IAAC;AAAC,MAAE,KAAK,YAAY,EAAE,kBAAgBI;AAAA,EAAC;AAAA,EAAC,gBAAgBJ,IAAE;AAJloD;AAImoD,eAAK,kBAAL,mBAAoB,WAAU,KAAK,gBAAcA;AAAA,EAAC;AAAA,EAAC,kBAAkBC,IAAE;AAAC,MAAE,KAAK,YAAY,KAAG,KAAK,aAAa,kBAAkBA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQD,IAAE;AAAC,UAAMC,KAAE,EAAE;AAAE,WAAO,KAAK,kBAAkB,IAAID,IAAEC,EAAC,GAAE,KAAK,cAAc,GAAEA,GAAE;AAAA,EAAO;AAAA,EAAC,wBAAuB;AAAC,eAAUD,MAAK,KAAK,SAAS,CAAAA,GAAE,eAAa;AAAA,EAAE;AAAA,EAAC,mBAAmBA,IAAE;AAAC,WAAM,EAAC,GAAG,MAAM,mBAAmBA,EAAC,GAAE,YAAW,MAAK,YAAW,MAAK,eAAc,IAAG,aAAY,KAAK,cAAa,cAAa,KAAK,eAAc,aAAY,CAAC,CAAC,KAAK,eAAc;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,KAAC,KAAK,WAASA,GAAE,cAAY,EAAE,OAAKA,GAAE,cAAY,EAAE,SAAO,WAAS,KAAK,iBAAe,MAAM,SAASA,EAAC;AAAA,EAAC;AAAA,EAAC,SAASC,IAAE;AAAC,WAAO,MAAM,SAASA,EAAC,GAAE,EAAE,KAAK,YAAY,IAAE,KAAK,aAAa,QAAQA,EAAC,IAAEA,GAAE,cAAY,MAAG,KAAK,cAAc,GAAEA;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,WAAO,EAAE,KAAK,YAAY,KAAG,KAAK,aAAa,WAAWA,EAAC,GAAE,KAAK,cAAc,GAAE,MAAM,YAAYA,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeD,IAAE;AAAC,UAAK,EAAC,WAAUC,GAAC,IAAED;AAAE,QAAGC,OAAI,EAAE,OAAM;AAAC,UAAG,KAAK,UAAUD,EAAC,GAAE,KAAK,kBAAkB,OAAK,GAAE;AAAC,QAAAA,GAAE,YAAU,EAAE;AAAQ,cAAME,KAAEF,GAAE,QAAQ,QAAQ;AAAW,QAAAE,GAAE,KAAKF,EAAC,GAAE,KAAK,UAAUA,EAAC,GAAEE,GAAE,KAAKF,IAAE,KAAK,iBAAiB,GAAEE,GAAE,OAAOF,EAAC,GAAEA,GAAE,YAAUC;AAAA,MAAC;AAAA,IAAC,MAAM,OAAM,eAAeD,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,aAAQC,KAAE,GAAEA,KAAE,KAAK,SAAS,QAAOA,MAAI;AAAC,YAAMC,KAAE,KAAK,SAASD,EAAC;AAAE,QAAE,KAAK,YAAY,KAAG,KAAK,aAAa,WAAWC,EAAC,GAAEA,GAAE,QAAQ;AAAA,IAAC;AAAC,UAAM,kBAAkB;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,WAAO,KAAK,SAAS,OAAQ,CAAAF,OAAGA,GAAE,qBAAmBA,GAAE,QAAQ,CAAE;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,MAAE,KAAK,YAAY,KAAG,KAAK,aAAa,iBAAiB;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAE;AAAC,UAAK,EAAC,SAAQC,GAAC,IAAED,IAAEE,KAAE,KAAK;AAAiB,QAAG,CAACA,GAAE;AAAO,UAAMI,MAAEJ,GAAE;AAAO,QAAIE,KAAE;AAAG,IAAAJ,GAAE,cAAY,EAAE,YAAUI,KAAE,QAAIF,GAAE,oBAAoB,SAAO,MAAIF,GAAE,aAAW,cAAa,KAAK,wBAAwBA,IAAEE,GAAE,mBAAmB,IAAG,MAAM,eAAeF,EAAC,GAAEA,GAAE,cAAY,EAAE,OAAK,KAAK,MAAMA,GAAE,cAAaA,GAAE,KAAK;AAAE,UAAMQ,KAAE,KAAK,SAAS,OAAQ,CAAAR,QAAGA,IAAE,WAASA,IAAE,QAAQ,CAAE;AAAE,QAAG,CAACQ,MAAG,MAAIA,GAAE,OAAO,QAAOP,GAAE,QAAQ,GAAEA,GAAE,sBAAsB,IAAE,GAAE,KAAKA,GAAE,mBAAmB,IAAE;AAAE,eAAUQ,OAAKD,GAAE,CAAAC,IAAE,gBAAc;AAAE,IAAAR,GAAE,oBAAoB,CAAC,GAAEA,GAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAEA,GAAE,aAAa,EAAE,MAAK,EAAE,MAAK,EAAE,OAAO,GAAEA,GAAE,sBAAsB,IAAE,GAAEA,GAAE,mBAAmB,KAAE,GAAEA,GAAE,oBAAoB,IAAE,GAAEA,GAAE,qBAAqB,IAAE,GAAEA,GAAE,iBAAiB,EAAE,MAAM,GAAEA,GAAE,cAAc,CAAC,GAAEA,GAAE,MAAMA,GAAE,GAAG,gBAAgB,GAAED,GAAE,aAAW;AAAS,aAAQS,MAAEH,IAAE,SAAO,GAAEG,OAAG,GAAEA,MAAI,MAAK,kBAAkBH,IAAEG,GAAC,GAAET,IAAEQ,EAAC;AAAE,IAAAP,GAAE,qBAAqB,KAAE,GAAEA,GAAE,mBAAmBG,EAAC,GAAEH,GAAE,yBAAyB,EAAE,KAAI,EAAE,qBAAoB,EAAE,KAAI,EAAE,mBAAmB,GAAED,GAAE,aAAW;AAAc,aAAQS,MAAE,GAAEA,MAAEH,IAAE,QAAOG,MAAI,MAAK,kBAAkBH,IAAEG,GAAC,GAAET,IAAEQ,EAAC;AAAE,IAAAP,GAAE,QAAQ,GAAEA,GAAE,sBAAsB,IAAE,GAAEA,GAAE,mBAAmB,IAAE;AAAA,EAAC;AAAA,EAAC,MAAMA,IAAEC,IAAE;AAAC,MAAE,KAAK,YAAY,MAAI,KAAK,aAAa,OAAOD,IAAEC,EAAC,KAAG,KAAK,cAAc;AAAA,EAAE;AAAA,EAAC,kBAAkBF,IAAEC,IAAEC,IAAE;AAAC,UAAK,EAAC,SAAQI,KAAE,YAAWF,GAAC,IAAEH;AAAE,QAAG,WAASD,GAAE;AAAO,UAAMQ,KAAER,GAAE,kBAAkB,YAAY;AAAE,QAAGQ,MAAGA,GAAE,SAAS,MAAI,EAAE,KAAK;AAAO,QAAIE;AAAE,YAAOV,GAAE,MAAK;AAAA,MAAC,KAAKU,GAAE;AAAW;AAAA,MAAO,KAAKA,GAAE;AAAK,YAAG,aAAWN,MAAG,kBAAgBH,GAAE,WAAW;AAAO,QAAAS,KAAE;AAAU;AAAA,MAAM,KAAKA,GAAE;AAAK,YAAG,kBAAgBN,GAAE;AAAO,QAAAM,KAAE;AAAU;AAAA,MAAM,KAAKA,GAAE;AAAO,YAAG,kBAAgBN,GAAE;AAAO,QAAAM,KAAE;AAAY;AAAA,MAAM,KAAKA,GAAE;AAAO,YAAG,kBAAgBN,GAAE;AAAO,QAAAM,KAAE;AAAA,IAAW;AAAC,QAAGR,KAAEF,GAAE,SAAOU,GAAE,SAAOR,GAAE,OAAQ,CAAAF,QAAGA,IAAE,WAAY,IAAEE,GAAE,OAAQ,CAAAF,QAAGA,IAAE,iBAAkB,GAAE,gBAAcU,IAAE;AAAC,YAAMJ,MAAEL,GAAE;AAAa,UAAG,MAAIC,GAAE,UAAQ,WAASF,GAAE,WAASA,GAAE,WAASM,MAAER,MAAG,WAASE,GAAE,WAASA,GAAE,UAAQM,MAAER,GAAE;AAAA,IAAM;AAAC,UAAMa,KAAEX,GAAE;AAAI,IAAAC,GAAE,gBAAcU,IAAEV,GAAE,aAAWD;AAAE,eAAUS,OAAKP,GAAE,KAAGO,IAAE,UAAU,IAAIE,EAAC,GAAE;AAAC,MAAAL,IAAE,cAAcL,IAAEC,IAAEQ,EAAC;AAAE;AAAA,IAAK;AAAA,EAAC;AAAA,EAAC,wBAAwBT,IAAEC,IAAE;AAAC,UAAK,EAAC,SAAQI,KAAE,cAAaE,IAAE,SAAQC,KAAE,OAAMG,GAAC,IAAEX,IAAEM,KAAE,KAAK;AAAiB,QAAIT,KAAE;AAAG,eAAUE,MAAKE,IAAE;AAAC,UAAGK,GAAE,aAAaP,EAAC,EAAE,SAAOU,GAAE,cAAYX,GAAEQ,GAAE,aAAaP,EAAC,GAAEQ,EAAC,GAAE;AAAC,QAAAV,KAAE;AAAG;AAAA,MAAK;AAAA,IAAC;AAAC,QAAG,CAACA,GAAE;AAAO,UAAMK,KAAE,KAAK,cAAc,gBAAgBF,GAAE,OAAM,GAAE,UAAU,GAAE,EAAC,OAAMY,IAAE,SAAQC,GAAC,IAAEX,IAAE,EAAC,OAAMY,GAAC,IAAED,IAAEE,KAAE,EAAE,GAAEC,KAAE,CAAC;AAAE,QAAG,KAAK,eAAc;AAAC,YAAMf,KAAE,KAAK,cAAc,CAAC;AAAE,QAAE,KAAK,cAAc,MAAIA,GAAE,QAAQ,CAAC,EAAE,aAAaD,EAAC,GAAEC,GAAE,QAAQ,CAAC,EAAE,SAASD,IAAE,KAAK,cAAc;AAAA,IAAE;AAAC,UAAM,IAAE,KAAK;AAAiB,QAAIiB,IAAE,IAAE;AAAE,eAAS,EAAC,KAAIlB,IAAE,SAAQmB,IAAE,OAAMT,GAAC,KAAIG,GAAE,UAAQZ,MAAEkB,IAAElB,OAAGS,IAAET,OAAI;AAAC,UAAG,IAAE,EAAE,OAAO,CAAAiB,KAAE,EAAE,CAAC,GAAEA,GAAE,IAAI,IAAIH,IAAEf,IAAEc,GAAE,aAAab,GAAC,GAAEa,GAAE,kBAAkBb,GAAC,CAAC,GAAE,KAAK,cAAc,cAAce,IAAEE,GAAE,KAAI,KAAE,GAAEA,GAAE,IAAEF,GAAE,CAAC,GAAEE,GAAE,IAAEF,GAAE,CAAC,GAAEE,GAAE,aAAW,KAAK,cAAc,kBAAkBH,EAAC;AAAA,WAAM;AAAC,cAAMb,KAAE,IAAIF,GAAEe,IAAEf,IAAEc,GAAE,aAAab,GAAC,GAAEa,GAAE,kBAAkBb,GAAC,CAAC,GAAEK,MAAE,KAAK,cAAc,cAAc,EAAE,GAAEJ,EAAC,GAAEM,KAAE,KAAK,cAAc,kBAAkBO,EAAC;AAAE,QAAAG,KAAE,IAAIjB,GAAEC,IAAEM,IAAEF,IAAE,CAAC,GAAEA,IAAE,CAAC,GAAE,KAAI,KAAI,MAAK,IAAI,GAAE,EAAE,KAAKY,EAAC;AAAA,MAAC;AAAC,MAAAA,GAAE,aAAaN,EAAC,GAAEK,GAAE,KAAKC,EAAC,GAAE;AAAA,IAAG;AAAC,IAAAZ,IAAE,oBAAoB,CAAC,GAAEA,IAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAEA,IAAE,aAAa,EAAE,MAAK,EAAE,MAAK,EAAE,OAAO,GAAEA,IAAE,mBAAmB,EAAE,OAAM,GAAE,GAAG;AAAE,QAAIc,KAAE;AAAG,IAAAnB,GAAE,cAAY,EAAE,YAAUmB,KAAE,QAAId,IAAE,sBAAsBc,EAAC;AAAE,eAAUpB,MAAKE,IAAE;AAAC,YAAMA,KAAEK,GAAE,aAAaP,EAAC;AAAE,MAAAE,GAAE,SAAOQ,GAAE,cAAYX,GAAEG,IAAEM,EAAC,MAAIP,GAAE,gBAAcC,GAAE,KAAID,GAAE,aAAWC,IAAEO,IAAE,cAAcR,IAAEgB,IAAE,eAAe;AAAA,IAAE;AAAC,IAAAR,GAAE,KAAK,QAAQN,EAAC;AAAA,EAAC;AAAC;;;ACA5oL,IAAMkB,KAAE,EAAC,UAAS,CAAC,IAAIC,GAAE,sBAAqB,GAAE,EAAE,OAAM,GAAE,CAAC,CAAC,EAAC;AAA7D,IAA+D,IAAE,oBAAI;AAAI,EAAE,IAAI,sBAAqB,CAAC;AAAE,IAAMC,KAAE,EAAC,QAAO,iBAAgB,QAAO,iBAAgB,YAAW,EAAC;AAAE,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAM,GAAE,KAAK,QAAMA;AAAA,EAAC;AAAA,EAAC,OAAO,UAAUA,IAAEJ,IAAE;AAAC,WAAOI,KAAEJ,MAAG;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAM,EAAC,KAAII,GAAE,EAAC;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,SAAK,kBAAkBA,EAAC,GAAE,KAAK,iBAAiBA,EAAC;AAAE,UAAK,EAAC,SAAQJ,GAAC,IAAEI;AAAE,IAAAJ,GAAE,WAAW,KAAK,QAAQ,GAAE,KAAK,SAAS,oBAAoB,aAAY,KAAK,WAAW,GAAG,GAAE,KAAK,SAAS,cAAc,YAAW,KAAK,MAAM,UAAUI,EAAC,CAAC,GAAE,KAAK,SAAS,cAAc,eAAc,KAAK,MAAM,aAAaA,EAAC,CAAC;AAAE,UAAK,EAAC,YAAWD,KAAE,WAAUE,GAAC,IAAE,KAAK,MAAM,QAAQD,EAAC;AAAE,SAAK,cAAc,QAAQD,GAAC,GAAE,KAAK,aAAa,QAAQE,EAAC,GAAEL,GAAE,QAAQ,KAAK,YAAY,GAAEA,GAAE,mBAAmB,IAAE,GAAEA,GAAE,iBAAiB,EAAE,KAAI,EAAE,mBAAmB,GAAEA,GAAE,oBAAoB,KAAE,GAAEA,GAAE,sBAAsB,KAAE,GAAEA,GAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAEA,GAAE,aAAaM,GAAE,WAAUD,GAAE,QAAO,EAAE,cAAa,CAAC;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,SAAK,eAAa,EAAE,KAAK,YAAY;AAAA,EAAC;AAAA,EAAC,kBAAkBD,IAAE;AAAC,IAAAD,GAAE,KAAK,WAAW,GAAG,GAAE,EAAE,KAAK,WAAW,KAAI,KAAK,WAAW,KAAI,CAAC,IAAG,CAAC,CAAC,GAAE,EAAE,KAAK,WAAW,KAAI,KAAK,WAAW,KAAI,CAAC,IAAEC,GAAE,MAAM,KAAK,CAAC,GAAE,KAAGA,GAAE,MAAM,KAAK,CAAC,GAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAAC,UAAK,EAAC,SAAQJ,GAAC,IAAEI;AAAE,SAAK,aAAW,KAAK,WAASA,GAAE,QAAQ,gBAAgB,WAAWH,EAAC,IAAG,KAAK,kBAAgB,KAAK,gBAAcK,GAAE,aAAaN,IAAE,EAAE,WAAW,IAAG,KAAK,iBAAe,KAAK,eAAaM,GAAE,YAAYN,IAAE,EAAE,WAAW,IAAG,KAAK,iBAAe,KAAK,eAAa,IAAIO,GAAEP,IAAE,GAAED,IAAE,EAAC,UAAS,KAAK,cAAa,GAAE,KAAK,YAAY;AAAA,EAAE;AAAC;;;ACApsE,IAAMS,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,qBAAmB,MAAK,KAAK,gBAAc,CAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBC,IAAE;AAAC,UAAMC,MAAED,GAAE,KAAK,QAAQA,EAAC,GAAEF,KAAE,MAAIG,IAAE,QAAM,OAAKD,GAAE,MAAMC,IAAE,QAAM,GAAEA,IAAE,OAAK,GAAEA,IAAE,OAAK,GAAEA,IAAE,KAAK;AAAE,WAAOD,GAAE,KAAK,QAAQC,GAAC,GAAEH;AAAA,EAAC;AAAA,EAAC,gBAAgBE,IAAEC,KAAEC,IAAE;AAAC,UAAMJ,KAAE,MAAM,gBAAgBE,IAAEC,KAAEC,EAAC;AAAE,QAAG,CAACJ,GAAE,QAAOA;AAAE,UAAMK,KAAE,KAAGL,GAAE,QAAQ;AAAM,WAAOA,GAAE,QAAMA,GAAE,MAAM,OAAQ,CAAAE,QAAGA,IAAE,OAAK,KAAGA,IAAE,MAAIG,EAAE,GAAEL;AAAA,EAAC;AAAA,EAAC,aAAaE,IAAE;AAAC,QAAG,KAAK,sBAAoB,KAAK,yBAAyB,KAAK,SAAS,GAAE,KAAK,cAAcA,EAAC,EAAE,QAAO,KAAK,cAAcA,EAAC;AAAE;AAAC,YAAMC,MAAE,KAAK;AAAmB,UAAGD,KAAEC,IAAE,CAAC,EAAE,MAAM,QAAOA,IAAE,CAAC,EAAE;AAAM,UAAIC,IAAEJ;AAAE,eAAQK,KAAE,GAAEA,KAAEF,IAAE,SAAO,GAAEE,KAAI,KAAGL,KAAEG,IAAEE,KAAE,CAAC,GAAEH,KAAEF,GAAE,MAAM,QAAOI,KAAED,IAAEE,EAAC,GAAED,GAAE,SAAOA,GAAE,QAAMF,OAAIE,GAAE,QAAMJ,GAAE;AAAO,aAAOG,IAAEA,IAAE,SAAO,CAAC,EAAE;AAAA,IAAK;AAAA,EAAC;AAAA,EAAC,yBAAyBA,KAAE;AAAC,QAAIC;AAAE,QAAG,MAAID,IAAE,CAAC,EAAE,MAAM,CAAAC,KAAED,IAAE,IAAK,CAAAD,QAAI,EAAC,OAAMA,GAAE,OAAM,YAAWA,GAAE,YAAW,OAAMA,GAAE,MAAK,EAAG;AAAA,SAAM;AAAC,YAAMC,MAAE,KAAK,SAAS,KAAK,CAAC,GAAEH,KAAE,KAAK,SAAS;AAAiB,MAAAI,KAAEE,GAAE,OAAO,EAAC,MAAKH,KAAE,kBAAiBH,GAAC,CAAC,EAAE,KAAK,IAAK,CAAAE,QAAI,EAAC,OAAMA,GAAE,OAAM,YAAWA,GAAE,YAAW,OAAMA,GAAE,MAAK,EAAG;AAAA,IAAC;AAAC,aAAQA,KAAE,GAAEA,KAAEE,GAAE,QAAOF,KAAI,MAAK,cAAcE,GAAEF,EAAC,EAAE,KAAK,IAAEE,GAAEF,EAAC,EAAE;AAAM,SAAK,qBAAmBE;AAAA,EAAC;AAAC;;;ACA6P,IAAI,IAAE,cAAcG,GAAEC,EAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,gBAAc,CAAC,GAAE,KAAK,cAAY,MAAK,KAAK,cAAY,MAAK,KAAK,sBAAoB,MAAK,KAAK,sBAAoB,OAAG,KAAK,oBAAkB,MAAK,KAAK,SAAO,OAAG,KAAK,oBAAkB,CAAAC,OAAG;AAAC,YAAK,EAAC,YAAWC,GAAC,IAAED,GAAE;AAAM,UAAIE,KAAE;AAAE,YAAMC,KAAE,CAAC,GAAEC,MAAE,CAAC;AAAE,iBAAUC,MAAK,KAAK,qBAAqB,SAAS,KAAGA,GAAE,QAAQ,YAAS,CAACL,KAAEM,GAAC,KAAID,GAAE,QAAQ,YAAUA,MAAKC,IAAE,YAAUN,OAAKK,GAAE,WAAU;AAAC,cAAMC,OAAGN,IAAE,UAAQA,IAAE,YAAUC,IAAEM,MAAGP,IAAE,UAAQA,IAAE,YAAUC,IAAEO,KAAER,IAAE,QAAMC,IAAEQ,KAAET,IAAE,SAAOC,IAAEF,KAAEM,GAAE,OAAO,MAAML,IAAE,SAAS,EAAE,gBAAc;AAAG,YAAG,CAACD,MAAG,UAAQ,KAAK,MAAM,mBAAmB;AAAS,cAAMW,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAEf,KAAE,IAAE,GAAED,KAAEC,KAAE,IAAE,GAAEgB,KAAET,GAAE,UAAUQ,IAAEhB,EAAC;AAAE,QAAAK,GAAE,KAAKG,KAAEC,IAAEQ,IAAET,MAAEE,IAAED,IAAEQ,IAAET,KAAEC,KAAEE,IAAEM,IAAET,MAAEE,IAAED,KAAEE,IAAEM,EAAC,GAAEX,IAAE,KAAKF,KAAE,GAAEA,KAAE,GAAEA,KAAE,GAAEA,KAAE,GAAEA,KAAE,GAAEA,KAAE,CAAC,GAAEA,MAAG;AAAE,cAAMc,KAAEjB,KAAEW,KAAEC,IAAEM,KAAElB,KAAEa,KAAEC,IAAEK,KAAEZ,GAAE,UAAUU,IAAEC,EAAC;AAAE,QAAAd,GAAE,KAAKG,KAAEC,IAAEW,IAAEZ,MAAEE,IAAED,IAAEW,IAAEZ,KAAEC,KAAE,GAAEW,IAAEZ,MAAEE,IAAED,KAAE,GAAEW,EAAC,GAAEd,IAAE,KAAKF,KAAE,GAAEA,KAAE,GAAEA,KAAE,GAAEA,KAAE,GAAEA,KAAE,GAAEA,KAAE,CAAC,GAAEA,MAAG,GAAEC,GAAE,KAAKG,KAAEC,KAAEE,KAAE,GAAES,IAAEZ,MAAEE,IAAED,KAAEE,KAAE,GAAES,IAAEZ,KAAEC,KAAEE,IAAES,IAAEZ,MAAEE,IAAED,KAAEE,IAAES,EAAC,GAAEd,IAAE,KAAKF,KAAE,GAAEA,KAAE,GAAEA,KAAE,GAAEA,KAAE,GAAEA,KAAE,GAAEA,KAAE,CAAC,GAAEA,MAAG,GAAEC,GAAE,KAAKG,KAAEC,IAAEW,IAAEZ,MAAE,GAAEC,IAAEW,IAAEZ,KAAEC,KAAEE,IAAES,IAAEZ,MAAE,GAAEC,KAAEE,IAAES,EAAC,GAAEd,IAAE,KAAKF,KAAE,GAAEA,KAAE,GAAEA,KAAE,GAAEA,KAAE,GAAEA,KAAE,GAAEA,KAAE,CAAC,GAAEA,MAAG,GAAEC,GAAE,KAAKG,MAAEE,KAAE,GAAED,IAAEW,IAAEZ,MAAEE,IAAED,IAAEW,IAAEZ,MAAEE,KAAE,GAAED,KAAEE,IAAES,IAAEZ,MAAEE,IAAED,KAAEE,IAAES,EAAC,GAAEd,IAAE,KAAKF,KAAE,GAAEA,KAAE,GAAEA,KAAE,GAAEA,KAAE,GAAEA,KAAE,GAAEA,KAAE,CAAC,GAAEA,MAAG;AAAA,MAAC;AAAC,aAAM,EAAC,YAAW,IAAI,WAAWC,EAAC,GAAE,WAAU,IAAI,YAAYC,GAAC,EAAC;AAAA,IAAC,GAAE,KAAK,sBAAoB,MAAI,CAAC,GAAE,KAAG,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,KAAG,GAAE,GAAE,KAAG,GAAE,CAAC,GAAE,KAAK,yBAAuB,MAAI,CAAC,MAAI,MAAI,MAAI,GAAE;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQJ,IAAEE,IAAE;AAAC,QAAG,CAAC,KAAK,oBAAoB,QAAO;AAAK,UAAM,KAAK;AAAoB,UAAMC,KAAE,MAAM,KAAK,qBAAqB,QAAQD,EAAC;AAAE,QAAG,CAACC,MAAG,MAAIA,GAAE,OAAO,QAAO;AAAK,UAAMC,MAAED,GAAE,CAAC,IAAE,GAAEE,KAAE,KAAK,kBAAiBC,MAAED,GAAE,mBAAmBD,GAAC;AAAE,QAAG,CAACE,IAAE,QAAO;AAAK,UAAMC,KAAEF,GAAE,mBAAmBC,IAAE,EAAE;AAAE,WAAM,CAAC,EAAC,MAAK,WAAU,UAASN,IAAE,OAAM,KAAK,OAAM,SAAQ,IAAI,EAAE,EAAC,YAAW,EAAC,SAAQO,IAAE,WAAUD,IAAE,IAAG,UAASF,IAAC,GAAE,OAAM,KAAK,OAAM,aAAY,KAAK,MAAK,CAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAOJ,IAAE;AAAC,QAAG,KAAK,uBAAqB,KAAK,oBAAoB,QAAOA,GAAE,eAAa,KAAK,aAAa,oBAAkB,KAAK,OAAO,GAAE,MAAK,KAAK,aAAa,mBAAiBA,GAAE,eAAa,MAAK,KAAK,cAAc,SAAO,IAAE,KAAK,sBAAoB,KAAK,mBAAmB,KAAG,KAAK,YAAY,MAAM,GAAE,KAAK,YAAY,MAAM,GAAE,KAAK,YAAY,QAAMA,GAAE,OAAM,KAAK,YAAY,QAAMA,GAAE,OAAM,KAAK,aAAa,OAAOA,EAAC,KAAG,KAAK,cAAc,GAAE,KAAK,YAAY,OAAO,GAAE,KAAK,YAAY,OAAO;AAAA,EAAG;AAAA,EAAC,SAAQ;AAAC,UAAK,EAAC,OAAMA,GAAC,IAAE,KAAK,MAAM;AAAiB,SAAK,mBAAiB,IAAIM,GAAEN,EAAC,GAAE,KAAK,gBAAc,IAAIC,GAAE,KAAK,MAAM,UAAS,KAAK,MAAM,UAAU,GAAE,KAAK,uBAAqB,IAAIkB,GAAE,KAAK,aAAa,GAAE,KAAK,eAAa,IAAIZ,GAAE,KAAK,OAAM,KAAK,kBAAiB,OAAO,oBAAkB,CAAC,GAAE,KAAK,UAAU,SAAS,KAAK,oBAAoB,GAAE,KAAK,OAAO,GAAE,KAAK,iBAAiB,CAAC,KAAK,qBAAqB,GAAG,cAAc,MAAI;AAAC,WAAK,SAAO,MAAG,KAAK,aAAa,UAAU,GAAE,KAAK,cAAc;AAAA,IAAC,CAAE,GAAE,KAAK,qBAAqB,GAAG,iBAAiB,MAAI;AAJ3sI;AAI4sI,iBAAK,sBAAL,mBAAwB,iBAAgB,KAAK,SAAO,OAAG,KAAK,aAAa,UAAU,GAAE,KAAK,cAAc;AAAA,IAAC,CAAE,GAAE,EAAG,MAAI,KAAK,MAAM,oBAAqB,CAAAP,QAAG;AAAC,iBAASA,MAAE,KAAK,sBAAoB,KAAK,oBAAkB,IAAIM,GAAE,EAAC,SAAQ,KAAK,mBAAkB,WAAU,KAAK,qBAAoB,cAAa,KAAK,uBAAsB,CAAC,GAAE,KAAK,UAAU,SAAS,KAAK,iBAAiB,KAAG,KAAK,sBAAoB,KAAK,UAAU,YAAY,KAAK,iBAAiB,GAAE,KAAK,oBAAkB,OAAM,KAAK,UAAU,cAAc;AAAA,IAAC,GAAGG,EAAC,GAAE,KAAK,MAAM,GAAG,gBAAgB,CAAAT,QAAG;AAAC,UAAGA,IAAE,aAAa,MAAK,cAAc,KAAK,EAAC,MAAKoB,GAAE,iBAAgB,MAAKpB,IAAC,CAAC,GAAE,KAAK,aAAa,UAAU,GAAE,KAAK,cAAc;AAAA,WAAM;AAAC,cAAMC,KAAE,KAAK,kBAAiBC,KAAED,GAAE,aAAaD,IAAE,KAAK;AAAE,YAAG,CAACE,GAAE;AAAO,cAAMC,KAAED,GAAE,SAAOG,GAAE;AAAO,QAAAJ,GAAE,mBAAmBD,IAAE,OAAMA,IAAE,KAAK,GAAEG,MAAG,KAAK,qBAAqB,iBAAiB,GAAE,KAAK,qBAAqB,cAAc;AAAA,MAAC;AAAA,IAAC,CAAE,GAAE,KAAK,MAAM,GAAG,iBAAiB,CAAAH,QAAG;AAAC,YAAMC,KAAE,KAAK,kBAAiBC,KAAED,GAAE,aAAaD,IAAE,KAAK;AAAE,UAAG,CAACE,GAAE;AAAO,YAAMC,KAAE,EAAED,GAAE,QAAOF,IAAE,MAAM;AAAE,UAAG,CAAC,EAAEG,EAAC,GAAE;AAAC,YAAGE,GAAEF,IAAE,YAAY,KAAG,MAAI,EAAEA,EAAC,EAAE,QAAOF,GAAE,oBAAoBD,IAAE,OAAMA,IAAE,MAAM,GAAEE,GAAE,SAAOG,GAAE,UAAQ,KAAK,qBAAqB,iBAAiB,GAAE,KAAK,KAAK,qBAAqB,cAAc;AAAE,aAAK,cAAc,KAAK,EAAC,MAAKe,GAAE,gBAAe,MAAKpB,IAAC,CAAC,GAAE,KAAK,aAAa,UAAU,GAAE,KAAK,cAAc;AAAA,MAAC;AAAA,IAAC,CAAE,GAAE,KAAK,MAAM,GAAG,iCAAiC,CAAAA,QAAG;AAAC,YAAMC,KAAE,KAAK,kBAAiBC,KAAED,GAAE,aAAaD,IAAE,KAAK;AAAE,MAAAE,OAAID,GAAE,wBAAwBD,IAAE,OAAMA,IAAE,UAAU,GAAEE,GAAE,SAAOG,GAAE,UAAQ,KAAK,qBAAqB,iBAAiB,GAAE,KAAK,qBAAqB,cAAc;AAAA,IAAE,CAAE,GAAE,KAAK,MAAM,GAAG,sBAAsB,CAAAL,QAAG;AAAC,WAAK,cAAc,KAAK,EAAC,MAAKoB,GAAE,eAAc,MAAKpB,IAAC,CAAC,GAAE,KAAK,aAAa,UAAU,GAAE,KAAK,cAAc;AAAA,IAAC,CAAE,GAAE,KAAK,MAAM,GAAG,sBAAsB,CAAAA,QAAG;AAAC,WAAK,cAAc,KAAK,EAAC,MAAKoB,GAAE,eAAc,MAAKpB,IAAC,CAAC,GAAE,KAAK,aAAa,UAAU,GAAE,KAAK,cAAc;AAAA,IAAC,CAAE,GAAE,KAAK,MAAM,GAAG,cAAc,MAAI,KAAK,WAAW,CAAE,GAAE,KAAK,MAAM,GAAG,uBAAuB,CAAAA,QAAG;AAAC,WAAK,mBAAiBA,IAAE,cAAa,KAAK,cAAc,KAAK,EAAC,MAAKoB,GAAE,iBAAgB,MAAK,KAAI,CAAC;AAAE,YAAMnB,KAAE,KAAK,iBAAiB;AAAO,iBAAUC,MAAKD,GAAE,SAAOC,GAAE,MAAK;AAAA,QAAC,KAAKG,GAAE;AAAO,UAAAH,GAAE,kBAAkB,YAAY,KAAG,KAAK,cAAc,KAAK,EAAC,MAAKkB,GAAE,gBAAe,MAAK,EAAC,OAAMlB,GAAE,IAAG,QAAOA,GAAE,OAAM,EAAC,CAAC;AAAE;AAAA,QAAM,KAAKG,GAAE;AAAK,UAAAH,GAAE,iBAAiB,cAAc,KAAG,KAAK,cAAc,KAAK,EAAC,MAAKkB,GAAE,iBAAgB,MAAK,EAAC,OAAMlB,GAAE,IAAG,OAAMA,GAAE,OAAM,cAAaA,GAAE,oBAAoB,EAAC,EAAC,CAAC;AAAE;AAAA,QAAM,KAAKG,GAAE;AAAK,UAAAH,GAAE,kBAAkB,cAAc,KAAG,KAAK,cAAc,KAAK,EAAC,MAAKkB,GAAE,iBAAgB,MAAK,EAAC,OAAMlB,GAAE,IAAG,OAAMA,GAAE,OAAM,cAAaA,GAAE,oBAAoB,EAAC,EAAC,CAAC;AAAA,MAAC;AAAC,WAAK,aAAa,UAAU,GAAE,KAAK,cAAc;AAAA,IAAC,CAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,MAAM,GAAE,KAAK,UAAU,kBAAkB,GAAE,KAAK,uBAAqB,EAAE,KAAK,oBAAoB,GAAE,KAAK,eAAa,EAAE,KAAK,YAAY;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,cAAc;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,qBAAmB,KAAK,qBAAqB,iBAAiB,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,yBAAyBF,IAAE;AAJ9rO;AAI+rO,WAAOqB,IAAE,UAAK,MAAM,aAAX,mBAAqB,kBAAiBrB,EAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,QAAIA,KAAE,MAAM,UAAU;AAAE,UAAK,EAAC,kBAAiBC,GAAC,IAAE,KAAK;AAAM,QAAGD,OAAGC,MAAA,gBAAAA,GAAG,kBAAgB;AAAC,YAAMC,KAAE,KAAK,KAAK,OAAM,EAAC,UAASC,IAAE,UAASC,IAAC,IAAEH,GAAE;AAAgB,MAAAA,MAAGA,GAAE,oBAAkBE,MAAGA,KAAED,OAAIF,KAAE,QAAII,OAAGA,MAAEF,OAAIF,KAAE;AAAA,IAAI;AAAC,WAAOA;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,UAAMA,KAAE,KAAK,qBAAqB;AAAS,WAAM,CAAC,KAAK,uBAAqB,CAAC,KAAK,eAAa,CAAC,KAAK,eAAa,KAAK,YAAY,YAAU,KAAK,YAAY,YAAUA,GAAE,SAAO,KAAGA,GAAE,KAAM,CAAAA,QAAGA,IAAE,YAAa,KAAG,KAAK;AAAA,EAAM;AAAA,EAAC,YAAYA,IAAE;AAJ7sP;AAI8sP,UAAMC,KAAE,KAAK,kBAAkBD,EAAC;AAAE,YAAO,UAAK,wBAAL,mBAA0B,KAAM,MAAI;AAAC,WAAK,YAAY,KAAKC,GAAE,GAAG,EAAE,KAAM,CAAAD,QAAG,KAAK,YAAY,KAAK,EAAC,KAAIC,GAAE,KAAI,MAAKD,IAAC,CAAC,CAAE,EAAE,KAAM,CAAAA,QAAG;AAAC,QAAAC,GAAE,KAAK,UAAU,MAAI,KAAK,cAAc,CAAE,GAAEA,GAAE,QAAQD,GAAC,GAAE,KAAK,cAAc,GAAE,KAAK,aAAa,UAAU;AAAA,MAAC,CAAE,EAAE,MAAO,CAAAA,QAAG;AAAC,aAAK,aAAa,UAAU,GAAE,EAAEA,GAAC,KAAG,EAAE,UAAU,KAAK,aAAa,EAAE,MAAMA,GAAC;AAAA,MAAC,CAAE;AAAA,IAAC,IAAIC;AAAA,EAAC;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAMC,KAAED,GAAE,IAAI;AAAG,SAAK,YAAY,MAAMC,EAAC,GAAE,KAAK,YAAY,MAAMA,EAAC,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,QAAG,KAAK,MAAM,GAAE,KAAK,eAAa,IAAII,GAAE,EAAC,aAAY,CAAAL,QAAG,KAAK,YAAYA,GAAC,GAAE,aAAY,CAAAA,QAAG,KAAK,YAAYA,GAAC,GAAE,cAAa,KAAK,cAAa,GAAE,KAAK,oBAAoB,GAAE,CAAC,KAAK,MAAM,iBAAiB;AAAO,UAAMA,KAAE,IAAI,mBAAgBC,KAAE,KAAK,aAAa,MAAM,EAAC,QAAOD,GAAE,OAAM,CAAC,EAAE,KAAM,MAAI;AAAC,WAAK,cAAY,IAAIW,GAAE,EAAC,cAAa,KAAK,eAAc,SAAQ,CAACX,KAAEC,QAAI,KAAK,aAAaD,KAAEC,GAAC,GAAE,aAAY,GAAE,CAAC,GAAE,KAAK,cAAY,IAAIU,GAAE,EAAC,cAAa,KAAK,eAAc,SAAQ,CAACX,KAAEC,QAAI,KAAK,eAAeD,KAAEC,GAAC,GAAE,aAAY,EAAC,CAAC,GAAE,KAAK,cAAc,GAAE,KAAK,sBAAoB;AAAA,IAAE,CAAE;AAAE,SAAK,aAAa,aAAa,KAAM,CAAAD,QAAG;AAAC,WAAK,qBAAqB,kBAAkBA,KAAE,KAAK,aAAa,aAAY,KAAK,gBAAgB,GAAE,KAAK,cAAc;AAAA,IAAC,CAAE,GAAE,KAAK,8BAA4BA,IAAE,KAAK,sBAAoBC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,QAAG,CAAC,KAAK,+BAA6B,CAAC,KAAK,qBAAqB;AAAO,UAAMD,KAAE,KAAK;AAA4B,IAAAA,MAAGA,GAAE,MAAM,GAAE,KAAK,sBAAoB,MAAK,KAAK,sBAAoB,OAAG,KAAK,cAAY,EAAE,KAAK,WAAW,GAAE,KAAK,cAAY,EAAE,KAAK,WAAW,GAAE,KAAK,eAAa,EAAE,KAAK,YAAY,GAAE,KAAK,qBAAqB,kBAAkB;AAAA,EAAC;AAAA,EAAC,MAAM,aAAaA,IAAEC,IAAE;AAAC,UAAMC,KAAE,MAAM,KAAK,aAAa,cAAcF,IAAEC,EAAC;AAAE,WAAO,KAAK,aAAa,UAAU,GAAEC;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeF,IAAEC,IAAE;AAAC,WAAO,KAAK,aAAa,cAAcD,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,qBAAoB;AAAC,SAAK,sBAAoB,OAAG,KAAK,YAAY,MAAM,GAAE,KAAK,YAAY,MAAM,GAAE,KAAK,YAAY,MAAM,GAAE,KAAK,YAAY,MAAM,GAAE,KAAK,aAAa,WAAW;AAAE,UAAMD,KAAE,KAAK;AAAc,QAAG;AAAC,YAAM,KAAK,aAAa,YAAYA,EAAC;AAAA,IAAC,SAAOO,IAAE;AAAC,QAAE,UAAU,KAAK,aAAa,EAAE,MAAM,4CAA2CA,GAAE,OAAO,GAAE,KAAK,YAAY,OAAO,GAAE,KAAK,YAAY,OAAO,GAAE,KAAK,sBAAoB;AAAA,IAAE;AAAC,UAAMN,KAAE,KAAK,kBAAiBC,KAAE,CAAC;AAAE,IAAAF,GAAE,QAAS,CAAAA,QAAG;AAAC,UAAGA,IAAE,SAAOoB,GAAE,cAAc;AAAO,YAAMjB,KAAEH,IAAE,MAAKI,MAAEH,GAAE,aAAaE,GAAE,KAAK;AAAE,MAAAC,OAAGF,GAAE,KAAKE,IAAE,GAAG;AAAA,IAAC,CAAE;AAAE,UAAMA,MAAE,CAAC;AAAE,QAAIC;AAAE,IAAAL,GAAE,QAAS,CAAAA,QAAG;AAAC,YAAME,KAAEF,IAAE,MAAKG,KAAEH,IAAE;AAAK,cAAOE,IAAE;AAAA,QAAC,KAAKkB,GAAE;AAAgB,UAAAnB,GAAE,mBAAmBE,GAAE,OAAMA,GAAE,KAAK,GAAEE,KAAEF,GAAE;AAAM;AAAA,QAAM,KAAKiB,GAAE;AAAe,UAAAnB,GAAE,oBAAoBE,GAAE,OAAMA,GAAE,MAAM,GAAEE,KAAEF,GAAE;AAAM;AAAA,QAAM,KAAKiB,GAAE;AAAc,iBAAO,KAAKnB,GAAE,iBAAiBE,GAAE,KAAK;AAAA,QAAE,KAAKiB,GAAE;AAAc,UAAAnB,GAAE,cAAcE,GAAE,OAAMA,GAAE,KAAK,GAAEE,KAAEF,GAAE,MAAM;AAAG;AAAA,QAAM,KAAKiB,GAAE;AAAgB,eAAK,qBAAqB,gBAAgB,KAAK,aAAa,gBAAgB,KAAK,gBAAgB,CAAC,GAAE,KAAK,mBAAiB,MAAKf,KAAE;AAAA,MAAI;AAAC,YAAMC,MAAEL,GAAE,aAAaI,EAAC;AAAE,MAAAC,OAAGF,IAAE,KAAKE,IAAE,GAAG;AAAA,IAAC,CAAE;AAAE,UAAMA,MAAE,KAAK,qBAAqB;AAAS,QAAGJ,GAAE,SAAO,GAAE;AAAC,WAAK,qBAAqB,kBAAkBA,EAAC;AAAE,iBAAUF,OAAKM,IAAE,CAAAN,IAAE,gBAAgBE,EAAC;AAAA,IAAC;AAAC,QAAG,KAAK,YAAY,OAAO,GAAE,KAAK,YAAY,OAAO,GAAEE,IAAE,SAAO,GAAE;AAAC,YAAMJ,MAAE,CAAC;AAAE,iBAAUC,OAAKK,KAAE;AAAC,cAAMJ,KAAE,KAAK,YAAY,KAAKD,IAAE,GAAG,EAAE,KAAM,CAAAD,QAAG,KAAK,YAAY,KAAK,EAAC,KAAIC,IAAE,KAAI,MAAKD,KAAE,gBAAeI,IAAC,CAAC,CAAE,EAAE,KAAM,CAAAJ,QAAGC,IAAE,QAAQD,GAAC,CAAE;AAAE,QAAAA,IAAE,KAAKE,EAAC;AAAA,MAAC;AAAC,YAAM,QAAQ,IAAIF,GAAC;AAAA,IAAC;AAAC,SAAK,gBAAc,CAAC,GAAE,KAAK,sBAAoB,MAAG,KAAK,aAAa,UAAU,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,MAAM,aAAY;AAAC,UAAK,EAAC,OAAMA,GAAC,IAAE,KAAK,MAAM,kBAAiBC,KAAE,EAAED,EAAC;AAAE,SAAK,sBAAoB,OAAG,KAAK,YAAY,MAAM,GAAE,KAAK,YAAY,MAAM,GAAE,KAAK,YAAY,MAAM,GAAE,KAAK,YAAY,MAAM,GAAE,KAAK,aAAa,UAAU,GAAE,KAAK,mBAAiB,IAAIM,GAAEL,EAAC,GAAE,KAAK,qBAAqB,QAAQ,GAAE,KAAK,aAAa,MAAM,GAAE,KAAK,4BAA4B,MAAM,GAAE,KAAK,8BAA4B,IAAI;AAAgB,UAAK,EAAC,QAAOE,GAAC,IAAE,KAAK;AAA4B,QAAG;AAAC,WAAK,sBAAoB,KAAK,aAAa,SAAS,KAAK,kBAAiBF,EAAC,GAAE,MAAM,KAAK;AAAA,IAAmB,SAAOI,IAAE;AAAC,UAAG,CAAC,EAAEA,EAAC,EAAE,OAAMA;AAAA,IAAC;AAAC,QAAGF,GAAE,QAAQ,QAAO,KAAK,YAAY,OAAO,GAAE,KAAK,YAAY,OAAO,GAAE,KAAK,sBAAoB,MAAG,KAAK,aAAa,UAAU,GAAE,KAAK,KAAK,cAAc;AAAE,UAAMC,MAAE,MAAM,KAAK,aAAa;AAAa,SAAK,qBAAqB,kBAAkBA,KAAE,KAAK,aAAa,aAAY,KAAK,gBAAgB,GAAE,KAAK,YAAY,OAAO,GAAE,KAAK,YAAY,OAAO,GAAE,KAAK,sBAAoB,MAAG,KAAK,aAAa,UAAU,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,kBAAkBJ,IAAE;AAAC,UAAMC,KAAE,KAAK,cAAc,cAAc,EAAE,GAAED,EAAC,GAAEE,KAAE,KAAK,cAAc,kBAAkBF,GAAE,KAAK;AAAE,WAAO,IAAIgB,GAAEhB,IAAEE,IAAED,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,KAAI,KAAI,KAAK,gBAAgB;AAAA,EAAC;AAAC;AAAE,SAAS,EAAED,IAAE;AAAC,MAAG,EAAEA,EAAC,EAAE,QAAO;AAAE,UAAOA,GAAE,MAAK;AAAA,IAAC,KAAI;AAAU,aAAO,OAAO,KAAKA,GAAE,IAAI,EAAE;AAAA,IAAO,KAAI;AAAW,aAAO,KAAK,IAAI,OAAO,KAAKA,GAAE,QAAQ,EAAE,QAAO,OAAO,KAAKA,GAAE,QAAQ,EAAE,MAAM;AAAA,IAAE,KAAI;AAAa,aAAO,OAAO,KAAKA,GAAE,KAAK,EAAE,SAAO,OAAO,KAAKA,GAAE,OAAO,EAAE,SAAO,OAAO,KAAKA,GAAE,OAAO,EAAE;AAAA,EAAM;AAAC;AAACA,GAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,uBAAsB,MAAM,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAE,IAAEA,GAAE,CAACK,GAAE,4CAA4C,CAAC,GAAE,CAAC;AAAE,IAAMe,KAAE;", "names": ["e", "t", "i", "s", "h", "r", "e", "i", "s", "h", "n", "a", "t", "o", "l", "c", "g", "_", "E", "s", "e", "t", "a", "r", "n", "i", "c", "g", "n", "t", "e", "s", "i", "h", "a", "r", "_", "g", "o", "E", "D", "c", "l", "p", "n", "e", "t", "s", "c", "r", "u", "i", "o", "a", "l", "h", "r", "o", "n", "e", "i", "a", "t", "c", "h", "s", "_", "f", "l", "d", "t", "s", "s", "t", "e", "o", "l", "i", "r", "n", "a", "c", "h", "f", "u", "m", "E", "o", "e", "t", "r", "i", "s", "n", "l", "E", "u", "f", "h", "c", "y", "a", "d", "m", "r", "e", "t", "s", "a", "i", "o", "h", "E", "u", "n", "l", "c", "i", "e", "t", "n", "o", "l", "a", "c", "h", "u", "d", "y", "g", "m", "f", "r", "s", "t", "o", "e", "i", "s", "r", "n", "l", "c", "h", "a", "l", "i", "n", "r", "e", "s", "o", "t", "y", "a", "f", "c", "n", "m", "t", "e", "r", "s", "i", "l", "t", "r", "e", "f", "_", "e", "t", "s", "b", "i", "_b", "r", "m", "o", "l", "a", "d", "h", "g", "T", "E", "C", "L", "R", "n", "F", "u", "t", "g", "l", "r", "e", "s", "E", "f", "t", "h", "e", "l", "s", "o", "j", "f", "u", "e", "t", "i", "s", "r", "a", "l", "n", "o", "h", "c", "y", "p", "d", "_", "g", "m", "C", "T", "b", "I", "E"]}