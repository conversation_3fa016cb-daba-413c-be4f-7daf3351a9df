import "./chunk-H3AJBOWU.js";

// node_modules/vue3-print-nb/dist/vue3-print-nb.es.js
var e = class {
  constructor(e2) {
    this.standards = { strict: "strict", loose: "loose", html5: "html5" }, this.previewBody = null, this.close = null, this.previewBodyUtilPrintBtn = null, this.selectArray = [], this.counter = 0, this.settings = { standard: this.standards.html5 }, Object.assign(this.settings, e2), this.init();
  }
  init() {
    this.counter++, this.settings.id = `printArea_${this.counter}`;
    let e2 = "";
    this.settings.url && !this.settings.asyncUrl && (e2 = this.settings.url);
    let t2 = this;
    if (this.settings.asyncUrl) return void t2.settings.asyncUrl(function(e3) {
      let i2 = t2.getPrintWindow(e3);
      t2.settings.preview ? t2.previewIfrmaeLoad() : t2.print(i2);
    }, t2.settings.vue);
    let i = this.getPrintWindow(e2);
    this.settings.url || this.write(i.doc), this.settings.preview ? this.previewIfrmaeLoad() : this.print(i);
  }
  addEvent(e2, t2, i) {
    e2.addEventListener ? e2.addEventListener(t2, i, false) : e2.attachEvent ? e2.attachEvent("on" + t2, i) : e2["on" + t2] = i;
  }
  previewIfrmaeLoad() {
    let e2 = document.getElementById("vue-pirnt-nb-previewBox");
    if (e2) {
      let t2 = this, i = e2.querySelector("iframe");
      this.settings.previewBeforeOpenCallback(), this.addEvent(i, "load", function() {
        t2.previewBoxShow(), t2.removeCanvasImg(), t2.settings.previewOpenCallback();
      }), this.addEvent(e2.querySelector(".previewBodyUtilPrintBtn"), "click", function() {
        t2.settings.beforeOpenCallback(), t2.settings.openCallback(), i.contentWindow.print(), t2.settings.closeCallback();
      });
    }
  }
  removeCanvasImg() {
    let e2 = this;
    try {
      if (e2.elsdom) {
        let t2 = e2.elsdom.querySelectorAll(".canvasImg");
        for (let e3 = 0; e3 < t2.length; e3++) t2[e3].remove();
      }
    } catch (t2) {
      console.log(t2);
    }
  }
  print(e2) {
    var t2 = this;
    let i = document.getElementById(this.settings.id) || e2.f, l = document.getElementById(this.settings.id).contentWindow || e2.f.contentWindow;
    t2.settings.beforeOpenCallback(), t2.addEvent(i, "load", function() {
      l.focus(), t2.settings.openCallback(), l.print(), i.remove(), t2.settings.closeCallback(), t2.removeCanvasImg();
    });
  }
  write(e2) {
    e2.open(), e2.write(`${this.docType()}<html>${this.getHead()}${this.getBody()}</html>`), e2.close();
  }
  docType() {
    return this.settings.standard === this.standards.html5 ? "<!DOCTYPE html>" : `<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01${this.settings.standard === this.standards.loose ? " Transitional" : ""}//EN" "http://www.w3.org/TR/html4/${this.settings.standard === this.standards.loose ? "loose" : "strict"}.dtd">`;
  }
  getHead() {
    let e2 = "", t2 = "", i = "";
    this.settings.extraHead && this.settings.extraHead.replace(/([^,]+)/g, (t3) => {
      e2 += t3;
    }), [].forEach.call(document.querySelectorAll("link"), function(e3) {
      e3.href.indexOf(".css") >= 0 && (t2 += `<link type="text/css" rel="stylesheet" href="${e3.href}" >`);
    });
    let l = document.styleSheets;
    if (l && l.length > 0) for (let r = 0; r < l.length; r++) try {
      if (l[r].cssRules || l[r].rules) {
        let e3 = l[r].cssRules || l[r].rules;
        for (let t3 = 0; t3 < e3.length; t3++) i += e3[t3].cssText;
      }
    } catch (s) {
      console.log(l[r].href + s);
    }
    return this.settings.extraCss && this.settings.extraCss.replace(/([^,\s]+)/g, (e3) => {
      t2 += `<link type="text/css" rel="stylesheet" href="${e3}">`;
    }), `<head><title>${this.settings.popTitle}</title>${e2}${t2}<style type="text/css">${i}</style></head>`;
  }
  getBody() {
    let e2 = this.settings.ids;
    return e2 = e2.replace(new RegExp("#", "g"), ""), this.elsdom = this.beforeHanler(document.getElementById(e2)), "<body>" + this.getFormData(this.elsdom).outerHTML + "</body>";
  }
  beforeHanler(e2) {
    let t2 = e2.querySelectorAll("canvas");
    for (let i = 0; i < t2.length; i++) if (!t2[i].style.display) {
      let e3 = t2[i].parentNode, l = t2[i].toDataURL("image/png"), s = new Image();
      s.className = "canvasImg", s.style.display = "none", s.src = l, e3.appendChild(s);
    }
    return e2;
  }
  getFormData(e2) {
    let t2 = e2.cloneNode(true), i = t2.querySelectorAll("input,select,textarea"), l = t2.querySelectorAll(".canvasImg,canvas"), s = -1;
    for (let r = 0; r < l.length; r++) {
      let e3 = l[r].parentNode, t3 = l[r];
      "canvas" === t3.tagName.toLowerCase() ? e3.removeChild(t3) : t3.style.display = "block";
    }
    for (let r = 0; r < i.length; r++) {
      let t3 = i[r], l2 = t3.getAttribute("type"), n = i[r];
      if (l2 || (l2 = "SELECT" === t3.tagName ? "select" : "TEXTAREA" === t3.tagName ? "textarea" : ""), "INPUT" === t3.tagName) "radio" === l2 || "checkbox" === l2 ? t3.checked && n.setAttribute("checked", t3.checked) : (n.value = t3.value, n.setAttribute("value", t3.value));
      else if ("select" === l2) {
        s++;
        for (let i2 = 0; i2 < e2.querySelectorAll("select").length; i2++) {
          let l3 = e2.querySelectorAll("select")[i2];
          if (!l3.getAttribute("newbs") && l3.setAttribute("newbs", i2), l3.getAttribute("newbs") == s) {
            let i3 = e2.querySelectorAll("select")[s].selectedIndex;
            t3.options[i3].setAttribute("selected", true);
          }
        }
      } else n.innerHTML = t3.value, n.setAttribute("html", t3.value);
    }
    return t2;
  }
  getPrintWindow(e2) {
    var t2 = this.Iframe(e2);
    return { f: t2, win: t2.contentWindow || t2, doc: t2.doc };
  }
  previewBoxShow() {
    let e2 = document.getElementById("vue-pirnt-nb-previewBox");
    e2 && (document.querySelector("html").setAttribute("style", "overflow: hidden"), e2.style.display = "block");
  }
  previewBoxHide() {
    let e2 = document.getElementById("vue-pirnt-nb-previewBox");
    e2 && (document.querySelector("html").setAttribute("style", "overflow: visible;"), e2.querySelector("iframe") && e2.querySelector("iframe").remove(), e2.style.display = "none");
  }
  previewBox() {
    let e2 = document.getElementById("vue-pirnt-nb-previewBox"), t2 = "previewBody";
    if (e2) return e2.querySelector("iframe") && e2.querySelector("iframe").remove(), { close: e2.querySelector(".previewClose"), previewBody: e2.querySelector(".previewBody") };
    let i = document.createElement("div");
    i.setAttribute("id", "vue-pirnt-nb-previewBox"), i.setAttribute("style", "position: fixed;top: 0px;left: 0px;width: 100%;height: 100%;background: white;display:none"), i.style.zIndex = this.settings.zIndex;
    let l = document.createElement("div");
    l.setAttribute("class", "previewHeader"), l.setAttribute("style", "padding: 5px 20px;"), l.innerHTML = this.settings.previewTitle, i.appendChild(l), this.close = document.createElement("div");
    let s = this.close;
    s.setAttribute("class", "previewClose"), s.setAttribute("style", "position: absolute;top: 5px;right: 20px;width: 25px;height: 20px;cursor: pointer;");
    let r = document.createElement("div"), n = document.createElement("div");
    r.setAttribute("class", "closeBefore"), r.setAttribute("style", "position: absolute;width: 3px;height: 100%;background: #040404;transform: rotate(45deg); top: 0px;left: 50%;"), n.setAttribute("class", "closeAfter"), n.setAttribute("style", "position: absolute;width: 3px;height: 100%;background: #040404;transform: rotate(-45deg); top: 0px;left: 50%;"), s.appendChild(r), s.appendChild(n), l.appendChild(s), this.previewBody = document.createElement("div");
    let o = this.previewBody;
    o.setAttribute("class", t2), o.setAttribute("style", "display: flex;flex-direction: column; height: 100%;"), i.appendChild(o);
    let a = document.createElement("div");
    a.setAttribute("class", "previewBodyUtil"), a.setAttribute("style", "height: 32px;background: #474747;position: relative;"), o.appendChild(a), this.previewBodyUtilPrintBtn = document.createElement("div");
    let d = this.previewBodyUtilPrintBtn;
    return d.setAttribute("class", "previewBodyUtilPrintBtn"), d.innerHTML = this.settings.previewPrintBtnLabel, d.setAttribute("style", "position: absolute;padding: 2px 10px;margin-top: 3px;left: 24px;font-size: 14px;color: white;cursor: pointer;background-color: rgba(0,0,0,.12);background-image: linear-gradient(hsla(0,0%,100%,.05),hsla(0,0%,100%,0));background-clip: padding-box;border: 1px solid rgba(0,0,0,.35);border-color: rgba(0,0,0,.32) rgba(0,0,0,.38) rgba(0,0,0,.42);box-shadow: inset 0 1px 0 hsla(0,0%,100%,.05), inset 0 0 1px hsla(0,0%,100%,.15), 0 1px 0 hsla(0,0%,100%,.05);"), a.appendChild(d), document.body.appendChild(i), { close: this.close, previewBody: this.previewBody };
  }
  iframeBox(e2, t2) {
    let i = document.createElement("iframe");
    return i.style.border = "0px", i.style.position = "absolute", i.style.width = "0px", i.style.height = "0px", i.style.right = "0px", i.style.top = "0px", i.setAttribute("id", e2), i.setAttribute("src", t2), i;
  }
  Iframe(e2) {
    let t2 = this.settings.id;
    e2 = e2 || (/* @__PURE__ */ new Date()).getTime();
    let i = this, l = this.iframeBox(t2, e2);
    try {
      if (this.settings.preview) {
        l.setAttribute("style", "border: 0px;flex: 1;");
        let e3 = this.previewBox(), t3 = e3.previewBody, s = e3.close;
        t3.appendChild(l), this.addEvent(s, "click", function() {
          i.previewBoxHide();
        });
      } else document.body.appendChild(l);
      l.doc = null, l.doc = l.contentDocument ? l.contentDocument : l.contentWindow ? l.contentWindow.document : l.document;
    } catch (s) {
      throw new Error(s + ". iframes may not be supported in this browser.");
    }
    if (null == l.doc) throw new Error("Cannot find document.");
    return l;
  }
};
var t = { directiveName: "print", mounted(t2, i, l) {
  let s = i.instance, r = "";
  var n, o, a;
  o = "click", a = () => {
    if ("string" == typeof i.value) r = i.value;
    else {
      if ("object" != typeof i.value || !i.value.id) return void window.print();
      {
        r = i.value.id;
        let e2 = r.replace(new RegExp("#", "g"), "");
        document.getElementById(e2) || (console.log("id in Error"), r = "");
      }
    }
    d();
  }, (n = t2).addEventListener ? n.addEventListener(o, a, false) : n.attachEvent ? n.attachEvent("on" + o, a) : n["on" + o] = a;
  const d = () => {
    new e({ ids: r, vue: s, url: i.value.url, standard: "", extraHead: i.value.extraHead, extraCss: i.value.extraCss, zIndex: i.value.zIndex || 20002, previewTitle: i.value.previewTitle || "打印预览", previewPrintBtnLabel: i.value.previewPrintBtnLabel || "打印", popTitle: i.value.popTitle, preview: i.value.preview || false, asyncUrl: i.value.asyncUrl, previewBeforeOpenCallback() {
      i.value.previewBeforeOpenCallback && i.value.previewBeforeOpenCallback(s);
    }, previewOpenCallback() {
      i.value.previewOpenCallback && i.value.previewOpenCallback(s);
    }, openCallback() {
      i.value.openCallback && i.value.openCallback(s);
    }, closeCallback() {
      i.value.closeCallback && i.value.closeCallback(s);
    }, beforeOpenCallback() {
      i.value.beforeOpenCallback && i.value.beforeOpenCallback(s);
    } });
  };
}, install: function(e2) {
  e2.directive("print", t);
} };
var vue3_print_nb_es_default = t;
export {
  vue3_print_nb_es_default as default
};
//# sourceMappingURL=vue3-print-nb.js.map
