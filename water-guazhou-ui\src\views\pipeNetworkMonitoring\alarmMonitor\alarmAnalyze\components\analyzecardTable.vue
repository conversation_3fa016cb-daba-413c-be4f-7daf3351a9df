<template>
  <SLCard
    :class="{'card':true,'maxheight':total<=3}"
    title=" "
  >
    <template #title>
      <div class="card_title">
        <div>{{ title }}</div>
        <el-button
          key=""
          text
        >
          <el-icon>
            <MoreFilled />
          </el-icon>
        </el-button>
      </div>
    </template>
    <FormTable
      ref="reftable"
      :class="{'card-table':true,'max-table-height':total<=3}"
      title="asdas"
      :config="TableConfig"
    />
  </SLCard>
</template>

<script lang="ts" setup>
import { MoreFilled } from '@element-plus/icons-vue'

const reftable = ref()

const props = defineProps<{
  title: string
  value:any[]
  total:number
}>()

const TableConfig = reactive<ICardTable>({
  defaultExpandAll: true,
  indexVisible: true,
  rowKey: 'id',
  columns: [
    { label: '规则名称', prop: 'title' },
    { label: '报警信息', prop: 'alarmInfo' }
  ],
  operations: [

  ],
  dataList: computed(() => props.value) as any,
  pagination: {
    hide: true
  }
})

defineExpose({
  reftable
})

</script>

<style lang="scss" scoped>

.card{
  height:210px;
}
.card_title {
  width: 100%;
  display: flex;
  font-weight: 600;
  line-height: 32px;
  justify-content: space-between;
}

.card-table{
  height: 152px;
}

.maxheight{
  height: 435px;
}
.max-table-height{
  height: 380px;
}
</style>
