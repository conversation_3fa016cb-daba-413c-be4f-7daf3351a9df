<!-- 统一工单-我的工单-发起工单 -->
<template>
  <div class="wrapper" style="overflow-y: auto;">
    <div class="form-box">
      <Form ref="refForm" :config="FormConfig">
        <template #fieldSlot="{ config, row }">
          <div
            v-if="config.field === 'coordinate'"
            style="width: 100%; height: 250px"
          >
            <FormMap
              v-model="state.coordinate"
              :row="row"
              :show-input="(config as IFormMap).showInput"
              :disabled="(config as IFormMap).disabled"
              :readonly="config.readonly"
              @change="config.onChange"
            >
            </FormMap>
          </div>
        </template>
      </Form>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref } from 'vue';
import {
  getEmergencyLevelOpetions,
  getFromOptions,
  getOrderTypeOptions,
  WorkOrderDealLevel
} from './config';
import { InverseGeocoding } from '@/api/mapservice/utils';
import { SLConfirm, SLMessage } from '@/utils/Message';
import { PostWorkOrder, getWorkOrderProcessLevelList } from '@/api/workorder';
import { getUserslistByAuth } from '@/api/user';
import { useUserStore } from '@/store';
import { removeSlash } from '@/utils/removeIdSlash';
import { traverse } from '@/utils/GlobalHelper';

const refForm = ref<IFormIns>();
let timer: any;
const state = reactive<{
  coordinate: any;
  WorkOrderProcessLevelList: any[];
}>({
  coordinate: undefined,
  WorkOrderProcessLevelList: []
});

function initOptions() {
  // 处理级别
  getWorkOrderProcessLevelList('1').then((res) => {
    state.WorkOrderProcessLevelList = traverse(
      res.data.data || [],
      'children',
      { label: 'name', value: 'name' }
    );
  });
}
const FormConfig = reactive<IFormConfig>({
  // labelPosition: 'top',
  group: [
    {
      fields: [
        {
          type: 'input',
          label: '标题',
          field: 'title',
          rules: [{ required: true, message: '请输入标题' }]
        },
        {
          xs: 6,
          type: 'select',
          label: '来源',
          field: 'source',
          options: getFromOptions(),
          rules: [{ required: true, message: '请选择来源' }]
        },
        {
          xs: 6,
          type: 'select',
          label: '紧急程度',
          field: 'level',
          options: getEmergencyLevelOpetions(),
          rules: [{ required: true, message: '请选择紧急程度' }]
        },
        {
          xs: 6,
          type: 'select-tree',
          label: '工单类型',
          field: 'type',
          options: getOrderTypeOptions(),
          rules: [{ required: true, message: '请选择工单类型' }]
        },
        // {
        //   xs: 6,
        //   field: 'uploadUserId',
        //   label: '上报人',
        //   type: 'select',
        //   options: []
        // }, {
        //   xs: 6,
        //   field: 'uploadPhone',
        //   label: '上报人电话',
        //   type: 'input'
        // },
        // {
        //   field: 'uploadAddress',
        //   label: '上报人地址',
        //   type: 'textarea'
        // },
        // {
        //   field: 'senderType',
        //   label: '发起人',
        //   type: 'switch',
        //   width: 60,
        //   activeText: '水司',
        //   inActiveText: '部门',
        //   activeValue: '1',
        //   inActiveValue: '2',
        //   inActiveColor: '#1d67cf',
        //   extraFormItem: [

        //   ]
        // },
        {
          field: 'address',
          label: '地址',
          type: 'textarea',
          rules: [{ required: true, message: '请输入地址' }]
        },
        {
          field: 'coordinate',
          label: '坐标',
          type: 'form-map',
          showInput: true,
          rules: [{ required: true, message: '请输入地址' }],
          onChange: (val) => {
            const params = {
              lon: val[0] || '0',
              lat: val[1] || '0'
            };
            if (!refForm.value) return;
            refForm.value.dataForm.coordinate = val;
            myDebounce(() => {
              InverseGeocoding(params).then((res) => {
                if (!refForm.value) return;
                refForm.value.dataForm.address =
                  res.data.result.formatted_address || '';
              });
            }, 500);
          }
        },
        { field: 'remark', label: '描述', type: 'textarea' }
      ],
      fieldset: {
        desc: '工单信息',
        type: 'underline'
      }
    },
    {
      fieldset: {
        desc: '分派信息',
        type: 'underline'
      },
      fields: [
        {
          type: 'switch',
          label: '是否直接派发',
          field: 'isDirectDispatch'
        },
        {
          handleHidden: (params, query, config) => {
            config.hidden = !params.isDirectDispatch;
          },
          label: '处理人',
          xs: 6,
          field: 'stepProcessUserId',
          type: 'select',
          options: []
        },
        // {
        //   xs: 8,
        //   field: 'dealer',
        //   label: '处理人',
        //   type: 'switch',
        //   activeText: '水司',
        //   inActiveText: '部门',
        //   activeValue: '1',
        //   inActiveValue: '2',
        //   inActiveColor: '#1d67cf',
        //   width: 60,
        //   extraFormItem: []
        // },

        {
          handleHidden: (params, query, config) => {
            config.hidden = !params.isDirectDispatch;
          },
          xs: 6,
          field: 'processLevelLabel',
          label: '处理级别',
          type: 'select',
          options: WorkOrderDealLevel(),
          onChange: (val) => {
            const key = state.WorkOrderProcessLevelList.find(
              (item) => item.value === val
            );
            if (refForm.value?.dataForm) {
              refForm.value.dataForm = {
                ...refForm.value.dataForm,
                processLevel:
                  key.dayTime * 1440 + key.hourTime * 60 + key.minuteTime
              };
            }
          }
        }
        // {
        //   handleHidden: (params, query, config) => {
        //     config.hidden = !params.isDirectDispatch
        //   },
        //   xs: 6,
        //   field: 'preCompleteTime',
        //   label: '预计完成时间',
        //   type: 'datetime'
        // }
      ]
    },
    {
      fields: [
        {
          field: 'ccUserId',
          label: '抄送人',
          type: 'select',
          multiple: true,
          options: []
        }
      ],
      fieldset: {
        desc: '抄送',
        type: 'underline'
      }
    },
    {
      fields: [
        {
          field: 'imgUrl',
          label: '现场图片',
          type: 'image',
          returnType: 'comma',
          limit: 2,
          multiple: true,
          accept: 'image/*'
        },
        {
          field: 'videoUrl',
          label: '现场视频',
          type: 'file',
          limit: 2,
          returnType: 'comma',
          accept: 'video/*',
          tips: '只能上传视频文件,最多上传2个，大小不能超过100M'
        },
        {
          field: 'audioUrl',
          label: '现场录音',
          type: 'file',
          limit: 2,
          returnType: 'comma',
          accept: 'audio/*',
          tips: '只能上传音频文件,最多上传2个，大小不能超过4M'
        },
        {
          field: 'otherFileUrl',
          label: '其它附件',
          type: 'file',
          limit: 2,
          returnType: 'comma',
          tips: '只能上传文件,最多上传2个，大小不能超过4M'
        }
      ]
    },
    {
      fields: [
        {
          type: 'btn-group',
          btns: [
            {
              perm: true,
              type: 'primary',
              isTextBtn: false,
              loading: (() => FormConfig.submitting === true) as any,
              text: '保存',
              click: () => {
                refForm.value?.Submit();
              }
            },
            {
              perm: true,
              type: 'default',
              isTextBtn: false,
              loading: (() => FormConfig.submitting === true) as any,
              text: '重置',
              click: () => {
                SLConfirm('确定重置表单？', '提示信息')
                  .then(() => {
                    refForm.value?.resetForm();
                  })
                  .catch(() => {
                    //
                  });
              }
            }
            // {
            //   perm: true,
            //   type: 'default',
            //   loading: (() => FormConfig.submitting === true) as any,
            //   text: '返回',
            //   click: () => {
            //     router.back()
            //   }
            // }
          ]
        }
      ]
    }
  ],
  submit: (params: any) => {
    SLConfirm('确定提交？', '提示信息')
      .then(async () => {
        FormConfig.submitting = true;
        try {
          params.ccUserId = params.ccUserId.join(',');
          const res = await PostWorkOrder({
            processLevel: 0,
            ...params,
            coordinate: state.coordinate?.join(',')
          });
          if (res.data?.code === 200) {
            SLMessage.success('提交成功');
            refForm.value?.resetForm();
          } else {
            SLMessage.error(res.data?.err || '提交失败');
          }
        } catch (error: any) {
          SLMessage.error(error.message || '提交失败');
        }
        FormConfig.submitting = false;
      })
      .catch(() => {
        //
      });
  },
  defaultValue: {
    isDirectDispatch: true,
    organizerId: useUserStore().user?.id?.id
  }
});

const myDebounce = (fn: () => void, delay: number | undefined) => {
  if (timer) {
    clearTimeout(timer);
  }
  timer = setTimeout(() => {
    fn();
  }, delay);
};

const initUserOptions = async () => {
  const res = await getUserslistByAuth({
    authType: 'CUSTOMER_USER'
  });

  const userField = ['uploadUserId', 'stepProcessUserId', 'ccUserId'];
  FormConfig.group.map((group) => {
    const filters = group.fields.filter(
      (item) => item.field && userField.indexOf(item.field) !== -1
    ) as IFormSelect[];
    filters.map((item) => {
      item.options = res.data.map((item) => {
        return {
          label: item.firstName,
          value: removeSlash(item.id.id)
        };
      });
    });
  });
};
onMounted(() => {
  initUserOptions();
  initOptions();
});
</script>
<style lang="scss" scoped>
.form-card {
  height: 100%;
  overflow-y: auto;
}

.form-box {
  padding: 8px;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
