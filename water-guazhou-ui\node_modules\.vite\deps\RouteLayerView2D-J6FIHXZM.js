import {
  i
} from "./chunk-25KH2VMR.js";
import "./chunk-76V27AD5.js";
import {
  ae
} from "./chunk-JWQLMC4D.js";
import "./chunk-Z4F6BT6Q.js";
import "./chunk-4IW3DWDX.js";
import "./chunk-THDEIRBK.js";
import "./chunk-OGWTQT66.js";
import "./chunk-XR4QWT37.js";
import "./chunk-6FMMG4VO.js";
import "./chunk-CUQZFD6D.js";
import "./chunk-JXO7W6XW.js";
import "./chunk-QQS4HCWF.js";
import "./chunk-5LZTDVVY.js";
import "./chunk-J6VS6FXY.js";
import "./chunk-L3FZV3M6.js";
import "./chunk-XAC3PEBY.js";
import "./chunk-AZEN5UFW.js";
import "./chunk-GCZ6JHKQ.js";
import "./chunk-ERH4WAJU.js";
import "./chunk-VYWZHTOQ.js";
import "./chunk-KEY2Y5WF.js";
import "./chunk-CVN5SSWT.js";
import "./chunk-SZNZM2TR.js";
import "./chunk-5SYMUP5B.js";
import "./chunk-U6GJBSCL.js";
import "./chunk-RY6ZYWKC.js";
import "./chunk-AEJDTXF3.js";
import "./chunk-223SE4BY.js";
import "./chunk-AOYBG2OC.js";
import "./chunk-O2JKCGK6.js";
import "./chunk-LGU2JTOA.js";
import "./chunk-MYYUEN6M.js";
import "./chunk-53FPJYCC.js";
import "./chunk-N73MYEJE.js";
import "./chunk-C65HMCEM.js";
import "./chunk-34BE5ZRD.js";
import "./chunk-KXNV6PXI.js";
import {
  D2 as D,
  O,
  b2 as b,
  f,
  g,
  h as h2,
  h2 as h3
} from "./chunk-4W6SQY5S.js";
import "./chunk-6G2NLXT7.js";
import "./chunk-UHA44FM7.js";
import "./chunk-TFWV44LH.js";
import "./chunk-TMGUQ6KD.js";
import "./chunk-IEBU4QQL.js";
import "./chunk-6OFWBRK2.js";
import {
  f as f2
} from "./chunk-J3EWJTCQ.js";
import {
  u
} from "./chunk-HWB4LNSZ.js";
import "./chunk-JSZR3BUH.js";
import "./chunk-R6ZFHGHU.js";
import "./chunk-JCXMTMKU.js";
import "./chunk-WAPZ634R.js";
import "./chunk-3CFQMNJK.js";
import "./chunk-FTRLEBHJ.js";
import "./chunk-PWCXATLS.js";
import "./chunk-RRNRSHX3.js";
import "./chunk-4M3AMTD4.js";
import "./chunk-BMTNBZRF.js";
import "./chunk-YDRLAXYR.js";
import "./chunk-RURSJOSG.js";
import "./chunk-YFVPK4WM.js";
import "./chunk-J5A2YARY.js";
import "./chunk-OEIEPNC6.js";
import "./chunk-ZQY4DQCR.js";
import "./chunk-3JR5KBYG.js";
import "./chunk-WZNPTIYX.js";
import "./chunk-FRO3RSRO.js";
import "./chunk-22FAZXOH.js";
import "./chunk-DFGMRI52.js";
import "./chunk-OZZFNS32.js";
import "./chunk-6KNIOA43.js";
import "./chunk-DSTI5UIS.js";
import "./chunk-MSIU52YL.js";
import "./chunk-5JCRZXRL.js";
import "./chunk-4CHRJPQP.js";
import "./chunk-DUEDINK5.js";
import "./chunk-MZ267CZB.js";
import "./chunk-QCTKOQ44.js";
import "./chunk-ST2RRB55.js";
import "./chunk-J4YX6DLU.js";
import "./chunk-26N6FACI.js";
import "./chunk-NWZTRS6O.js";
import "./chunk-77E52HT5.js";
import "./chunk-SROTSYJS.js";
import "./chunk-FOE4ICAJ.js";
import "./chunk-DBDYNPQT.js";
import "./chunk-RE7K5Z3I.js";
import "./chunk-SEO6KEGF.js";
import {
  l as l2
} from "./chunk-TDC6MNNF.js";
import "./chunk-4DICTWL2.js";
import "./chunk-WGU7CS6R.js";
import "./chunk-4JOAXMIS.js";
import "./chunk-UYHYCNVE.js";
import "./chunk-INCFUNSS.js";
import "./chunk-KYTIKHPN.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-7UNBPRRZ.js";
import "./chunk-D3MAF4VS.js";
import "./chunk-2WMCP27R.js";
import "./chunk-3HW44BD3.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-Q4VCSCSY.js";
import "./chunk-65QKNYBL.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-ORU3OGKZ.js";
import "./chunk-LHJ7PAFO.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-FRJZOKDL.js";
import "./chunk-DKEAXJKM.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-L66EKMJP.js";
import "./chunk-3M3FTH72.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-52L4L36S.js";
import "./chunk-VTOVDEW2.js";
import "./chunk-UKESBP6X.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-TD7BKFJX.js";
import "./chunk-LNGMNZ4I.js";
import "./chunk-24NZLSKM.js";
import "./chunk-BNFBWJYJ.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-23VNIYCK.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-RFYOGM4H.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-OVIRLBJH.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import "./chunk-XTO3XXZ3.js";
import {
  h,
  l
} from "./chunk-QUHG7NMD.js";
import "./chunk-4YKNTCHK.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import "./chunk-CGBA4LNQ.js";
import "./chunk-2RZLOKYO.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-HHMRK4FX.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/views/2d/layers/RouteLayerView2D.js
var k = Object.freeze({ remove() {
}, pause() {
}, resume() {
} });
var v = ["route-info", "direction-line", "direction-point", "polygon-barrier", "polyline-barrier", "point-barrier", "stop"];
var M = { graphic: null, property: null, oldValue: null, newValue: null };
function V(t2) {
  return t2 instanceof b || t2 instanceof h2 || t2 instanceof O || t2 instanceof g || t2 instanceof f || t2 instanceof h3 || t2 instanceof D;
}
function j2(t2) {
  return j.isCollection(t2) && t2.length && V(t2.getItemAt(0));
}
function G(t2) {
  return Array.isArray(t2) && t2.length > 0 && V(t2[0]);
}
var I = class extends f2(u) {
  constructor() {
    super(...arguments), this._graphics = new j(), this._highlightIds = /* @__PURE__ */ new Map(), this._networkFeatureMap = /* @__PURE__ */ new Map(), this._networkGraphicMap = /* @__PURE__ */ new Map();
  }
  get _routeItems() {
    return new l2({ getCollections: () => r(this.layer) && !this.destroyed ? [r(this.layer.routeInfo) ? new j([this.layer.routeInfo]) : null, this.layer.directionLines, this.layer.directionPoints, this.layer.polygonBarriers, this.layer.polylineBarriers, this.layer.pointBarriers, this.layer.stops] : [] });
  }
  initialize() {
    this.updatingHandles.addOnCollectionChange(() => this._routeItems, (t2) => this._routeItemsChanged(t2), h);
  }
  destroy() {
    var _a;
    this._networkFeatureMap.clear(), this._networkGraphicMap.clear(), this._graphics.removeAll(), (_a = this._get("_routeItems")) == null ? void 0 : _a.destroy();
  }
  attach() {
    this._createGraphicsView();
  }
  detach() {
    this._destroyGraphicsView();
  }
  async fetchPopupFeatures(t2) {
    return this._graphicsView.hitTest(t2).filter((t3) => !!t3.popupTemplate);
  }
  highlight(t2) {
    let e2;
    e2 = V(t2) ? [this._getNetworkFeatureUid(t2)] : G(t2) ? t2.map((t3) => this._getNetworkFeatureUid(t3)) : j2(t2) ? t2.map((t3) => this._getNetworkFeatureUid(t3)).toArray() : [t2.uid];
    const r2 = e2.filter(r);
    return r2.length ? (this._addHighlight(r2), { remove: () => this._removeHighlight(r2) }) : k;
  }
  async hitTest(t2, e2) {
    if (this.suspended) return null;
    const r2 = this._graphicsView.hitTest(t2).filter(r).map((t3) => this._networkGraphicMap.get(t3));
    if (!r2.length) return null;
    const { layer: s } = this;
    return r2.reverse().map((e3) => ({ type: "route", layer: s, mapPoint: t2, networkFeature: e3 }));
  }
  isUpdating() {
    return this._graphicsView.updating;
  }
  moveStart() {
  }
  moveEnd() {
  }
  update(t2) {
    this._graphicsView.processUpdate(t2);
  }
  viewChange() {
    this._graphicsView.viewChange();
  }
  _addHighlight(t2) {
    for (const e2 of t2) if (this._highlightIds.has(e2)) {
      const t3 = this._highlightIds.get(e2);
      this._highlightIds.set(e2, t3 + 1);
    } else this._highlightIds.set(e2, 1);
    this._updateHighlight();
  }
  _createGraphic(t2) {
    const e2 = t2.toGraphic();
    return e2.layer = this.layer, e2.sourceLayer = this.layer, e2;
  }
  _createGraphicsView() {
    const t2 = this.view, e2 = () => this.requestUpdate(), r2 = new i(t2.featuresTilingScheme);
    this._graphicsView = new ae({ container: r2, graphics: this._graphics, requestUpdateCallback: e2, view: t2 }), this.container.addChild(r2), this._updateHighlight();
  }
  _destroyGraphicsView() {
    this.container.removeChild(this._graphicsView.container), this._graphicsView.destroy();
  }
  _getDrawOrder(t2) {
    const e2 = this._networkGraphicMap.get(t2);
    return v.indexOf(e2.type);
  }
  _getNetworkFeatureUid(t2) {
    return this._networkFeatureMap.has(t2) ? this._networkFeatureMap.get(t2).uid : null;
  }
  _removeHighlight(t2) {
    for (const e2 of t2) if (this._highlightIds.has(e2)) {
      const t3 = this._highlightIds.get(e2) - 1;
      0 === t3 ? this._highlightIds.delete(e2) : this._highlightIds.set(e2, t3);
    }
    this._updateHighlight();
  }
  _routeItemsChanged(t2) {
    if (t2.removed.length) {
      this._graphics.removeMany(t2.removed.map((t3) => {
        const e2 = this._networkFeatureMap.get(t3);
        return this._networkFeatureMap.delete(t3), this._networkGraphicMap.delete(e2), e2;
      }));
      for (const e2 of t2.removed) this.removeHandles(e2);
    }
    if (t2.added.length) {
      this._graphics.addMany(t2.added.map((t3) => {
        const e2 = this._createGraphic(t3);
        return t(e2.symbol) ? null : (this._networkFeatureMap.set(t3, e2), this._networkGraphicMap.set(e2, t3), e2);
      }).filter(r));
      for (const e2 of t2.added) this.addHandles([l(() => e2.geometry, (t3, r2) => {
        this._updateGraphic(e2, "geometry", t3, r2);
      }), l(() => e2.symbol, (t3, r2) => {
        this._updateGraphic(e2, "symbol", t3, r2);
      })], e2);
      this._graphics.sort((t3, e2) => this._getDrawOrder(t3) - this._getDrawOrder(e2));
    }
  }
  _updateGraphic(t2, e2, r2, i2) {
    if (!this._networkFeatureMap.has(t2)) {
      const e3 = this._createGraphic(t2);
      return this._networkFeatureMap.set(t2, e3), this._networkGraphicMap.set(e3, t2), void this._graphics.add(e3);
    }
    const s = this._networkFeatureMap.get(t2);
    s[e2] = r2, M.graphic = s, M.property = e2, M.oldValue = i2, M.newValue = r2, this._graphicsView.graphicUpdateHandler(M);
  }
  _updateHighlight() {
    const t2 = Array.from(this._highlightIds.keys());
    this._graphicsView.setHighlight(t2);
  }
};
e([y()], I.prototype, "_graphics", void 0), e([y()], I.prototype, "_routeItems", null), I = e([a("esri.views.2d.layers.RouteLayerView2D")], I);
var F = I;
export {
  F as default
};
//# sourceMappingURL=RouteLayerView2D-J6FIHXZM.js.map
