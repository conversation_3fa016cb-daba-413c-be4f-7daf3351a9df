{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/features/processors.js", "../../@arcgis/core/views/2d/layers/features/support/FeatureSetReaderPBFHeader.js", "../../@arcgis/core/views/2d/layers/features/support/FeatureSetReaderPBF.js", "../../@arcgis/core/views/2d/layers/features/controllers/support/sourceAdapters.js", "../../@arcgis/core/views/2d/layers/features/support/UpdateToken.js", "../../@arcgis/core/views/2d/layers/features/sources/DataTileSubscription.js", "../../@arcgis/core/views/2d/layers/features/sources/DataTileSource.js", "../../@arcgis/core/views/2d/layers/features/sources/BaseFeatureSource.js", "../../@arcgis/core/views/2d/layers/features/sources/DrillDownFeatureSource.js", "../../@arcgis/core/views/2d/layers/features/sources/PagedFeatureSource.js", "../../@arcgis/core/views/2d/layers/features/sources/SnapshotFeatureSource.js", "../../@arcgis/core/layers/graphics/data/StreamFeatureManager.js", "../../@arcgis/core/views/2d/layers/features/sources/StreamSource.js", "../../@arcgis/core/views/2d/layers/features/sources/createSource.js", "../../@arcgis/core/geohash/geohashUtils.js", "../../@arcgis/core/geohash/GeohashTree.js", "../../@arcgis/core/views/2d/layers/features/support/BinStore.js", "../../@arcgis/core/views/2d/layers/features/support/ClusterStore.js", "../../@arcgis/core/views/2d/layers/features/controllers/FeatureController2D.js", "../../@arcgis/core/views/2d/layers/features/Pipeline.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nfunction o(o){return\"heatmap\"===o?import(\"./processors/HeatmapProcessor.js\"):import(\"./processors/SymbolProcessor.js\")}export{o as loadProcessorModule};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../../core/Error.js\";import{parseTransform as t,parseFieldType as s}from\"../../../../../rest/query/operations/pbfFeatureServiceParser.js\";const r=268435455;class n{constructor(){this.fieldMap=new Map,this.fields=[],this.hasFeatures=!1,this.exceededTransferLimit=!1,this.fieldCount=0,this.featureCount=0,this.objectIdFieldIndex=0,this.vertexCount=0,this.offsets={attributes:new Array,geometry:new Array},this.centroid=new Array}hasField(e){return this.fieldMap.has(e)}isDateField(e){return(null!=e&&this.fieldMap.get(e)?.isDate)??!1}getFieldIndex(e){return null!=e?this.fieldMap.get(e)?.index:void 0}}function a(e){const t=1,r=2,n=e.asUnsafe(),a=n.getLength(),i=n.pos()+a,o={name:\"\",isDate:!1};for(;n.pos()<i&&n.next();)switch(n.tag()){case t:o.name=n.getString();break;case r:\"esriFieldTypeDate\"===s(n.getEnum())&&(o.isDate=!0);break;default:n.skip()}return o}function i(e){return e.toLowerCase().trim()}function o(s,o,f=!1){const c=1,d=3,l=9,u=12,g=13,p=15,h=s.asUnsafe(),m=h.pos(),b=new n;let w=0,k=0;const x=1,y=2,I=4,F=3;let L=null,A=null,C=null,S=!1;for(;h.next();)switch(h.tag()){case c:L=h.getString();break;case d:A=h.getString();break;case u:C=h.processMessage(t);break;case l:if(b.exceededTransferLimit=h.getBool(),b.exceededTransferLimit){b.offsets.geometry=f?new Float64Array(8e3):new Int32Array(8e3),b.centroid=f?new Float64Array(16e3):new Int32Array(16e3);for(let e=0;e<b.centroid.length;e++)b.centroid[e]=r}break;case g:{const e=a(s),t=e.name,r=i(e.name),n={fieldName:t,index:w++,isDate:e.isDate};b.fields.push(n),b.fieldMap.set(e.name,n),b.fieldMap.set(r,n);break}case p:{const e=h.getLength(),t=h.pos()+e;if(!b.exceededTransferLimit){const e=b.offsets.geometry,t=b.centroid;e.push(0),t.push(r),t.push(r)}!S&&b.exceededTransferLimit&&(S=!0,b.offsets.attributes=f?new Float64Array(8e3*w):new Uint32Array(8e3*w));let s=k*w;for(;h.pos()<t&&h.next();)switch(h.tag()){case x:{if(S)b.offsets.attributes[s++]=h.pos();else{b.offsets.attributes.push(h.pos())}const e=h.getLength();h.skipLen(e);break}case y:if(o){const e=h.getLength(),t=h.pos()+e;for(;h.pos()<t&&h.next();)switch(h.tag()){case F:{h.getUInt32();const e=h.getSInt64(),t=h.getSInt64();b.centroid[2*k]=e,b.centroid[2*k+1]=t;break}default:h.skip()}}else{b.offsets.geometry[k]=h.pos();const e=h.getLength();b.vertexCount+=e,h.skipLen(e)}break;case I:{const e=h.getLength(),t=h.pos()+e;for(;h.pos()<t&&h.next();)switch(h.tag()){case F:{h.getUInt32();const e=h.getSInt64(),t=h.getSInt64();b.centroid[2*k]=e,b.centroid[2*k+1]=t;break}default:h.skip()}break}default:h.skip()}k++,b.hasFeatures=!0;break}default:h.skip()}const M=L||A;if(!M)throw new e(\"FeatureSet has no objectId or globalId field name\");return b.featureCount=k,b.fieldCount=w,b.objectIdFieldIndex=b.getFieldIndex(M),b.transform=C,b.displayIds=new Uint32Array(b.featureCount),b.groupIds=new Uint16Array(b.featureCount),h.move(m),b}export{n as FeatureSetHeader,o as parseHeader};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../../core/Error.js\";import t from\"../../../../../core/Logger.js\";import{isNone as r,isSome as s,assertIsSome as i}from\"../../../../../core/maybe.js\";import n from\"../../../../../core/pbf.js\";import{convertToGeometry as a,unquantizeOptimizedGeometry as h}from\"../../../../../layers/graphics/featureConversionUtils.js\";import{OptimizedFeature as o}from\"../../../../../layers/graphics/OptimizedFeature.js\";import d from\"../../../../../layers/graphics/OptimizedGeometry.js\";import{FeatureSetReader as u}from\"./FeatureSetReader.js\";import{parseHeader as c}from\"./FeatureSetReaderPBFHeader.js\";const g=!0,l=268435455,_=128,f=128e3,y={small:{delta:new Int32Array(_),decoded:new Int32Array(_)},large:{delta:new Int32Array(f),decoded:new Int32Array(f)}};function I(e){return e<=y.small.delta.length?y.small:(e<=y.large.delta.length||(y.large.delta=new Int32Array(Math.round(1.25*e)),y.large.decoded=new Int32Array(Math.round(1.25*e))),y.large)}function p(e){return e.toLowerCase().trim()}function m(r){try{const e=2,t=new n(new Uint8Array(r),new DataView(r));for(;t.next();){if(t.tag()===e)return x(t.getMessage());t.skip()}}catch(s){const r=new e(\"query:parsing-pbf\",\"Error while parsing FeatureSet PBF payload\",{error:s});t.getLogger(\"esri.view.2d.layers.features.support.FeatureSetReaderPBF\").error(r)}return null}function x(e){const t=1;for(;e.next();){if(e.tag()===t)return e.getMessage();e.skip()}return null}function S(e){const t=1,r=2,s=3,i=4,n=5,a=6,h=7,o=8,d=9,u=e.getLength(),c=e.pos()+u;for(;e.pos()<c&&e.next();)switch(e.tag()){case t:return e.getString();case r:return e.getFloat();case s:return e.getDouble();case i:return e.getSInt32();case n:return e.getUInt32();case a:return e.getInt64();case h:return e.getUInt64();case o:return e.getSInt64();case d:return e.getBool();default:return e.skip(),null}return null}function F(e,t,r,s,i,n){return.5*Math.abs(e*s+r*n+i*t-e*n-r*t-i*s)}function v(e,t,r,s){return 0===e*s-r*t&&e*r+t*s>0}class G extends u{static fromBuffer(e,t,r=!1){const s=t.geometryType,i=m(e),n=c(i,\"esriGeometryPoint\"===s,r),a=u.createInstance();return new G(a,i,n,t)}constructor(e,t,r,s){super(e,s),this._hasNext=!1,this._isPoints=!1,this._featureIndex=-1,this._featureOffset=0,this._cache={area:0,unquantGeometry:void 0,geometry:void 0,centroid:void 0,legacyFeature:void 0,optFeature:void 0},this._geometryType=s.geometryType,this._reader=t,this._header=r,this._hasNext=r.hasFeatures,this._isPoints=\"esriGeometryPoint\"===s.geometryType}get geometryType(){return this._geometryType}get _size(){return this._header.featureCount}get hasZ(){return!1}get hasM(){return!1}get stride(){return 2+(this.hasZ?1:0)+(this.hasM?1:0)}get hasFeatures(){return this._header.hasFeatures}get hasNext(){return this._hasNext}get exceededTransferLimit(){return this._header.exceededTransferLimit}hasField(e){return this._header.hasField(e)||this._header.hasField(p(e))}getFieldNames(){return this._header.fields.map((e=>e.fieldName))}getSize(){return this._size}getQuantizationTransform(){return this._header.transform}getCursor(){return this.copy()}getIndex(){return this._featureIndex}setIndex(e){this._cache.area=0,this._cache.unquantGeometry=void 0,this._cache.geometry=void 0,this._cache.centroid=void 0,this._cache.legacyFeature=void 0,this._cache.optFeature=void 0,this._featureIndex=e}getAttributeHash(){let e=\"\";return this._header.fields.forEach((({index:t})=>{e+=this._readAttributeAtIndex(t)+\".\"})),e}getObjectId(){return this._readAttributeAtIndex(this._header.objectIdFieldIndex)}getDisplayId(){return this._header.displayIds[this._featureIndex]}setDisplayId(e){this._header.displayIds[this._featureIndex]=e}getGroupId(){return this._header.groupIds[this._featureIndex]}setGroupId(e){this._header.groupIds[this._featureIndex]=e}readLegacyFeature(){if(void 0===this._cache.legacyFeature){const e=this.readCentroid(),t={attributes:this.readAttributes(),geometry:this._isPoints?this.readLegacyPointGeometry():this.readLegacyGeometry(),centroid:(e&&{x:e.coords[0],y:e.coords[1]})??null};return this._cache.legacyFeature=t,t}return this._cache.legacyFeature}readOptimizedFeature(){if(void 0===this._cache.optFeature){const e=new o(this.readGeometry(),this.readAttributes(),this.readCentroid());return e.objectId=this.getObjectId(),e.displayId=this.getDisplayId(),this._cache.optFeature=e,e}return this._cache.optFeature}getXHydrated(){const e=this._header.centroid[2*this._featureIndex],t=this.getQuantizationTransform();return r(t)?e:e*t.scale[0]+t.translate[0]}getYHydrated(){const e=this._header.centroid[2*this._featureIndex+1],t=this.getQuantizationTransform();return r(t)?e:t.translate[1]-e*t.scale[1]}getX(){return this._header.centroid[2*this._featureIndex]*this._sx+this._tx}getY(){return this._header.centroid[2*this._featureIndex+1]*this._sy+this._ty}readLegacyPointGeometry(){return{x:this.getX(),y:this.getY()}}readLegacyGeometry(e){const t=this.readGeometry(e);return a(t,this.geometryType,!1,!1)}readLegacyCentroid(){const e=this.readCentroid();if(!e)return null;const[t,r]=e.coords;return{x:t,y:r}}readGeometryArea(){return this._cache.area||this.readGeometry(!0),this._cache.area}readUnquantizedGeometry(e=!1){if(void 0===this._cache.unquantGeometry){const t=this.readGeometry(e);if(!t)return this._cache.unquantGeometry=void 0,null;const r=I(t.coords.length).decoded,s=t.clone(r),i=s.coords;let n=0;for(const e of s.lengths){for(let t=1;t<e;t++){const e=2*(n+t),r=2*(n+t-1);i[e]+=i[r],i[e+1]+=i[r+1]}n+=e}return this._cache.unquantGeometry=s,s}return this._cache.unquantGeometry}readHydratedGeometry(){if(this._isPoints){if(this._header.centroid[2*this._featureIndex]===l)return null;const e=this.getXHydrated(),t=this.getYHydrated();return new d([],[e,t])}const e=this.readGeometry();if(!e)return null;const t=e.clone(),r=this.getQuantizationTransform();return s(r)&&h(t,t,this.hasZ,this.hasM,r),t}readGeometry(e=!1){if(void 0===this._cache.geometry){let r=null;if(this._isPoints){if(this._header.centroid[2*this._featureIndex]===l)return null;const e=this.getX(),t=this.getY();r=new d([],[e,t])}else{const s=this._header.offsets.geometry[this._featureIndex],i=this._reader;if(0===s){const e=this._readServerCentroid();if(!e)return null;const[t,r]=e.coords;return this.createQuantizedExtrudedQuad(t,r)}i.move(s);try{if(r=e?this._parseGeometryForDisplay(i):this._parseGeometry(i),null===r){const e=this._readServerCentroid();if(!e)return null;const[t,r]=e.coords;return this.createQuantizedExtrudedQuad(t,r)}}catch(t){return console.error(\"Failed to parse geometry!\",t),null}}return this._cache.geometry=r,r}return this._cache.geometry}readCentroid(){if(void 0===this._cache.centroid){let e;return e=this._computeCentroid(),e||(e=this._readServerCentroid()),this._cache.centroid=e??void 0,e??null}return this._cache.centroid}copy(){const e=this._reader.clone(),t=new G(this.instance,e,this._header,this.fullSchema());return this.copyInto(t),t}next(){for(this._cache.area=0,this._cache.unquantGeometry=void 0,this._cache.geometry=void 0,this._cache.centroid=void 0,this._cache.legacyFeature=void 0,this._cache.optFeature=void 0;++this._featureIndex<this._size&&!this._getExists(););return this._featureIndex<this._size}_readAttribute(e,t){const r=this._header.hasField(e)?e:p(e),s=this._header.getFieldIndex(r);if(null==s)return;const i=this._readAttributeAtIndex(s);if(!t)return i;if(null==i)return i;return this._header.isDateField(r)?new Date(i):i}_readAttributes(){const e={};return this._header.fields.forEach((({fieldName:t,index:r})=>{e[t]=this._readAttributeAtIndex(r)})),e}copyInto(e){super.copyInto(e),e._featureIndex=this._featureIndex,e._featureOffset=this._featureOffset,e._hasNext=this._hasNext}_readAttributeAtIndex(e){const t=this._header.offsets.attributes[this._featureIndex*this._header.fieldCount+e],r=this._reader;return r.move(t),S(r)}_readServerCentroid(){const e=this._header.centroid[2*this._featureIndex]+this._tx,t=this._header.centroid[2*this._featureIndex+1]+this._ty;return e===l?null:new d([],[e,t])}_parseGeometry(e){const t=2,r=3,s=e.asUnsafe(),i=s.getLength(),n=s.pos()+i,a=[],h=[];for(;s.pos()<n&&s.next();)switch(s.tag()){case t:{const e=s.getUInt32(),t=s.pos()+e;for(;s.pos()<t;)h.push(s.getUInt32());break}case r:{const e=s.getUInt32(),t=s.pos()+e;for(a.push(s.getSInt32()+this._tx),a.push(s.getSInt32()+this._ty),this.hasZ&&s.getSInt32(),this.hasM&&s.getSInt32();s.pos()<t;)a.push(s.getSInt32()),a.push(s.getSInt32()),this.hasZ&&s.getSInt32(),this.hasM&&s.getSInt32();break}default:s.skip()}return new d(h,a)}_parseGeometryForDisplay(e){const t=2,r=3,s=e.asUnsafe(),n=s.getLength(),a=s.pos()+n,h=[],o=[];let u=0,c=0,l=null,_=0;const f=\"esriGeometryPolygon\"===this.geometryType;for(;s.pos()<a&&s.next();)switch(s.tag()){case t:{const e=s.getUInt32(),t=s.pos()+e;for(;s.pos()<t;){const e=s.getUInt32();h.push(e),u+=e}l=I(2*u).delta;break}case r:{s.getUInt32();const e=2+(this.hasZ?1:0)+(this.hasM?1:0);i(l);for(const t of h)if(c+e*t>l.length)for(let e=0;e<t;e++)s.getSInt32(),s.getSInt32(),this.hasZ&&s.getSInt32(),this.hasM&&s.getSInt32();else if(f&&g){const e=this.getAreaSimplificationThreshold(t,this._header.vertexCount);let r=2,i=1;const n=!1;let a=s.getSInt32(),h=s.getSInt32();l[c++]=a,l[c++]=h,this.hasZ&&s.getSInt32(),this.hasM&&s.getSInt32();let d=s.getSInt32(),u=s.getSInt32();for(this.hasZ&&s.getSInt32(),this.hasM&&s.getSInt32();r<t;){let t=s.getSInt32(),n=s.getSInt32();this.hasZ&&s.getSInt32(),this.hasM&&s.getSInt32();const o=a+d,g=h+u;F(a,h,o,g,o+t,g+n)>=e?(_+=-.5*(o-a)*(g+h),i>1&&v(l[c-2],l[c-1],d,u)?(l[c-2]+=d,l[c-1]+=u):(l[c++]=d,l[c++]=u,i++),a=o,h=g):(t+=d,n+=u),d=t,u=n,r++}i<3||n?c-=2*i:(_+=-.5*(a+d-a)*(h+u+h),v(l[c-2],l[c-1],d,u)?(l[c-2]+=d,l[c-1]+=u,o.push(i)):(l[c++]=d,l[c++]=u,o.push(++i)))}else{let e=0,r=s.getSInt32(),i=s.getSInt32();this.hasZ&&s.getSInt32(),this.hasM&&s.getSInt32(),l[c++]=r,l[c++]=i,e+=1;for(let n=1;n<t;n++){const t=s.getSInt32(),a=s.getSInt32(),h=r+t,o=i+a;_+=-.5*(h-r)*(o+i),this.hasZ&&s.getSInt32(),this.hasM&&s.getSInt32(),n>2&&v(l[c-2],l[c-1],t,a)?(l[c-2]+=t,l[c-1]+=a):(l[c++]=t,l[c++]=a,e+=1),r=h,i=o}o.push(e)}break}default:s.skip()}if(this._cache.area=_,!o.length)return null;if(this._tx||this._ty){let e=0;i(l);for(const t of o)l[2*e]+=this._tx,l[2*e+1]+=this._ty,e+=t}return new d(o,l)}}export{G as FeatureSetReaderPBF};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../../../../../../core/has.js\";import{isSome as e,unwrap as t}from\"../../../../../../core/maybe.js\";import r from\"../../../../../../core/workers/Connection.js\";import{toQuantizationTransform as s}from\"../../../../../../geometry/support/quantizationUtils.js\";import{convertFromFeatureSet as i,quantizeOptimizedFeatureSet as a}from\"../../../../../../layers/graphics/featureConversionUtils.js\";import{queryOptimizedFeatureSet as o}from\"../../../../../../layers/ogc/ogcFeatureUtils.js\";import{executeQueryPBFBuffer as n,executeQuery as c}from\"../../../../../../rest/query/operations/query.js\";import{FeatureSetReaderJSON as u}from\"../../support/FeatureSetReaderJSON.js\";import{FeatureSetReaderPBF as m}from\"../../support/FeatureSetReaderPBF.js\";class p{constructor(e){this.service=e}destroy(){}}function f(e){return Array.isArray(e.source)}function l(e){return\"ogc-source\"===e?.type}function y(e){const{capabilities:t}=e;return l(e.source)?new q(e):f(e)?new d(e):t.query.supportsFormatPBF&&has(\"featurelayer-pbf\")?new v(e):new F(e)}async function h(e){const t=new r;return await t.open(e,{}),t}class d extends p{constructor(e){super(e),this._portsOpen=h(e.source).then((e=>this.client=e))}destroy(){this.client.close(),this.client=null}async executeQuery(e,t){await this._portsOpen;const r=await this.client.invoke(\"queryFeatures\",e.toJSON(),t);return u.fromFeatureSet(r,this.service)}}class v extends p{async executeQuery(e,t){const{data:r}=await n(this.service.source,e,t),s=!e.quantizationParameters;return m.fromBuffer(r,this.service,s)}}class F extends p{async executeQuery(r,o){const{source:n,capabilities:m,spatialReference:p,objectIdField:f,geometryType:l}=this.service;if(e(r.quantizationParameters)&&!m.query.supportsQuantization){const e=r.clone(),m=s(t(e.quantizationParameters));e.quantizationParameters=null;const{data:l}=await c(n,e,p,o),y=i(l,f);return a(m,y),u.fromOptimizedFeatureSet(y,this.service)}const{data:y}=await c(n,r,this.service.spatialReference,o);return\"esriGeometryPoint\"===l&&(y.features=y.features?.filter((t=>{if(e(t.geometry)){const e=t.geometry;return Number.isFinite(e.x)&&Number.isFinite(e.y)}return!0}))),u.fromFeatureSet(y,this.service)}}class q extends p{async executeQuery(e,r){const{capabilities:i}=this.service;if(e.quantizationParameters&&!i.query.supportsQuantization){const i=e.clone(),n=s(t(i.quantizationParameters));i.quantizationParameters=null;const c=await o(this.service.source,e,r);return a(n,c),u.fromOptimizedFeatureSet(c,this.service)}const n=await o(this.service.source,e,r);return u.fromOptimizedFeatureSet(n,this.service)}}export{p as SourceAdapter,y as createSourceAdapter};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nclass t{constructor(){this.version=0,this.source=!1,this.targets={feature:!1,aggregate:!1},this.storage={filters:!1,data:!1},this.mesh=!1,this.queryFilter=!1,this.why={mesh:[],source:[]}}static create(e){const s=new t;for(const t in e){const r=e[t];if(\"object\"==typeof r)for(const e in r){const a=r[e];s[t][e]=a}s[t]=r}return s}static empty(){return t.create({})}static all(){return t.create({source:!0,targets:{feature:!0,aggregate:!0},storage:{filters:!0,data:!0},mesh:!0})}unset(t){this.version=t.version,t.source&&(this.source=!1),t.targets.feature&&(this.targets.feature=!1),t.targets.aggregate&&(this.targets.aggregate=!1),t.storage.filters&&(this.storage.filters=!1),t.storage.data&&(this.storage.data=!1),t.mesh&&(this.mesh=!1),t.queryFilter&&(this.queryFilter=!1)}any(){return this.source||this.mesh||this.storage.filters||this.storage.data||this.targets.feature||this.targets.aggregate||this.queryFilter}describe(){let t=0,e=\"\";if(this.mesh){t+=20,e+=\"-> (20) Mesh needs update\\n\";for(const t of this.why.mesh)e+=`    + ${t}\\n`}if(this.source){t+=10,e+=\"-> (10) The source needs update\\n\";for(const t of this.why.source)e+=`    + ${t}\\n`}this.targets.feature&&(t+=5,e+=\"-> (5) Feature target parameters changed\\n\"),this.storage.filters&&(t+=5,e+=\"-> (5) Feature filter parameters changed\\n\"),this.targets.aggregate&&(t+=4,e+=\"-> (4) Aggregate target parameters changed\\n\"),this.storage.data&&(t+=1,e+=\"-> (1) Texture storage parameters changed\");const s=t<5?\"Fastest\":t<10?\"Fast\":t<15?\"Moderate\":t<20?\"Slow\":\"Very Slow\";console.debug(`Applying ${s} update of cost ${t}/45 `),console.debug(e)}toJSON(){return{queryFilter:this.queryFilter,source:this.source,targets:this.targets,storage:this.storage,mesh:this.mesh}}}export{t as UpdateToken};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../../core/CircularArray.js\";import has from\"../../../../../core/has.js\";import{isNone as t,isSome as s,unwrap as r}from\"../../../../../core/maybe.js\";import{FeatureSetReaderJSON as i}from\"../support/FeatureSetReaderJSON.js\";import{UpdateToken as d}from\"../support/UpdateToken.js\";class a{constructor(t,s){this.requests={done:new Array,stream:new e(10)},this._edits=null,this._abortController=new AbortController,this._version=0,this._done=!1,this.didSend=!1,this.tile=t,this._version=s}get signal(){return this._abortController.signal}get options(){return{signal:this._abortController.signal}}get empty(){return!this.requests.done.length&&t(this.edits)}get edits(){return this._edits}get done(){return this._done}end(){this._done=!0}clear(){this.requests.done=[]}applyUpdate(e){this.requests.done.forEach((t=>t.message.status.unset(e))),this._version=e.version,s(this._edits)&&this._edits.status.unset(e)}add(e){e.message.status=e.message.status??d.empty(),e.message.status.version=this._version,has(\"esri-2d-update-debug\")&&console.debug(this.tile.id,\"DataTileSubscription:add\",this._version),e.message.end&&this.requests.done.forEach((e=>{s(e.message)&&e.message.end&&(e.message.end=!1)})),this.requests.done.push(e)}edit(e,a){const o=e.getQuantizationTransform(),n=e.fullSchema(),h=Array.from(e.features()).filter(s),u=[...a,...h.map((e=>e.objectId))];if(this.removeIds(u),this._invalidate(),t(this._edits))return void(this._edits={type:\"append\",addOrUpdate:i.fromOptimizedFeatures(h,n,r(o)),id:this.tile.id,status:d.empty(),end:!0});this.requests.done.forEach((e=>e.message.end=!1));r(this._edits.addOrUpdate).append(e.features())}*readers(){for(const{message:e}of this.requests.done)s(e.addOrUpdate)&&(yield e.addOrUpdate);s(this._edits)&&s(this._edits.addOrUpdate)&&(yield this._edits.addOrUpdate)}_invalidate(){for(const e of this.requests.done)e.message.status=d.empty();s(this._edits)&&(this._edits.status=d.empty())}removeIds(e){this._invalidate();for(const{message:t}of this.requests.done){const r=t.addOrUpdate;s(r)&&(r.removeIds(e),r.isEmpty&&(has(\"esri-2d-update-debug\")&&console.debug(\"Removing FeatureSetReader\"),t.addOrUpdate=null))}s(this._edits)&&s(this._edits.addOrUpdate)&&this._edits.addOrUpdate.removeIds(e),this.requests.done=this.requests.done.filter((e=>e.message.addOrUpdate||e.message.end))}abort(){this._abortController.abort()}}export{a as DataTileSubscription};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../../TimeExtent.js\";import s from\"../../../../../core/Accessor.js\";import t from\"../../../../../core/Evented.js\";import has from\"../../../../../core/has.js\";import{isSome as i}from\"../../../../../core/maybe.js\";import{createResolver as r,ignoreAbortErrors as o,eachAlwaysValues as n,eachAlways as c}from\"../../../../../core/promiseUtils.js\";import{diff as u,hasDiff as h,hasDiffAny as a}from\"../../../../../core/accessorSupport/diffUtils.js\";import d from\"../../../../../rest/support/Query.js\";import{DataTileSubscription as p}from\"./DataTileSubscription.js\";import{UpdateToken as l}from\"../support/UpdateToken.js\";function m(e,s){const t=new Set;return e&&e.forEach((e=>t.add(e))),s&&s.forEach((e=>t.add(e))),t.has(\"*\")?[\"*\"]:Array.from(t)}class f extends s{constructor(e){super(),this.events=new t,this._resolver=r(),this._didEdit=!1,this._subscriptions=new Map,this._outSR=e.outSR,this._serviceInfo=e.serviceInfo,this._onTileUpdateMessage=e.onMessage}async _onMessage(e){const s=this._subscriptions.get(e.id);if(!s)return;const t={...e,remove:e.remove??[],status:e.status??l.empty()};return o(this._onTileUpdateMessage(t,s.options))}update(s,t){const i=t.fields.length;t.outFields=m(this._schema?.outFields,t.outFields),t.outFields=t.outFields.length>=.75*i?[\"*\"]:t.outFields,t.outFields.sort();const r=u(this._schema,t);if(!r)return;has(\"esri-2d-update-debug\")&&console.debug(\"Applying Update - Source:\",r);const o=\"orderByFields\"in this._serviceInfo&&this._serviceInfo.orderByFields?this._serviceInfo.orderByFields:this._serviceInfo.objectIdField+\" ASC\",n={returnCentroid:\"esriGeometryPolygon\"===this._serviceInfo.geometryType,returnGeometry:!0,timeReferenceUnknownClient:\"stream\"!==this._serviceInfo.type&&this._serviceInfo.timeReferenceUnknownClient,outFields:t.outFields,outSpatialReference:this._outSR,orderByFields:[o],where:t.definitionExpression||\"1=1\",gdbVersion:t.gdbVersion,historicMoment:t.historicMoment,timeExtent:t.timeExtent?e.fromJSON(t.timeExtent):null},c=this._schema&&h(r,\"outFields\");this._schema&&a(r,[\"timeExtent\",\"definitionExpression\",\"gdbVersion\",\"historicMoment\",\"customParameters\"])&&(s.why.mesh.push(\"Layer filter and/or custom parameters changed\"),s.why.source.push(\"Layer filter and/or custom parameters changed\"),s.mesh=!0,s.source=!0,s.queryFilter=!0),c&&(s.why.source.push(\"Layer required fields changed\"),s.source=!0),u(n,this._queryInfo)&&(this._queryInfo=n),this._schema=t,this._resolver.resolve()}whenInitialized(){return this._resolver.promise}async applyUpdate(e){if(e.queryFilter||e.source&&this._didEdit)return this.refresh(e.version),void(this._didEdit=!1);this._subscriptions.forEach((s=>s.applyUpdate(e))),await this.resend()}refresh(e,s){for(const t of this._tiles())this.unsubscribe(t),this.subscribe(t,e)}subscribe(e,s){const t=new p(e,s);this._subscriptions.set(e.id,t)}unsubscribe(e){const s=this.getSubscription(e.id);i(s)&&s.abort(),this._subscriptions.delete(e.id)}createQuery(e={}){const s=this._queryInfo.historicMoment?new Date(this._queryInfo.historicMoment):null;return new d({...this._queryInfo,historicMoment:s,...e})}getSubscription(e){return this._subscriptions.has(e)?this._subscriptions.get(e):null}async queryLastEditDate(){throw new Error(\"Service does not support query type\")}async query(e,s){throw new Error(\"Service does not support query\")}*_tiles(){const e=Array.from(this._subscriptions.values());for(const s of e)yield s.tile}async edit(e,s){const t=Array.from(this._subscriptions.values()),i=t.map((({tile:e})=>e));for(const r of t)r.removeIds(s);if(e.length){const t=i.map((s=>{const t=this.createTileQuery(s);return t.objectIds=e,{tile:s,query:t}})).map((async({tile:e,query:s})=>({tile:e,result:await this.query(s,{query:{tile:has(\"esri-tiles-debug\")?e.id.replace(/\\//g,\".\"):void 0}}),query:s}))),r=(await n(t)).map((async({tile:t,result:i})=>{if(!i.hasFeatures&&!s.length&&!e.length)return;const r=this._subscriptions.get(t.key.id);r&&r.edit(i,e)}));await c(r)}this._didEdit=!0}}export{f as DataTileSource};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../../request.js\";import t from\"../../../../../core/Error.js\";import has from\"../../../../../core/has.js\";import r from\"../../../../../core/Logger.js\";import{isSome as s,unwrapOr as i,isNone as a}from\"../../../../../core/maybe.js\";import{throwIfAborted as o,isAbortError as n}from\"../../../../../core/promiseUtils.js\";import{createSourceAdapter as u}from\"../controllers/support/sourceAdapters.js\";import{DataTileSource as c}from\"./DataTileSource.js\";import{QueueProcessor as l}from\"../../../../support/QueueProcessor.js\";const p=4;class d extends c{constructor(e){super(e),this.type=\"feature\",this.mode=\"on-demand\",this._adapter=u(e.serviceInfo),this._queue=new l({concurrency:8,process:async e=>{if(o(e),s(e.tile)){const t=e.tile.key.id,{signal:r}=e,s=has(\"esri-tiles-debug\")?{tile:t.replace(/\\//g,\".\"),depth:e.depth}:void 0,i=await this._adapter.executeQuery(e.query,{signal:r,query:{...s,...this._schema?.customParameters}});return i.level=e.tile.key.level,i}return this._adapter.executeQuery(e.query,{...e,query:this._schema?.customParameters})}}),this._patchQueue=new l({concurrency:8,process:async e=>{if(o(e),s(e.tile)){const t=e.tile.key.id,{signal:r}=e,s=has(\"esri-tiles-debug\")?{tile:t.replace(/\\//g,\".\"),depth:e.depth}:void 0,i=await this._adapter.executeQuery(e.query,{signal:r,query:{...s,...this._schema?.customParameters}});return i.level=e.tile.key.level,i}return this._adapter.executeQuery(e.query,{...e,query:this._schema?.customParameters})}})}destroy(){super.destroy(),this._adapter.destroy(),this._queue.destroy(),this._patchQueue.destroy()}get updating(){return!!this._queue.length||Array.from(this._subscriptions.values()).some((e=>!e.done))}get maxRecordCountFactor(){const{query:e}=this._serviceInfo.capabilities;return e.supportsMaxRecordCountFactor?p:null}get maxPageSize(){const{query:e}=this._serviceInfo.capabilities;return(e.maxRecordCount??8e3)*i(this.maxRecordCountFactor,1)}get pageSize(){return Math.min(8e3,this.maxPageSize)}enableEvent(e,t){}subscribe(e,s){super.subscribe(e,s);const i=this._subscriptions.get(e.id);this._fetchDataTile(e).catch((s=>{n(s)||r.getLogger(\"esri.views.2d.layers.features.sources.BaseFeatureSource\").error(new t(\"mapview-query-error\",\"Encountered error when fetching tile\",{tile:e,error:s}))})).then((()=>i.end()))}unsubscribe(e){super.unsubscribe(e)}readers(e){return this._subscriptions.get(e).readers()}async query(e,t={}){const r=t.query??{};return this._adapter.executeQuery(e,{...t,query:{...r,...this._schema?.customParameters}})}async queryLastEditDate(){const t=this._serviceInfo.source,r={...t.query,f:\"json\"};return(await e(t.path,{query:r,responseType:\"json\"})).data.editingInfo.lastEditDate}createTileQuery(e,t={}){const r=this._serviceInfo.geometryType,s=this.createQuery(t);s.quantizationParameters=t.quantizationParameters??e.getQuantizationParameters(),s.resultType=\"tile\",s.geometry=e.extent,this._serviceInfo.capabilities.query.supportsQuantization?\"esriGeometryPolyline\"===r&&(s.maxAllowableOffset=e.resolution*has(\"feature-polyline-generalization-factor\")):\"esriGeometryPolyline\"!==r&&\"esriGeometryPolygon\"!==r||(s.maxAllowableOffset=e.resolution,\"esriGeometryPolyline\"===r&&(s.maxAllowableOffset*=has(\"feature-polyline-generalization-factor\")));const i=this._serviceInfo.capabilities.query;return s.defaultSpatialReferenceEnabled=i.supportsDefaultSpatialReference,s.compactGeometryEnabled=i.supportsCompactGeometry,s}async _executePatchQuery(e,t,r,i){const a=t.clone();a.outFields=[this._serviceInfo.objectIdField,...r],a.returnCentroid=!1,a.returnGeometry=!1;const o=s(a.start)?a.start/8e3:0,n=i.signal;return this._patchQueue.push({tile:e,query:a,signal:n,depth:o})}async _resend(e,t){const{query:r,message:i}=e,n=s(r.outFields)?r.outFields:[],u=this._queryInfo.outFields,c=u.filter((e=>!n.includes(e)));if(a(i.addOrUpdate))this._onMessage({...i,type:\"append\"});else if(c.length)try{const e=this._subscriptions.get(i.id).tile,s=await this._executePatchQuery(e,r,c,t);o(t),r.outFields=u,i.addOrUpdate.joinAttributes(s),this._onMessage({...i,end:i.end,type:\"append\"})}catch(l){}else this._onMessage({...i,type:\"append\"})}async _resendSubscription(e){if(has(\"esri-2d-update-debug\")&&console.debug(e.tile.id,\"Resend Subscription\"),e.empty)return this._onMessage({id:e.tile.id,addOrUpdate:null,end:!1,type:\"append\"});const t=e.signal;for(const r of e.requests.done)await this._resend(r,{signal:t});return s(e.edits)?this._onMessage(e.edits):void 0}async resend(){const e=Array.from(this._subscriptions.values());await Promise.all(e.map((e=>this._resendSubscription(e))))}}export{d as BaseFeatureSource};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport has from\"../../../../../core/has.js\";import{throwIfAborted as e,isAbortError as t}from\"../../../../../core/promiseUtils.js\";import{BaseFeatureSource as r}from\"./BaseFeatureSource.js\";const s=has(\"esri-mobile\"),i={maxDrillLevel:s?1:4,maxRecordCountFactor:s?1:3};class a extends r{constructor(e){super(e)}async _fetchDataTile(r){const s=this._serviceInfo.capabilities.query.supportsMaxRecordCountFactor,a=this._subscriptions.get(r.key.id),o=a.signal,n=r.getQuantizationParameters();let c=0;const d=async(u,l)=>{const p=this._queryInfo,m=this.createTileQuery(u,{maxRecordCountFactor:s?i.maxRecordCountFactor:void 0,returnExceededLimitFeatures:!1,quantizationParameters:n});c++;try{const t=await this._queue.push({tile:r,query:m,signal:o,depth:l});if(c--,e(o),!t)return;if(p!==this._queryInfo)return void d(u,l);if(t.exceededTransferLimit&&l<i.maxDrillLevel){for(const e of u.createChildTiles())d(e,l+1);return}const s={id:r.id,addOrUpdate:t,end:0===c,type:\"append\"};a.add({query:m,message:s}),this._onMessage(s)}catch(h){t(h)||this._onMessage({id:r.id,addOrUpdate:null,end:!0,type:\"append\"})}};d(r,0)}}export{a as DrillDownFeatureSource};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../../../core/Error.js\";import t from\"../../../../../core/Logger.js\";import{isSome as r}from\"../../../../../core/maybe.js\";import{isAbortError as a,throwIfAborted as s,throwIfNotAbortError as i}from\"../../../../../core/promiseUtils.js\";import{BaseFeatureSource as o}from\"./BaseFeatureSource.js\";class n extends o{constructor(e){super(e)}async _fetchDataTile(r){const i=6,o=20,n=this._subscriptions.get(r.key.id);let d=!1,c=0,u=0;const p=(e,t)=>{u--,s(n);const a=r.id,i=e.reader,o=e.query;if(!i.exceededTransferLimit){if(d=!0,0!==t&&!i.hasFeatures){const e={id:a,addOrUpdate:i,end:0===u,type:\"append\"};return n.add({message:e,query:o}),void this._onMessage(e)}const e={id:a,addOrUpdate:i,end:0===u,type:\"append\"};return n.add({message:e,query:o}),void this._onMessage(e)}const c={id:a,addOrUpdate:i,end:d&&0===u,type:\"append\"};n.add({message:c,query:o}),this._onMessage(c)};let h=0,m=0;for(;!d&&m++<o;){let o;for(let s=0;s<h+1;s++){const s=c++;u++,o=this._fetchDataTilePage(r,s,n).then((e=>e&&p(e,s))).catch((s=>{d=!0,a(s)||(t.getLogger(\"esri.views.2d.layers.features.sources.PagedFeatureSource\").error(new e(\"mapview-query-error\",\"Encountered error when fetching tile\",{tile:r,error:s})),this._onMessage({id:r.id,addOrUpdate:null,end:d,type:\"append\"}))}))}await o,s(n),h=Math.min(h+2,i)}}async _fetchDataTilePage(e,t,a){s(a);const o=this._queryInfo,n={start:this.pageSize*t,num:this.pageSize,returnExceededLimitFeatures:!0,quantizationParameters:e.getQuantizationParameters()};r(this.maxRecordCountFactor)&&(n.maxRecordCountFactor=this.maxRecordCountFactor);const d=this.createTileQuery(e,n);try{const r=a.signal,i=await this._queue.push({tile:e,query:d,signal:r,depth:t});return s(a),i?o!==this._queryInfo?this._fetchDataTilePage(e,t,a):{reader:i,query:d}:null}catch(c){return i(c),null}}}export{n as PagedFeatureSource};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../../core/has.js\";import t from\"../../../../../core/Logger.js\";import{isSome as e,unwrapOrThrow as s,isNone as r}from\"../../../../../core/maybe.js\";import{throwIfAborted as o,after as a}from\"../../../../../core/promiseUtils.js\";import n from\"../../../../../core/RandomLCG.js\";import{BaseFeatureSource as i}from\"./BaseFeatureSource.js\";import{FeatureSetReaderIndirect as d}from\"../support/FeatureSetReaderPBFIndirect.js\";import{UpdateToken as u}from\"../support/UpdateToken.js\";function h(t,e,s){const r=t.getXHydrated(),o=t.getYHydrated(),a=e.getColumnForX(r),n=Math.floor(e.normalizeCol(a));return`${s}/${Math.floor(e.getRowForY(o))}/${n}`}function l(t,e){if(r(t))return null;const s=e.transform,o=t.getQuantizationTransform();if(r(o)){const[e,r]=s.scale,[o,a]=s.translate,n=-o/e,i=1/e,d=a/r,u=1/-r;return t.transform(n,d,i,u)}const[a,n]=o.scale,[i,d]=o.translate,[u,h]=s.scale,[l,c]=s.translate,g=a/u,_=(i-l)/u,p=n/h,f=(-d+c)/h;return t.transform(_,f,g,p)}class c extends i{constructor(t){super(t),this.mode=\"snapshot\",this._loading=!0,this._controller=new AbortController,this._downloadPromise=null,this._didSendEnd=!1,this._queries=new Array,this._invalidated=!1,this._hasAggregates=!1,this._random=new n(1e3),this._store=t.store,this._markedIdsBufId=this._store.storage.createBitset()}destroy(){super.destroy(),this._controller.abort()}get loading(){return this._loading}get _signal(){return this._controller.signal}update(t,s){super.update(t,s),null==this._featureCount&&(this._featureCount=s.initialFeatureCount),e(s.changedFeatureCount)&&(this._featureCount=s.changedFeatureCount),this._hasAggregates=!!t.targets?.aggregate}async resend(t=!1){if(await this._downloadPromise,this._invalidated||t){const t=s(this._featureCount,\"Expected featureCount to be defined\");return this._invalidated=!1,this._subscriptions.forEach((t=>t.clear())),this._downloadPromise=this._download(t),void await this._downloadPromise}const e=this._queries.map((({query:t,reader:e})=>this._sendPatchQuery(t,e)));await Promise.all(e),this._subscriptions.forEach((t=>{t.requests.done.forEach((t=>this._onMessage(t.message)))}))}async refresh(t,e){e&&(this._featureCount=e.featureCount),await this.resend(!0)}async _sendPatchQuery(t,s){const r=e(t.outFields)?t.outFields:[],a=this._queryInfo.outFields,n=a.filter((t=>!r.includes(t)));if(!n.length)return;const i=t.clone(),d=this._signal;i.returnGeometry=!1,i.returnCentroid=!1,i.outFields=n,t.outFields=a;const u=await this._queue.push({query:i,depth:0,signal:d});o({signal:d}),s.joinAttributes(u)}async _fetchDataTile(t){if(!this._downloadPromise){const t=s(this._featureCount,\"Expected featureCount to be defined\");this._downloadPromise=this._download(t)}const e=this._store.search(t),r=this._subscriptions.get(t.key.id),o=e.length-1;for(let s=0;s<o;s++){const o=l(e[s],t),n={type:\"append\",id:t.id,addOrUpdate:o,end:!1,status:u.empty()};r.add({query:null,message:n}),this._hasAggregates||await a(1),this._onMessage(n)}const n=l(o>=0?e[o]:null,t),i=this._didSendEnd,d={type:\"append\",id:t.id,addOrUpdate:n,end:i,status:u.empty()};r.add({query:null,message:d}),this._onMessage(d)}async _download(e){try{await this.whenInitialized();const t=this._store.storage.getBitset(this._markedIdsBufId),s=new Set;t.clear();const r=Math.ceil(e/this.pageSize),o=Array.from({length:r},((t,e)=>e)).sort(((t,e)=>this._random.getInt()-this._random.getInt())).map((e=>this._downloadPage(e,t,s)));await Promise.all(o),this._store.sweepFeatures(t,this._store.storage),this._store.sweepFeatureSets(s)}catch(s){t.getLogger(\"esri.views.2d.layers.features.sources.SnapshotFeatureSource\").error(\"mapview-snapshot-source\",\"Encountered and error when downloading feature snapshot\",s)}this._sendEnd(),this._loading=!1}async _downloadPage(t,s,r){const a=this.pageSize,n={start:t*a,num:a,cacheHint:!0};e(this.maxRecordCountFactor)&&(n.maxRecordCountFactor=this.maxRecordCountFactor);const i=this.createQuery(n),d=this._signal,u=await this._queue.push({query:i,depth:t,signal:d});o({signal:d}),this._queries.push({query:i,reader:u}),this._store.insert(u),r.add(u.instance);const h=u.getCursor();for(;h.next();)s.set(h.getDisplayId());this._send(u)}_send(t){if(!this._subscriptions.size)return;let s=null;const o=new Map,a=new Set,n=new Map;this._subscriptions.forEach((t=>{const e=t.tile;o.set(e.key.id,null),s=e.tileInfoView,a.add(e.level);const{row:r,col:i}=e.key,d=`${e.level}/${r}/${i}`,u=n.get(d)??[];u.push(t),n.set(d,u)}));for(const e of a){const a=s.getLODInfoAt(e),i=t.getCursor();for(;i.next();){const t=h(i,a,e),s=i.getIndex();if(n.has(t))for(const e of n.get(t)){const t=e.tile.id;let a=o.get(t);r(a)&&(a=[],o.set(t,a)),a.push(s)}}}o.forEach(((s,r)=>{if(e(s)){const e=this._subscriptions.get(r),o={type:\"append\",id:r,addOrUpdate:l(d.from(t,s),e.tile),end:!1,status:u.empty()};e.add({query:null,message:o}),this._onMessage(o)}}))}_sendEnd(){this._subscriptions.forEach((t=>{const e={type:\"append\",id:t.tile.id,addOrUpdate:null,end:!0,status:u.empty()};t.add({query:null,message:e}),this._onMessage(e)})),this._didSendEnd=!0}}export{c as SnapshotFeatureSource};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../../core/CircularArray.js\";import{clamp as e}from\"../../../core/mathUtils.js\";import{isNone as s,isSome as i,unwrapOr as r,unwrap as o}from\"../../../core/maybe.js\";const d=\"__esri_stream_id__\",a=\"__esri_timestamp__\",h=1e3;class n{constructor(t,e,s,i,r=128){this._trackIdToObservations=new Map,this._idCounter=0,this._lastPurge=performance.now(),this._addOrUpdated=new Map,this._removed=[],this._maxAge=0,this._timeInfo=s,this._purgeOptions=i,this.store=t,this.objectIdField=e,this.purgeInterval=r,this._useGeneratedIds=this.objectIdField===d}removeById(t){this._removed.push(t)}removeByTrackId(t){const e=this._trackIdToObservations.get(t);if(e)for(const s of e.entries)this._removed.push(s)}add(r){if(this._useGeneratedIds){const t=this._nextId();r.attributes[this.objectIdField]=t,r.objectId=t}else r.objectId=r.attributes[this.objectIdField];const o=r.objectId;if(this._addOrUpdated.set(o,r),this._maxAge=Math.max(this._maxAge,r.attributes[this._timeInfo.startTimeField]),!this._timeInfo.trackIdField)return s(this._trackIdLessObservations)&&(this._trackIdLessObservations=new t(1e5)),void this._trackIdLessObservations.enqueue(o);const d=r.attributes[this._timeInfo.trackIdField];if(!this._trackIdToObservations.has(d)){const s=i(this._purgeOptions)&&null!=this._purgeOptions.maxObservations?this._purgeOptions.maxObservations:h,r=e(s,0,h);this._trackIdToObservations.set(d,new t(r))}const a=this._trackIdToObservations.get(d)?.enqueue(o);i(a)&&(this._addOrUpdated.has(a)?this._addOrUpdated.delete(a):this._removed.push(a))}checkForUpdates(){const t=this._getToAdd(),e=this._getToRemove(),s=performance.now();s-this._lastPurge>=this.purgeInterval&&(this._purge(s),this._lastPurge=s);const o=[];if(i(e))for(const r of e){const t=this.store.removeById(r);i(t)&&o.push(t)}const d=[];if(i(t)){const i=new Set(r(e,[]));for(const e of t)i.has(e.objectId)||(e.attributes[a]=s,this.store.add(e),d.push(e))}(d.length||o?.length)&&this.store.update(d,o)}_getToAdd(){if(!this._addOrUpdated.size)return null;const t=new Array(this._addOrUpdated.size);let e=0;return this._addOrUpdated.forEach((s=>t[e++]=s)),this._addOrUpdated.clear(),t}_getToRemove(){const t=this._removed;return this._removed.length?(this._removed=[],t):null}_nextId(){const t=this._idCounter;return this._idCounter=(this._idCounter+1)%4294967294+1,t}_purge(t){const e=this._purgeOptions;i(e)&&(this._purgeSomeByDisplayCount(e),this._purgeByAge(e),this._purgeByAgeReceived(t,e),this._purgeTracks())}_purgeSomeByDisplayCount(t){if(!t.displayCount)return;let e=this.store.size;if(e>t.displayCount){if(this._timeInfo.trackIdField)for(const s of this._trackIdToObservations.values())if(e>t.displayCount&&s.size){const t=o(s.dequeue());this._removed.push(t),e--}if(i(this._trackIdLessObservations)){let s=e-t.displayCount;for(;s-- >0;){const t=this._trackIdLessObservations.dequeue();i(t)&&this._removed.push(t)}}}}_purgeByAge(t){const e=this._timeInfo?.startTimeField;if(!t.age||!e)return;const s=60*t.age*1e3,i=this._maxAge-s;this.store.forEach((t=>{t.attributes[e]<i&&this._removed.push(t.objectId)}))}_purgeByAgeReceived(t,e){if(!e.ageReceived)return;const s=t-60*e.ageReceived*1e3;this.store.forEach((t=>{t.attributes[a]<s&&this._removed.push(t.objectId)}))}_purgeTracks(){this._trackIdToObservations.forEach(((t,e)=>{0===t.size&&this._trackIdToObservations.delete(e)}))}}export{d as DEFAULT_STREAM_ID_FIELD,a as ESRI_TIMESTAMP,n as StreamFeatureManager};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../chunks/tslib.es6.js\";import\"../../../../../core/has.js\";import{isSome as t}from\"../../../../../core/maybe.js\";import{property as s}from\"../../../../../core/accessorSupport/decorators/property.js\";import\"../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../core/arrayUtils.js\";import{subclass as r}from\"../../../../../core/accessorSupport/decorators/subclass.js\";import{r as i}from\"../../../../../chunks/rbush.js\";import{convertFromFeature as o,quantizeOptimizedGeometry as n,quantizeX as a,quantizeY as c}from\"../../../../../layers/graphics/featureConversionUtils.js\";import d from\"../../../../../layers/graphics/OptimizedGeometry.js\";import{StreamFeatureManager as h}from\"../../../../../layers/graphics/data/StreamFeatureManager.js\";import{createConnection as u}from\"../../../../../layers/graphics/sources/connections/createConnection.js\";import{DataTileSource as p}from\"./DataTileSource.js\";import{FeatureSetReaderJSON as l}from\"../support/FeatureSetReaderJSON.js\";import{UpdateToken as m}from\"../support/UpdateToken.js\";const _=2500;function y(e,s){const r=e.weakClone();if(t(e.geometry)){const t=a(s,e.geometry.coords[0]),i=c(s,e.geometry.coords[1]);r.geometry=new d([],[t,i])}return r}function g(e){return\"esriGeometryPoint\"===e?y:(t,s)=>{const r=t.weakClone(),i=new d,o=!1,a=!1,c=n(i,t.geometry,o,a,e,s,!1,!1);return r.geometry=c,r}}function f(e){return\"esriGeometryPoint\"===e?e=>t(e.geometry)?{minX:e.geometry.coords[0],minY:e.geometry.coords[1],maxX:e.geometry.coords[0],maxY:e.geometry.coords[1]}:{minX:1/0,minY:1/0,maxX:-1/0,maxY:-1/0}:e=>{let s=1/0,r=1/0,i=-1/0,o=-1/0;return t(e.geometry)&&e.geometry.forEachVertex(((e,t)=>{s=Math.min(s,e),r=Math.min(r,t),i=Math.max(i,e),o=Math.max(o,t)})),{minX:s,minY:r,maxX:i,maxY:o}}}function b(e,t){const s=i(9,f(t));return s.load(e),s}function v(e,t){return e.search({minX:t.bounds[0],minY:t.bounds[1],maxX:t.bounds[2],maxY:t.bounds[3]})}class I{constructor(e,t){this.onUpdate=e,this._geometryType=t,this._objectIdToFeature=new Map,this._index=null}get _features(){const e=[];return this._objectIdToFeature.forEach((t=>e.push(t))),e}add(e){this._objectIdToFeature.set(e.objectId,e),this._index=null}get(e){return this._objectIdToFeature.has(e)?this._objectIdToFeature.get(e):null}forEach(e){this._objectIdToFeature.forEach(e)}search(e){return this._index||(this._index=b(this._features,this._geometryType)),v(this._index,e)}clear(){this._index=null,this._objectIdToFeature.clear()}removeById(e){const t=this._objectIdToFeature.get(e);return t?(this._objectIdToFeature.delete(e),this._index=null,t):null}update(e,t){this.onUpdate(e,t)}get size(){return this._objectIdToFeature.size}}let T=class extends p{constructor(e){super(e),this.type=\"stream\",this._updateIntervalId=0,this._level=0,this._updateInfo={websocket:0,client:0},this._isPaused=!1,this._inUpdate=!1;const{outSR:t}=e,{geometryType:s,objectIdField:r,timeInfo:i,purgeOptions:o,source:n,spatialReference:a,serviceFilter:c,maxReconnectionAttempts:d,maxReconnectionInterval:p,updateInterval:l,customParameters:m,enabledEventTypes:y}=e.serviceInfo,f=new I(this._onUpdate.bind(this),s),b=new h(f,r,i,o),v=u(n,a,t,s,c,d,p,m??{});this._store=f,this._manager=b,this._connection=v,this._quantize=g(s),this._enabledEventTypes=new Set(y),this._handles=[this._connection.on(\"data-received\",(e=>this._onFeature(e))),this._connection.on(\"message-received\",(e=>this._onWebSocketMessage(e)))],this._initUpdateInterval=()=>{let t=performance.now();this._updateIntervalId=setInterval((()=>{const s=performance.now(),r=s-t;if(r>_){t=s;const e=Math.round(this._updateInfo.client/(r/1e3)),i=Math.round(this._updateInfo.websocket/(r/1e3));this._updateInfo.client=0,this._updateInfo.websocket=0,this.events.emit(\"updateRate\",{client:e,websocket:i})}e.canAcceptRequest()&&!this._inUpdate&&this._manager.checkForUpdates()}),l)},this._initUpdateInterval()}destroy(){super.destroy(),this._clearUpdateInterval(),this._handles.forEach((e=>e.remove())),this._connection.destroy()}_fetchDataTile(){}get connectionStatus(){return this._isPaused?\"paused\":this._connection?.connectionStatus}get errorString(){return this._connection?.errorString}updateCustomParameters(e){this._connection.updateCustomParameters(e)}pauseStream(){this._isPaused||(this._isPaused=!0,this._clearUpdateInterval())}resumeStream(){this._isPaused&&(this._isPaused=!1,this._initUpdateInterval())}sendMessageToSocket(e){this._connection.sendMessageToSocket(e)}sendMessageToClient(e){this._connection.sendMessageToClient(e)}enableEvent(e,t){t?this._enabledEventTypes.add(e):this._enabledEventTypes.delete(e)}get updating(){return!1}subscribe(e,t){super.subscribe(e,t);const s=this._subscriptions.get(e.id);this._level=e.level;const r=this._getTileFeatures(e);this._onMessage({type:\"append\",id:e.key.id,addOrUpdate:r,end:!0}),s.didSend=!0}unsubscribe(e){super.unsubscribe(e)}*readers(e){const t=this._subscriptions.get(e),{tile:s}=t;yield this._getTileFeatures(s)}createTileQuery(e){throw new Error(\"Service does not support tile  queries\")}async resend(){this._subscriptions.forEach((e=>{const{tile:t}=e,s={type:\"append\",id:t.id,addOrUpdate:this._getTileFeatures(t),end:!0};this._onMessage(s)}))}_getTileFeatures(e){const t=this._store.search(e).map((t=>this._quantize(t,e.transform)));return l.fromOptimizedFeatures(t,this._serviceInfo,e.transform)}_onWebSocketMessage(e){if(this._enabledEventTypes.has(\"message-received\")&&this.events.emit(\"message-received\",e),\"type\"in e)switch(e.type){case\"delete\":if(e.objectIds)for(const t of e.objectIds)this._manager.removeById(t);if(e.trackIds)for(const t of e.trackIds)this._manager.removeByTrackId(t);break;case\"clear\":this._store.forEach((e=>this._manager.removeById(e.objectId)))}}_onFeature(e){this._updateInfo.websocket++;try{this._enabledEventTypes.has(\"data-received\")&&this.events.emit(\"data-received\",e);const t=o(e,this._serviceInfo.geometryType,!1,!1,this._serviceInfo.objectIdField);this._manager.add(t)}catch(t){}}_clearUpdateInterval(){clearInterval(this._updateIntervalId),this._updateIntervalId=0}async _onUpdate(e,s){this._inUpdate=!0;try{t(e)&&(this._updateInfo.client+=e.length),this._subscriptions.forEach(((e,t)=>{e.didSend&&e.tile.level===this._level&&this._onMessage({type:\"append\",id:t,addOrUpdate:null,clear:!0,end:!1})}));const s=[];this._subscriptions.forEach(((e,t)=>{if(!e.didSend||e.tile.level!==this._level)return;const r=e.tile,i={type:\"append\",id:t,addOrUpdate:this._getTileFeatures(r),remove:[],end:!1,status:m.empty()};e.requests.stream.enqueue(i),s.push(this._onMessage(i))})),await Promise.all(s),this._subscriptions.forEach(((e,t)=>{e.didSend&&e.tile.level===this._level&&this._onMessage({type:\"append\",id:t,addOrUpdate:null,end:!0})}))}catch{}this._inUpdate=!1}};e([s()],T.prototype,\"_isPaused\",void 0),e([s()],T.prototype,\"connectionStatus\",null),e([s()],T.prototype,\"errorString\",null),T=e([r(\"esri.views.2d.layers.features.sources\")],T);export{T as StreamSource};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{unwrapOr as e}from\"../../../../../core/maybe.js\";import{isHostedAgolService as r}from\"../../../../../layers/support/arcgisLayerUrl.js\";import{DrillDownFeatureSource as t}from\"./DrillDownFeatureSource.js\";import{PagedFeatureSource as o}from\"./PagedFeatureSource.js\";import{SnapshotFeatureSource as s}from\"./SnapshotFeatureSource.js\";import{StreamSource as n}from\"./StreamSource.js\";function a(e,r,a,u,i,p){const f=c(e,r,a,u,i,p);switch(f.type){case\"feature\":switch(f.origin){case\"hosted\":case\"local\":return new o(f);case\"snapshot\":return new s(f);default:return new t(f)}case\"stream\":return new n(f)}}function c(t,o,s,n,a,c){switch(t.type){case\"snapshot\":return{type:\"feature\",origin:\"snapshot\",featureCount:e(t.featureCount,0),serviceInfo:t,onMessage:n,outSR:o,tileInfoView:s,canAcceptRequest:a,store:c};case\"stream\":return{type:\"stream\",serviceInfo:t,onMessage:n,outSR:o,canAcceptRequest:a};case\"memory\":case\"on-demand\":return{type:\"feature\",serviceInfo:t,onMessage:n,outSR:o,origin:u(t.source),tileInfoView:s,canAcceptRequest:a}}function u(e){return Array.isArray(e)?\"local\":\"path\"in e&&r(e.path)?\"hosted\":\"unknown\"}}export{a as createSource};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nconst t=16,n=8,o=4,e=2,r=1,c=new Float64Array(2),f=new Float64Array(2),l=-90,u=90,h=-180,s=180,i=\"0123456789bcdefghjkmnpqrstuvwxyz\";function a(t){return t<=57?t-48:t<=104?t-88:t<=107?t-89:t<=110?t-90:t-91}function g(t){return i[t]}function A(t){return(t[0]+t[1])/2}function d(t,n,o){return t[0]=n,t[1]=o,t}function M(t,n){const o=A(t),e=n,r=!n;t[0]=r*t[0]+e*o,t[1]=r*o+e*t[1]}function w(t,n){const o=n>A(t);return M(t,o),o}function y(i,g){const w=d(c,l,u),y=d(f,h,s);for(let c=0;c<g.length;c++){const f=a(g.charCodeAt(c));c%2==0?(M(y,!!(t&f)),M(y,!!(o&f)),M(y,!!(r&f)),M(w,!!(n&f)),M(w,!!(e&f))):(M(w,!!(t&f)),M(w,!!(o&f)),M(w,!!(r&f)),M(y,!!(n&f)),M(y,!!(e&f)))}return i[0]=A(w),i[1]=A(y),i}function C(t,n){let o=0,e=0,r=30,c=30;for(let f=0;f<n.length;f++){const t=a(n.charCodeAt(f)),l=f%2==0;r-=l?3:2,c-=l?2:3,o|=F(t,l)<<r,e|=j(t,l)<<c}return{geohashX:o,geohashY:e}}function X(t,n){let o=-90,e=90,r=-180,c=180;for(let f=0;f<n;f++){const n=Math.ceil((f+1)/2),l=Math.floor((f+1)/2),u=1-f%2,h=30-(3*n+2*l),s=30-(2*n+3*l),i=3*u+2*(1-u),a=2*u+3*(1-u),g=3*u+7*(1-u)<<s,A=(7*u+3*(1-u)<<h&t.geohashX)>>h,d=(g&t.geohashY)>>s;for(let t=i-1;t>=0;t--){const n=(r+c)/2,o=A&1<<t?1:0;r=(1-o)*r+o*n,c=(1-o)*n+o*c}for(let t=a-1;t>=0;t--){const n=(o+e)/2,r=d&1<<t?1:0;o=(1-r)*o+r*n,e=(1-r)*n+r*e}}return[r,o,c,e]}function Y(t,n,o,e){e%2&&(e+=1);let r=0,c=0,f=-90,l=90,u=-180,h=180;for(let s=0;s<e/2;s++){for(let t=0;t<5;t++){const n=(u+h)/2,e=o>n?1:0;r|=e<<29-(t+5*s),u=(1-e)*u+e*n,h=(1-e)*n+e*h}for(let t=0;t<5;t++){const o=(f+l)/2,e=n>o?1:0;c|=e<<29-(t+5*s),f=(1-e)*f+e*o,l=(1-e)*o+e*l}}t.geohashX=r,t.geohashY=c}function b(t,n,o,e,r){r%2&&(r+=1);let c=0,f=0,l=-90,u=90,h=-180,s=180;for(let i=0;i<r/2;i++){for(let t=0;t<5;t++){const n=(h+s)/2,o=e>n?1:0;c|=o<<29-(t+5*i),h=(1-o)*h+o*n,s=(1-o)*n+o*s}for(let t=0;t<5;t++){const n=(l+u)/2,e=o>n?1:0;f|=e<<29-(t+5*i),l=(1-e)*l+e*n,u=(1-e)*n+e*u}}t[2*n]=c,t[2*n+1]=f}function p(t,n,o){let e=\"\";const r=d(c,-90,90),l=d(f,-180,180);for(let c=0;c<o;c++){let o=0;!(c%2)?(o|=w(l,n)<<4,o|=w(r,t)<<3,o|=w(l,n)<<2,o|=w(r,t)<<1,o|=w(l,n)<<0):(o|=w(r,t)<<4,o|=w(l,n)<<3,o|=w(r,t)<<2,o|=w(l,n)<<1,o|=w(r,t)<<0),e+=g(o)}return e}function x(t,n,c){return c?t&r|(n&r)<<1|(t&e)<<1|(n&e)<<2|(t&o)<<2:n&r|(t&r)<<1|(n&e)<<1|(t&e)<<2|(n&o)<<2}function F(c,f){return f?r&c|(o&c)>>1|(t&c)>>2:(e&c)>>1|(n&c)>>2}function j(c,f){return f?(e&c)>>1|(n&c)>>2:r&c|(o&c)>>1|(t&c)>>2}function k(c,f,l){const u=!((c.length-1)%2),h=c.substring(0,c.length-1),s=a(c.charCodeAt(c.length-1));let i=0,A=0,d=0,M=0;u?(i=8,A=4,d=r&s|(o&s)>>1|(t&s)>>2,M=(e&s)>>1|(n&s)>>2):(i=4,A=8,M=r&s|(o&s)>>1|(t&s)>>2,d=(e&s)>>1|(n&s)>>2);const w=d+f,y=M+l,C=Math.floor(w/i),X=Math.floor(y/A),Y=g(x(w-C*i,y-X*A,u));return c.length>1&&(C||X)?k(h,C,X)+Y:h+Y}export{C as convertGeohash32ToXY,a as decodeBase32Char,y as decodeGeohash,X as decodeGeohashXY,g as encodeBase32Char,p as encodeGeohash,k as getRelativeGeohash,b as setGeohashBuf,Y as setGeohashXY,F as unpackXBits,j as unpackYBits};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../core/CircularArray.js\";import{isSome as e,mapOr as s}from\"../core/maybe.js\";import{decodeGeohashXY as i}from\"./geohashUtils.js\";import n from\"../geometry/SpatialReference.js\";import{convertFromPolygon as o,quantizeOptimizedGeometry as a,convertFromPoint as r}from\"../layers/graphics/featureConversionUtils.js\";import l from\"../layers/graphics/OptimizedGeometry.js\";import{project as h}from\"../layers/graphics/data/projectionSupport.js\";import{FeatureSetReaderJSON as c}from\"../views/2d/layers/features/support/FeatureSetReaderJSON.js\";class u{constructor(e=[],s,i=8096){this.onRelease=t=>{},this._nodes=0,this._root=new d(this,0,0,0),this._statisticFields=e,this._pool=i?new t(8096):null,this._serviceInfo=s}destroy(){this.clear()}_acquire(t,s,i){this._nodes++;let n=null;return e(this._pool)&&(n=this._pool.dequeue()),e(n)?n.realloc(t,s,i):n=new d(this,t,s,i),n}_release(t){this.onRelease(t),this._nodes--,e(this._pool)&&this._pool.enqueue(t)}get count(){return this._root.count}get size(){return this._nodes}get poolSize(){return s(this._pool,0,(t=>t.size))}get depth(){let t=0;return this.forEach((e=>t=Math.max(t,e.depth))),t}dropLevels(t){this.forEach((e=>{if(e.depth>=t)for(let t=0;t<e.children.length;t++){const s=e.children[t];s&&this._release(s)}})),this.forEach((e=>{if(e.depth>=t)for(let t=0;t<e.children.length;t++)e.children[t]=null}))}clear(){this.forEach((t=>this._release(t))),this._root=new d(this,0,0,0)}insert(t,e,s=0){const i=c.fromOptimizedFeatures([t],this._serviceInfo).getCursor();i.next();const n=i.readGeometry();if(!n)return;const[o,a]=n.coords,r=t.geohashX,l=t.geohashY;this.insertCursor(i,t.displayId,o,a,r,l,e,s)}insertCursor(t,e,s,i,n,o,a,r=0){let l=this._root,h=0,c=0,u=0;for(;null!==l;){if(l.depth>=r&&(l.count+=1,l.xTotal+=s,l.yTotal+=i,l.xGeohashTotal+=n,l.yGeohashTotal+=o,l.referenceId=e,this._updateStatisticsCursor(t,l,1)),h>=a)return void l.add(e);const d=Math.ceil((h+1)/2),f=Math.floor((h+1)/2),x=1-h%2,m=30-(3*d+2*f),g=30-(2*d+3*f),y=(n&7*x+3*(1-x)<<m)>>m,p=(o&3*x+7*(1-x)<<g)>>g,_=y+p*(8*x+4*(1-x));c=c<<3*x+2*(1-x)|y,u=u<<2*x+3*(1-x)|p,null==l.children[_]&&(l.children[_]=this._acquire(c,u,h+1)),h+=1,l=l.children[_]}}remove(t,e){const s=c.fromOptimizedFeatures([t],this._serviceInfo).getCursor();s.next();const i=s.readGeometry();if(!i)return;const[n,o]=i.coords,a=t.geohashX,r=t.geohashY;this.removeCursor(s,n,o,a,r,e)}removeCursor(t,e,s,i,n,o){let a=this._root,r=0;for(;null!==a;){if(a.count-=1,a.xTotal-=e,a.yTotal-=s,a.xGeohashTotal-=i,a.yGeohashTotal-=n,this._updateStatisticsCursor(t,a,-1),r>=o)return void a.remove(t.getDisplayId());const l=Math.ceil((r+1)/2),h=Math.floor((r+1)/2),c=1-r%2,u=30-(3*l+2*h),d=30-(2*l+3*h),f=((i&7*c+3*(1-c)<<u)>>u)+((n&3*c+7*(1-c)<<d)>>d)*(8*c+4*(1-c)),x=a.children[f];1===x?.count&&(this._release(x),a.children[f]=null),r+=1,a=x}}forEach(t){let e=this._root;for(;null!==e;){const s=this._linkChildren(e)||e.next;t(e),e=s}}find(t,e,s){return this._root.find(t,e,s,0,0,0)}findIf(t){let e=null;return this.forEach((s=>{t(s)&&(e=s)})),e}findAllIf(t){const e=[];return this.forEach((s=>{t(s)&&e.push(s)})),e}findSingleOccupancyNode(t,e,s,i,n){let o=this._root;for(;null!==o;){const a=o.depth,r=o.xNode,l=o.yNode,h=1-a%2,c=o.xGeohashTotal/o.count,u=o.yGeohashTotal/o.count;if(1===o.count&&t<c&&c<=s&&e<u&&u<=i)return o;if(a>=n){o=o.next;continue}const d=Math.ceil((a+1)/2),f=Math.floor((a+1)/2),x=30-(3*d+2*f),m=30-(2*d+3*f),g=~((1<<x)-1),y=~((1<<m)-1),p=(t&g)>>x,_=(e&y)>>m,v=(s&g)>>x,M=(i&y)>>m,T=r<<3*h+2*(1-h),b=l<<2*h+3*(1-h),k=T+8*h+4*(1-h),N=b+4*h+8*(1-h),I=Math.max(T,p),C=Math.max(b,_),G=Math.min(k,v),L=Math.min(N,M);let S=null,w=null;for(let t=C;t<=L;t++)for(let e=I;e<=G;e++){const s=e-T+(t-b)*(8*h+4*(1-h)),i=o.children[s];i&&(S||(S=i,S.next=o.next),w&&(w.next=i),w=i,i.next=o.next)}o=S||o.next}return null}getRegionDisplayIds(t){let e=this._root;const{bounds:s,geohashBounds:i,level:n}=t,[o,a,r,l]=s,h=[];for(;null!==e;){const t=e.depth,s=e.xNode,c=e.yNode;if(t>=n){const t=e.xTotal/e.count,s=e.yTotal/e.count;t>=o&&t<=r&&s>=a&&s<=l&&e.displayIds.forEach((t=>h.push(t))),e=e.next;continue}const u=Math.ceil((t+1)/2),d=Math.floor((t+1)/2),f=1-t%2,x=30-(3*u+2*d),m=30-(2*u+3*d),g=~((1<<x)-1),y=~((1<<m)-1),p=(i.xLL&g)>>x,_=(i.yLL&y)>>m,v=(i.xTR&g)>>x,M=(i.yTR&y)>>m,T=s<<3*f+2*(1-f),b=c<<2*f+3*(1-f),k=T+8*f+4*(1-f),N=b+4*f+8*(1-f),I=Math.max(T,p),C=Math.max(b,_),G=Math.min(k,v),L=Math.min(N,M);let S=null,w=null;for(let i=C;i<=L;i++)for(let t=I;t<=G;t++){const s=t-T+(i-b)*(8*f+4*(1-f)),n=e.children[s];n&&(S||(S=n,S.next=e.next),w&&(w.next=n),w=n,n.next=e.next)}e=S||e.next}return h}getRegionStatistics(t){let e=this._root,s=0,i=0,n=0;const o={},{bounds:a,geohashBounds:r,level:l}=t,[h,c,u,d]=a;let f=0;for(;null!==e;){const t=e.depth,a=e.xNode,x=e.yNode;if(t>=l){const t=e.xTotal/e.count,a=e.yTotal/e.count;t>h&&t<=u&&a>c&&a<=d&&(s+=e.count,i+=e.xTotal,n+=e.yTotal,1===e.count&&(f=e.referenceId),this._aggregateStatistics(o,e.statistics)),e=e.next;continue}const m=Math.ceil((t+1)/2),g=Math.floor((t+1)/2),y=1-t%2,p=30-(3*m+2*g),_=30-(2*m+3*g),v=~((1<<p)-1),M=~((1<<_)-1),T=(r.xLL&v)>>p,b=(r.yLL&M)>>_,k=(r.xTR&v)>>p,N=(r.yTR&M)>>_,I=a<<3*y+2*(1-y),C=x<<2*y+3*(1-y),G=I+8*y+4*(1-y),L=C+4*y+8*(1-y),S=Math.max(I,T),w=Math.max(C,b),R=Math.min(G,k),F=Math.min(L,N);let j=null,z=null;for(let r=w;r<=F;r++)for(let t=S;t<=R;t++){const a=t-I+(r-C)*(8*y+4*(1-y)),l=e.children[a];if(l){if(r!==w&&r!==F&&t!==S&&t!==R){const t=l.xTotal/l.count,e=l.yTotal/l.count;t>h&&t<=u&&e>c&&e<=d&&(s+=l.count,i+=l.xTotal,n+=l.yTotal,1===l.count&&(f=l.referenceId),this._aggregateStatistics(o,l.statistics));continue}j||(j=l,j.next=e.next),z&&(z.next=l),z=l,l.next=e.next}}e=j||e.next}return{count:s,attributes:this.normalizeStatistics(o,s),xTotal:i,yTotal:n,referenceId:f}}getBins(t){const e=[],{geohashBounds:s,level:i}=t;let n=this._root;for(;null!==n;){const t=n.depth,o=n.xNode,a=n.yNode;if(t>=i){e.push(n),n=n.next;continue}const r=Math.ceil((t+1)/2),l=Math.floor((t+1)/2),h=1-t%2,c=30-(3*r+2*l),u=30-(2*r+3*l),d=~((1<<c)-1),f=~((1<<u)-1),x=(s.xLL&d)>>c,m=(s.yLL&f)>>u,g=(s.xTR&d)>>c,y=(s.yTR&f)>>u,p=o<<3*h+2*(1-h),_=a<<2*h+3*(1-h),v=p+8*h+4*(1-h),M=_+4*h+8*(1-h),T=Math.max(p,x),b=Math.max(_,m),k=Math.min(v,g),N=Math.min(M,y);let I=null,C=null;for(let e=b;e<=N;e++)for(let t=T;t<=k;t++){const s=t-p+(e-_)*(8*h+4*(1-h)),i=n.children[s];i&&(I||(I=i,I.next=n.next),C&&(C.next=i),C=i,i.next=n.next)}n=I||n.next}return e}_linkChildren(t){let e=null,s=null;for(let i=0;i<=t.children.length;i++){const n=t.children[i];n&&(e||(e=n,e.next=t.next),s&&(s.next=n),s=n,n.next=t.next)}return e}_updateStatisticsCursor(t,e,s){for(const i of this._statisticFields){const n=i.name,o=i.inField?t.readAttribute(i.inField):t.getComputedNumericAtIndex(i.inFieldIndex);switch(i.statisticType){case\"min\":{if(isNaN(o))break;if(!e.statistics[n]){e.statistics[n]={value:o};break}const t=e.statistics[n].value;e.statistics[n].value=Math.min(t,o);break}case\"max\":{if(isNaN(o))break;if(!e.statistics[n]){e.statistics[n]={value:o};break}const t=e.statistics[n].value;e.statistics[n].value=Math.max(t,o);break}case\"count\":break;case\"sum\":case\"avg\":{e.statistics[n]||(e.statistics[n]={value:0,nanCount:0});const t=e.statistics[n].value,i=e.statistics[n].nanCount??0;null==o||isNaN(o)?e.statistics[n].nanCount=i+s:e.statistics[n].value=t+s*o;break}case\"avg_angle\":{e.statistics[n]||(e.statistics[n]={x:0,y:0,nanCount:0});const t=e.statistics[n].x,i=e.statistics[n].y,a=e.statistics[n].nanCount??0,r=Math.PI/180;null==o||isNaN(o)?e.statistics[n].nanCount=a+s:(e.statistics[n].x=t+s*Math.cos(o*r),e.statistics[n].y=i+s*Math.sin(o*r));break}case\"mode\":{e.statistics[n]||(e.statistics[n]={});const t=e.statistics[n][o]||0;e.statistics[n][o]=t+s;break}}}}_aggregateStatistics(t,e){for(const s of this._statisticFields){const i=s.name;switch(s.statisticType){case\"min\":{if(!t[i]){t[i]={value:e[i].value};break}const s=t[i].value;t[i].value=Math.min(s,e[i].value);break}case\"max\":{if(!t[i]){t[i]={value:e[i].value};break}const s=t[i].value;t[i].value=Math.max(s,e[i].value);break}case\"count\":break;case\"sum\":case\"avg\":case\"avg_angle\":case\"mode\":t[i]||(t[i]={});for(const s in e[i]){const n=t[i][s]||0;t[i][s]=n+e[i][s]}}}}normalizeStatistics(t,e){const s={};for(const i of this._statisticFields){const n=i.name;switch(i.statisticType){case\"min\":case\"max\":{const i=t[n];if(!e||!i)break;s[n]=i.value;break}case\"count\":if(!e)break;s[n]=e;break;case\"sum\":{if(!e)break;const{value:i,nanCount:o}=t[n];if(!(e-o))break;s[n]=i;break}case\"avg\":{if(!e)break;const{value:i,nanCount:o}=t[n];if(!(e-o))break;s[n]=i/(e-o);break}case\"avg_angle\":{if(!e)break;const{x:i,y:o,nanCount:a}=t[n];if(!(e-a))break;const r=i/(e-a),l=o/(e-a),h=180/Math.PI,c=Math.atan2(l,r)*h;s[n]=c;break}case\"mode\":{const e=t[n];let i=0,o=0,a=null;for(const t in e){const s=e[t];s===i?o+=1:s>i&&(i=s,o=1,a=t)}s[n]=\"null\"===a||o>1?null:a;break}}}return s}}class d{constructor(t,e,s,i){this.count=0,this.xTotal=0,this.yTotal=0,this.statistics={},this.displayId=0,this.referenceId=0,this.displayIds=new Set,this.next=null,this.depth=0,this.xNode=0,this.yNode=0,this.xGeohashTotal=0,this.yGeohashTotal=0,this._tree=t,this.children=new Array(32);for(let n=0;n<this.children.length;n++)this.children[n]=null;this.xNode=e,this.yNode=s,this.depth=i}realloc(t,e,s){for(let i=0;i<this.children.length;i++)this.children[i]=null;return this.xNode=t,this.yNode=e,this.depth=s,this.next=null,this.xGeohashTotal=0,this.yGeohashTotal=0,this.displayId=0,this.referenceId=0,this.xTotal=0,this.yTotal=0,this.count=0,this.statistics={},this.displayIds.clear(),this}get id(){return`${this.xNode}.${this.yNode}`}add(t){this.displayIds.add(t)}remove(t){this.displayIds.delete(t)}getAttributes(){const t=this._tree.normalizeStatistics(this.statistics,this.count);return t.referenceId=null,t.aggregateId=this.id,t.aggregateCount=this.count,t}getGeometry(t,s){const i=this.getLngLatBounds(),[r,c,u,d]=i,f=h({rings:[[[r,c],[r,d],[u,d],[u,c],[r,c]]]},n.WGS84,t),x=o(new l,f,!1,!1);if(e(s)){return a(new l,x,!1,!1,\"esriGeometryPolygon\",s,!1,!1)}return x}getGeometryCentroid(t,s){const i=this.getLngLatBounds(),[o,c,u,d]=i,f=h({x:(o+u)/2,y:(c+d)/2},n.WGS84,t),x=r(new l,f);if(e(s)){return a(new l,x,!1,!1,\"esriGeometryPoint\",s,!1,!1)}return x}getLngLatBounds(){const t=this.depth,e=Math.ceil(t/2),s=Math.floor(t/2),n=30-(3*e+2*s),o=30-(2*e+3*s),a=this.xNode<<n,r=this.yNode<<o;return i({geohashX:a,geohashY:r},this.depth)}find(t,e,s,i,n,o){if(i>=s)return this;const a=1-i%2,r=3*a+2*(1-a),l=2*a+3*(1-a),h=30-n-r,c=30-o-l,u=((t&7*a+3*(1-a)<<h)>>h)+((e&3*a+7*(1-a)<<c)>>c)*(8*a+4*(1-a)),d=this.children[u];return null==d?null:d.find(t,e,s,i+1,n+r,o+l)}}export{d as GeohashNode,u as GeohashTree};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../../geometry.js\";import e from\"../../../../../core/Evented.js\";import has from\"../../../../../core/has.js\";import t from\"../../../../../core/Logger.js\";import{isNone as s,applySome as r,mapOr as i}from\"../../../../../core/maybe.js\";import{diff as o,hasDiff as a}from\"../../../../../core/accessorSupport/diffUtils.js\";import{GeohashTree as h}from\"../../../../../geohash/GeohashTree.js\";import{setGeohashBuf as n,setGeohashXY as d}from\"../../../../../geohash/geohashUtils.js\";import{create as l}from\"../../../../../geometry/support/aaBoundingBox.js\";import{earth as g}from\"../../../../../geometry/support/Ellipsoid.js\";import{getInfo as p}from\"../../../../../geometry/support/spatialReferenceUtils.js\";import{getBoundsOptimizedGeometry as c,convertToGeometry as f,convertFromPolygon as u}from\"../../../../../layers/graphics/featureConversionUtils.js\";import{OptimizedFeature as m}from\"../../../../../layers/graphics/OptimizedFeature.js\";import _ from\"../../../../../layers/graphics/OptimizedGeometry.js\";import{checkProjectionSupport as y,project as I}from\"../../../../../layers/graphics/data/projectionSupport.js\";import{createDisplayId as b}from\"../../../engine/webgl/DisplayId.js\";import{featureAdapter as j}from\"../FeatureStore2D.js\";import{Store2D as v}from\"../Store2D.js\";import{FeatureSetReaderJSON as B}from\"./FeatureSetReaderJSON.js\";import x from\"../../../../../geometry/SpatialReference.js\";import S from\"../../../../../geometry/Polygon.js\";import F from\"../../../../../geometry/Extent.js\";const G=t.getLogger(\"esri.view.2d.layers.features.support.BinStore\"),R=12,L=64,T=l(),A=5;function D(e){return 57.29577951308232*e}class O extends v{constructor(t,s,r,i){super(t,r),this.type=\"bin\",this.events=new e,this.objectIdField=\"aggregateId\",this.featureAdapter=j,this._geohashLevel=A,this._geohashBuf=[],this._serviceInfo=i,this.geometryInfo=t.geometryInfo,this._spatialReference=s,this._projectionSupportCheck=y(s,x.WGS84),this._bitsets.geohash=r.getBitset(r.createBitset()),this._bitsets.inserted=r.getBitset(r.createBitset())}destroy(){this._tree&&this._tree.destroy()}get featureSpatialReference(){return this._spatialReference}get fields(){return this._fields}async updateSchema(e,t){const r=this._schema;try{await super.updateSchema(e,t),await this._projectionSupportCheck}catch(n){}this._fields=this._schema.params.fields;const i=o(r,t);t&&(!s(i)||e.source||e.storage.filters)?((a(i,\"params.fields\")||a(i,\"params\")||!this._tree||e.source)&&(this._tree&&this._tree.destroy(),this._tree=new h(this._statisticFields,this._serviceInfo),this._tree.onRelease=e=>e.displayId&&this._storage.releaseDisplayId(e.displayId),this._geohashLevel=this._schema.params.fixedBinLevel,this._rebuildTree(),has(\"esri-2d-update-debug\")&&G.info(\"Aggregate mesh needs update due to tree changing\")),has(\"esri-2d-update-debug\")&&G.info(\"Aggregate mesh needs update due to tree changing\"),e.targets[t.name]=!0,e.mesh=!1):r&&(e.mesh=!0)}clear(){this._rebuildTree()}sweepFeatures(e,t){this._bitsets.inserted.forEachSet((s=>{if(!e.has(s)){const e=t.lookupByDisplayIdUnsafe(s);this._remove(e)}}))}sweepAggregates(e,t,s){}onTileData(e,t,r,i,o=!0){if(!this._schema||s(t.addOrUpdate))return t;this.events.emit(\"changed\");const a=this._getTransforms(e,this._spatialReference);{const e=t.addOrUpdate.getCursor();for(;e.next();)this._update(e,i)}if(t.status.mesh||!o)return t;const h=new Array;this._getBinsForTile(h,e,a,r),t.addOrUpdate=B.fromOptimizedFeatures(h,{...this._serviceInfo,geometryType:\"esriGeometryPolygon\"}),t.addOrUpdate.attachStorage(r),t.end=!0,t.isRepush||(t.clear=!0);{const s=t.addOrUpdate.getCursor();for(;s.next();){const t=s.getDisplayId();this._bitsets.computed.unset(t),this.setComputedAttributes(r,s,t,e.scale)}}return t}forEachBin(e){this._tree.forEach(e)}forEach(e){this._tree.forEach((t=>{if(t.depth!==this._geohashLevel)return;const s=this._toFeatureJSON(t),r=B.fromFeatures([s],{objectIdField:this.objectIdField,globalIdField:null,geometryType:this.geometryInfo.geometryType,fields:this.fields}).getCursor();r.next(),e(r)}))}forEachInBounds(e,t){}forEachBounds(e,t){const{hasM:r,hasZ:i}=this.geometryInfo;for(const o of e){const e=c(T,o.readGeometry(),i,r);s(e)||t(e)}}onTileUpdate(e){}getAggregate(e){const t=b(e,!0),s=this._tree.findIf((e=>e.displayId===t));return r(s,(e=>this._toFeatureJSON(e)))}getAggregates(){return this._tree.findAllIf((e=>e.depth===this._geohashLevel)).map(this._toFeatureJSON.bind(this))}getDisplayId(e){const t=this._tree.findIf((t=>t.id===e));return r(t,(e=>e.displayId))}getFeatureDisplayIdsForAggregate(e){const t=this._tree.findIf((t=>t.id===e));return i(t,[],(e=>Array.from(e.displayIds)))}getDisplayIdForReferenceId(e){const t=this._tree.findIf((t=>1===t.displayIds.size&&t.displayIds.has(e)));return r(t,(e=>e.displayId))}_toFeatureJSON(e){const t=this._spatialReference;return{displayId:e.displayId,attributes:e.getAttributes(),geometry:f(e.getGeometry(t),\"esriGeometryPolygon\",!1,!1),centroid:null}}_rebuildTree(){this._bitsets.computed.clear(),this._bitsets.inserted.clear(),this._tree&&this._tree.clear()}_remove(e){const t=e.getDisplayId(),s=e.getXHydrated(),r=e.getYHydrated(),i=this._geohashBuf[2*t],o=this._geohashBuf[2*t+1];this._bitsets.inserted.has(t)&&(this._bitsets.inserted.unset(t),this._tree.removeCursor(e,s,r,i,o,this._geohashLevel))}_update(e,t){const s=e.getDisplayId(),r=this._bitsets.inserted,i=t.isVisible(s);if(i===r.has(s))return;if(!i)return void this._remove(e);const o=e.getXHydrated(),a=e.getYHydrated();if(!this._setGeohash(s,o,a))return;const h=this._geohashBuf[2*s],n=this._geohashBuf[2*s+1];this._tree.insertCursor(e,s,o,a,h,n,this._geohashLevel),r.set(s)}_setGeohash(e,t,s){if(this._bitsets.geohash.has(e))return!0;const r=this._geohashBuf;if(this._spatialReference.isWebMercator){const i=D(t/g.radius),o=i-360*Math.floor((i+180)/360),a=D(Math.PI/2-2*Math.atan(Math.exp(-s/g.radius)));n(r,e,a,o,R)}else{const i=I({x:t,y:s},this._spatialReference,x.WGS84);if(!i)return!1;n(r,e,i.y,i.x,R)}return this._bitsets.geohash.set(e),!0}_getBinsForTile(e,t,s,r){try{const i=this._getGeohashBounds(t),o=this._tree.getBins(i);for(const t of o){t.displayId||(t.displayId=r.createDisplayId(!0));let i=null;const o=t.getGeometry(this._spatialReference,s.tile);o||(i=t.getGeometryCentroid(this._spatialReference,s.tile));const a=new m(o,t.getAttributes(),i);a.objectId=t.id,a.displayId=t.displayId,e.push(a)}}catch(i){return void G.error(\"Unable to get bins for tile\",t.key.id)}}_getGeohash(e,t,s){const r={geohashX:0,geohashY:0};return d(r,t,e,s),r}_getGeohashBounds(e){const t=this._getGeohashLevel(e.key.level),s=[e.extent.xmin,e.extent.ymin,e.extent.xmax,e.extent.ymax],r=S.fromExtent(F.fromBounds(s,this._spatialReference)),i=I(r,this._spatialReference,x.WGS84,{densificationStep:e.resolution*L}),o=u(new _,i,!1,!1),a=o.coords.filter(((e,t)=>!(t%2))),h=o.coords.filter(((e,t)=>t%2)),n=Math.min(...a),d=Math.min(...h),l=Math.max(...a),g=Math.max(...h),p=this._getGeohash(n,d,t),c=this._getGeohash(l,g,t);return{bounds:s,geohashBounds:{xLL:p.geohashX,yLL:p.geohashY,xTR:c.geohashX,yTR:c.geohashY},level:t}}_getGeohashLevel(e){return this._schema.params.fixedBinLevel}_getTransforms(e,t){const s={originPosition:\"upperLeft\",scale:[e.resolution,e.resolution],translate:[e.bounds[0],e.bounds[3]]},r=p(t);if(!r)return{tile:s,left:null,right:null};const[i,o]=r.valid;return{tile:s,left:{...s,translate:[o,e.bounds[3]]},right:{...s,translate:[i-o+e.bounds[0],e.bounds[3]]}}}}export{O as BinStore};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../../geometry.js\";import e from\"../../../../../core/Evented.js\";import has from\"../../../../../core/has.js\";import{isNone as t,isSome as s,applySome as r}from\"../../../../../core/maybe.js\";import{diff as o,hasDiff as a}from\"../../../../../core/accessorSupport/diffUtils.js\";import{GeohashTree as i}from\"../../../../../geohash/GeohashTree.js\";import{setGeohashBuf as h,setGeohashXY as n}from\"../../../../../geohash/geohashUtils.js\";import{create as l}from\"../../../../../geometry/support/aaBoundingBox.js\";import{earth as u}from\"../../../../../geometry/support/Ellipsoid.js\";import{getInfo as g}from\"../../../../../geometry/support/spatialReferenceUtils.js\";import{getBoundsOptimizedGeometry as c,quantizeX as d,quantizeY as f,convertFromPolygon as p}from\"../../../../../layers/graphics/featureConversionUtils.js\";import{OptimizedFeatureWithGeometry as m,OptimizedFeature as _}from\"../../../../../layers/graphics/OptimizedFeature.js\";import y from\"../../../../../layers/graphics/OptimizedGeometry.js\";import{checkProjectionSupport as I,project as b}from\"../../../../../layers/graphics/data/projectionSupport.js\";import{TILE_SIZE as v}from\"../../../engine/webgl/definitions.js\";import{DISPLAY_ID_TEXEL_MASK as R}from\"../../../engine/webgl/DisplayId.js\";import{featureAdapter as M}from\"../FeatureStore2D.js\";import{Store2D as x}from\"../Store2D.js\";import{FeatureSetReaderJSON as C}from\"./FeatureSetReaderJSON.js\";import j from\"../../../../../geometry/SpatialReference.js\";import L from\"../../../../../geometry/Polygon.js\";import B from\"../../../../../geometry/Extent.js\";const F=12,w=64,S=1,V=l();class T extends m{constructor(e,t,s,r,o){super(new y([],[t,s]),r,null,e),this.geohashBoundsInfo=o}get count(){return this.attributes.cluster_count}static create(e,t,s,r,o,a,i,h){const n=new T(t,s,r,a,i);return n.displayId=e.createDisplayId(!0),n.referenceId=h,n.tileLevel=o,n}update(e,t,s,r,o,a){return this.geometry.coords[0]=e,this.geometry.coords[1]=t,this.tileLevel=s,this.attributes=r,this.geohashBoundsInfo=o,this.referenceId=null,this.referenceId=a,this}toJSON(){return{attributes:{...this.attributes,aggregateId:this.objectId,referenceId:1===this.attributes.cluster_count?this.referenceId:null},geometry:{x:this.geometry.coords[0],y:this.geometry.coords[1]}}}}function D(e){return 57.29577951308232*e}class G extends x{constructor(t,s,r,o){super(t,r),this.type=\"cluster\",this.events=new e,this.objectIdField=\"aggregateId\",this.featureAdapter=M,this._geohashLevel=0,this._tileLevel=0,this._aggregateValueRanges={},this._aggregateValueRangesChanged=!1,this._geohashBuf=[],this._clusters=new Map,this._tiles=new Map,this._serviceInfo=o,this.geometryInfo=t.geometryInfo,this._spatialReference=s,this._projectionSupportCheck=I(s,j.WGS84),this._bitsets.geohash=r.getBitset(r.createBitset()),this._bitsets.inserted=r.getBitset(r.createBitset())}destroy(){this._tree.destroy()}get featureSpatialReference(){return this._spatialReference}get fields(){return this._fields}async updateSchema(e,s){const r=this._schema;try{await super.updateSchema(e,s),await this._projectionSupportCheck}catch(n){}this._fields=this._schema.params.fields;const h=o(r,s);s&&(!t(h)||e.source||e.storage.filters)?((a(h,\"params.fields\")||!this._tree||e.source)&&(this._tree&&this._tree.destroy(),this._tree=new i(this._statisticFields,this._serviceInfo),this._rebuildTree(),has(\"esri-2d-update-debug\")&&console.debug(\"Aggregate mesh needs update due to tree changing\")),has(\"esri-2d-update-debug\")&&console.debug(\"Applying Update - ClusterStore:\",h),e.targets[s.name]=!0,e.mesh=!1,this._aggregateValueRanges={}):r&&(e.mesh=!0)}clear(){this._rebuildTree()}sweepFeatures(e,t){this._bitsets.inserted.forEachSet((s=>{if(!e.has(s)){const e=t.lookupByDisplayIdUnsafe(s);this._remove(e)}}))}sweepAggregates(e,t,s){this._clusters.forEach(((r,o)=>{r&&r.tileLevel!==s&&(e.releaseDisplayId(r.displayId),t.unsetAttributeData(r.displayId),this._clusters.delete(o))}))}onTileData(e,s,r,o,a=!0){if(!this._schema||t(s.addOrUpdate))return s;this.events.emit(\"changed\");const i=this._getTransforms(e,this._spatialReference);{const e=s.addOrUpdate.getCursor();for(;e.next();)this._update(e,o)}if(s.status.mesh||!a)return s;const h=new Array,n=this._schema.params.clusterRadius;this._getClustersForTile(h,e,n,r,i),s.addOrUpdate=C.fromOptimizedFeatures(h,this._serviceInfo),s.addOrUpdate.attachStorage(r),s.clear=!0,s.end=!0;{const t=s.addOrUpdate.getCursor();for(;t.next();){const s=t.getDisplayId();this._bitsets.computed.unset(s),this.setComputedAttributes(r,t,s,e.scale)}}return this._aggregateValueRangesChanged&&s.end&&(this.events.emit(\"valueRangesChanged\",{valueRanges:this._aggregateValueRanges}),this._aggregateValueRangesChanged=!1),s}onTileUpdate({added:e,removed:t}){if(e.length){const t=e[0].level;this._tileLevel=t,this._setGeohashLevel(t)}if(!this._schema)return;const s=this._schema.params.clusterRadius;t.forEach((e=>{this._tiles.delete(e.key.id),this._markTileClustersForDeletion(e,s)}))}getAggregate(e){for(const t of this._clusters.values())if((t?.displayId&R)==(e&R))return t.toJSON();return null}getAggregates(){const e=[];for(const t of this._clusters.values())t?.tileLevel===this._tileLevel&&e.push(t.toJSON());return e}getDisplayId(e){const t=this._clusters.get(e);return t?t.displayId:null}getFeatureDisplayIdsForAggregate(e){const t=this._clusters.get(e);return t?this._tree.getRegionDisplayIds(t.geohashBoundsInfo):[]}getDisplayIdForReferenceId(e){for(const t of this._clusters.values())if(t?.referenceId===e)return t.displayId;return null}getAggregateValueRanges(){return this._aggregateValueRanges}forEach(e){this._clusters.forEach((t=>{if(!t)return;const s=t.toJSON(),r=C.fromFeatures([s],{objectIdField:this.objectIdField,globalIdField:null,geometryType:this.geometryInfo.geometryType,fields:this.fields}).getCursor();r.next(),e(r)}))}forEachInBounds(e,t){}forEachBounds(e,s){const{hasM:r,hasZ:o}=this.geometryInfo;for(const a of e){const e=c(V,a.readGeometry(),o,r);t(e)||s(e)}}size(){let e=0;return this.forEach((t=>e++)),e}_rebuildTree(){this._bitsets.computed.clear(),this._bitsets.inserted.clear(),this._tree&&this._tree.clear()}_remove(e){const t=e.getDisplayId(),s=e.getXHydrated(),r=e.getYHydrated(),o=this._geohashBuf[2*t],a=this._geohashBuf[2*t+1];this._bitsets.inserted.has(t)&&(this._bitsets.inserted.unset(t),this._tree.removeCursor(e,s,r,o,a,this._geohashLevel))}_update(e,t){const s=e.getDisplayId(),r=this._bitsets.inserted,o=t.isVisible(s);if(o===r.has(s))return;if(!o)return void this._remove(e);const a=e.getXHydrated(),i=e.getYHydrated();if(!this._setGeohash(s,a,i))return;const h=this._geohashBuf[2*s],n=this._geohashBuf[2*s+1];this._tree.insertCursor(e,s,a,i,h,n,this._geohashLevel),r.set(s)}_setGeohash(e,t,s){if(this._bitsets.geohash.has(e))return!0;const r=this._geohashBuf;if(this._spatialReference.isWebMercator){const o=D(t/u.radius),a=o-360*Math.floor((o+180)/360),i=D(Math.PI/2-2*Math.atan(Math.exp(-s/u.radius)));h(r,e,i,a,F)}else{const o=b({x:t,y:s},this._spatialReference,j.WGS84);if(!o)return!1;h(r,e,o.y,o.x,F)}return this._bitsets.geohash.set(e),!0}_getClustersForTile(e,o,a,i,h,n=!0){const l=this._schema.params.clusterPixelBuffer,u=2*a,g=Math.ceil(2**o.key.level*v/u)+1,c=Math.ceil(l/u)+0,p=Math.ceil(v/u),{row:m,col:y}=o.key,I=y*v,b=m*v,R=Math.floor(I/u)-c,M=Math.floor(b/u)-c,x=R+p+2*c,C=M+p+2*c,j=o.tileInfoView.getLODInfoAt(o.key.level);for(let v=R;v<=x;v++)for(let a=M;a<=C;a++){let l=v;j.wrap&&(l=v<0?v+g:v%g);const u=j.wrap&&v<0,c=j.wrap&&v%g!==v,p=this._lookupCluster(i,j,o.key.level,l,a,o);if(s(p)){const o=r(h,(e=>u?e.left:c?e.right:e.tile));if(n&&t(o))continue;if(!p.count)continue;if(s(o)&&n){const t=p.geometry.clone();let r=p.attributes;t.coords[0]=d(o,t.coords[0]),t.coords[1]=f(o,t.coords[1]),1===p.count&&s(p.referenceId)&&(r={...p.attributes,referenceId:p.referenceId});const a=new _(t,r);a.displayId=p.displayId,e.push(a)}}}}_getGeohashLevel(e){return Math.min(Math.ceil(e/2+2),F)}_setGeohashLevel(e){const t=this._getGeohashLevel(e),s=(Math.floor(t/S)+1)*S-1;if(this._geohashLevel!==s)return this._geohashLevel=s,this._rebuildTree(),void this._bitsets.geohash.clear()}_getTransforms(e,t){const s={originPosition:\"upperLeft\",scale:[e.resolution,e.resolution],translate:[e.bounds[0],e.bounds[3]]},r=g(t);if(!r)return{tile:s,left:null,right:null};const[o,a]=r.valid;return{tile:s,left:{...s,translate:[a,e.bounds[3]]},right:{...s,translate:[o-a+e.bounds[0],e.bounds[3]]}}}_getClusterId(e,t,s){return(15&e)<<28|(16383&t)<<14|16383&s}_markForDeletion(e,t,s){const r=this._getClusterId(e,t,s);this._clusters.delete(r)}_getClusterBounds(e,t,s){const r=this._schema.params.clusterRadius,o=2*r;let a=s%2?t*o:t*o-r;const i=s*o;let h=a+o;const n=i-o,l=2**e.level*v;e.wrap&&a<0&&(a=0),e.wrap&&h>l&&(h=l);const u=a/v,g=i/v,c=h/v,d=n/v;return[e.getXForColumn(u),e.getYForRow(g),e.getXForColumn(c),e.getYForRow(d)]}_getGeohash(e,t,s){const r={geohashX:0,geohashY:0};return n(r,t,e,s),r}_getGeohashBounds(e,t){const s=this._getGeohashLevel(e.key.level);if(this._spatialReference.isWebMercator){const[e,r,o,a]=t,i={x:e,y:r},h={x:o,y:a};let l=0,g=0,c=0,d=0;{const e=D(i.x/u.radius);l=e-360*Math.floor((e+180)/360),g=D(Math.PI/2-2*Math.atan(Math.exp(-i.y/u.radius)))}{const e=D(h.x/u.radius);c=e-360*Math.floor((e+180)/360),d=D(Math.PI/2-2*Math.atan(Math.exp(-h.y/u.radius)))}const f={geohashX:0,geohashY:0},p={geohashX:0,geohashY:0};n(f,g,l,s),n(p,d,c,s);return{bounds:[e,r,o,a],geohashBounds:{xLL:f.geohashX,yLL:f.geohashY,xTR:p.geohashX,yTR:p.geohashY},level:s}}const r=L.fromExtent(B.fromBounds(t,this._spatialReference)),o=b(r,this._spatialReference,j.WGS84,{densificationStep:e.resolution*w});if(!o)return null;const a=p(new y,o,!1,!1),i=a.coords.filter(((e,t)=>!(t%2))),h=a.coords.filter(((e,t)=>t%2)),l=Math.min(...i),g=Math.min(...h),c=Math.max(...i),d=Math.max(...h),f=this._getGeohash(l,g,s),m=this._getGeohash(c,d,s);return{bounds:t,geohashBounds:{xLL:f.geohashX,yLL:f.geohashY,xTR:m.geohashX,yTR:m.geohashY},level:s}}_lookupCluster(e,r,o,a,i,h){const n=this._getClusterId(o,a,i),l=this._clusters.get(n),u=this._getClusterBounds(r,a,i),g=this._getGeohashBounds(h,u);if(t(g))return null;const c=this._tree.getRegionStatistics(g),{count:d,xTotal:f,yTotal:p,referenceId:m}=c,_=d?f/d:0,y=d?p/d:0;if(0===d)return this._clusters.set(n,null),null;const I={cluster_count:d,...c.attributes},b=s(l)?l.update(_,y,o,I,g,m):T.create(e,n,_,y,o,I,g,m);if(0===d){const[e,t,s,r]=u;b.geometry.coords[0]=(e+s)/2,b.geometry.coords[1]=(t+r)/2}return this._clusters.set(n,b),this._updateAggregateValueRangeForCluster(b,b.tileLevel),b}_updateAggregateValueRangeForCluster(e,t){const s=this._aggregateValueRanges[t]||{minValue:1/0,maxValue:0},r=s.minValue,o=s.maxValue;s.minValue=Math.min(r,e.count),s.maxValue=Math.max(o,e.count),this._aggregateValueRanges[t]=s,r===s.minValue&&o===s.maxValue||(this._aggregateValueRangesChanged=!0)}_markTileClustersForDeletion(e,t){const s=2*t,r=Math.ceil(v/s),{row:o,col:a}=e.key,i=a*v,h=o*v,n=Math.floor(i/s),l=Math.floor(h/s);for(let u=n;u<n+r;u++)for(let t=l;t<l+r;t++)this._markForDeletion(e.key.level,u,t)}}export{T as ClusterInfo,G as ClusterStore};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../../chunks/tslib.es6.js\";import t from\"../../../../../core/Accessor.js\";import has from\"../../../../../core/has.js\";import{isNone as r,applySome as s,isSome as i,filterNones as a}from\"../../../../../core/maybe.js\";import{ignoreAbortErrors as o,after as n,throwIfAborted as u,throwIfNotAbortError as g,isAbortError as h}from\"../../../../../core/promiseUtils.js\";import{when as d,watch as c,whenOnce as l}from\"../../../../../core/reactiveUtils.js\";import{property as p}from\"../../../../../core/accessorSupport/decorators/property.js\";import\"../../../../../core/accessorSupport/ensureType.js\";import\"../../../../../core/arrayUtils.js\";import{subclass as y}from\"../../../../../core/accessorSupport/decorators/subclass.js\";import{convertToGeometry as f}from\"../../../../../layers/graphics/featureConversionUtils.js\";import{QueryEngine as m}from\"../../../../../layers/graphics/data/QueryEngine.js\";import S from\"../../../../../layers/support/FieldsIndex.js\";import{FeatureStore2D as _}from\"../FeatureStore2D.js\";import{createSource as v}from\"../sources/createSource.js\";import b from\"../support/AttributeStore.js\";import{BinStore as I}from\"../support/BinStore.js\";import{ClusterStore as Q}from\"../support/ClusterStore.js\";import{ComputedAttributeStorage as C}from\"../support/ComputedAttributeStorage.js\";import{FeatureSetReaderJSON as E}from\"../support/FeatureSetReaderJSON.js\";import{UpdateToken as w}from\"../support/UpdateToken.js\";import{QueueProcessor as F}from\"../../../../support/QueueProcessor.js\";const T=5e3,k=\"tileRenderer.featuresView.attributeView.initialize\",j=\"tileRenderer.featuresView.attributeView.requestUpdate\",x=\"tileRenderer.featuresView.requestRender\";function R(e){return\"worker:port-closed\"===e.name}function A(e){if(!h(e)&&!R(e))throw e}function U(e){return\"feature\"===e.type&&\"snapshot\"===e.mode}let q=class extends t{constructor(){super(...arguments),this._storage=new C,this._markedIdsBufId=this._storage.createBitset(),this._lastCleanup=performance.now(),this._cleanupNeeded=!1,this._invalidated=!1,this._tileToResolver=new Map,this._didEdit=!1,this._updateVersion=1,this.tileStore=null,this.config=null,this.processor=null,this.remoteClient=null,this.service=null}initialize(){this._initStores(),this._initSource(),this._updateQueue=new F({concurrency:\"stream\"===this._source.type?1:4,process:(e,t)=>this._onTileMessage(e,{signal:t})}),this.addHandles([this.tileStore.on(\"update\",this.onTileUpdate.bind(this)),d((()=>!this.updating),(()=>this.onIdle()))]),this._checkUpdating=setInterval((()=>this.notifyChange(\"updating\")),300)}_initSource(){const e=this.tileStore.tileScheme,t=()=>this._updateQueue&&this._updateQueue.length<50,r=(e,t)=>(this._invalidated=!0,this._patchTile(e,t));this._source=v(this.service,this.spatialReference,e,r,t,this.featureStore),this._proxyEvents()}_proxyEvents(){if(\"stream\"===this._source.type){const e=this._source.events,t=this._source;this.addHandles([c((()=>t.connectionStatus),(e=>this.remoteClient.invoke(\"setProperty\",{propertyName:\"connectionStatus\",value:e}).catch(A)),{initial:!0}),c((()=>t.errorString),(e=>this.remoteClient.invoke(\"setProperty\",{propertyName:\"errorString\",value:e}).catch(A)),{initial:!0}),e.on(\"data-received\",(e=>this.remoteClient.invoke(\"emitEvent\",{name:\"data-received\",event:{attributes:e.attributes,centroid:e.centroid,geometry:e.geometry}}).catch(A))),e.on(\"message-received\",(e=>this.remoteClient.invoke(\"emitEvent\",{name:\"message-received\",event:e}).catch(A))),e.on(\"updateRate\",(e=>this.remoteClient.invoke(\"emitEvent\",{name:\"update-rate\",event:{...e}}).catch(A)))])}}_initAttributeStore(e){this.attributeStore||(this.attributeStore=new b({type:\"remote\",initialize:(e,t)=>o(this.remoteClient.invoke(k,e,{signal:t}).catch(A)),update:(e,t)=>o(this.remoteClient.invoke(j,e,{signal:t}).catch(A)),render:e=>o(this.remoteClient.invoke(x,void 0,{signal:e}).catch(A))},e,(()=>this.notifyChange(\"updating\"))))}_initStores(){const e=\"snapshot\"===this.service.type?\"snapshot\":\"on-demand\",t={geometryInfo:{geometryType:this.service.geometryType,hasM:!1,hasZ:!1},spatialReference:this.spatialReference,fieldsIndex:this.fieldsIndex,fields:this.service.fields};this.featureStore=new _(t,this._storage,e)}_initQueryEngine(e){const t=this;this.featureQueryEngine?.destroy(),this.featureQueryEngine=new m({definitionExpression:e.schema.source.definitionExpression??void 0,fields:this.service.fields,geometryType:this.service.geometryType,objectIdField:this.service.objectIdField,hasM:!1,hasZ:!1,spatialReference:this.spatialReference.toJSON(),cacheSpatialQueries:!0,featureStore:this.featureStore,aggregateAdapter:{getFeatureObjectIds(e){if(r(t.aggregateStore))return[];return t.aggregateStore.getFeatureDisplayIdsForAggregate(e).map((e=>t.getObjectId(e)))}},timeInfo:this.service.timeInfo})}_initAggregateQueryEngine(e,t){if(this.aggregateQueryEngine?.destroy(),r(e))return;const s=t.targets.aggregate.params.fields.slice();this.aggregateQueryEngine=new m({definitionExpression:void 0,fields:s,geometryType:e.geometryInfo.geometryType,objectIdField:e.objectIdField,hasM:e.geometryInfo.hasM,hasZ:e.geometryInfo.hasZ,spatialReference:this.spatialReference.toJSON(),cacheSpatialQueries:!1,featureStore:e,aggregateAdapter:{getFeatureObjectIds:e=>[]}})}destroy(){this._updateQueue.destroy(),this._source.destroy(),this.featureQueryEngine?.destroy(),this.aggregateQueryEngine?.destroy(),this.attributeStore?.destroy();for(const e of this.tileStore.tiles)this._source.unsubscribe(e);clearInterval(this._checkUpdating)}get fieldsIndex(){return new S(this.service.fields)}get spatialReference(){return this.tileStore.tileScheme.spatialReference}get updating(){return this.isUpdating()}isUpdating(){const e=this._source.updating,t=!!this._updateQueue.length,r=!this.attributeStore||this.attributeStore.isUpdating(),s=e||t||r;return has(\"esri-2d-log-updating\")&&console.log(`Updating FeatureController2D: ${s}\\n  -> updatingSource ${e}\\n  -> updateQueue ${t}\\n  -> updatingAttributeStore ${r}\\n`),s}updateCustomParameters(e){\"stream\"===this._source.type&&this._source.updateCustomParameters(e)}enableEvent(e){this._source.enableEvent(e.name,e.value)}pause(){this._updateQueue.pause(),this._updateQueue.clear()}resume(){this._updateQueue.resume()}pauseStream(){\"stream\"===this._source.type&&this._source.pauseStream()}resumeStream(){\"stream\"===this._source.type&&this._source.resumeStream()}sendMessageToSocket(e){\"stream\"===this._source.type&&this._source.sendMessageToSocket(e)}sendMessageToClient(e){\"stream\"===this._source.type&&this._source.sendMessageToClient(e)}_initAggregateStore(e){const t=e.schema.targets?.aggregate?.type,r=s(this.config,(e=>e.schema.targets?.aggregate?.type));if(r!==t&&(i(this.aggregateStore)&&(this.removeHandles(\"valueRangesChanged\"),this.aggregateStore.destroy(),this.aggregateStore=null),t)){switch(t){case\"cluster\":{const e={geometryInfo:{geometryType:\"esriGeometryPoint\",hasM:!1,hasZ:!1},spatialReference:this.spatialReference,fieldsIndex:this.fieldsIndex,fields:this.service.fields};this.aggregateStore=new Q(e,this.spatialReference,this._storage,this.service),this.addHandles(this.aggregateStore.events.on(\"valueRangesChanged\",(e=>{this.remoteClient.invoke(\"emitEvent\",{name:\"valueRangesChanged\",event:{valueRanges:e.valueRanges}}).catch(A)})),\"valueRangesChanged\");break}case\"bin\":{const e={geometryInfo:{geometryType:\"esriGeometryPolygon\",hasM:!1,hasZ:!1},spatialReference:this.spatialReference,fieldsIndex:this.fieldsIndex,fields:this.service.fields};this.aggregateStore=new I(e,this.spatialReference,this._storage,this.service);break}}this.aggregateStore.onTileUpdate({added:this.tileStore.tiles,removed:[]})}}async update(e,t){this._updateVersion++,this._initQueryEngine(t),this._initAttributeStore(t),this.pause(),await Promise.all([this._source.update(e,t.schema.source),this.featureStore.updateSchema(e,t.schema.targets.feature),this.attributeStore.update(e,t),this.attributeStore.updateFilters(e,t,this)]),this._initAggregateStore(t),i(this.aggregateStore)&&await this.aggregateStore.updateSchema(e,t.schema.targets.aggregate),this._initAggregateQueryEngine(this.aggregateStore,t.schema),has(\"esri-2d-update-debug\")&&e.describe(),this._set(\"config\",t)}async applyUpdate(e){e.version=this._updateVersion,has(\"esri-2d-update-debug\")&&console.debug(`Applying update ${e.version}`),e.mesh&&this.clearTiles(),this._updateQueue.resume(),await this._source.applyUpdate(e),this.notifyChange(\"updating\"),await l((()=>!this.updating)),i(this.aggregateStore)&&(await n(10),await l((()=>!this.updating)))}async onEdits({edits:e}){has(\"esri-2d-update-debug\")&&console.debug(\"Applying Edit:\",e),this._didEdit=!0;try{const t=e.removed.map((e=>e.objectId&&-1!==e.objectId?e.objectId:this._lookupObjectIdByGlobalId(e.globalId))),r=e.addOrModified.map((({objectId:e})=>e));this.featureStore.invalidate(),await this._source.edit(r,t),this.clearTiles(),this.notifyChange(\"updating\"),i(this.aggregateStore)&&this.aggregateStore.clear(),await this._source.resend(),await l((()=>!this.updating))}catch(t){}}async refresh(e){if(!e.dataChanged){const e=w.empty();return e.storage.filters=!0,this.applyUpdate(e)}this.featureStore.invalidate(),this.clearTiles(),this._source.refresh(this._updateVersion,e),this._cleanupNeeded=!0,this.notifyChange(\"updating\"),await l((()=>!this.updating))}clearTiles(){for(const e of this.tileStore.tiles)this.processor.onTileClear(e)}onTileUpdate(e){i(this.aggregateStore)&&this.aggregateStore.onTileUpdate(e);for(const t of e.added)this._source.subscribe(t,this._updateVersion),this._level=t.level;for(const t of e.removed)this._source.unsubscribe(t),this._cleanupNeeded=!0,this._tileToResolver.has(t.id)&&(this._tileToResolver.get(t.id).resolve(),this._tileToResolver.delete(t.id));this.notifyChange(\"updating\")}async onIdle(){this._invalidated&&(this._invalidated=!1,(i(this.aggregateStore)||\"heatmap\"===this.processor.type)&&await this._repushCurrentLevelTiles()),this._markAndSweep()}async querySummaryStatistics({query:e,params:t}){return this.featureQueryEngine.executeQueryForSummaryStatistics(e,t)}async queryAggregateSummaryStatistics({query:e,params:t}){return this.aggregateQueryEngine.executeQueryForSummaryStatistics(e,t)}async queryUniqueValues({query:e,params:t}){return this.featureQueryEngine.executeQueryForUniqueValues(e,t)}async queryAggregateUniqueValues({query:e,params:t}){return this.aggregateQueryEngine.executeQueryForUniqueValues(e,t)}async queryClassBreaks({query:e,params:t}){return this.featureQueryEngine.executeQueryForClassBreaks(e,t)}async queryAggregateClassBreaks({query:e,params:t}){return this.aggregateQueryEngine.executeQueryForClassBreaks(e,t)}async queryHistogram({query:e,params:t}){return this.featureQueryEngine.executeQueryForHistogram(e,t)}async queryAggregateHistogram({query:e,params:t}){return this.aggregateQueryEngine.executeQueryForHistogram(e,t)}queryExtent(e){return this.featureQueryEngine.executeQueryForExtent(e)}queryAggregates(e){return this.aggregateQueryEngine.executeQuery(e)}queryAggregateCount(e){return this.aggregateQueryEngine.executeQueryForCount(e)}queryAggregateIds(e){return this.aggregateQueryEngine.executeQueryForIds(e)}queryFeatures(e){return this.featureQueryEngine.executeQuery(e)}async queryVisibleFeatures(e){const t=await this.featureQueryEngine.executeQuery(e),r=t.objectIdFieldName;return t.features=t.features.filter((e=>{const t=e.attributes[r],i=this.getDisplayId(t);return s(i,(e=>this.attributeStore.isVisible(e)))})),t}queryFeatureCount(e){return this.featureQueryEngine.executeQueryForCount(e)}queryLatestObservations(e){return this.featureQueryEngine.executeQueryForLatestObservations(e)}queryObjectIds(e){return this.featureQueryEngine.executeQueryForIds(e)}async queryStatistics(){return this.featureStore.storeStatistics}getObjectId(e){return this.featureStore.lookupObjectId(e,this._storage)}getDisplayId(e){if(i(this.aggregateStore)){const t=this.aggregateStore.getDisplayId(e);if(r(t)){const t=this.featureStore.lookupDisplayId(e);return this.aggregateStore.getDisplayIdForReferenceId(t)}return t}return this.featureStore.lookupDisplayId(e)}getFeatures(e){const t=[],r=[];for(const s of e){const e=i(this.aggregateStore)?this.getAggregate(s):null;if(i(e))if(i(e.attributes.referenceId)){const r=this.getFeature(e.attributes.referenceId);i(r)&&t.push(r)}else r.push(e);else{const e=this.getFeature(s);i(e)&&t.push(e)}}return{features:t,aggregates:r}}getFeature(e){const t=this.featureStore.lookupFeatureByDisplayId(e,this._storage);if(r(t))return null;const s=t.readHydratedGeometry(),i=f(s,t.geometryType,t.hasZ,t.hasM);return{attributes:t.readAttributes(),geometry:i}}getAggregate(e){return r(this.aggregateStore)?null:this.aggregateStore.getAggregate(e)}getAggregates(){return r(this.aggregateStore)?[]:this.aggregateStore.getAggregates()}async setHighlight(e){const t=a(e.map((e=>this.getDisplayId(e))));return this.attributeStore.setHighlight(e,t)}_lookupObjectIdByGlobalId(e){const t=this.service.globalIdField;if(r(t))throw new Error(\"Expected globalIdField to be defined\");let s=null;if(this.featureStore.forEach((r=>{e===r.readAttribute(t)&&(s=r.getObjectId())})),r(s))throw new Error(`Expected to find a feature with globalId ${e}`);return s}async _repushCurrentLevelTiles(){const e=this.tileStore.tiles.filter((e=>e.level===this._level));e.map((async e=>this._patchTile({type:\"append\",id:e.key.id,clear:!0,addOrUpdate:null,end:!1})));const t=e.map((async e=>this._patchTile({type:\"append\",id:e.key.id,addOrUpdate:E.fromOptimizedFeatures([],this.service),remove:[],end:!0,isRepush:!0,status:w.empty()})));await Promise.all(t)}_maybeForceCleanup(){performance.now()-this._lastCleanup>T&&this._markAndSweep()}_patchTile(e,t){const r=this._updateQueue.push(e,t).then((()=>{this.notifyChange(\"updating\")})).catch((e=>{this.notifyChange(\"updating\")}));return this.notifyChange(\"updating\"),r}async _onTileMessage(e,t){if(u(t),has(\"esri-2d-update-debug\")){const t=s(e.addOrUpdate,(e=>e.hasFeatures));console.debug(e.id,`FeatureController:onTileMessage: [clear:${e.clear}, end:${e.end}, features: ${t}]`)}const a=this.tileStore.get(e.id);if(!a)return;if(e.clear)return this.processor.onTileClear(a);const o=e.status;this._cleanupNeeded=!0;const n=[];for(const r of e.remove??[]){const e=this.featureStore.lookupDisplayId(r);e&&n.push(e)}e.remove=n;try{if(r(e.addOrUpdate))return void this.processor.onTileMessage(a,{...e,addOrUpdate:null},i(this.aggregateStore),t).catch(g);if(e.addOrUpdate.setArcadeSpatialReference(this.spatialReference),this.featureStore.hasInstance(e.addOrUpdate.instance)&&o.targets.feature||(o.targets.feature=!0,this.featureStore.onTileData(a,e)),!o.storage.data||!o.storage.filters){o.storage.data=!0,o.storage.filters=!0,this.attributeStore.onTileData(a,e);\"stream\"===this._source.type||this._didEdit?(await this.attributeStore.sendUpdates(),u(t)):this.attributeStore.sendUpdates()}if(i(this.aggregateStore)&&!o.targets.aggregate){o.targets.aggregate=!0;const t=U(this._source)&&this._source.loading,r=!U(this._source)||t||e.end;if(this.aggregateStore.onTileData(a,e,this._storage,this.attributeStore,r),!r)return;o.mesh||(this.attributeStore.onTileData(a,e),await this.attributeStore.sendUpdates())}if(!o.mesh){o.mesh=!0;const r=i(this.aggregateStore)&&\"cluster\"===this.aggregateStore.type;await this.processor.onTileMessage(a,e,r,t),u(t)}this._maybeForceCleanup()}catch(h){g(h)}}_mark(e,t,r){const s=(4294901760&this._storage.getInstanceId(e))>>>16;e&&(t.add(s),r.set(e))}_markAndSweep(){this._lastCleanup=performance.now();if(!(!(\"feature\"===this._source.type&&\"snapshot\"===this._source.mode)&&(\"stream\"===this._source.type||this._cleanupNeeded)))return;this._cleanupNeeded=!1;const e=this._storage.getBitset(this._markedIdsBufId),t=new Set;e.clear();for(const r of this.tileStore.tiles)for(const s of this._source.readers(r.id)){const r=s.getCursor();for(;r.next();){let s=r.getDisplayId();if(!s){const e=r.getObjectId();s=this.featureStore.lookupDisplayId(e)}this._mark(s,t,e)}}\"symbol\"===this.processor.type&&this.processor.forEachBufferId((r=>{this._mark(r,t,e)})),this._updateQueue.forEach((r=>{for(const s of r.remove??[]){const r=this.featureStore.lookupDisplayId(s);this._mark(r,t,e)}})),i(this.aggregateStore)&&(this.aggregateStore.sweepFeatures(e,this.featureStore),\"sweepAggregates\"in this.aggregateStore&&this.aggregateStore.sweepAggregates(this._storage,this.attributeStore,this._level)),this.featureStore.sweepFeatures(e,this._storage,this.attributeStore),this.featureStore.sweepFeatureSets(t)}};e([p({constructOnly:!0})],q.prototype,\"tileStore\",void 0),e([p()],q.prototype,\"config\",void 0),e([p({readOnly:!0})],q.prototype,\"fieldsIndex\",null),e([p()],q.prototype,\"processor\",void 0),e([p({constructOnly:!0})],q.prototype,\"remoteClient\",void 0),e([p({constructOnly:!0})],q.prototype,\"service\",void 0),e([p()],q.prototype,\"spatialReference\",null),e([p()],q.prototype,\"updating\",null),q=e([y(\"esri.views.2d.layers.features.controllers.FeatureController2D\")],q);const O=q;export{O as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../../chunks/tslib.es6.js\";import{HandleOwner as t}from\"../../../../core/HandleOwner.js\";import\"../../../../core/has.js\";import{watch as r}from\"../../../../core/reactiveUtils.js\";import{property as s}from\"../../../../core/accessorSupport/decorators/property.js\";import\"../../../../core/accessorSupport/ensureType.js\";import\"../../../../core/arrayUtils.js\";import{subclass as o}from\"../../../../core/accessorSupport/decorators/subclass.js\";import{equals as i}from\"../../../../geometry/support/spatialReferenceUtils.js\";import l from\"../../../../layers/support/TileInfo.js\";import{loadProcessorModule as n}from\"./processors.js\";import a from\"./controllers/FeatureController2D.js\";import c from\"./support/TileStore.js\";import{UpdateToken as p}from\"./support/UpdateToken.js\";import h from\"../../tiling/TileInfoView.js\";let d=class extends t{constructor(){super(...arguments),this.controller=null,this.processor=null,this.remoteClient=null,this.tileStore=null,this.service=null,this.viewState=null,this._paused=!1,this._pendingTileUpdates=[]}initialize(){this.handles.add(r((()=>this.updating),(e=>{this.remoteClient.invoke(\"setUpdating\",e).catch((e=>{}))})))}destroy(){this.stop(),this.controller?.destroy(),this.processor?.destroy(),this.controller=this.processor=this.tileStore=this.remoteClient=null}get updating(){return!this.controller||this.controller.updating}stop(){this._paused=!0,Array.isArray(this.service?.source)&&(this.service.source.forEach((e=>e.close())),this.service.source.length=0),this.tileStore?.updateTiles({added:[],removed:this.tileStore.tiles.map((e=>e.id))}),this.tileStore?.destroy(),this.tileStore=null,this._pendingTileUpdates.length=0}async startup({service:e,config:t,tileInfo:r,tiles:s}){if(this._paused=!0,Array.isArray(this.service?.source)&&(this.service.source.forEach((e=>e.close())),this.service.source.length=0),this.service=e,!this.tileStore||!i(this.tileStore.tileScheme.spatialReference,r.spatialReference)){const e=new h(l.fromJSON(r));s.added.length=s.removed.length=0,this.tileStore?.updateTiles({added:[],removed:this.tileStore.tiles.map((e=>e.id))}),this.tileStore?.destroy(),this.tileStore=new c(e),this._pendingTileUpdates.length=0}for(await this._createProcessorAndController(t),await this.update({config:t}),this.controller.resume(),this.tileStore.clear(),this.tileStore.updateTiles(s),this._paused=!1;this._pendingTileUpdates.length;)this.tileStore.updateTiles(this._pendingTileUpdates.pop())}async updateTiles(e){this._paused?this._pendingTileUpdates.push(e):this.tileStore?.updateTiles(e)}async update({config:e}){const t=p.empty();return await Promise.all([this.processor.update(t,e),this.controller.update(t,e)]),t.toJSON()}async applyUpdate(e){return this.controller.applyUpdate(p.create(e))}async _createProcessorAndController(e){await Promise.all([this._handleControllerConfig(e),this._handleProcessorConfig(e)]),this.controller.processor=this.processor}async _handleControllerConfig(e){return this._createController(this.service,e)}async _handleProcessorConfig(e){return this._createProcessor(this.service,e)}async _createController(e,t){this.controller&&this.controller.destroy();const{tileStore:r,remoteClient:s}=this,o=new a({service:e,tileStore:r,remoteClient:s});return this.controller=o,o}async _createProcessor(e,t){const r=t.schema.processors[0].type,s=(await n(r)).default,{remoteClient:o,tileStore:i}=this,l=new s({service:e,config:t,tileStore:i,remoteClient:o});return this.processor&&this.processor.destroy(),this.processor=l,l}};e([s()],d.prototype,\"controller\",void 0),e([s()],d.prototype,\"processor\",void 0),e([s()],d.prototype,\"updating\",null),e([s()],d.prototype,\"viewState\",void 0),d=e([o(\"esri.views.2d.layers.features.Pipeline\")],d);const u=d;export{u as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,SAASA,GAAEA,IAAE;AAAC,SAAM,cAAYA,KAAE,OAAO,gCAAkC,IAAE,OAAO,+BAAiC;AAAC;;;ACA2C,IAAMC,KAAE;AAAU,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,WAAS,oBAAI,OAAI,KAAK,SAAO,CAAC,GAAE,KAAK,cAAY,OAAG,KAAK,wBAAsB,OAAG,KAAK,aAAW,GAAE,KAAK,eAAa,GAAE,KAAK,qBAAmB,GAAE,KAAK,cAAY,GAAE,KAAK,UAAQ,EAAC,YAAW,IAAI,SAAM,UAAS,IAAI,QAAK,GAAE,KAAK,WAAS,IAAI;AAAA,EAAK;AAAA,EAAC,SAASC,IAAE;AAAC,WAAO,KAAK,SAAS,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAJxf;AAIyf,YAAO,QAAMA,QAAG,UAAK,SAAS,IAAIA,EAAC,MAAnB,mBAAsB,YAAS;AAAA,EAAE;AAAA,EAAC,cAAcA,IAAE;AAJ3jB;AAI4jB,WAAO,QAAMA,MAAE,UAAK,SAAS,IAAIA,EAAC,MAAnB,mBAAsB,QAAM;AAAA,EAAM;AAAC;AAAC,SAASC,GAAED,IAAE;AAAC,QAAME,KAAE,GAAEJ,KAAE,GAAEC,KAAEC,GAAE,SAAS,GAAEC,MAAEF,GAAE,UAAU,GAAEI,KAAEJ,GAAE,IAAI,IAAEE,KAAEG,KAAE,EAAC,MAAK,IAAG,QAAO,MAAE;AAAE,SAAKL,GAAE,IAAI,IAAEI,MAAGJ,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKG;AAAE,MAAAE,GAAE,OAAKL,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKD;AAAE,8BAAsBO,GAAEN,GAAE,QAAQ,CAAC,MAAIK,GAAE,SAAO;AAAI;AAAA,IAAM;AAAQ,MAAAL,GAAE,KAAK;AAAA,EAAC;AAAC,SAAOK;AAAC;AAAC,SAASD,GAAEH,IAAE;AAAC,SAAOA,GAAE,YAAY,EAAE,KAAK;AAAC;AAAC,SAASI,GAAEE,IAAEF,IAAEG,MAAE,OAAG;AAAC,QAAMF,KAAE,GAAEG,KAAE,GAAEC,KAAE,GAAEC,KAAE,IAAGC,KAAE,IAAGC,KAAE,IAAGC,KAAEP,GAAE,SAAS,GAAEQ,KAAED,GAAE,IAAI,GAAEE,KAAE,IAAIhB;AAAE,MAAIiB,KAAE,GAAEC,KAAE;AAAE,QAAMC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE;AAAE,MAAIC,KAAE,MAAKC,KAAE,MAAK,IAAE,MAAKC,KAAE;AAAG,SAAKX,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKR;AAAE,MAAAiB,KAAET,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKL;AAAE,MAAAe,KAAEV,GAAE,UAAU;AAAE;AAAA,IAAM,KAAKH;AAAE,UAAEG,GAAE,eAAeY,EAAC;AAAE;AAAA,IAAM,KAAKhB;AAAE,UAAGM,GAAE,wBAAsBF,GAAE,QAAQ,GAAEE,GAAE,uBAAsB;AAAC,QAAAA,GAAE,QAAQ,WAASR,MAAE,IAAI,aAAa,GAAG,IAAE,IAAI,WAAW,GAAG,GAAEQ,GAAE,WAASR,MAAE,IAAI,aAAa,IAAI,IAAE,IAAI,WAAW,IAAI;AAAE,iBAAQP,KAAE,GAAEA,KAAEe,GAAE,SAAS,QAAOf,KAAI,CAAAe,GAAE,SAASf,EAAC,IAAEF;AAAA,MAAC;AAAC;AAAA,IAAM,KAAKa,IAAE;AAAC,YAAMX,KAAEC,GAAEK,EAAC,GAAEJ,KAAEF,GAAE,MAAKF,KAAEK,GAAEH,GAAE,IAAI,GAAED,KAAE,EAAC,WAAUG,IAAE,OAAMc,MAAI,QAAOhB,GAAE,OAAM;AAAE,MAAAe,GAAE,OAAO,KAAKhB,EAAC,GAAEgB,GAAE,SAAS,IAAIf,GAAE,MAAKD,EAAC,GAAEgB,GAAE,SAAS,IAAIjB,IAAEC,EAAC;AAAE;AAAA,IAAK;AAAA,IAAC,KAAKa,IAAE;AAAC,YAAMZ,KAAEa,GAAE,UAAU,GAAEX,KAAEW,GAAE,IAAI,IAAEb;AAAE,UAAG,CAACe,GAAE,uBAAsB;AAAC,cAAMf,KAAEe,GAAE,QAAQ,UAASb,KAAEa,GAAE;AAAS,QAAAf,GAAE,KAAK,CAAC,GAAEE,GAAE,KAAKJ,EAAC,GAAEI,GAAE,KAAKJ,EAAC;AAAA,MAAC;AAAC,OAAC0B,MAAGT,GAAE,0BAAwBS,KAAE,MAAGT,GAAE,QAAQ,aAAWR,MAAE,IAAI,aAAa,MAAIS,EAAC,IAAE,IAAI,YAAY,MAAIA,EAAC;AAAG,UAAIV,MAAEW,KAAED;AAAE,aAAKH,GAAE,IAAI,IAAEX,MAAGW,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,QAAC,KAAKK,IAAE;AAAC,cAAGM,GAAE,CAAAT,GAAE,QAAQ,WAAWT,KAAG,IAAEO,GAAE,IAAI;AAAA,eAAM;AAAC,YAAAE,GAAE,QAAQ,WAAW,KAAKF,GAAE,IAAI,CAAC;AAAA,UAAC;AAAC,gBAAMb,KAAEa,GAAE,UAAU;AAAE,UAAAA,GAAE,QAAQb,EAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAKmB;AAAE,cAAGf,IAAE;AAAC,kBAAMJ,KAAEa,GAAE,UAAU,GAAEX,KAAEW,GAAE,IAAI,IAAEb;AAAE,mBAAKa,GAAE,IAAI,IAAEX,MAAGW,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,cAAC,KAAKQ,IAAE;AAAC,gBAAAR,GAAE,UAAU;AAAE,sBAAMb,KAAEa,GAAE,UAAU,GAAEX,KAAEW,GAAE,UAAU;AAAE,gBAAAE,GAAE,SAAS,IAAEE,EAAC,IAAEjB,IAAEe,GAAE,SAAS,IAAEE,KAAE,CAAC,IAAEf;AAAE;AAAA,cAAK;AAAA,cAAC;AAAQ,gBAAAW,GAAE,KAAK;AAAA,YAAC;AAAA,UAAC,OAAK;AAAC,YAAAE,GAAE,QAAQ,SAASE,EAAC,IAAEJ,GAAE,IAAI;AAAE,kBAAMb,KAAEa,GAAE,UAAU;AAAE,YAAAE,GAAE,eAAaf,IAAEa,GAAE,QAAQb,EAAC;AAAA,UAAC;AAAC;AAAA,QAAM,KAAKoB,IAAE;AAAC,gBAAMpB,KAAEa,GAAE,UAAU,GAAEX,KAAEW,GAAE,IAAI,IAAEb;AAAE,iBAAKa,GAAE,IAAI,IAAEX,MAAGW,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,YAAC,KAAKQ,IAAE;AAAC,cAAAR,GAAE,UAAU;AAAE,oBAAMb,KAAEa,GAAE,UAAU,GAAEX,KAAEW,GAAE,UAAU;AAAE,cAAAE,GAAE,SAAS,IAAEE,EAAC,IAAEjB,IAAEe,GAAE,SAAS,IAAEE,KAAE,CAAC,IAAEf;AAAE;AAAA,YAAK;AAAA,YAAC;AAAQ,cAAAW,GAAE,KAAK;AAAA,UAAC;AAAC;AAAA,QAAK;AAAA,QAAC;AAAQ,UAAAA,GAAE,KAAK;AAAA,MAAC;AAAC,MAAAI,MAAIF,GAAE,cAAY;AAAG;AAAA,IAAK;AAAA,IAAC;AAAQ,MAAAF,GAAE,KAAK;AAAA,EAAC;AAAC,QAAMa,KAAEJ,MAAGC;AAAE,MAAG,CAACG,GAAE,OAAM,IAAIpB,GAAE,mDAAmD;AAAE,SAAOS,GAAE,eAAaE,IAAEF,GAAE,aAAWC,IAAED,GAAE,qBAAmBA,GAAE,cAAcW,EAAC,GAAEX,GAAE,YAAU,GAAEA,GAAE,aAAW,IAAI,YAAYA,GAAE,YAAY,GAAEA,GAAE,WAAS,IAAI,YAAYA,GAAE,YAAY,GAAEF,GAAE,KAAKC,EAAC,GAAEC;AAAC;;;ACA9tE,IAAMY,KAAE;AAAR,IAAWC,KAAE;AAAb,IAAuB,IAAE;AAAzB,IAA6BC,KAAE;AAA/B,IAAqCC,KAAE,EAAC,OAAM,EAAC,OAAM,IAAI,WAAW,CAAC,GAAE,SAAQ,IAAI,WAAW,CAAC,EAAC,GAAE,OAAM,EAAC,OAAM,IAAI,WAAWD,EAAC,GAAE,SAAQ,IAAI,WAAWA,EAAC,EAAC,EAAC;AAAE,SAAS,EAAEE,IAAE;AAAC,SAAOA,MAAGD,GAAE,MAAM,MAAM,SAAOA,GAAE,SAAOC,MAAGD,GAAE,MAAM,MAAM,WAASA,GAAE,MAAM,QAAM,IAAI,WAAW,KAAK,MAAM,OAAKC,EAAC,CAAC,GAAED,GAAE,MAAM,UAAQ,IAAI,WAAW,KAAK,MAAM,OAAKC,EAAC,CAAC,IAAGD,GAAE;AAAM;AAAC,SAASE,GAAED,IAAE;AAAC,SAAOA,GAAE,YAAY,EAAE,KAAK;AAAC;AAAC,SAASE,GAAEC,IAAE;AAAC,MAAG;AAAC,UAAMH,KAAE,GAAEI,KAAE,IAAIC,GAAE,IAAI,WAAWF,EAAC,GAAE,IAAI,SAASA,EAAC,CAAC;AAAE,WAAKC,GAAE,KAAK,KAAG;AAAC,UAAGA,GAAE,IAAI,MAAIJ,GAAE,QAAOM,GAAEF,GAAE,WAAW,CAAC;AAAE,MAAAA,GAAE,KAAK;AAAA,IAAC;AAAA,EAAC,SAAOG,IAAE;AAAC,UAAMJ,KAAE,IAAII,GAAE,qBAAoB,8CAA6C,EAAC,OAAMA,GAAC,CAAC;AAAE,MAAE,UAAU,0DAA0D,EAAE,MAAMJ,EAAC;AAAA,EAAC;AAAC,SAAO;AAAI;AAAC,SAASG,GAAEN,IAAE;AAAC,QAAMI,KAAE;AAAE,SAAKJ,GAAE,KAAK,KAAG;AAAC,QAAGA,GAAE,IAAI,MAAII,GAAE,QAAOJ,GAAE,WAAW;AAAE,IAAAA,GAAE,KAAK;AAAA,EAAC;AAAC,SAAO;AAAI;AAAC,SAAS,EAAEA,IAAE;AAAC,QAAMI,KAAE,GAAED,KAAE,GAAEI,KAAE,GAAEC,KAAE,GAAEH,KAAE,GAAEI,MAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAE,GAAEC,KAAEb,GAAE,UAAU,GAAEc,KAAEd,GAAE,IAAI,IAAEa;AAAE,SAAKb,GAAE,IAAI,IAAEc,MAAGd,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,IAAC,KAAKI;AAAE,aAAOJ,GAAE,UAAU;AAAA,IAAE,KAAKG;AAAE,aAAOH,GAAE,SAAS;AAAA,IAAE,KAAKO;AAAE,aAAOP,GAAE,UAAU;AAAA,IAAE,KAAKQ;AAAE,aAAOR,GAAE,UAAU;AAAA,IAAE,KAAKK;AAAE,aAAOL,GAAE,UAAU;AAAA,IAAE,KAAKS;AAAE,aAAOT,GAAE,SAAS;AAAA,IAAE,KAAKU;AAAE,aAAOV,GAAE,UAAU;AAAA,IAAE,KAAKW;AAAE,aAAOX,GAAE,UAAU;AAAA,IAAE,KAAKY;AAAE,aAAOZ,GAAE,QAAQ;AAAA,IAAE;AAAQ,aAAOA,GAAE,KAAK,GAAE;AAAA,EAAI;AAAC,SAAO;AAAI;AAAC,SAAS,EAAEA,IAAEI,IAAED,IAAEI,IAAEC,IAAEH,IAAE;AAAC,SAAM,MAAG,KAAK,IAAIL,KAAEO,KAAEJ,KAAEE,KAAEG,KAAEJ,KAAEJ,KAAEK,KAAEF,KAAEC,KAAEI,KAAED,EAAC;AAAC;AAAC,SAASQ,GAAEf,IAAEI,IAAED,IAAEI,IAAE;AAAC,SAAO,MAAIP,KAAEO,KAAEJ,KAAEC,MAAGJ,KAAEG,KAAEC,KAAEG,KAAE;AAAC;AAAC,IAAMS,KAAN,MAAM,WAAUC,GAAC;AAAA,EAAC,OAAO,WAAWjB,IAAEI,IAAED,KAAE,OAAG;AAAC,UAAMI,KAAEH,GAAE,cAAaI,KAAEN,GAAEF,EAAC,GAAEK,KAAEM,GAAEH,IAAE,wBAAsBD,IAAEJ,EAAC,GAAEM,MAAEQ,GAAE,eAAe;AAAE,WAAO,IAAI,GAAER,KAAED,IAAEH,IAAED,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYJ,IAAEI,IAAED,IAAEI,IAAE;AAAC,UAAMP,IAAEO,EAAC,GAAE,KAAK,WAAS,OAAG,KAAK,YAAU,OAAG,KAAK,gBAAc,IAAG,KAAK,iBAAe,GAAE,KAAK,SAAO,EAAC,MAAK,GAAE,iBAAgB,QAAO,UAAS,QAAO,UAAS,QAAO,eAAc,QAAO,YAAW,OAAM,GAAE,KAAK,gBAAcA,GAAE,cAAa,KAAK,UAAQH,IAAE,KAAK,UAAQD,IAAE,KAAK,WAASA,GAAE,aAAY,KAAK,YAAU,wBAAsBI,GAAE;AAAA,EAAY;AAAA,EAAC,IAAI,eAAc;AAAC,WAAO,KAAK;AAAA,EAAa;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAY;AAAA,EAAC,IAAI,OAAM;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,IAAI,OAAM;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAG,KAAK,OAAK,IAAE,MAAI,KAAK,OAAK,IAAE;AAAA,EAAE;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAW;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,wBAAuB;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAqB;AAAA,EAAC,SAASP,IAAE;AAAC,WAAO,KAAK,QAAQ,SAASA,EAAC,KAAG,KAAK,QAAQ,SAASC,GAAED,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,WAAO,KAAK,QAAQ,OAAO,IAAK,CAAAA,OAAGA,GAAE,SAAU;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,WAAO,KAAK;AAAA,EAAK;AAAA,EAAC,2BAA0B;AAAC,WAAO,KAAK,QAAQ;AAAA,EAAS;AAAA,EAAC,YAAW;AAAC,WAAO,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,WAAO,KAAK;AAAA,EAAa;AAAA,EAAC,SAASA,IAAE;AAAC,SAAK,OAAO,OAAK,GAAE,KAAK,OAAO,kBAAgB,QAAO,KAAK,OAAO,WAAS,QAAO,KAAK,OAAO,WAAS,QAAO,KAAK,OAAO,gBAAc,QAAO,KAAK,OAAO,aAAW,QAAO,KAAK,gBAAcA;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,QAAIA,KAAE;AAAG,WAAO,KAAK,QAAQ,OAAO,QAAS,CAAC,EAAC,OAAMI,GAAC,MAAI;AAAC,MAAAJ,MAAG,KAAK,sBAAsBI,EAAC,IAAE;AAAA,IAAG,CAAE,GAAEJ;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,WAAO,KAAK,sBAAsB,KAAK,QAAQ,kBAAkB;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,WAAO,KAAK,QAAQ,WAAW,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,SAAK,QAAQ,WAAW,KAAK,aAAa,IAAEA;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,QAAQ,SAAS,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,SAAK,QAAQ,SAAS,KAAK,aAAa,IAAEA;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,QAAG,WAAS,KAAK,OAAO,eAAc;AAAC,YAAMA,KAAE,KAAK,aAAa,GAAEI,KAAE,EAAC,YAAW,KAAK,eAAe,GAAE,UAAS,KAAK,YAAU,KAAK,wBAAwB,IAAE,KAAK,mBAAmB,GAAE,WAAUJ,MAAG,EAAC,GAAEA,GAAE,OAAO,CAAC,GAAE,GAAEA,GAAE,OAAO,CAAC,EAAC,MAAI,KAAI;AAAE,aAAO,KAAK,OAAO,gBAAcI,IAAEA;AAAA,IAAC;AAAC,WAAO,KAAK,OAAO;AAAA,EAAa;AAAA,EAAC,uBAAsB;AAAC,QAAG,WAAS,KAAK,OAAO,YAAW;AAAC,YAAMJ,KAAE,IAAIO,GAAE,KAAK,aAAa,GAAE,KAAK,eAAe,GAAE,KAAK,aAAa,CAAC;AAAE,aAAOP,GAAE,WAAS,KAAK,YAAY,GAAEA,GAAE,YAAU,KAAK,aAAa,GAAE,KAAK,OAAO,aAAWA,IAAEA;AAAA,IAAC;AAAC,WAAO,KAAK,OAAO;AAAA,EAAU;AAAA,EAAC,eAAc;AAAC,UAAMA,KAAE,KAAK,QAAQ,SAAS,IAAE,KAAK,aAAa,GAAEI,KAAE,KAAK,yBAAyB;AAAE,WAAO,EAAEA,EAAC,IAAEJ,KAAEA,KAAEI,GAAE,MAAM,CAAC,IAAEA,GAAE,UAAU,CAAC;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,UAAMJ,KAAE,KAAK,QAAQ,SAAS,IAAE,KAAK,gBAAc,CAAC,GAAEI,KAAE,KAAK,yBAAyB;AAAE,WAAO,EAAEA,EAAC,IAAEJ,KAAEI,GAAE,UAAU,CAAC,IAAEJ,KAAEI,GAAE,MAAM,CAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK,QAAQ,SAAS,IAAE,KAAK,aAAa,IAAE,KAAK,MAAI,KAAK;AAAA,EAAG;AAAA,EAAC,OAAM;AAAC,WAAO,KAAK,QAAQ,SAAS,IAAE,KAAK,gBAAc,CAAC,IAAE,KAAK,MAAI,KAAK;AAAA,EAAG;AAAA,EAAC,0BAAyB;AAAC,WAAM,EAAC,GAAE,KAAK,KAAK,GAAE,GAAE,KAAK,KAAK,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBJ,IAAE;AAAC,UAAMI,KAAE,KAAK,aAAaJ,EAAC;AAAE,WAAO,GAAEI,IAAE,KAAK,cAAa,OAAG,KAAE;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,UAAMJ,KAAE,KAAK,aAAa;AAAE,QAAG,CAACA,GAAE,QAAO;AAAK,UAAK,CAACI,IAAED,EAAC,IAAEH,GAAE;AAAO,WAAM,EAAC,GAAEI,IAAE,GAAED,GAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,WAAO,KAAK,OAAO,QAAM,KAAK,aAAa,IAAE,GAAE,KAAK,OAAO;AAAA,EAAI;AAAA,EAAC,wBAAwBH,KAAE,OAAG;AAAC,QAAG,WAAS,KAAK,OAAO,iBAAgB;AAAC,YAAMI,KAAE,KAAK,aAAaJ,EAAC;AAAE,UAAG,CAACI,GAAE,QAAO,KAAK,OAAO,kBAAgB,QAAO;AAAK,YAAMD,KAAE,EAAEC,GAAE,OAAO,MAAM,EAAE,SAAQG,KAAEH,GAAE,MAAMD,EAAC,GAAEK,KAAED,GAAE;AAAO,UAAIF,KAAE;AAAE,iBAAUL,MAAKO,GAAE,SAAQ;AAAC,iBAAQH,KAAE,GAAEA,KAAEJ,IAAEI,MAAI;AAAC,gBAAMJ,KAAE,KAAGK,KAAED,KAAGD,KAAE,KAAGE,KAAED,KAAE;AAAG,UAAAI,GAAER,EAAC,KAAGQ,GAAEL,EAAC,GAAEK,GAAER,KAAE,CAAC,KAAGQ,GAAEL,KAAE,CAAC;AAAA,QAAC;AAAC,QAAAE,MAAGL;AAAA,MAAC;AAAC,aAAO,KAAK,OAAO,kBAAgBO,IAAEA;AAAA,IAAC;AAAC,WAAO,KAAK,OAAO;AAAA,EAAe;AAAA,EAAC,uBAAsB;AAAC,QAAG,KAAK,WAAU;AAAC,UAAG,KAAK,QAAQ,SAAS,IAAE,KAAK,aAAa,MAAIV,GAAE,QAAO;AAAK,YAAMG,KAAE,KAAK,aAAa,GAAEI,KAAE,KAAK,aAAa;AAAE,aAAO,IAAIA,GAAE,CAAC,GAAE,CAACJ,IAAEI,EAAC,CAAC;AAAA,IAAC;AAAC,UAAMJ,KAAE,KAAK,aAAa;AAAE,QAAG,CAACA,GAAE,QAAO;AAAK,UAAMI,KAAEJ,GAAE,MAAM,GAAEG,KAAE,KAAK,yBAAyB;AAAE,WAAO,EAAEA,EAAC,KAAG,GAAEC,IAAEA,IAAE,KAAK,MAAK,KAAK,MAAKD,EAAC,GAAEC;AAAA,EAAC;AAAA,EAAC,aAAaJ,KAAE,OAAG;AAAC,QAAG,WAAS,KAAK,OAAO,UAAS;AAAC,UAAIG,KAAE;AAAK,UAAG,KAAK,WAAU;AAAC,YAAG,KAAK,QAAQ,SAAS,IAAE,KAAK,aAAa,MAAIN,GAAE,QAAO;AAAK,cAAMG,KAAE,KAAK,KAAK,GAAEI,KAAE,KAAK,KAAK;AAAE,QAAAD,KAAE,IAAIC,GAAE,CAAC,GAAE,CAACJ,IAAEI,EAAC,CAAC;AAAA,MAAC,OAAK;AAAC,cAAMG,KAAE,KAAK,QAAQ,QAAQ,SAAS,KAAK,aAAa,GAAEC,KAAE,KAAK;AAAQ,YAAG,MAAID,IAAE;AAAC,gBAAMP,KAAE,KAAK,oBAAoB;AAAE,cAAG,CAACA,GAAE,QAAO;AAAK,gBAAK,CAACI,IAAED,EAAC,IAAEH,GAAE;AAAO,iBAAO,KAAK,4BAA4BI,IAAED,EAAC;AAAA,QAAC;AAAC,QAAAK,GAAE,KAAKD,EAAC;AAAE,YAAG;AAAC,cAAGJ,KAAEH,KAAE,KAAK,yBAAyBQ,EAAC,IAAE,KAAK,eAAeA,EAAC,GAAE,SAAOL,IAAE;AAAC,kBAAMH,KAAE,KAAK,oBAAoB;AAAE,gBAAG,CAACA,GAAE,QAAO;AAAK,kBAAK,CAACI,IAAED,EAAC,IAAEH,GAAE;AAAO,mBAAO,KAAK,4BAA4BI,IAAED,EAAC;AAAA,UAAC;AAAA,QAAC,SAAOC,IAAE;AAAC,iBAAO,QAAQ,MAAM,6BAA4BA,EAAC,GAAE;AAAA,QAAI;AAAA,MAAC;AAAC,aAAO,KAAK,OAAO,WAASD,IAAEA;AAAA,IAAC;AAAC,WAAO,KAAK,OAAO;AAAA,EAAQ;AAAA,EAAC,eAAc;AAAC,QAAG,WAAS,KAAK,OAAO,UAAS;AAAC,UAAIH;AAAE,aAAOA,KAAE,KAAK,iBAAiB,GAAEA,OAAIA,KAAE,KAAK,oBAAoB,IAAG,KAAK,OAAO,WAASA,MAAG,QAAOA,MAAG;AAAA,IAAI;AAAC,WAAO,KAAK,OAAO;AAAA,EAAQ;AAAA,EAAC,OAAM;AAAC,UAAMA,KAAE,KAAK,QAAQ,MAAM,GAAEI,KAAE,IAAI,GAAE,KAAK,UAASJ,IAAE,KAAK,SAAQ,KAAK,WAAW,CAAC;AAAE,WAAO,KAAK,SAASI,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,SAAI,KAAK,OAAO,OAAK,GAAE,KAAK,OAAO,kBAAgB,QAAO,KAAK,OAAO,WAAS,QAAO,KAAK,OAAO,WAAS,QAAO,KAAK,OAAO,gBAAc,QAAO,KAAK,OAAO,aAAW,QAAO,EAAE,KAAK,gBAAc,KAAK,SAAO,CAAC,KAAK,WAAW,IAAG;AAAC,WAAO,KAAK,gBAAc,KAAK;AAAA,EAAK;AAAA,EAAC,eAAeJ,IAAEI,IAAE;AAAC,UAAMD,KAAE,KAAK,QAAQ,SAASH,EAAC,IAAEA,KAAEC,GAAED,EAAC,GAAEO,KAAE,KAAK,QAAQ,cAAcJ,EAAC;AAAE,QAAG,QAAMI,GAAE;AAAO,UAAMC,KAAE,KAAK,sBAAsBD,EAAC;AAAE,QAAG,CAACH,GAAE,QAAOI;AAAE,QAAG,QAAMA,GAAE,QAAOA;AAAE,WAAO,KAAK,QAAQ,YAAYL,EAAC,IAAE,IAAI,KAAKK,EAAC,IAAEA;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,UAAMR,KAAE,CAAC;AAAE,WAAO,KAAK,QAAQ,OAAO,QAAS,CAAC,EAAC,WAAUI,IAAE,OAAMD,GAAC,MAAI;AAAC,MAAAH,GAAEI,EAAC,IAAE,KAAK,sBAAsBD,EAAC;AAAA,IAAC,CAAE,GAAEH;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAAC,UAAM,SAASA,EAAC,GAAEA,GAAE,gBAAc,KAAK,eAAcA,GAAE,iBAAe,KAAK,gBAAeA,GAAE,WAAS,KAAK;AAAA,EAAQ;AAAA,EAAC,sBAAsBA,IAAE;AAAC,UAAMI,KAAE,KAAK,QAAQ,QAAQ,WAAW,KAAK,gBAAc,KAAK,QAAQ,aAAWJ,EAAC,GAAEG,KAAE,KAAK;AAAQ,WAAOA,GAAE,KAAKC,EAAC,GAAE,EAAED,EAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,UAAMH,KAAE,KAAK,QAAQ,SAAS,IAAE,KAAK,aAAa,IAAE,KAAK,KAAII,KAAE,KAAK,QAAQ,SAAS,IAAE,KAAK,gBAAc,CAAC,IAAE,KAAK;AAAI,WAAOJ,OAAIH,KAAE,OAAK,IAAIO,GAAE,CAAC,GAAE,CAACJ,IAAEI,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,eAAeJ,IAAE;AAAC,UAAMI,KAAE,GAAED,KAAE,GAAEI,KAAEP,GAAE,SAAS,GAAEQ,KAAED,GAAE,UAAU,GAAEF,KAAEE,GAAE,IAAI,IAAEC,IAAEC,MAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,WAAKH,GAAE,IAAI,IAAEF,MAAGE,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,MAAC,KAAKH,IAAE;AAAC,cAAMJ,KAAEO,GAAE,UAAU,GAAEH,KAAEG,GAAE,IAAI,IAAEP;AAAE,eAAKO,GAAE,IAAI,IAAEH,KAAG,CAAAM,GAAE,KAAKH,GAAE,UAAU,CAAC;AAAE;AAAA,MAAK;AAAA,MAAC,KAAKJ,IAAE;AAAC,cAAMH,KAAEO,GAAE,UAAU,GAAEH,KAAEG,GAAE,IAAI,IAAEP;AAAE,aAAIS,IAAE,KAAKF,GAAE,UAAU,IAAE,KAAK,GAAG,GAAEE,IAAE,KAAKF,GAAE,UAAU,IAAE,KAAK,GAAG,GAAE,KAAK,QAAMA,GAAE,UAAU,GAAE,KAAK,QAAMA,GAAE,UAAU,GAAEA,GAAE,IAAI,IAAEH,KAAG,CAAAK,IAAE,KAAKF,GAAE,UAAU,CAAC,GAAEE,IAAE,KAAKF,GAAE,UAAU,CAAC,GAAE,KAAK,QAAMA,GAAE,UAAU,GAAE,KAAK,QAAMA,GAAE,UAAU;AAAE;AAAA,MAAK;AAAA,MAAC;AAAQ,QAAAA,GAAE,KAAK;AAAA,IAAC;AAAC,WAAO,IAAIH,GAAEM,IAAED,GAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBT,IAAE;AAAC,UAAMI,KAAE,GAAED,KAAE,GAAEI,KAAEP,GAAE,SAAS,GAAEK,KAAEE,GAAE,UAAU,GAAEE,MAAEF,GAAE,IAAI,IAAEF,IAAEK,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,QAAIE,KAAE,GAAEC,KAAE,GAAEjB,KAAE,MAAKqB,KAAE;AAAE,UAAMpB,MAAE,0BAAwB,KAAK;AAAa,WAAKS,GAAE,IAAI,IAAEE,OAAGF,GAAE,KAAK,IAAG,SAAOA,GAAE,IAAI,GAAE;AAAA,MAAC,KAAKH,IAAE;AAAC,cAAMJ,KAAEO,GAAE,UAAU,GAAEH,KAAEG,GAAE,IAAI,IAAEP;AAAE,eAAKO,GAAE,IAAI,IAAEH,MAAG;AAAC,gBAAMJ,KAAEO,GAAE,UAAU;AAAE,UAAAG,GAAE,KAAKV,EAAC,GAAEa,MAAGb;AAAA,QAAC;AAAC,QAAAH,KAAE,EAAE,IAAEgB,EAAC,EAAE;AAAM;AAAA,MAAK;AAAA,MAAC,KAAKV,IAAE;AAAC,QAAAI,GAAE,UAAU;AAAE,cAAMP,KAAE,KAAG,KAAK,OAAK,IAAE,MAAI,KAAK,OAAK,IAAE;AAAG,UAAEH,EAAC;AAAE,mBAAUO,MAAKM,GAAE,KAAGI,KAAEd,KAAEI,KAAEP,GAAE,OAAO,UAAQG,KAAE,GAAEA,KAAEI,IAAEJ,KAAI,CAAAO,GAAE,UAAU,GAAEA,GAAE,UAAU,GAAE,KAAK,QAAMA,GAAE,UAAU,GAAE,KAAK,QAAMA,GAAE,UAAU;AAAA,iBAAUT,OAAGF,IAAE;AAAC,gBAAMI,KAAE,KAAK,+BAA+BI,IAAE,KAAK,QAAQ,WAAW;AAAE,cAAID,KAAE,GAAEK,KAAE;AAAE,gBAAMH,KAAE;AAAG,cAAII,MAAEF,GAAE,UAAU,GAAEG,KAAEH,GAAE,UAAU;AAAE,UAAAV,GAAEiB,IAAG,IAAEL,KAAEZ,GAAEiB,IAAG,IAAEJ,IAAE,KAAK,QAAMH,GAAE,UAAU,GAAE,KAAK,QAAMA,GAAE,UAAU;AAAE,cAAIK,KAAEL,GAAE,UAAU,GAAEM,KAAEN,GAAE,UAAU;AAAE,eAAI,KAAK,QAAMA,GAAE,UAAU,GAAE,KAAK,QAAMA,GAAE,UAAU,GAAEJ,KAAEC,MAAG;AAAC,gBAAIA,KAAEG,GAAE,UAAU,GAAEF,KAAEE,GAAE,UAAU;AAAE,iBAAK,QAAMA,GAAE,UAAU,GAAE,KAAK,QAAMA,GAAE,UAAU;AAAE,kBAAMI,KAAEF,MAAEG,IAAEhB,KAAEc,KAAEG;AAAE,cAAEJ,KAAEC,IAAEC,IAAEf,IAAEe,KAAEP,IAAER,KAAES,EAAC,KAAGL,MAAGkB,MAAG,QAAKP,KAAEF,QAAIb,KAAEc,KAAGF,KAAE,KAAGO,GAAElB,GAAEiB,KAAE,CAAC,GAAEjB,GAAEiB,KAAE,CAAC,GAAEF,IAAEC,EAAC,KAAGhB,GAAEiB,KAAE,CAAC,KAAGF,IAAEf,GAAEiB,KAAE,CAAC,KAAGD,OAAIhB,GAAEiB,IAAG,IAAEF,IAAEf,GAAEiB,IAAG,IAAED,IAAEL,OAAKC,MAAEE,IAAED,KAAEd,OAAIQ,MAAGQ,IAAEP,MAAGQ,KAAGD,KAAER,IAAES,KAAER,IAAEF;AAAA,UAAG;AAAC,UAAAK,KAAE,KAAGH,KAAES,MAAG,IAAEN,MAAGU,MAAG,QAAKT,MAAEG,KAAEH,QAAIC,KAAEG,KAAEH,KAAGK,GAAElB,GAAEiB,KAAE,CAAC,GAAEjB,GAAEiB,KAAE,CAAC,GAAEF,IAAEC,EAAC,KAAGhB,GAAEiB,KAAE,CAAC,KAAGF,IAAEf,GAAEiB,KAAE,CAAC,KAAGD,IAAEF,GAAE,KAAKH,EAAC,MAAIX,GAAEiB,IAAG,IAAEF,IAAEf,GAAEiB,IAAG,IAAED,IAAEF,GAAE,KAAK,EAAEH,EAAC;AAAA,QAAG,OAAK;AAAC,cAAIR,KAAE,GAAEG,KAAEI,GAAE,UAAU,GAAEC,KAAED,GAAE,UAAU;AAAE,eAAK,QAAMA,GAAE,UAAU,GAAE,KAAK,QAAMA,GAAE,UAAU,GAAEV,GAAEiB,IAAG,IAAEX,IAAEN,GAAEiB,IAAG,IAAEN,IAAER,MAAG;AAAE,mBAAQK,KAAE,GAAEA,KAAED,IAAEC,MAAI;AAAC,kBAAMD,KAAEG,GAAE,UAAU,GAAEE,MAAEF,GAAE,UAAU,GAAEG,KAAEP,KAAEC,IAAEO,KAAEH,KAAEC;AAAE,YAAAS,MAAG,QAAKR,KAAEP,OAAIQ,KAAEH,KAAG,KAAK,QAAMD,GAAE,UAAU,GAAE,KAAK,QAAMA,GAAE,UAAU,GAAEF,KAAE,KAAGU,GAAElB,GAAEiB,KAAE,CAAC,GAAEjB,GAAEiB,KAAE,CAAC,GAAEV,IAAEK,GAAC,KAAGZ,GAAEiB,KAAE,CAAC,KAAGV,IAAEP,GAAEiB,KAAE,CAAC,KAAGL,QAAIZ,GAAEiB,IAAG,IAAEV,IAAEP,GAAEiB,IAAG,IAAEL,KAAET,MAAG,IAAGG,KAAEO,IAAEF,KAAEG;AAAA,UAAC;AAAC,UAAAA,GAAE,KAAKX,EAAC;AAAA,QAAC;AAAC;AAAA,MAAK;AAAA,MAAC;AAAQ,QAAAO,GAAE,KAAK;AAAA,IAAC;AAAC,QAAG,KAAK,OAAO,OAAKW,IAAE,CAACP,GAAE,OAAO,QAAO;AAAK,QAAG,KAAK,OAAK,KAAK,KAAI;AAAC,UAAIX,KAAE;AAAE,QAAEH,EAAC;AAAE,iBAAUO,MAAKO,GAAE,CAAAd,GAAE,IAAEG,EAAC,KAAG,KAAK,KAAIH,GAAE,IAAEG,KAAE,CAAC,KAAG,KAAK,KAAIA,MAAGI;AAAA,IAAC;AAAC,WAAO,IAAIA,GAAEO,IAAEd,EAAC;AAAA,EAAC;AAAC;;;ACA7wS,IAAMsB,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,UAAQA;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAC;AAAC,SAASC,GAAED,IAAE;AAAC,SAAO,MAAM,QAAQA,GAAE,MAAM;AAAC;AAAC,SAASE,GAAEF,IAAE;AAAC,SAAM,kBAAeA,MAAA,gBAAAA,GAAG;AAAI;AAAC,SAASG,GAAEH,IAAE;AAAC,QAAK,EAAC,cAAaI,GAAC,IAAEJ;AAAE,SAAOE,GAAEF,GAAE,MAAM,IAAE,IAAIK,GAAEL,EAAC,IAAEC,GAAED,EAAC,IAAE,IAAIM,GAAEN,EAAC,IAAEI,GAAE,MAAM,qBAAmB,IAAI,kBAAkB,IAAE,IAAIG,GAAEP,EAAC,IAAE,IAAIQ,GAAER,EAAC;AAAC;AAAC,eAAeS,GAAET,IAAE;AAAC,QAAMI,KAAE,IAAIK;AAAE,SAAO,MAAML,GAAE,KAAKJ,IAAE,CAAC,CAAC,GAAEI;AAAC;AAAC,IAAME,KAAN,cAAgBP,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,aAAWS,GAAET,GAAE,MAAM,EAAE,KAAM,CAAAA,OAAG,KAAK,SAAOA,EAAE;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,OAAO,MAAM,GAAE,KAAK,SAAO;AAAA,EAAI;AAAA,EAAC,MAAM,aAAaA,IAAEI,IAAE;AAAC,UAAM,KAAK;AAAW,UAAMM,KAAE,MAAM,KAAK,OAAO,OAAO,iBAAgBV,GAAE,OAAO,GAAEI,EAAC;AAAE,WAAOO,GAAE,eAAeD,IAAE,KAAK,OAAO;AAAA,EAAC;AAAC;AAAC,IAAMH,KAAN,cAAgBR,GAAC;AAAA,EAAC,MAAM,aAAaC,IAAEI,IAAE;AAAC,UAAK,EAAC,MAAKM,GAAC,IAAE,MAAMJ,GAAE,KAAK,QAAQ,QAAON,IAAEI,EAAC,GAAEQ,KAAE,CAACZ,GAAE;AAAuB,WAAOa,GAAE,WAAWH,IAAE,KAAK,SAAQE,EAAC;AAAA,EAAC;AAAC;AAAC,IAAMJ,KAAN,cAAgBT,GAAC;AAAA,EAAC,MAAM,aAAaW,IAAEI,IAAE;AAJ3jD;AAI4jD,UAAK,EAAC,QAAOC,IAAE,cAAaC,IAAE,kBAAiBjB,IAAE,eAAcE,KAAE,cAAaC,GAAC,IAAE,KAAK;AAAQ,QAAG,EAAEQ,GAAE,sBAAsB,KAAG,CAACM,GAAE,MAAM,sBAAqB;AAAC,YAAMhB,KAAEU,GAAE,MAAM,GAAEM,KAAEJ,GAAE,EAAEZ,GAAE,sBAAsB,CAAC;AAAE,MAAAA,GAAE,yBAAuB;AAAK,YAAK,EAAC,MAAKE,GAAC,IAAE,MAAMS,GAAEI,IAAEf,IAAED,IAAEe,EAAC,GAAEX,KAAE,GAAED,IAAED,GAAC;AAAE,aAAO,GAAEe,IAAEb,EAAC,GAAEQ,GAAE,wBAAwBR,IAAE,KAAK,OAAO;AAAA,IAAC;AAAC,UAAK,EAAC,MAAKA,GAAC,IAAE,MAAMQ,GAAEI,IAAEL,IAAE,KAAK,QAAQ,kBAAiBI,EAAC;AAAE,WAAM,wBAAsBZ,OAAIC,GAAE,YAAS,KAAAA,GAAE,aAAF,mBAAY,OAAQ,CAAAC,OAAG;AAAC,UAAG,EAAEA,GAAE,QAAQ,GAAE;AAAC,cAAMJ,KAAEI,GAAE;AAAS,eAAO,OAAO,SAASJ,GAAE,CAAC,KAAG,OAAO,SAASA,GAAE,CAAC;AAAA,MAAC;AAAC,aAAM;AAAA,IAAE,KAAKW,GAAE,eAAeR,IAAE,KAAK,OAAO;AAAA,EAAC;AAAC;AAAC,IAAME,KAAN,cAAgBN,GAAC;AAAA,EAAC,MAAM,aAAaC,IAAEU,IAAE;AAAC,UAAK,EAAC,cAAaO,GAAC,IAAE,KAAK;AAAQ,QAAGjB,GAAE,0BAAwB,CAACiB,GAAE,MAAM,sBAAqB;AAAC,YAAMA,KAAEjB,GAAE,MAAM,GAAEe,KAAEH,GAAE,EAAEK,GAAE,sBAAsB,CAAC;AAAE,MAAAA,GAAE,yBAAuB;AAAK,YAAMN,KAAE,MAAMN,GAAE,KAAK,QAAQ,QAAOL,IAAEU,EAAC;AAAE,aAAO,GAAEK,IAAEJ,EAAC,GAAEA,GAAE,wBAAwBA,IAAE,KAAK,OAAO;AAAA,IAAC;AAAC,UAAMI,KAAE,MAAMV,GAAE,KAAK,QAAQ,QAAOL,IAAEU,EAAC;AAAE,WAAOC,GAAE,wBAAwBI,IAAE,KAAK,OAAO;AAAA,EAAC;AAAC;;;ACAniF,IAAMG,KAAN,MAAM,GAAC;AAAA,EAAC,cAAa;AAAC,SAAK,UAAQ,GAAE,KAAK,SAAO,OAAG,KAAK,UAAQ,EAAC,SAAQ,OAAG,WAAU,MAAE,GAAE,KAAK,UAAQ,EAAC,SAAQ,OAAG,MAAK,MAAE,GAAE,KAAK,OAAK,OAAG,KAAK,cAAY,OAAG,KAAK,MAAI,EAAC,MAAK,CAAC,GAAE,QAAO,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,OAAO,OAAOC,IAAE;AAAC,UAAMC,KAAE,IAAI;AAAE,eAAUF,MAAKC,IAAE;AAAC,YAAME,KAAEF,GAAED,EAAC;AAAE,UAAG,YAAU,OAAOG,GAAE,YAAUF,MAAKE,IAAE;AAAC,cAAMC,MAAED,GAAEF,EAAC;AAAE,QAAAC,GAAEF,EAAC,EAAEC,EAAC,IAAEG;AAAA,MAAC;AAAC,MAAAF,GAAEF,EAAC,IAAEG;AAAA,IAAC;AAAC,WAAOD;AAAA,EAAC;AAAA,EAAC,OAAO,QAAO;AAAC,WAAO,GAAE,OAAO,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,MAAK;AAAC,WAAO,GAAE,OAAO,EAAC,QAAO,MAAG,SAAQ,EAAC,SAAQ,MAAG,WAAU,KAAE,GAAE,SAAQ,EAAC,SAAQ,MAAG,MAAK,KAAE,GAAE,MAAK,KAAE,CAAC;AAAA,EAAC;AAAA,EAAC,MAAMF,IAAE;AAAC,SAAK,UAAQA,GAAE,SAAQA,GAAE,WAAS,KAAK,SAAO,QAAIA,GAAE,QAAQ,YAAU,KAAK,QAAQ,UAAQ,QAAIA,GAAE,QAAQ,cAAY,KAAK,QAAQ,YAAU,QAAIA,GAAE,QAAQ,YAAU,KAAK,QAAQ,UAAQ,QAAIA,GAAE,QAAQ,SAAO,KAAK,QAAQ,OAAK,QAAIA,GAAE,SAAO,KAAK,OAAK,QAAIA,GAAE,gBAAc,KAAK,cAAY;AAAA,EAAG;AAAA,EAAC,MAAK;AAAC,WAAO,KAAK,UAAQ,KAAK,QAAM,KAAK,QAAQ,WAAS,KAAK,QAAQ,QAAM,KAAK,QAAQ,WAAS,KAAK,QAAQ,aAAW,KAAK;AAAA,EAAW;AAAA,EAAC,WAAU;AAAC,QAAIA,KAAE,GAAEC,KAAE;AAAG,QAAG,KAAK,MAAK;AAAC,MAAAD,MAAG,IAAGC,MAAG;AAA8B,iBAAUD,MAAK,KAAK,IAAI,KAAK,CAAAC,MAAG,SAASD,EAAC;AAAA;AAAA,IAAI;AAAC,QAAG,KAAK,QAAO;AAAC,MAAAA,MAAG,IAAGC,MAAG;AAAoC,iBAAUD,MAAK,KAAK,IAAI,OAAO,CAAAC,MAAG,SAASD,EAAC;AAAA;AAAA,IAAI;AAAC,SAAK,QAAQ,YAAUA,MAAG,GAAEC,MAAG,+CAA8C,KAAK,QAAQ,YAAUD,MAAG,GAAEC,MAAG,+CAA8C,KAAK,QAAQ,cAAYD,MAAG,GAAEC,MAAG,iDAAgD,KAAK,QAAQ,SAAOD,MAAG,GAAEC,MAAG;AAA6C,UAAMC,KAAEF,KAAE,IAAE,YAAUA,KAAE,KAAG,SAAOA,KAAE,KAAG,aAAWA,KAAE,KAAG,SAAO;AAAY,YAAQ,MAAM,YAAYE,EAAC,mBAAmBF,EAAC,MAAM,GAAE,QAAQ,MAAMC,EAAC;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,WAAM,EAAC,aAAY,KAAK,aAAY,QAAO,KAAK,QAAO,SAAQ,KAAK,SAAQ,SAAQ,KAAK,SAAQ,MAAK,KAAK,KAAI;AAAA,EAAC;AAAC;;;ACA54C,IAAMI,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,SAAK,WAAS,EAAC,MAAK,IAAI,SAAM,QAAO,IAAIA,GAAE,EAAE,EAAC,GAAE,KAAK,SAAO,MAAK,KAAK,mBAAiB,IAAI,mBAAgB,KAAK,WAAS,GAAE,KAAK,QAAM,OAAG,KAAK,UAAQ,OAAG,KAAK,OAAKD,IAAE,KAAK,WAASC;AAAA,EAAC;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK,iBAAiB;AAAA,EAAM;AAAA,EAAC,IAAI,UAAS;AAAC,WAAM,EAAC,QAAO,KAAK,iBAAiB,OAAM;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAM,CAAC,KAAK,SAAS,KAAK,UAAQ,EAAE,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK;AAAA,EAAM;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK;AAAA,EAAK;AAAA,EAAC,MAAK;AAAC,SAAK,QAAM;AAAA,EAAE;AAAA,EAAC,QAAO;AAAC,SAAK,SAAS,OAAK,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,SAAK,SAAS,KAAK,QAAS,CAAAF,OAAGA,GAAE,QAAQ,OAAO,MAAME,EAAC,CAAE,GAAE,KAAK,WAASA,GAAE,SAAQ,EAAE,KAAK,MAAM,KAAG,KAAK,OAAO,OAAO,MAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAE;AAAC,IAAAA,GAAE,QAAQ,SAAOA,GAAE,QAAQ,UAAQF,GAAE,MAAM,GAAEE,GAAE,QAAQ,OAAO,UAAQ,KAAK,UAAS,IAAI,sBAAsB,KAAG,QAAQ,MAAM,KAAK,KAAK,IAAG,4BAA2B,KAAK,QAAQ,GAAEA,GAAE,QAAQ,OAAK,KAAK,SAAS,KAAK,QAAS,CAAAA,OAAG;AAAC,QAAEA,GAAE,OAAO,KAAGA,GAAE,QAAQ,QAAMA,GAAE,QAAQ,MAAI;AAAA,IAAG,CAAE,GAAE,KAAK,SAAS,KAAK,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAEH,KAAE;AAAC,UAAMI,KAAED,GAAE,yBAAyB,GAAEE,KAAEF,GAAE,WAAW,GAAEG,KAAE,MAAM,KAAKH,GAAE,SAAS,CAAC,EAAE,OAAO,CAAC,GAAEI,KAAE,CAAC,GAAGP,KAAE,GAAGM,GAAE,IAAK,CAAAH,OAAGA,GAAE,QAAS,CAAC;AAAE,QAAG,KAAK,UAAUI,EAAC,GAAE,KAAK,YAAY,GAAE,EAAE,KAAK,MAAM,EAAE,QAAO,MAAK,KAAK,SAAO,EAAC,MAAK,UAAS,aAAYC,GAAE,sBAAsBF,IAAED,IAAE,EAAED,EAAC,CAAC,GAAE,IAAG,KAAK,KAAK,IAAG,QAAOH,GAAE,MAAM,GAAE,KAAI,KAAE;AAAG,SAAK,SAAS,KAAK,QAAS,CAAAE,OAAGA,GAAE,QAAQ,MAAI,KAAG;AAAE,MAAE,KAAK,OAAO,WAAW,EAAE,OAAOA,GAAE,SAAS,CAAC;AAAA,EAAC;AAAA,EAAC,CAAC,UAAS;AAAC,eAAS,EAAC,SAAQA,GAAC,KAAI,KAAK,SAAS,KAAK,GAAEA,GAAE,WAAW,MAAI,MAAMA,GAAE;AAAa,MAAE,KAAK,MAAM,KAAG,EAAE,KAAK,OAAO,WAAW,MAAI,MAAM,KAAK,OAAO;AAAA,EAAY;AAAA,EAAC,cAAa;AAAC,eAAUA,MAAK,KAAK,SAAS,KAAK,CAAAA,GAAE,QAAQ,SAAOF,GAAE,MAAM;AAAE,MAAE,KAAK,MAAM,MAAI,KAAK,OAAO,SAAOA,GAAE,MAAM;AAAA,EAAE;AAAA,EAAC,UAAUE,IAAE;AAAC,SAAK,YAAY;AAAE,eAAS,EAAC,SAAQF,GAAC,KAAI,KAAK,SAAS,MAAK;AAAC,YAAMQ,KAAER,GAAE;AAAY,QAAEQ,EAAC,MAAIA,GAAE,UAAUN,EAAC,GAAEM,GAAE,YAAU,IAAI,sBAAsB,KAAG,QAAQ,MAAM,2BAA2B,GAAER,GAAE,cAAY;AAAA,IAAM;AAAC,MAAE,KAAK,MAAM,KAAG,EAAE,KAAK,OAAO,WAAW,KAAG,KAAK,OAAO,YAAY,UAAUE,EAAC,GAAE,KAAK,SAAS,OAAK,KAAK,SAAS,KAAK,OAAQ,CAAAA,OAAGA,GAAE,QAAQ,eAAaA,GAAE,QAAQ,GAAI;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,iBAAiB,MAAM;AAAA,EAAC;AAAC;;;ACA9sD,SAASO,GAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAE,oBAAI;AAAI,SAAOF,MAAGA,GAAE,QAAS,CAAAA,OAAGE,GAAE,IAAIF,EAAC,CAAE,GAAEC,MAAGA,GAAE,QAAS,CAAAD,OAAGE,GAAE,IAAIF,EAAC,CAAE,GAAEE,GAAE,IAAI,GAAG,IAAE,CAAC,GAAG,IAAE,MAAM,KAAKA,EAAC;AAAC;AAAC,IAAMC,KAAN,cAAgB,EAAC;AAAA,EAAC,YAAYH,IAAE;AAAC,UAAM,GAAE,KAAK,SAAO,IAAI,KAAE,KAAK,YAAU,EAAE,GAAE,KAAK,WAAS,OAAG,KAAK,iBAAe,oBAAI,OAAI,KAAK,SAAOA,GAAE,OAAM,KAAK,eAAaA,GAAE,aAAY,KAAK,uBAAqBA,GAAE;AAAA,EAAS;AAAA,EAAC,MAAM,WAAWA,IAAE;AAAC,UAAMC,KAAE,KAAK,eAAe,IAAID,GAAE,EAAE;AAAE,QAAG,CAACC,GAAE;AAAO,UAAMC,KAAE,EAAC,GAAGF,IAAE,QAAOA,GAAE,UAAQ,CAAC,GAAE,QAAOA,GAAE,UAAQE,GAAE,MAAM,EAAC;AAAE,WAAOE,GAAE,KAAK,qBAAqBF,IAAED,GAAE,OAAO,CAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAEC,IAAE;AAJnpC;AAIopC,UAAMG,KAAEH,GAAE,OAAO;AAAO,IAAAA,GAAE,YAAUH,IAAE,UAAK,YAAL,mBAAc,WAAUG,GAAE,SAAS,GAAEA,GAAE,YAAUA,GAAE,UAAU,UAAQ,OAAIG,KAAE,CAAC,GAAG,IAAEH,GAAE,WAAUA,GAAE,UAAU,KAAK;AAAE,UAAMI,KAAE,EAAE,KAAK,SAAQJ,EAAC;AAAE,QAAG,CAACI,GAAE;AAAO,QAAI,sBAAsB,KAAG,QAAQ,MAAM,6BAA4BA,EAAC;AAAE,UAAMC,KAAE,mBAAkB,KAAK,gBAAc,KAAK,aAAa,gBAAc,KAAK,aAAa,gBAAc,KAAK,aAAa,gBAAc,QAAOC,KAAE,EAAC,gBAAe,0BAAwB,KAAK,aAAa,cAAa,gBAAe,MAAG,4BAA2B,aAAW,KAAK,aAAa,QAAM,KAAK,aAAa,4BAA2B,WAAUN,GAAE,WAAU,qBAAoB,KAAK,QAAO,eAAc,CAACK,EAAC,GAAE,OAAML,GAAE,wBAAsB,OAAM,YAAWA,GAAE,YAAW,gBAAeA,GAAE,gBAAe,YAAWA,GAAE,aAAWO,GAAE,SAASP,GAAE,UAAU,IAAE,KAAI,GAAEQ,KAAE,KAAK,WAASC,GAAEL,IAAE,WAAW;AAAE,SAAK,WAASM,GAAEN,IAAE,CAAC,cAAa,wBAAuB,cAAa,kBAAiB,kBAAkB,CAAC,MAAIL,GAAE,IAAI,KAAK,KAAK,+CAA+C,GAAEA,GAAE,IAAI,OAAO,KAAK,+CAA+C,GAAEA,GAAE,OAAK,MAAGA,GAAE,SAAO,MAAGA,GAAE,cAAY,OAAIS,OAAIT,GAAE,IAAI,OAAO,KAAK,+BAA+B,GAAEA,GAAE,SAAO,OAAI,EAAEO,IAAE,KAAK,UAAU,MAAI,KAAK,aAAWA,KAAG,KAAK,UAAQN,IAAE,KAAK,UAAU,QAAQ;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,WAAO,KAAK,UAAU;AAAA,EAAO;AAAA,EAAC,MAAM,YAAYF,IAAE;AAAC,QAAGA,GAAE,eAAaA,GAAE,UAAQ,KAAK,SAAS,QAAO,KAAK,QAAQA,GAAE,OAAO,GAAE,MAAK,KAAK,WAAS;AAAI,SAAK,eAAe,QAAS,CAAAC,OAAGA,GAAE,YAAYD,EAAC,CAAE,GAAE,MAAM,KAAK,OAAO;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAEC,IAAE;AAAC,eAAUC,MAAK,KAAK,OAAO,EAAE,MAAK,YAAYA,EAAC,GAAE,KAAK,UAAUA,IAAEF,EAAC;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAEC,IAAE;AAAC,UAAMC,KAAE,IAAIS,GAAEX,IAAEC,EAAC;AAAE,SAAK,eAAe,IAAID,GAAE,IAAGE,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYF,IAAE;AAAC,UAAMC,KAAE,KAAK,gBAAgBD,GAAE,EAAE;AAAE,MAAEC,EAAC,KAAGA,GAAE,MAAM,GAAE,KAAK,eAAe,OAAOD,GAAE,EAAE;AAAA,EAAC;AAAA,EAAC,YAAYA,KAAE,CAAC,GAAE;AAAC,UAAMC,KAAE,KAAK,WAAW,iBAAe,IAAI,KAAK,KAAK,WAAW,cAAc,IAAE;AAAK,WAAO,IAAI,EAAE,EAAC,GAAG,KAAK,YAAW,gBAAeA,IAAE,GAAGD,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,WAAO,KAAK,eAAe,IAAIA,EAAC,IAAE,KAAK,eAAe,IAAIA,EAAC,IAAE;AAAA,EAAI;AAAA,EAAC,MAAM,oBAAmB;AAAC,UAAM,IAAI,MAAM,qCAAqC;AAAA,EAAC;AAAA,EAAC,MAAM,MAAMA,IAAEC,IAAE;AAAC,UAAM,IAAI,MAAM,gCAAgC;AAAA,EAAC;AAAA,EAAC,CAAC,SAAQ;AAAC,UAAMD,KAAE,MAAM,KAAK,KAAK,eAAe,OAAO,CAAC;AAAE,eAAUC,MAAKD,GAAE,OAAMC,GAAE;AAAA,EAAI;AAAA,EAAC,MAAM,KAAKD,IAAEC,IAAE;AAAC,UAAMC,KAAE,MAAM,KAAK,KAAK,eAAe,OAAO,CAAC,GAAEG,KAAEH,GAAE,IAAK,CAAC,EAAC,MAAKF,GAAC,MAAIA,EAAE;AAAE,eAAUM,MAAKJ,GAAE,CAAAI,GAAE,UAAUL,EAAC;AAAE,QAAGD,GAAE,QAAO;AAAC,YAAME,KAAEG,GAAE,IAAK,CAAAJ,QAAG;AAAC,cAAMC,KAAE,KAAK,gBAAgBD,GAAC;AAAE,eAAOC,GAAE,YAAUF,IAAE,EAAC,MAAKC,KAAE,OAAMC,GAAC;AAAA,MAAC,CAAE,EAAE,IAAK,OAAM,EAAC,MAAKF,IAAE,OAAMC,IAAC,OAAK,EAAC,MAAKD,IAAE,QAAO,MAAM,KAAK,MAAMC,KAAE,EAAC,OAAM,EAAC,MAAK,IAAI,kBAAkB,IAAED,GAAE,GAAG,QAAQ,OAAM,GAAG,IAAE,OAAM,EAAC,CAAC,GAAE,OAAMC,IAAC,EAAG,GAAEK,MAAG,MAAM,EAAEJ,EAAC,GAAG,IAAK,OAAM,EAAC,MAAKA,IAAE,QAAOG,GAAC,MAAI;AAAC,YAAG,CAACA,GAAE,eAAa,CAACJ,GAAE,UAAQ,CAACD,GAAE,OAAO;AAAO,cAAMM,KAAE,KAAK,eAAe,IAAIJ,GAAE,IAAI,EAAE;AAAE,QAAAI,MAAGA,GAAE,KAAKD,IAAEL,EAAC;AAAA,MAAC,CAAE;AAAE,YAAM,EAAEM,EAAC;AAAA,IAAC;AAAC,SAAK,WAAS;AAAA,EAAE;AAAC;;;ACAj3G,IAAMO,KAAE;AAAE,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK,WAAU,KAAK,OAAK,aAAY,KAAK,WAASC,GAAED,GAAE,WAAW,GAAE,KAAK,SAAO,IAAIE,GAAE,EAAC,aAAY,GAAE,SAAQ,OAAMF,OAAG;AAJ/sB;AAIgtB,UAAG,EAAEA,EAAC,GAAE,EAAEA,GAAE,IAAI,GAAE;AAAC,cAAMG,KAAEH,GAAE,KAAK,IAAI,IAAG,EAAC,QAAOI,GAAC,IAAEJ,IAAEK,KAAE,IAAI,kBAAkB,IAAE,EAAC,MAAKF,GAAE,QAAQ,OAAM,GAAG,GAAE,OAAMH,GAAE,MAAK,IAAE,QAAOM,KAAE,MAAM,KAAK,SAAS,aAAaN,GAAE,OAAM,EAAC,QAAOI,IAAE,OAAM,EAAC,GAAGC,IAAE,IAAG,UAAK,YAAL,mBAAc,iBAAgB,EAAC,CAAC;AAAE,eAAOC,GAAE,QAAMN,GAAE,KAAK,IAAI,OAAMM;AAAA,MAAC;AAAC,aAAO,KAAK,SAAS,aAAaN,GAAE,OAAM,EAAC,GAAGA,IAAE,QAAM,UAAK,YAAL,mBAAc,iBAAgB,CAAC;AAAA,IAAC,EAAC,CAAC,GAAE,KAAK,cAAY,IAAIE,GAAE,EAAC,aAAY,GAAE,SAAQ,OAAMF,OAAG;AAJ1mC;AAI2mC,UAAG,EAAEA,EAAC,GAAE,EAAEA,GAAE,IAAI,GAAE;AAAC,cAAMG,KAAEH,GAAE,KAAK,IAAI,IAAG,EAAC,QAAOI,GAAC,IAAEJ,IAAEK,KAAE,IAAI,kBAAkB,IAAE,EAAC,MAAKF,GAAE,QAAQ,OAAM,GAAG,GAAE,OAAMH,GAAE,MAAK,IAAE,QAAOM,KAAE,MAAM,KAAK,SAAS,aAAaN,GAAE,OAAM,EAAC,QAAOI,IAAE,OAAM,EAAC,GAAGC,IAAE,IAAG,UAAK,YAAL,mBAAc,iBAAgB,EAAC,CAAC;AAAE,eAAOC,GAAE,QAAMN,GAAE,KAAK,IAAI,OAAMM;AAAA,MAAC;AAAC,aAAO,KAAK,SAAS,aAAaN,GAAE,OAAM,EAAC,GAAGA,IAAE,QAAM,UAAK,YAAL,mBAAc,iBAAgB,CAAC;AAAA,IAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAM,QAAQ,GAAE,KAAK,SAAS,QAAQ,GAAE,KAAK,OAAO,QAAQ,GAAE,KAAK,YAAY,QAAQ;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAM,CAAC,CAAC,KAAK,OAAO,UAAQ,MAAM,KAAK,KAAK,eAAe,OAAO,CAAC,EAAE,KAAM,CAAAA,OAAG,CAACA,GAAE,IAAK;AAAA,EAAC;AAAA,EAAC,IAAI,uBAAsB;AAAC,UAAK,EAAC,OAAMA,GAAC,IAAE,KAAK,aAAa;AAAa,WAAOA,GAAE,+BAA6BH,KAAE;AAAA,EAAI;AAAA,EAAC,IAAI,cAAa;AAAC,UAAK,EAAC,OAAMG,GAAC,IAAE,KAAK,aAAa;AAAa,YAAOA,GAAE,kBAAgB,OAAK,EAAE,KAAK,sBAAqB,CAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,IAAI,KAAI,KAAK,WAAW;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAEG,IAAE;AAAA,EAAC;AAAA,EAAC,UAAUH,IAAEK,IAAE;AAAC,UAAM,UAAUL,IAAEK,EAAC;AAAE,UAAMC,KAAE,KAAK,eAAe,IAAIN,GAAE,EAAE;AAAE,SAAK,eAAeA,EAAC,EAAE,MAAO,CAAAK,QAAG;AAAC,QAAEA,GAAC,KAAG,EAAE,UAAU,yDAAyD,EAAE,MAAM,IAAIA,GAAE,uBAAsB,wCAAuC,EAAC,MAAKL,IAAE,OAAMK,IAAC,CAAC,CAAC;AAAA,IAAC,CAAE,EAAE,KAAM,MAAIC,GAAE,IAAI,CAAE;AAAA,EAAC;AAAA,EAAC,YAAYN,IAAE;AAAC,UAAM,YAAYA,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,WAAO,KAAK,eAAe,IAAIA,EAAC,EAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,MAAM,MAAMA,IAAEG,KAAE,CAAC,GAAE;AAJ52E;AAI62E,UAAMC,KAAED,GAAE,SAAO,CAAC;AAAE,WAAO,KAAK,SAAS,aAAaH,IAAE,EAAC,GAAGG,IAAE,OAAM,EAAC,GAAGC,IAAE,IAAG,UAAK,YAAL,mBAAc,iBAAgB,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,oBAAmB;AAAC,UAAMD,KAAE,KAAK,aAAa,QAAOC,KAAE,EAAC,GAAGD,GAAE,OAAM,GAAE,OAAM;AAAE,YAAO,MAAMI,GAAEJ,GAAE,MAAK,EAAC,OAAMC,IAAE,cAAa,OAAM,CAAC,GAAG,KAAK,YAAY;AAAA,EAAY;AAAA,EAAC,gBAAgBJ,IAAEG,KAAE,CAAC,GAAE;AAAC,UAAMC,KAAE,KAAK,aAAa,cAAaC,KAAE,KAAK,YAAYF,EAAC;AAAE,IAAAE,GAAE,yBAAuBF,GAAE,0BAAwBH,GAAE,0BAA0B,GAAEK,GAAE,aAAW,QAAOA,GAAE,WAASL,GAAE,QAAO,KAAK,aAAa,aAAa,MAAM,uBAAqB,2BAAyBI,OAAIC,GAAE,qBAAmBL,GAAE,aAAW,IAAI,wCAAwC,KAAG,2BAAyBI,MAAG,0BAAwBA,OAAIC,GAAE,qBAAmBL,GAAE,YAAW,2BAAyBI,OAAIC,GAAE,sBAAoB,IAAI,wCAAwC;AAAI,UAAMC,KAAE,KAAK,aAAa,aAAa;AAAM,WAAOD,GAAE,iCAA+BC,GAAE,iCAAgCD,GAAE,yBAAuBC,GAAE,yBAAwBD;AAAA,EAAC;AAAA,EAAC,MAAM,mBAAmBL,IAAEG,IAAEC,IAAEE,IAAE;AAAC,UAAME,MAAEL,GAAE,MAAM;AAAE,IAAAK,IAAE,YAAU,CAAC,KAAK,aAAa,eAAc,GAAGJ,EAAC,GAAEI,IAAE,iBAAe,OAAGA,IAAE,iBAAe;AAAG,UAAMC,KAAE,EAAED,IAAE,KAAK,IAAEA,IAAE,QAAM,MAAI,GAAEE,KAAEJ,GAAE;AAAO,WAAO,KAAK,YAAY,KAAK,EAAC,MAAKN,IAAE,OAAMQ,KAAE,QAAOE,IAAE,OAAMD,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQT,IAAEG,IAAE;AAAC,UAAK,EAAC,OAAMC,IAAE,SAAQE,GAAC,IAAEN,IAAEU,KAAE,EAAEN,GAAE,SAAS,IAAEA,GAAE,YAAU,CAAC,GAAEO,KAAE,KAAK,WAAW,WAAUC,KAAED,GAAE,OAAQ,CAAAX,OAAG,CAACU,GAAE,SAASV,EAAC,CAAE;AAAE,QAAG,EAAEM,GAAE,WAAW,EAAE,MAAK,WAAW,EAAC,GAAGA,IAAE,MAAK,SAAQ,CAAC;AAAA,aAAUM,GAAE,OAAO,KAAG;AAAC,YAAMZ,KAAE,KAAK,eAAe,IAAIM,GAAE,EAAE,EAAE,MAAKD,KAAE,MAAM,KAAK,mBAAmBL,IAAEI,IAAEQ,IAAET,EAAC;AAAE,QAAEA,EAAC,GAAEC,GAAE,YAAUO,IAAEL,GAAE,YAAY,eAAeD,EAAC,GAAE,KAAK,WAAW,EAAC,GAAGC,IAAE,KAAIA,GAAE,KAAI,MAAK,SAAQ,CAAC;AAAA,IAAC,SAAOJ,IAAE;AAAA,IAAC;AAAA,QAAM,MAAK,WAAW,EAAC,GAAGI,IAAE,MAAK,SAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,oBAAoBN,IAAE;AAAC,QAAG,IAAI,sBAAsB,KAAG,QAAQ,MAAMA,GAAE,KAAK,IAAG,qBAAqB,GAAEA,GAAE,MAAM,QAAO,KAAK,WAAW,EAAC,IAAGA,GAAE,KAAK,IAAG,aAAY,MAAK,KAAI,OAAG,MAAK,SAAQ,CAAC;AAAE,UAAMG,KAAEH,GAAE;AAAO,eAAUI,MAAKJ,GAAE,SAAS,KAAK,OAAM,KAAK,QAAQI,IAAE,EAAC,QAAOD,GAAC,CAAC;AAAE,WAAO,EAAEH,GAAE,KAAK,IAAE,KAAK,WAAWA,GAAE,KAAK,IAAE;AAAA,EAAM;AAAA,EAAC,MAAM,SAAQ;AAAC,UAAMA,KAAE,MAAM,KAAK,KAAK,eAAe,OAAO,CAAC;AAAE,UAAM,QAAQ,IAAIA,GAAE,IAAK,CAAAA,OAAG,KAAK,oBAAoBA,EAAC,CAAE,CAAC;AAAA,EAAC;AAAC;;;ACAnyI,IAAMa,KAAE,IAAI,aAAa;AAAzB,IAA2BC,KAAE,EAAC,eAAcD,KAAE,IAAE,GAAE,sBAAqBA,KAAE,IAAE,EAAC;AAAE,IAAME,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeC,IAAE;AAAC,UAAML,KAAE,KAAK,aAAa,aAAa,MAAM,8BAA6BE,MAAE,KAAK,eAAe,IAAIG,GAAE,IAAI,EAAE,GAAEC,KAAEJ,IAAE,QAAOK,KAAEF,GAAE,0BAA0B;AAAE,QAAIG,KAAE;AAAE,UAAML,KAAE,OAAMM,IAAEC,OAAI;AAAC,YAAMC,KAAE,KAAK,YAAWC,KAAE,KAAK,gBAAgBH,IAAE,EAAC,sBAAqBT,KAAEC,GAAE,uBAAqB,QAAO,6BAA4B,OAAG,wBAAuBM,GAAC,CAAC;AAAE,MAAAC;AAAI,UAAG;AAAC,cAAMK,KAAE,MAAM,KAAK,OAAO,KAAK,EAAC,MAAKR,IAAE,OAAMO,IAAE,QAAON,IAAE,OAAMI,GAAC,CAAC;AAAE,YAAGF,MAAI,EAAEF,EAAC,GAAE,CAACO,GAAE;AAAO,YAAGF,OAAI,KAAK,WAAW,QAAO,KAAKR,GAAEM,IAAEC,EAAC;AAAE,YAAGG,GAAE,yBAAuBH,KAAET,GAAE,eAAc;AAAC,qBAAUG,MAAKK,GAAE,iBAAiB,EAAE,CAAAN,GAAEC,IAAEM,KAAE,CAAC;AAAE;AAAA,QAAM;AAAC,cAAMV,MAAE,EAAC,IAAGK,GAAE,IAAG,aAAYQ,IAAE,KAAI,MAAIL,IAAE,MAAK,SAAQ;AAAE,QAAAN,IAAE,IAAI,EAAC,OAAMU,IAAE,SAAQZ,IAAC,CAAC,GAAE,KAAK,WAAWA,GAAC;AAAA,MAAC,SAAOc,IAAE;AAAC,UAAEA,EAAC,KAAG,KAAK,WAAW,EAAC,IAAGT,GAAE,IAAG,aAAY,MAAK,KAAI,MAAG,MAAK,SAAQ,CAAC;AAAA,MAAC;AAAA,IAAC;AAAE,IAAAF,GAAEE,IAAE,CAAC;AAAA,EAAC;AAAC;;;ACAtxB,IAAMU,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeC,IAAE;AAAC,UAAMC,KAAE,GAAEC,KAAE,IAAGL,KAAE,KAAK,eAAe,IAAIG,GAAE,IAAI,EAAE;AAAE,QAAIF,KAAE,OAAGK,KAAE,GAAEC,KAAE;AAAE,UAAMC,KAAE,CAACN,IAAEO,OAAI;AAAC,MAAAF,MAAI,EAAEP,EAAC;AAAE,YAAMU,MAAEP,GAAE,IAAGC,KAAEF,GAAE,QAAOG,KAAEH,GAAE;AAAM,UAAG,CAACE,GAAE,uBAAsB;AAAC,YAAGH,KAAE,MAAG,MAAIQ,MAAG,CAACL,GAAE,aAAY;AAAC,gBAAMF,KAAE,EAAC,IAAGQ,KAAE,aAAYN,IAAE,KAAI,MAAIG,IAAE,MAAK,SAAQ;AAAE,iBAAOP,GAAE,IAAI,EAAC,SAAQE,IAAE,OAAMG,GAAC,CAAC,GAAE,KAAK,KAAK,WAAWH,EAAC;AAAA,QAAC;AAAC,cAAMA,KAAE,EAAC,IAAGQ,KAAE,aAAYN,IAAE,KAAI,MAAIG,IAAE,MAAK,SAAQ;AAAE,eAAOP,GAAE,IAAI,EAAC,SAAQE,IAAE,OAAMG,GAAC,CAAC,GAAE,KAAK,KAAK,WAAWH,EAAC;AAAA,MAAC;AAAC,YAAMI,MAAE,EAAC,IAAGI,KAAE,aAAYN,IAAE,KAAIH,MAAG,MAAIM,IAAE,MAAK,SAAQ;AAAE,MAAAP,GAAE,IAAI,EAAC,SAAQM,KAAE,OAAMD,GAAC,CAAC,GAAE,KAAK,WAAWC,GAAC;AAAA,IAAC;AAAE,QAAIK,KAAE,GAAEC,KAAE;AAAE,WAAK,CAACX,MAAGW,OAAIP,MAAG;AAAC,UAAIA;AAAE,eAAQQ,KAAE,GAAEA,KAAEF,KAAE,GAAEE,MAAI;AAAC,cAAMA,MAAEP;AAAI,QAAAC,MAAIF,KAAE,KAAK,mBAAmBF,IAAEU,KAAEb,EAAC,EAAE,KAAM,CAAAE,OAAGA,MAAGM,GAAEN,IAAEW,GAAC,CAAE,EAAE,MAAO,CAAAA,QAAG;AAAC,UAAAZ,KAAE,MAAG,EAAEY,GAAC,MAAI,EAAE,UAAU,0DAA0D,EAAE,MAAM,IAAIA,GAAE,uBAAsB,wCAAuC,EAAC,MAAKV,IAAE,OAAMU,IAAC,CAAC,CAAC,GAAE,KAAK,WAAW,EAAC,IAAGV,GAAE,IAAG,aAAY,MAAK,KAAIF,IAAE,MAAK,SAAQ,CAAC;AAAA,QAAE,CAAE;AAAA,MAAC;AAAC,YAAMI,IAAE,EAAEL,EAAC,GAAEW,KAAE,KAAK,IAAIA,KAAE,GAAEP,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,mBAAmBF,IAAEO,IAAEC,KAAE;AAAC,MAAEA,GAAC;AAAE,UAAML,KAAE,KAAK,YAAWL,KAAE,EAAC,OAAM,KAAK,WAASS,IAAE,KAAI,KAAK,UAAS,6BAA4B,MAAG,wBAAuBP,GAAE,0BAA0B,EAAC;AAAE,MAAE,KAAK,oBAAoB,MAAIF,GAAE,uBAAqB,KAAK;AAAsB,UAAMC,KAAE,KAAK,gBAAgBC,IAAEF,EAAC;AAAE,QAAG;AAAC,YAAMG,KAAEO,IAAE,QAAON,KAAE,MAAM,KAAK,OAAO,KAAK,EAAC,MAAKF,IAAE,OAAMD,IAAE,QAAOE,IAAE,OAAMM,GAAC,CAAC;AAAE,aAAO,EAAEC,GAAC,GAAEN,KAAEC,OAAI,KAAK,aAAW,KAAK,mBAAmBH,IAAEO,IAAEC,GAAC,IAAE,EAAC,QAAON,IAAE,OAAMH,GAAC,IAAE;AAAA,IAAI,SAAOK,IAAE;AAAC,aAAO,EAAEA,EAAC,GAAE;AAAA,IAAI;AAAA,EAAC;AAAC;;;ACApyC,SAASQ,GAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEH,GAAE,aAAa,GAAEI,KAAEJ,GAAE,aAAa,GAAEK,MAAEJ,GAAE,cAAcE,EAAC,GAAEG,KAAE,KAAK,MAAML,GAAE,aAAaI,GAAC,CAAC;AAAE,SAAM,GAAGH,EAAC,IAAI,KAAK,MAAMD,GAAE,WAAWG,EAAC,CAAC,CAAC,IAAIE,EAAC;AAAE;AAAC,SAASC,GAAEP,IAAEC,IAAE;AAAC,MAAG,EAAED,EAAC,EAAE,QAAO;AAAK,QAAME,KAAED,GAAE,WAAUG,KAAEJ,GAAE,yBAAyB;AAAE,MAAG,EAAEI,EAAC,GAAE;AAAC,UAAK,CAACH,IAAEE,EAAC,IAAED,GAAE,OAAM,CAACE,IAAEC,GAAC,IAAEH,GAAE,WAAUI,KAAE,CAACF,KAAEH,IAAEO,KAAE,IAAEP,IAAEQ,MAAEJ,MAAEF,IAAEO,KAAE,IAAE,CAACP;AAAE,WAAOH,GAAE,UAAUM,IAAEG,KAAED,IAAEE,EAAC;AAAA,EAAC;AAAC,QAAK,CAACL,KAAEC,EAAC,IAAEF,GAAE,OAAM,CAACI,IAAEC,EAAC,IAAEL,GAAE,WAAU,CAACM,IAAEX,EAAC,IAAEG,GAAE,OAAM,CAACK,IAAEI,EAAC,IAAET,GAAE,WAAUU,KAAEP,MAAEK,IAAEG,MAAGL,KAAED,MAAGG,IAAEI,KAAER,KAAEP,IAAEgB,OAAG,CAACN,KAAEE,MAAGZ;AAAE,SAAOC,GAAE,UAAUa,IAAEE,KAAEH,IAAEE,EAAC;AAAC;AAAC,IAAMH,KAAN,cAAgBF,GAAC;AAAA,EAAC,YAAYT,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK,YAAW,KAAK,WAAS,MAAG,KAAK,cAAY,IAAI,mBAAgB,KAAK,mBAAiB,MAAK,KAAK,cAAY,OAAG,KAAK,WAAS,IAAI,SAAM,KAAK,eAAa,OAAG,KAAK,iBAAe,OAAG,KAAK,UAAQ,IAAIA,GAAE,GAAG,GAAE,KAAK,SAAOA,GAAE,OAAM,KAAK,kBAAgB,KAAK,OAAO,QAAQ,aAAa;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAM,QAAQ,GAAE,KAAK,YAAY,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK;AAAA,EAAQ;AAAA,EAAC,IAAI,UAAS;AAAC,WAAO,KAAK,YAAY;AAAA,EAAM;AAAA,EAAC,OAAOA,IAAEE,IAAE;AAJx6C;AAIy6C,UAAM,OAAOF,IAAEE,EAAC,GAAE,QAAM,KAAK,kBAAgB,KAAK,gBAAcA,GAAE,sBAAqB,EAAEA,GAAE,mBAAmB,MAAI,KAAK,gBAAcA,GAAE,sBAAqB,KAAK,iBAAe,CAAC,GAAC,KAAAF,GAAE,YAAF,mBAAW;AAAA,EAAS;AAAA,EAAC,MAAM,OAAOA,KAAE,OAAG;AAAC,QAAG,MAAM,KAAK,kBAAiB,KAAK,gBAAcA,IAAE;AAAC,YAAMA,KAAE,EAAE,KAAK,eAAc,qCAAqC;AAAE,aAAO,KAAK,eAAa,OAAG,KAAK,eAAe,QAAS,CAAAA,OAAGA,GAAE,MAAM,CAAE,GAAE,KAAK,mBAAiB,KAAK,UAAUA,EAAC,GAAE,KAAK,MAAM,KAAK;AAAA,IAAgB;AAAC,UAAMC,KAAE,KAAK,SAAS,IAAK,CAAC,EAAC,OAAMD,IAAE,QAAOC,GAAC,MAAI,KAAK,gBAAgBD,IAAEC,EAAC,CAAE;AAAE,UAAM,QAAQ,IAAIA,EAAC,GAAE,KAAK,eAAe,QAAS,CAAAD,OAAG;AAAC,MAAAA,GAAE,SAAS,KAAK,QAAS,CAAAA,OAAG,KAAK,WAAWA,GAAE,OAAO,CAAE;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQA,IAAEC,IAAE;AAAC,IAAAA,OAAI,KAAK,gBAAcA,GAAE,eAAc,MAAM,KAAK,OAAO,IAAE;AAAA,EAAC;AAAA,EAAC,MAAM,gBAAgBD,IAAEE,IAAE;AAAC,UAAMC,KAAE,EAAEH,GAAE,SAAS,IAAEA,GAAE,YAAU,CAAC,GAAEK,MAAE,KAAK,WAAW,WAAUC,KAAED,IAAE,OAAQ,CAAAL,OAAG,CAACG,GAAE,SAASH,EAAC,CAAE;AAAE,QAAG,CAACM,GAAE,OAAO;AAAO,UAAME,KAAER,GAAE,MAAM,GAAES,KAAE,KAAK;AAAQ,IAAAD,GAAE,iBAAe,OAAGA,GAAE,iBAAe,OAAGA,GAAE,YAAUF,IAAEN,GAAE,YAAUK;AAAE,UAAMK,KAAE,MAAM,KAAK,OAAO,KAAK,EAAC,OAAMF,IAAE,OAAM,GAAE,QAAOC,GAAC,CAAC;AAAE,MAAE,EAAC,QAAOA,GAAC,CAAC,GAAEP,GAAE,eAAeQ,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeV,IAAE;AAAC,QAAG,CAAC,KAAK,kBAAiB;AAAC,YAAMA,KAAE,EAAE,KAAK,eAAc,qCAAqC;AAAE,WAAK,mBAAiB,KAAK,UAAUA,EAAC;AAAA,IAAC;AAAC,UAAMC,KAAE,KAAK,OAAO,OAAOD,EAAC,GAAEG,KAAE,KAAK,eAAe,IAAIH,GAAE,IAAI,EAAE,GAAEI,KAAEH,GAAE,SAAO;AAAE,aAAQC,KAAE,GAAEA,KAAEE,IAAEF,MAAI;AAAC,YAAME,KAAEG,GAAEN,GAAEC,EAAC,GAAEF,EAAC,GAAEM,KAAE,EAAC,MAAK,UAAS,IAAGN,GAAE,IAAG,aAAYI,IAAE,KAAI,OAAG,QAAOJ,GAAE,MAAM,EAAC;AAAE,MAAAG,GAAE,IAAI,EAAC,OAAM,MAAK,SAAQG,GAAC,CAAC,GAAE,KAAK,kBAAgB,MAAM,EAAE,CAAC,GAAE,KAAK,WAAWA,EAAC;AAAA,IAAC;AAAC,UAAMA,KAAEC,GAAEH,MAAG,IAAEH,GAAEG,EAAC,IAAE,MAAKJ,EAAC,GAAEQ,KAAE,KAAK,aAAYC,KAAE,EAAC,MAAK,UAAS,IAAGT,GAAE,IAAG,aAAYM,IAAE,KAAIE,IAAE,QAAOR,GAAE,MAAM,EAAC;AAAE,IAAAG,GAAE,IAAI,EAAC,OAAM,MAAK,SAAQM,GAAC,CAAC,GAAE,KAAK,WAAWA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,UAAUR,IAAE;AAAC,QAAG;AAAC,YAAM,KAAK,gBAAgB;AAAE,YAAMD,KAAE,KAAK,OAAO,QAAQ,UAAU,KAAK,eAAe,GAAEE,KAAE,oBAAI;AAAI,MAAAF,GAAE,MAAM;AAAE,YAAMG,KAAE,KAAK,KAAKF,KAAE,KAAK,QAAQ,GAAEG,KAAE,MAAM,KAAK,EAAC,QAAOD,GAAC,GAAG,CAACH,IAAEC,OAAIA,EAAE,EAAE,KAAM,CAACD,IAAEC,OAAI,KAAK,QAAQ,OAAO,IAAE,KAAK,QAAQ,OAAO,CAAE,EAAE,IAAK,CAAAA,OAAG,KAAK,cAAcA,IAAED,IAAEE,EAAC,CAAE;AAAE,YAAM,QAAQ,IAAIE,EAAC,GAAE,KAAK,OAAO,cAAcJ,IAAE,KAAK,OAAO,OAAO,GAAE,KAAK,OAAO,iBAAiBE,EAAC;AAAA,IAAC,SAAOA,IAAE;AAAC,QAAE,UAAU,6DAA6D,EAAE,MAAM,2BAA0B,2DAA0DA,EAAC;AAAA,IAAC;AAAC,SAAK,SAAS,GAAE,KAAK,WAAS;AAAA,EAAE;AAAA,EAAC,MAAM,cAAcF,IAAEE,IAAEC,IAAE;AAAC,UAAME,MAAE,KAAK,UAASC,KAAE,EAAC,OAAMN,KAAEK,KAAE,KAAIA,KAAE,WAAU,KAAE;AAAE,MAAE,KAAK,oBAAoB,MAAIC,GAAE,uBAAqB,KAAK;AAAsB,UAAME,KAAE,KAAK,YAAYF,EAAC,GAAEG,KAAE,KAAK,SAAQC,KAAE,MAAM,KAAK,OAAO,KAAK,EAAC,OAAMF,IAAE,OAAMR,IAAE,QAAOS,GAAC,CAAC;AAAE,MAAE,EAAC,QAAOA,GAAC,CAAC,GAAE,KAAK,SAAS,KAAK,EAAC,OAAMD,IAAE,QAAOE,GAAC,CAAC,GAAE,KAAK,OAAO,OAAOA,EAAC,GAAEP,GAAE,IAAIO,GAAE,QAAQ;AAAE,UAAMX,KAAEW,GAAE,UAAU;AAAE,WAAKX,GAAE,KAAK,IAAG,CAAAG,GAAE,IAAIH,GAAE,aAAa,CAAC;AAAE,SAAK,MAAMW,EAAC;AAAA,EAAC;AAAA,EAAC,MAAMV,IAAE;AAAC,QAAG,CAAC,KAAK,eAAe,KAAK;AAAO,QAAIE,KAAE;AAAK,UAAME,KAAE,oBAAI,OAAIC,MAAE,oBAAI,OAAIC,KAAE,oBAAI;AAAI,SAAK,eAAe,QAAS,CAAAN,OAAG;AAAC,YAAMC,KAAED,GAAE;AAAK,MAAAI,GAAE,IAAIH,GAAE,IAAI,IAAG,IAAI,GAAEC,KAAED,GAAE,cAAaI,IAAE,IAAIJ,GAAE,KAAK;AAAE,YAAK,EAAC,KAAIE,IAAE,KAAIK,GAAC,IAAEP,GAAE,KAAIQ,KAAE,GAAGR,GAAE,KAAK,IAAIE,EAAC,IAAIK,EAAC,IAAGE,KAAEJ,GAAE,IAAIG,EAAC,KAAG,CAAC;AAAE,MAAAC,GAAE,KAAKV,EAAC,GAAEM,GAAE,IAAIG,IAAEC,EAAC;AAAA,IAAC,CAAE;AAAE,eAAUT,MAAKI,KAAE;AAAC,YAAMA,MAAEH,GAAE,aAAaD,EAAC,GAAEO,KAAER,GAAE,UAAU;AAAE,aAAKQ,GAAE,KAAK,KAAG;AAAC,cAAMR,KAAED,GAAES,IAAEH,KAAEJ,EAAC,GAAEC,MAAEM,GAAE,SAAS;AAAE,YAAGF,GAAE,IAAIN,EAAC,EAAE,YAAUC,MAAKK,GAAE,IAAIN,EAAC,GAAE;AAAC,gBAAMA,KAAEC,GAAE,KAAK;AAAG,cAAII,MAAED,GAAE,IAAIJ,EAAC;AAAE,YAAEK,GAAC,MAAIA,MAAE,CAAC,GAAED,GAAE,IAAIJ,IAAEK,GAAC,IAAGA,IAAE,KAAKH,GAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAE,GAAE,QAAS,CAACF,KAAEC,OAAI;AAAC,UAAG,EAAED,GAAC,GAAE;AAAC,cAAMD,KAAE,KAAK,eAAe,IAAIE,EAAC,GAAEC,KAAE,EAAC,MAAK,UAAS,IAAGD,IAAE,aAAYI,GAAEJ,GAAE,KAAKH,IAAEE,GAAC,GAAED,GAAE,IAAI,GAAE,KAAI,OAAG,QAAOD,GAAE,MAAM,EAAC;AAAE,QAAAC,GAAE,IAAI,EAAC,OAAM,MAAK,SAAQG,GAAC,CAAC,GAAE,KAAK,WAAWA,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,WAAU;AAAC,SAAK,eAAe,QAAS,CAAAJ,OAAG;AAAC,YAAMC,KAAE,EAAC,MAAK,UAAS,IAAGD,GAAE,KAAK,IAAG,aAAY,MAAK,KAAI,MAAG,QAAOA,GAAE,MAAM,EAAC;AAAE,MAAAA,GAAE,IAAI,EAAC,OAAM,MAAK,SAAQC,GAAC,CAAC,GAAE,KAAK,WAAWA,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,cAAY;AAAA,EAAE;AAAC;;;ACAjwJ,IAAMe,KAAE;AAAR,IAA6BC,KAAE;AAA/B,IAAoDC,KAAE;AAAI,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAEC,IAAEC,KAAE,KAAI;AAAC,SAAK,yBAAuB,oBAAI,OAAI,KAAK,aAAW,GAAE,KAAK,aAAW,YAAY,IAAI,GAAE,KAAK,gBAAc,oBAAI,OAAI,KAAK,WAAS,CAAC,GAAE,KAAK,UAAQ,GAAE,KAAK,YAAUF,IAAE,KAAK,gBAAcC,IAAE,KAAK,QAAMH,IAAE,KAAK,gBAAcC,IAAE,KAAK,gBAAcG,IAAE,KAAK,mBAAiB,KAAK,kBAAgBR;AAAA,EAAC;AAAA,EAAC,WAAWI,IAAE;AAAC,SAAK,SAAS,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,UAAMC,KAAE,KAAK,uBAAuB,IAAID,EAAC;AAAE,QAAGC,GAAE,YAAUC,MAAKD,GAAE,QAAQ,MAAK,SAAS,KAAKC,EAAC;AAAA,EAAC;AAAA,EAAC,IAAIE,IAAE;AAJ7sB;AAI8sB,QAAG,KAAK,kBAAiB;AAAC,YAAMJ,KAAE,KAAK,QAAQ;AAAE,MAAAI,GAAE,WAAW,KAAK,aAAa,IAAEJ,IAAEI,GAAE,WAASJ;AAAA,IAAC,MAAM,CAAAI,GAAE,WAASA,GAAE,WAAW,KAAK,aAAa;AAAE,UAAMC,KAAED,GAAE;AAAS,QAAG,KAAK,cAAc,IAAIC,IAAED,EAAC,GAAE,KAAK,UAAQ,KAAK,IAAI,KAAK,SAAQA,GAAE,WAAW,KAAK,UAAU,cAAc,CAAC,GAAE,CAAC,KAAK,UAAU,aAAa,QAAO,EAAE,KAAK,wBAAwB,MAAI,KAAK,2BAAyB,IAAIF,GAAE,GAAG,IAAG,KAAK,KAAK,yBAAyB,QAAQG,EAAC;AAAE,UAAMT,KAAEQ,GAAE,WAAW,KAAK,UAAU,YAAY;AAAE,QAAG,CAAC,KAAK,uBAAuB,IAAIR,EAAC,GAAE;AAAC,YAAMM,KAAE,EAAE,KAAK,aAAa,KAAG,QAAM,KAAK,cAAc,kBAAgB,KAAK,cAAc,kBAAgBJ,IAAEM,KAAEP,GAAEK,IAAE,GAAEJ,EAAC;AAAE,WAAK,uBAAuB,IAAIF,IAAE,IAAIM,GAAEE,EAAC,CAAC;AAAA,IAAC;AAAC,UAAMP,OAAE,UAAK,uBAAuB,IAAID,EAAC,MAAjC,mBAAoC,QAAQS;AAAG,MAAER,GAAC,MAAI,KAAK,cAAc,IAAIA,GAAC,IAAE,KAAK,cAAc,OAAOA,GAAC,IAAE,KAAK,SAAS,KAAKA,GAAC;AAAA,EAAE;AAAA,EAAC,kBAAiB;AAAC,UAAMG,KAAE,KAAK,UAAU,GAAEC,KAAE,KAAK,aAAa,GAAEC,KAAE,YAAY,IAAI;AAAE,IAAAA,KAAE,KAAK,cAAY,KAAK,kBAAgB,KAAK,OAAOA,EAAC,GAAE,KAAK,aAAWA;AAAG,UAAMG,KAAE,CAAC;AAAE,QAAG,EAAEJ,EAAC,EAAE,YAAUG,MAAKH,IAAE;AAAC,YAAMD,KAAE,KAAK,MAAM,WAAWI,EAAC;AAAE,QAAEJ,EAAC,KAAGK,GAAE,KAAKL,EAAC;AAAA,IAAC;AAAC,UAAMJ,KAAE,CAAC;AAAE,QAAG,EAAEI,EAAC,GAAE;AAAC,YAAMG,KAAE,IAAI,IAAI,EAAEF,IAAE,CAAC,CAAC,CAAC;AAAE,iBAAUA,MAAKD,GAAE,CAAAG,GAAE,IAAIF,GAAE,QAAQ,MAAIA,GAAE,WAAWJ,EAAC,IAAEK,IAAE,KAAK,MAAM,IAAID,EAAC,GAAEL,GAAE,KAAKK,EAAC;AAAA,IAAE;AAAC,KAACL,GAAE,WAAQS,MAAA,gBAAAA,GAAG,YAAS,KAAK,MAAM,OAAOT,IAAES,EAAC;AAAA,EAAC;AAAA,EAAC,YAAW;AAAC,QAAG,CAAC,KAAK,cAAc,KAAK,QAAO;AAAK,UAAML,KAAE,IAAI,MAAM,KAAK,cAAc,IAAI;AAAE,QAAIC,KAAE;AAAE,WAAO,KAAK,cAAc,QAAS,CAAAC,OAAGF,GAAEC,IAAG,IAAEC,EAAE,GAAE,KAAK,cAAc,MAAM,GAAEF;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,UAAMA,KAAE,KAAK;AAAS,WAAO,KAAK,SAAS,UAAQ,KAAK,WAAS,CAAC,GAAEA,MAAG;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,UAAMA,KAAE,KAAK;AAAW,WAAO,KAAK,cAAY,KAAK,aAAW,KAAG,aAAW,GAAEA;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,UAAMC,KAAE,KAAK;AAAc,MAAEA,EAAC,MAAI,KAAK,yBAAyBA,EAAC,GAAE,KAAK,YAAYA,EAAC,GAAE,KAAK,oBAAoBD,IAAEC,EAAC,GAAE,KAAK,aAAa;AAAA,EAAE;AAAA,EAAC,yBAAyBD,IAAE;AAAC,QAAG,CAACA,GAAE,aAAa;AAAO,QAAIC,KAAE,KAAK,MAAM;AAAK,QAAGA,KAAED,GAAE,cAAa;AAAC,UAAG,KAAK,UAAU;AAAa,mBAAUE,MAAK,KAAK,uBAAuB,OAAO,EAAE,KAAGD,KAAED,GAAE,gBAAcE,GAAE,MAAK;AAAC,gBAAMF,KAAE,EAAEE,GAAE,QAAQ,CAAC;AAAE,eAAK,SAAS,KAAKF,EAAC,GAAEC;AAAA,QAAG;AAAA;AAAC,UAAG,EAAE,KAAK,wBAAwB,GAAE;AAAC,YAAIC,KAAED,KAAED,GAAE;AAAa,eAAKE,OAAK,KAAG;AAAC,gBAAMF,KAAE,KAAK,yBAAyB,QAAQ;AAAE,YAAEA,EAAC,KAAG,KAAK,SAAS,KAAKA,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAJx1F;AAIy1F,UAAMC,MAAE,UAAK,cAAL,mBAAgB;AAAe,QAAG,CAACD,GAAE,OAAK,CAACC,GAAE;AAAO,UAAMC,KAAE,KAAGF,GAAE,MAAI,KAAIG,KAAE,KAAK,UAAQD;AAAE,SAAK,MAAM,QAAS,CAAAF,OAAG;AAAC,MAAAA,GAAE,WAAWC,EAAC,IAAEE,MAAG,KAAK,SAAS,KAAKH,GAAE,QAAQ;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAEC,IAAE;AAAC,QAAG,CAACA,GAAE,YAAY;AAAO,UAAMC,KAAEF,KAAE,KAAGC,GAAE,cAAY;AAAI,SAAK,MAAM,QAAS,CAAAD,OAAG;AAAC,MAAAA,GAAE,WAAWH,EAAC,IAAEK,MAAG,KAAK,SAAS,KAAKF,GAAE,QAAQ;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,SAAK,uBAAuB,QAAS,CAACA,IAAEC,OAAI;AAAC,YAAID,GAAE,QAAM,KAAK,uBAAuB,OAAOC,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAC;;;ACAluE,IAAMK,KAAE;AAAK,SAASC,GAAEC,IAAEC,IAAE;AAAC,QAAMC,KAAEF,GAAE,UAAU;AAAE,MAAG,EAAEA,GAAE,QAAQ,GAAE;AAAC,UAAMG,KAAE,EAAEF,IAAED,GAAE,SAAS,OAAO,CAAC,CAAC,GAAEI,KAAE,EAAEH,IAAED,GAAE,SAAS,OAAO,CAAC,CAAC;AAAE,IAAAE,GAAE,WAAS,IAAIC,GAAE,CAAC,GAAE,CAACA,IAAEC,EAAC,CAAC;AAAA,EAAC;AAAC,SAAOF;AAAC;AAAC,SAASG,GAAEL,IAAE;AAAC,SAAM,wBAAsBA,KAAED,KAAE,CAACI,IAAEF,OAAI;AAAC,UAAMC,KAAEC,GAAE,UAAU,GAAEC,KAAE,IAAID,MAAEG,KAAE,OAAGC,MAAE,OAAGC,KAAE,GAAEJ,IAAED,GAAE,UAASG,IAAEC,KAAEP,IAAEC,IAAE,OAAG,KAAE;AAAE,WAAOC,GAAE,WAASM,IAAEN;AAAA,EAAC;AAAC;AAAC,SAASO,GAAET,IAAE;AAAC,SAAM,wBAAsBA,KAAE,CAAAA,OAAG,EAAEA,GAAE,QAAQ,IAAE,EAAC,MAAKA,GAAE,SAAS,OAAO,CAAC,GAAE,MAAKA,GAAE,SAAS,OAAO,CAAC,GAAE,MAAKA,GAAE,SAAS,OAAO,CAAC,GAAE,MAAKA,GAAE,SAAS,OAAO,CAAC,EAAC,IAAE,EAAC,MAAK,IAAE,GAAE,MAAK,IAAE,GAAE,MAAK,KAAG,GAAE,MAAK,KAAG,EAAC,IAAE,CAAAA,OAAG;AAAC,QAAIC,KAAE,IAAE,GAAEC,KAAE,IAAE,GAAEE,KAAE,KAAG,GAAEE,KAAE,KAAG;AAAE,WAAO,EAAEN,GAAE,QAAQ,KAAGA,GAAE,SAAS,cAAe,CAACA,IAAEG,OAAI;AAAC,MAAAF,KAAE,KAAK,IAAIA,IAAED,EAAC,GAAEE,KAAE,KAAK,IAAIA,IAAEC,EAAC,GAAEC,KAAE,KAAK,IAAIA,IAAEJ,EAAC,GAAEM,KAAE,KAAK,IAAIA,IAAEH,EAAC;AAAA,IAAC,CAAE,GAAE,EAAC,MAAKF,IAAE,MAAKC,IAAE,MAAKE,IAAE,MAAKE,GAAC;AAAA,EAAC;AAAC;AAAC,SAASI,GAAEV,IAAEG,IAAE;AAAC,QAAMF,KAAEG,GAAE,GAAEK,GAAEN,EAAC,CAAC;AAAE,SAAOF,GAAE,KAAKD,EAAC,GAAEC;AAAC;AAAC,SAASU,GAAEX,IAAEG,IAAE;AAAC,SAAOH,GAAE,OAAO,EAAC,MAAKG,GAAE,OAAO,CAAC,GAAE,MAAKA,GAAE,OAAO,CAAC,GAAE,MAAKA,GAAE,OAAO,CAAC,GAAE,MAAKA,GAAE,OAAO,CAAC,EAAC,CAAC;AAAC;AAAC,IAAMS,KAAN,MAAO;AAAA,EAAC,YAAYZ,IAAEG,IAAE;AAAC,SAAK,WAASH,IAAE,KAAK,gBAAcG,IAAE,KAAK,qBAAmB,oBAAI,OAAI,KAAK,SAAO;AAAA,EAAI;AAAA,EAAC,IAAI,YAAW;AAAC,UAAMH,KAAE,CAAC;AAAE,WAAO,KAAK,mBAAmB,QAAS,CAAAG,OAAGH,GAAE,KAAKG,EAAC,CAAE,GAAEH;AAAA,EAAC;AAAA,EAAC,IAAIA,IAAE;AAAC,SAAK,mBAAmB,IAAIA,GAAE,UAASA,EAAC,GAAE,KAAK,SAAO;AAAA,EAAI;AAAA,EAAC,IAAIA,IAAE;AAAC,WAAO,KAAK,mBAAmB,IAAIA,EAAC,IAAE,KAAK,mBAAmB,IAAIA,EAAC,IAAE;AAAA,EAAI;AAAA,EAAC,QAAQA,IAAE;AAAC,SAAK,mBAAmB,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,WAAO,KAAK,WAAS,KAAK,SAAOU,GAAE,KAAK,WAAU,KAAK,aAAa,IAAGC,GAAE,KAAK,QAAOX,EAAC;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,SAAO,MAAK,KAAK,mBAAmB,MAAM;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,UAAMG,KAAE,KAAK,mBAAmB,IAAIH,EAAC;AAAE,WAAOG,MAAG,KAAK,mBAAmB,OAAOH,EAAC,GAAE,KAAK,SAAO,MAAKG,MAAG;AAAA,EAAI;AAAA,EAAC,OAAOH,IAAEG,IAAE;AAAC,SAAK,SAASH,IAAEG,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK,mBAAmB;AAAA,EAAI;AAAC;AAAC,IAAIU,KAAE,cAAcJ,GAAC;AAAA,EAAC,YAAYT,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,OAAK,UAAS,KAAK,oBAAkB,GAAE,KAAK,SAAO,GAAE,KAAK,cAAY,EAAC,WAAU,GAAE,QAAO,EAAC,GAAE,KAAK,YAAU,OAAG,KAAK,YAAU;AAAG,UAAK,EAAC,OAAMG,GAAC,IAAEH,IAAE,EAAC,cAAaC,IAAE,eAAcC,IAAE,UAASE,IAAE,cAAaE,IAAE,QAAOQ,IAAE,kBAAiBP,KAAE,eAAcC,IAAE,yBAAwBO,IAAE,yBAAwBC,IAAE,gBAAeC,IAAE,kBAAiBC,IAAE,mBAAkBnB,GAAC,IAAEC,GAAE,aAAYS,MAAE,IAAIG,GAAE,KAAK,UAAU,KAAK,IAAI,GAAEX,EAAC,GAAES,KAAE,IAAII,GAAEL,KAAEP,IAAEE,IAAEE,EAAC,GAAEK,KAAEL,GAAEQ,IAAEP,KAAEJ,IAAEF,IAAEO,IAAEO,IAAEC,IAAEE,MAAG,CAAC,CAAC;AAAE,SAAK,SAAOT,KAAE,KAAK,WAASC,IAAE,KAAK,cAAYC,IAAE,KAAK,YAAUN,GAAEJ,EAAC,GAAE,KAAK,qBAAmB,IAAI,IAAIF,EAAC,GAAE,KAAK,WAAS,CAAC,KAAK,YAAY,GAAG,iBAAiB,CAAAC,OAAG,KAAK,WAAWA,EAAC,CAAE,GAAE,KAAK,YAAY,GAAG,oBAAoB,CAAAA,OAAG,KAAK,oBAAoBA,EAAC,CAAE,CAAC,GAAE,KAAK,sBAAoB,MAAI;AAAC,UAAIG,KAAE,YAAY,IAAI;AAAE,WAAK,oBAAkB,YAAa,MAAI;AAAC,cAAMF,MAAE,YAAY,IAAI,GAAEC,KAAED,MAAEE;AAAE,YAAGD,KAAEJ,IAAE;AAAC,UAAAK,KAAEF;AAAE,gBAAMD,KAAE,KAAK,MAAM,KAAK,YAAY,UAAQE,KAAE,IAAI,GAAEE,KAAE,KAAK,MAAM,KAAK,YAAY,aAAWF,KAAE,IAAI;AAAE,eAAK,YAAY,SAAO,GAAE,KAAK,YAAY,YAAU,GAAE,KAAK,OAAO,KAAK,cAAa,EAAC,QAAOF,IAAE,WAAUI,GAAC,CAAC;AAAA,QAAC;AAAC,QAAAJ,GAAE,iBAAiB,KAAG,CAAC,KAAK,aAAW,KAAK,SAAS,gBAAgB;AAAA,MAAC,GAAGiB,EAAC;AAAA,IAAC,GAAE,KAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,UAAM,QAAQ,GAAE,KAAK,qBAAqB,GAAE,KAAK,SAAS,QAAS,CAAAjB,OAAGA,GAAE,OAAO,CAAE,GAAE,KAAK,YAAY,QAAQ;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAJx9H;AAIy9H,WAAO,KAAK,YAAU,YAAS,UAAK,gBAAL,mBAAkB;AAAA,EAAgB;AAAA,EAAC,IAAI,cAAa;AAJ5iI;AAI6iI,YAAO,UAAK,gBAAL,mBAAkB;AAAA,EAAW;AAAA,EAAC,uBAAuBA,IAAE;AAAC,SAAK,YAAY,uBAAuBA,EAAC;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,SAAK,cAAY,KAAK,YAAU,MAAG,KAAK,qBAAqB;AAAA,EAAE;AAAA,EAAC,eAAc;AAAC,SAAK,cAAY,KAAK,YAAU,OAAG,KAAK,oBAAoB;AAAA,EAAE;AAAA,EAAC,oBAAoBA,IAAE;AAAC,SAAK,YAAY,oBAAoBA,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,SAAK,YAAY,oBAAoBA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAEG,IAAE;AAAC,IAAAA,KAAE,KAAK,mBAAmB,IAAIH,EAAC,IAAE,KAAK,mBAAmB,OAAOA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,WAAU;AAAC,WAAM;AAAA,EAAE;AAAA,EAAC,UAAUA,IAAEG,IAAE;AAAC,UAAM,UAAUH,IAAEG,EAAC;AAAE,UAAMF,KAAE,KAAK,eAAe,IAAID,GAAE,EAAE;AAAE,SAAK,SAAOA,GAAE;AAAM,UAAME,KAAE,KAAK,iBAAiBF,EAAC;AAAE,SAAK,WAAW,EAAC,MAAK,UAAS,IAAGA,GAAE,IAAI,IAAG,aAAYE,IAAE,KAAI,KAAE,CAAC,GAAED,GAAE,UAAQ;AAAA,EAAE;AAAA,EAAC,YAAYD,IAAE;AAAC,UAAM,YAAYA,EAAC;AAAA,EAAC;AAAA,EAAC,CAAC,QAAQA,IAAE;AAAC,UAAMG,KAAE,KAAK,eAAe,IAAIH,EAAC,GAAE,EAAC,MAAKC,GAAC,IAAEE;AAAE,UAAM,KAAK,iBAAiBF,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBD,IAAE;AAAC,UAAM,IAAI,MAAM,wCAAwC;AAAA,EAAC;AAAA,EAAC,MAAM,SAAQ;AAAC,SAAK,eAAe,QAAS,CAAAA,OAAG;AAAC,YAAK,EAAC,MAAKG,GAAC,IAAEH,IAAEC,KAAE,EAAC,MAAK,UAAS,IAAGE,GAAE,IAAG,aAAY,KAAK,iBAAiBA,EAAC,GAAE,KAAI,KAAE;AAAE,WAAK,WAAWF,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,iBAAiBD,IAAE;AAAC,UAAMG,KAAE,KAAK,OAAO,OAAOH,EAAC,EAAE,IAAK,CAAAG,OAAG,KAAK,UAAUA,IAAEH,GAAE,SAAS,CAAE;AAAE,WAAOQ,GAAE,sBAAsBL,IAAE,KAAK,cAAaH,GAAE,SAAS;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,QAAG,KAAK,mBAAmB,IAAI,kBAAkB,KAAG,KAAK,OAAO,KAAK,oBAAmBA,EAAC,GAAE,UAASA,GAAE,SAAOA,GAAE,MAAK;AAAA,MAAC,KAAI;AAAS,YAAGA,GAAE,UAAU,YAAUG,MAAKH,GAAE,UAAU,MAAK,SAAS,WAAWG,EAAC;AAAE,YAAGH,GAAE,SAAS,YAAUG,MAAKH,GAAE,SAAS,MAAK,SAAS,gBAAgBG,EAAC;AAAE;AAAA,MAAM,KAAI;AAAQ,aAAK,OAAO,QAAS,CAAAH,OAAG,KAAK,SAAS,WAAWA,GAAE,QAAQ,CAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,SAAK,YAAY;AAAY,QAAG;AAAC,WAAK,mBAAmB,IAAI,eAAe,KAAG,KAAK,OAAO,KAAK,iBAAgBA,EAAC;AAAE,YAAMG,KAAE,GAAEH,IAAE,KAAK,aAAa,cAAa,OAAG,OAAG,KAAK,aAAa,aAAa;AAAE,WAAK,SAAS,IAAIG,EAAC;AAAA,IAAC,SAAOA,IAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,kBAAc,KAAK,iBAAiB,GAAE,KAAK,oBAAkB;AAAA,EAAC;AAAA,EAAC,MAAM,UAAUH,IAAEC,IAAE;AAAC,SAAK,YAAU;AAAG,QAAG;AAAC,QAAED,EAAC,MAAI,KAAK,YAAY,UAAQA,GAAE,SAAQ,KAAK,eAAe,QAAS,CAACA,IAAEG,OAAI;AAAC,QAAAH,GAAE,WAASA,GAAE,KAAK,UAAQ,KAAK,UAAQ,KAAK,WAAW,EAAC,MAAK,UAAS,IAAGG,IAAE,aAAY,MAAK,OAAM,MAAG,KAAI,MAAE,CAAC;AAAA,MAAC,CAAE;AAAE,YAAMF,MAAE,CAAC;AAAE,WAAK,eAAe,QAAS,CAACD,IAAEG,OAAI;AAAC,YAAG,CAACH,GAAE,WAASA,GAAE,KAAK,UAAQ,KAAK,OAAO;AAAO,cAAME,KAAEF,GAAE,MAAKI,KAAE,EAAC,MAAK,UAAS,IAAGD,IAAE,aAAY,KAAK,iBAAiBD,EAAC,GAAE,QAAO,CAAC,GAAE,KAAI,OAAG,QAAOC,GAAE,MAAM,EAAC;AAAE,QAAAH,GAAE,SAAS,OAAO,QAAQI,EAAC,GAAEH,IAAE,KAAK,KAAK,WAAWG,EAAC,CAAC;AAAA,MAAC,CAAE,GAAE,MAAM,QAAQ,IAAIH,GAAC,GAAE,KAAK,eAAe,QAAS,CAACD,IAAEG,OAAI;AAAC,QAAAH,GAAE,WAASA,GAAE,KAAK,UAAQ,KAAK,UAAQ,KAAK,WAAW,EAAC,MAAK,UAAS,IAAGG,IAAE,aAAY,MAAK,KAAI,KAAE,CAAC;AAAA,MAAC,CAAE;AAAA,IAAC,QAAM;AAAA,IAAC;AAAC,SAAK,YAAU;AAAA,EAAE;AAAC;AAAEH,GAAE,CAAC,EAAE,CAAC,GAAEa,GAAE,WAAU,aAAY,MAAM,GAAEb,GAAE,CAAC,EAAE,CAAC,GAAEa,GAAE,WAAU,oBAAmB,IAAI,GAAEb,GAAE,CAAC,EAAE,CAAC,GAAEa,GAAE,WAAU,eAAc,IAAI,GAAEA,KAAEb,GAAE,CAAC,EAAE,uCAAuC,CAAC,GAAEa,EAAC;;;ACA14M,SAASM,GAAEC,IAAEC,IAAEF,KAAEG,IAAEC,IAAEC,IAAE;AAAC,QAAMC,MAAEC,GAAEN,IAAEC,IAAEF,KAAEG,IAAEC,IAAEC,EAAC;AAAE,UAAOC,IAAE,MAAK;AAAA,IAAC,KAAI;AAAU,cAAOA,IAAE,QAAO;AAAA,QAAC,KAAI;AAAA,QAAS,KAAI;AAAQ,iBAAO,IAAIE,GAAEF,GAAC;AAAA,QAAE,KAAI;AAAW,iBAAO,IAAIC,GAAED,GAAC;AAAA,QAAE;AAAQ,iBAAO,IAAIN,GAAEM,GAAC;AAAA,MAAC;AAAA,IAAC,KAAI;AAAS,aAAO,IAAIG,GAAEH,GAAC;AAAA,EAAC;AAAC;AAAC,SAASC,GAAEG,IAAEC,IAAEC,IAAEJ,IAAER,KAAEO,IAAE;AAAC,UAAOG,GAAE,MAAK;AAAA,IAAC,KAAI;AAAW,aAAM,EAAC,MAAK,WAAU,QAAO,YAAW,cAAa,EAAEA,GAAE,cAAa,CAAC,GAAE,aAAYA,IAAE,WAAUF,IAAE,OAAMG,IAAE,cAAaC,IAAE,kBAAiBZ,KAAE,OAAMO,GAAC;AAAA,IAAE,KAAI;AAAS,aAAM,EAAC,MAAK,UAAS,aAAYG,IAAE,WAAUF,IAAE,OAAMG,IAAE,kBAAiBX,IAAC;AAAA,IAAE,KAAI;AAAA,IAAS,KAAI;AAAY,aAAM,EAAC,MAAK,WAAU,aAAYU,IAAE,WAAUF,IAAE,OAAMG,IAAE,QAAOR,GAAEO,GAAE,MAAM,GAAE,cAAaE,IAAE,kBAAiBZ,IAAC;AAAA,EAAC;AAAC,WAASG,GAAEF,IAAE;AAAC,WAAO,MAAM,QAAQA,EAAC,IAAE,UAAQ,UAASA,MAAGY,GAAEZ,GAAE,IAAI,IAAE,WAAS;AAAA,EAAS;AAAC;;;ACArmC,IAA2Ba,KAAE,IAAI,aAAa,CAAC;AAA/C,IAAiDC,KAAE,IAAI,aAAa,CAAC;AAA+xB,SAAS,EAAEC,IAAEC,IAAE;AAAC,MAAIC,KAAE,KAAIC,KAAE,IAAGC,KAAE,MAAKC,KAAE;AAAI,WAAQC,MAAE,GAAEA,MAAEL,IAAEK,OAAI;AAAC,UAAML,KAAE,KAAK,MAAMK,MAAE,KAAG,CAAC,GAAEC,KAAE,KAAK,OAAOD,MAAE,KAAG,CAAC,GAAEE,KAAE,IAAEF,MAAE,GAAEG,KAAE,MAAI,IAAER,KAAE,IAAEM,KAAGG,KAAE,MAAI,IAAET,KAAE,IAAEM,KAAGI,KAAE,IAAEH,KAAE,KAAG,IAAEA,KAAGI,MAAE,IAAEJ,KAAE,KAAG,IAAEA,KAAGK,KAAE,IAAEL,KAAE,KAAG,IAAEA,OAAIE,IAAEI,MAAG,IAAEN,KAAE,KAAG,IAAEA,OAAIC,KAAET,GAAE,aAAWS,IAAEM,MAAGF,KAAEb,GAAE,aAAWU;AAAE,aAAQV,KAAEW,KAAE,GAAEX,MAAG,GAAEA,MAAI;AAAC,YAAMC,MAAGG,KAAEC,MAAG,GAAEH,KAAEY,KAAE,KAAGd,KAAE,IAAE;AAAE,MAAAI,MAAG,IAAEF,MAAGE,KAAEF,KAAED,IAAEI,MAAG,IAAEH,MAAGD,KAAEC,KAAEG;AAAA,IAAC;AAAC,aAAQL,KAAEY,MAAE,GAAEZ,MAAG,GAAEA,MAAI;AAAC,YAAMC,MAAGC,KAAEC,MAAG,GAAEC,KAAEW,KAAE,KAAGf,KAAE,IAAE;AAAE,MAAAE,MAAG,IAAEE,MAAGF,KAAEE,KAAEH,IAAEE,MAAG,IAAEC,MAAGH,KAAEG,KAAED;AAAA,IAAC;AAAA,EAAC;AAAC,SAAM,CAACC,IAAEF,IAAEG,IAAEF,EAAC;AAAC;AAAC,SAAS,EAAEH,IAAEC,IAAEC,IAAEC,IAAE;AAAC,EAAAA,KAAE,MAAIA,MAAG;AAAG,MAAIC,KAAE,GAAEC,KAAE,GAAEC,MAAE,KAAIC,KAAE,IAAGC,KAAE,MAAKC,KAAE;AAAI,WAAQC,KAAE,GAAEA,KAAEP,KAAE,GAAEO,MAAI;AAAC,aAAQV,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAMC,MAAGO,KAAEC,MAAG,GAAEN,KAAED,KAAED,KAAE,IAAE;AAAE,MAAAG,MAAGD,MAAG,MAAIH,KAAE,IAAEU,KAAGF,MAAG,IAAEL,MAAGK,KAAEL,KAAEF,IAAEQ,MAAG,IAAEN,MAAGF,KAAEE,KAAEM;AAAA,IAAC;AAAC,aAAQT,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAME,MAAGI,MAAEC,MAAG,GAAEJ,KAAEF,KAAEC,KAAE,IAAE;AAAE,MAAAG,MAAGF,MAAG,MAAIH,KAAE,IAAEU,KAAGJ,OAAG,IAAEH,MAAGG,MAAEH,KAAED,IAAEK,MAAG,IAAEJ,MAAGD,KAAEC,KAAEI;AAAA,IAAC;AAAA,EAAC;AAAC,EAAAP,GAAE,WAASI,IAAEJ,GAAE,WAASK;AAAC;AAAC,SAASW,GAAEhB,IAAEC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,EAAAA,KAAE,MAAIA,MAAG;AAAG,MAAIC,KAAE,GAAEC,MAAE,GAAEC,KAAE,KAAIC,KAAE,IAAGC,KAAE,MAAKC,KAAE;AAAI,WAAQC,KAAE,GAAEA,KAAEP,KAAE,GAAEO,MAAI;AAAC,aAAQX,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAMC,MAAGQ,KAAEC,MAAG,GAAER,KAAEC,KAAEF,KAAE,IAAE;AAAE,MAAAI,MAAGH,MAAG,MAAIF,KAAE,IAAEW,KAAGF,MAAG,IAAEP,MAAGO,KAAEP,KAAED,IAAES,MAAG,IAAER,MAAGD,KAAEC,KAAEQ;AAAA,IAAC;AAAC,aAAQV,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAMC,MAAGM,KAAEC,MAAG,GAAEL,KAAED,KAAED,KAAE,IAAE;AAAE,MAAAK,OAAGH,MAAG,MAAIH,KAAE,IAAEW,KAAGJ,MAAG,IAAEJ,MAAGI,KAAEJ,KAAEF,IAAEO,MAAG,IAAEL,MAAGF,KAAEE,KAAEK;AAAA,IAAC;AAAA,EAAC;AAAC,EAAAR,GAAE,IAAEC,EAAC,IAAEI,IAAEL,GAAE,IAAEC,KAAE,CAAC,IAAEK;AAAC;;;ACAh0C,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYW,KAAE,CAAC,GAAEC,IAAEC,KAAE,MAAK;AAAC,SAAK,YAAU,CAAAC,OAAG;AAAA,IAAC,GAAE,KAAK,SAAO,GAAE,KAAK,QAAM,IAAIC,GAAE,MAAK,GAAE,GAAE,CAAC,GAAE,KAAK,mBAAiBJ,IAAE,KAAK,QAAME,KAAE,IAAID,GAAE,IAAI,IAAE,MAAK,KAAK,eAAaA;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,MAAM;AAAA,EAAC;AAAA,EAAC,SAASE,IAAEF,IAAEC,IAAE;AAAC,SAAK;AAAS,QAAIG,KAAE;AAAK,WAAO,EAAE,KAAK,KAAK,MAAIA,KAAE,KAAK,MAAM,QAAQ,IAAG,EAAEA,EAAC,IAAEA,GAAE,QAAQF,IAAEF,IAAEC,EAAC,IAAEG,KAAE,IAAID,GAAE,MAAKD,IAAEF,IAAEC,EAAC,GAAEG;AAAA,EAAC;AAAA,EAAC,SAASF,IAAE;AAAC,SAAK,UAAUA,EAAC,GAAE,KAAK,UAAS,EAAE,KAAK,KAAK,KAAG,KAAK,MAAM,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,MAAM;AAAA,EAAK;AAAA,EAAC,IAAI,OAAM;AAAC,WAAO,KAAK;AAAA,EAAM;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,EAAE,KAAK,OAAM,GAAG,CAAAA,OAAGA,GAAE,IAAK;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,QAAIA,KAAE;AAAE,WAAO,KAAK,QAAS,CAAAH,OAAGG,KAAE,KAAK,IAAIA,IAAEH,GAAE,KAAK,CAAE,GAAEG;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAE;AAAC,SAAK,QAAS,CAAAH,OAAG;AAAC,UAAGA,GAAE,SAAOG,GAAE,UAAQA,KAAE,GAAEA,KAAEH,GAAE,SAAS,QAAOG,MAAI;AAAC,cAAMF,KAAED,GAAE,SAASG,EAAC;AAAE,QAAAF,MAAG,KAAK,SAASA,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE,GAAE,KAAK,QAAS,CAAAD,OAAG;AAAC,UAAGA,GAAE,SAAOG,GAAE,UAAQA,KAAE,GAAEA,KAAEH,GAAE,SAAS,QAAOG,KAAI,CAAAH,GAAE,SAASG,EAAC,IAAE;AAAA,IAAI,CAAE;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,QAAS,CAAAA,OAAG,KAAK,SAASA,EAAC,CAAE,GAAE,KAAK,QAAM,IAAIC,GAAE,MAAK,GAAE,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,OAAOD,IAAEH,IAAEC,KAAE,GAAE;AAAC,UAAMC,KAAEI,GAAE,sBAAsB,CAACH,EAAC,GAAE,KAAK,YAAY,EAAE,UAAU;AAAE,IAAAD,GAAE,KAAK;AAAE,UAAMG,KAAEH,GAAE,aAAa;AAAE,QAAG,CAACG,GAAE;AAAO,UAAK,CAACE,IAAEC,GAAC,IAAEH,GAAE,QAAOI,KAAEN,GAAE,UAASO,KAAEP,GAAE;AAAS,SAAK,aAAaD,IAAEC,GAAE,WAAUI,IAAEC,KAAEC,IAAEC,IAAEV,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaE,IAAEH,IAAEC,IAAEC,IAAEG,IAAEE,IAAEC,KAAEC,KAAE,GAAE;AAAC,QAAIC,KAAE,KAAK,OAAMC,KAAE,GAAEL,KAAE,GAAEM,KAAE;AAAE,WAAK,SAAOF,MAAG;AAAC,UAAGA,GAAE,SAAOD,OAAIC,GAAE,SAAO,GAAEA,GAAE,UAAQT,IAAES,GAAE,UAAQR,IAAEQ,GAAE,iBAAeL,IAAEK,GAAE,iBAAeH,IAAEG,GAAE,cAAYV,IAAE,KAAK,wBAAwBG,IAAEO,IAAE,CAAC,IAAGC,MAAGH,IAAE,QAAO,KAAKE,GAAE,IAAIV,EAAC;AAAE,YAAMI,KAAE,KAAK,MAAMO,KAAE,KAAG,CAAC,GAAEE,MAAE,KAAK,OAAOF,KAAE,KAAG,CAAC,GAAEG,KAAE,IAAEH,KAAE,GAAEI,KAAE,MAAI,IAAEX,KAAE,IAAES,MAAGG,KAAE,MAAI,IAAEZ,KAAE,IAAES,MAAGI,MAAGZ,KAAE,IAAES,KAAE,KAAG,IAAEA,OAAIC,OAAIA,IAAEG,MAAGX,KAAE,IAAEO,KAAE,KAAG,IAAEA,OAAIE,OAAIA,IAAEG,KAAEF,KAAEC,MAAG,IAAEJ,KAAE,KAAG,IAAEA;AAAI,MAAAR,KAAEA,MAAG,IAAEQ,KAAE,KAAG,IAAEA,MAAGG,IAAEL,KAAEA,MAAG,IAAEE,KAAE,KAAG,IAAEA,MAAGI,IAAE,QAAMR,GAAE,SAASS,EAAC,MAAIT,GAAE,SAASS,EAAC,IAAE,KAAK,SAASb,IAAEM,IAAED,KAAE,CAAC,IAAGA,MAAG,GAAED,KAAEA,GAAE,SAASS,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAOhB,IAAEH,IAAE;AAAC,UAAMC,KAAEK,GAAE,sBAAsB,CAACH,EAAC,GAAE,KAAK,YAAY,EAAE,UAAU;AAAE,IAAAF,GAAE,KAAK;AAAE,UAAMC,KAAED,GAAE,aAAa;AAAE,QAAG,CAACC,GAAE;AAAO,UAAK,CAACG,IAAEE,EAAC,IAAEL,GAAE,QAAOM,MAAEL,GAAE,UAASM,KAAEN,GAAE;AAAS,SAAK,aAAaF,IAAEI,IAAEE,IAAEC,KAAEC,IAAET,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaG,IAAEH,IAAEC,IAAEC,IAAEG,IAAEE,IAAE;AAAC,QAAIC,MAAE,KAAK,OAAMC,KAAE;AAAE,WAAK,SAAOD,OAAG;AAAC,UAAGA,IAAE,SAAO,GAAEA,IAAE,UAAQR,IAAEQ,IAAE,UAAQP,IAAEO,IAAE,iBAAeN,IAAEM,IAAE,iBAAeH,IAAE,KAAK,wBAAwBF,IAAEK,KAAE,EAAE,GAAEC,MAAGF,GAAE,QAAO,KAAKC,IAAE,OAAOL,GAAE,aAAa,CAAC;AAAE,YAAMO,KAAE,KAAK,MAAMD,KAAE,KAAG,CAAC,GAAEE,KAAE,KAAK,OAAOF,KAAE,KAAG,CAAC,GAAEH,KAAE,IAAEG,KAAE,GAAEG,KAAE,MAAI,IAAEF,KAAE,IAAEC,KAAGP,KAAE,MAAI,IAAEM,KAAE,IAAEC,KAAGE,QAAIX,KAAE,IAAEI,KAAE,KAAG,IAAEA,OAAIM,OAAIA,QAAKP,KAAE,IAAEC,KAAE,KAAG,IAAEA,OAAIF,OAAIA,OAAI,IAAEE,KAAE,KAAG,IAAEA,MAAIQ,KAAEN,IAAE,SAASK,GAAC;AAAE,aAAIC,MAAA,gBAAAA,GAAG,WAAQ,KAAK,SAASA,EAAC,GAAEN,IAAE,SAASK,GAAC,IAAE,OAAMJ,MAAG,GAAED,MAAEM;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,QAAQX,IAAE;AAAC,QAAIH,KAAE,KAAK;AAAM,WAAK,SAAOA,MAAG;AAAC,YAAMC,KAAE,KAAK,cAAcD,EAAC,KAAGA,GAAE;AAAK,MAAAG,GAAEH,EAAC,GAAEA,KAAEC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,KAAKE,IAAEH,IAAEC,IAAE;AAAC,WAAO,KAAK,MAAM,KAAKE,IAAEH,IAAEC,IAAE,GAAE,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,OAAOE,IAAE;AAAC,QAAIH,KAAE;AAAK,WAAO,KAAK,QAAS,CAAAC,OAAG;AAAC,MAAAE,GAAEF,EAAC,MAAID,KAAEC;AAAA,IAAE,CAAE,GAAED;AAAA,EAAC;AAAA,EAAC,UAAUG,IAAE;AAAC,UAAMH,KAAE,CAAC;AAAE,WAAO,KAAK,QAAS,CAAAC,OAAG;AAAC,MAAAE,GAAEF,EAAC,KAAGD,GAAE,KAAKC,EAAC;AAAA,IAAC,CAAE,GAAED;AAAA,EAAC;AAAA,EAAC,wBAAwBG,IAAEH,IAAEC,IAAEC,IAAEG,IAAE;AAAC,QAAIE,KAAE,KAAK;AAAM,WAAK,SAAOA,MAAG;AAAC,YAAMC,MAAED,GAAE,OAAME,KAAEF,GAAE,OAAMG,KAAEH,GAAE,OAAMI,KAAE,IAAEH,MAAE,GAAEF,KAAEC,GAAE,gBAAcA,GAAE,OAAMK,KAAEL,GAAE,gBAAcA,GAAE;AAAM,UAAG,MAAIA,GAAE,SAAOJ,KAAEG,MAAGA,MAAGL,MAAGD,KAAEY,MAAGA,MAAGV,GAAE,QAAOK;AAAE,UAAGC,OAAGH,IAAE;AAAC,QAAAE,KAAEA,GAAE;AAAK;AAAA,MAAQ;AAAC,YAAMH,KAAE,KAAK,MAAMI,MAAE,KAAG,CAAC,GAAEK,MAAE,KAAK,OAAOL,MAAE,KAAG,CAAC,GAAEM,KAAE,MAAI,IAAEV,KAAE,IAAES,MAAGE,KAAE,MAAI,IAAEX,KAAE,IAAES,MAAGG,KAAE,GAAG,KAAGF,MAAG,IAAGG,KAAE,GAAG,KAAGF,MAAG,IAAGG,MAAGf,KAAEa,OAAIF,IAAEK,MAAGnB,KAAEiB,OAAIF,IAAEK,MAAGnB,KAAEe,OAAIF,IAAEO,MAAGnB,KAAEe,OAAIF,IAAEO,KAAEb,MAAG,IAAEE,KAAE,KAAG,IAAEA,KAAGY,KAAEb,MAAG,IAAEC,KAAE,KAAG,IAAEA,KAAGa,KAAEF,KAAE,IAAEX,KAAE,KAAG,IAAEA,KAAG,IAAEY,KAAE,IAAEZ,KAAE,KAAG,IAAEA,KAAGc,KAAE,KAAK,IAAIH,IAAEJ,EAAC,GAAE,IAAE,KAAK,IAAIK,IAAEJ,EAAC,GAAEO,KAAE,KAAK,IAAIF,IAAEJ,EAAC,GAAEO,KAAE,KAAK,IAAI,GAAEN,EAAC;AAAE,UAAIO,KAAE,MAAKC,KAAE;AAAK,eAAQ1B,KAAE,GAAEA,MAAGwB,IAAExB,KAAI,UAAQH,KAAEyB,IAAEzB,MAAG0B,IAAE1B,MAAI;AAAC,cAAMC,MAAED,KAAEsB,MAAGnB,KAAEoB,OAAI,IAAEZ,KAAE,KAAG,IAAEA,MAAIT,KAAEK,GAAE,SAASN,GAAC;AAAE,QAAAC,OAAI0B,OAAIA,KAAE1B,IAAE0B,GAAE,OAAKrB,GAAE,OAAMsB,OAAIA,GAAE,OAAK3B,KAAG2B,KAAE3B,IAAEA,GAAE,OAAKK,GAAE;AAAA,MAAK;AAAC,MAAAA,KAAEqB,MAAGrB,GAAE;AAAA,IAAI;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,oBAAoBJ,IAAE;AAAC,QAAIH,KAAE,KAAK;AAAM,UAAK,EAAC,QAAOC,IAAE,eAAcC,IAAE,OAAMG,GAAC,IAAEF,IAAE,CAACI,IAAEC,KAAEC,IAAEC,EAAC,IAAET,IAAEU,KAAE,CAAC;AAAE,WAAK,SAAOX,MAAG;AAAC,YAAMG,KAAEH,GAAE,OAAMC,MAAED,GAAE,OAAMM,KAAEN,GAAE;AAAM,UAAGG,MAAGE,IAAE;AAAC,cAAMF,KAAEH,GAAE,SAAOA,GAAE,OAAMC,MAAED,GAAE,SAAOA,GAAE;AAAM,QAAAG,MAAGI,MAAGJ,MAAGM,MAAGR,OAAGO,OAAGP,OAAGS,MAAGV,GAAE,WAAW,QAAS,CAAAG,OAAGQ,GAAE,KAAKR,EAAC,CAAE,GAAEH,KAAEA,GAAE;AAAK;AAAA,MAAQ;AAAC,YAAMY,KAAE,KAAK,MAAMT,KAAE,KAAG,CAAC,GAAEC,KAAE,KAAK,OAAOD,KAAE,KAAG,CAAC,GAAEU,MAAE,IAAEV,KAAE,GAAEW,KAAE,MAAI,IAAEF,KAAE,IAAER,KAAGW,KAAE,MAAI,IAAEH,KAAE,IAAER,KAAGY,KAAE,GAAG,KAAGF,MAAG,IAAGG,KAAE,GAAG,KAAGF,MAAG,IAAGG,MAAGhB,GAAE,MAAIc,OAAIF,IAAEK,MAAGjB,GAAE,MAAIe,OAAIF,IAAEK,MAAGlB,GAAE,MAAIc,OAAIF,IAAEO,MAAGnB,GAAE,MAAIe,OAAIF,IAAEO,KAAErB,OAAG,IAAEY,MAAE,KAAG,IAAEA,MAAGU,KAAEjB,MAAG,IAAEO,MAAE,KAAG,IAAEA,MAAGW,KAAEF,KAAE,IAAET,MAAE,KAAG,IAAEA,MAAG,IAAEU,KAAE,IAAEV,MAAE,KAAG,IAAEA,MAAGY,KAAE,KAAK,IAAIH,IAAEJ,EAAC,GAAE,IAAE,KAAK,IAAIK,IAAEJ,EAAC,GAAEO,KAAE,KAAK,IAAIF,IAAEJ,EAAC,GAAEO,KAAE,KAAK,IAAI,GAAEN,EAAC;AAAE,UAAIO,KAAE,MAAKC,KAAE;AAAK,eAAQ3B,KAAE,GAAEA,MAAGyB,IAAEzB,KAAI,UAAQC,KAAEsB,IAAEtB,MAAGuB,IAAEvB,MAAI;AAAC,cAAMF,MAAEE,KAAEmB,MAAGpB,KAAEqB,OAAI,IAAEV,MAAE,KAAG,IAAEA,OAAIR,KAAEL,GAAE,SAASC,GAAC;AAAE,QAAAI,OAAIuB,OAAIA,KAAEvB,IAAEuB,GAAE,OAAK5B,GAAE,OAAM6B,OAAIA,GAAE,OAAKxB,KAAGwB,KAAExB,IAAEA,GAAE,OAAKL,GAAE;AAAA,MAAK;AAAC,MAAAA,KAAE4B,MAAG5B,GAAE;AAAA,IAAI;AAAC,WAAOW;AAAA,EAAC;AAAA,EAAC,oBAAoBR,IAAE;AAAC,QAAIH,KAAE,KAAK,OAAMC,KAAE,GAAEC,KAAE,GAAEG,KAAE;AAAE,UAAME,KAAE,CAAC,GAAE,EAAC,QAAOC,KAAE,eAAcC,IAAE,OAAMC,GAAC,IAAEP,IAAE,CAACQ,IAAEL,IAAEM,IAAER,EAAC,IAAEI;AAAE,QAAIK,MAAE;AAAE,WAAK,SAAOb,MAAG;AAAC,YAAMG,KAAEH,GAAE,OAAMQ,MAAER,GAAE,OAAMc,KAAEd,GAAE;AAAM,UAAGG,MAAGO,IAAE;AAAC,cAAMP,KAAEH,GAAE,SAAOA,GAAE,OAAMQ,MAAER,GAAE,SAAOA,GAAE;AAAM,QAAAG,KAAEQ,MAAGR,MAAGS,MAAGJ,MAAEF,MAAGE,OAAGJ,OAAIH,MAAGD,GAAE,OAAME,MAAGF,GAAE,QAAOK,MAAGL,GAAE,QAAO,MAAIA,GAAE,UAAQa,MAAEb,GAAE,cAAa,KAAK,qBAAqBO,IAAEP,GAAE,UAAU,IAAGA,KAAEA,GAAE;AAAK;AAAA,MAAQ;AAAC,YAAMe,KAAE,KAAK,MAAMZ,KAAE,KAAG,CAAC,GAAEa,KAAE,KAAK,OAAOb,KAAE,KAAG,CAAC,GAAEc,KAAE,IAAEd,KAAE,GAAEe,KAAE,MAAI,IAAEH,KAAE,IAAEC,KAAGG,KAAE,MAAI,IAAEJ,KAAE,IAAEC,KAAGI,KAAE,GAAG,KAAGF,MAAG,IAAGG,KAAE,GAAG,KAAGF,MAAG,IAAGG,MAAGb,GAAE,MAAIW,OAAIF,IAAEK,MAAGd,GAAE,MAAIY,OAAIF,IAAEK,MAAGf,GAAE,MAAIW,OAAIF,IAAE,KAAGT,GAAE,MAAIY,OAAIF,IAAEM,KAAEjB,OAAG,IAAES,KAAE,KAAG,IAAEA,KAAG,IAAEH,MAAG,IAAEG,KAAE,KAAG,IAAEA,KAAGS,KAAED,KAAE,IAAER,KAAE,KAAG,IAAEA,KAAGU,KAAE,IAAE,IAAEV,KAAE,KAAG,IAAEA,KAAGW,KAAE,KAAK,IAAIH,IAAEH,EAAC,GAAEO,KAAE,KAAK,IAAI,GAAEN,EAAC,GAAEO,KAAE,KAAK,IAAIJ,IAAEF,EAAC,GAAEO,KAAE,KAAK,IAAIJ,IAAE,CAAC;AAAE,UAAIK,KAAE,MAAK,IAAE;AAAK,eAAQvB,KAAEoB,IAAEpB,MAAGsB,IAAEtB,KAAI,UAAQN,KAAEyB,IAAEzB,MAAG2B,IAAE3B,MAAI;AAAC,cAAMK,MAAEL,KAAEsB,MAAGhB,KAAE,MAAI,IAAEQ,KAAE,KAAG,IAAEA,MAAIP,KAAEV,GAAE,SAASQ,GAAC;AAAE,YAAGE,IAAE;AAAC,cAAGD,OAAIoB,MAAGpB,OAAIsB,MAAG5B,OAAIyB,MAAGzB,OAAI2B,IAAE;AAAC,kBAAM3B,KAAEO,GAAE,SAAOA,GAAE,OAAMV,KAAEU,GAAE,SAAOA,GAAE;AAAM,YAAAP,KAAEQ,MAAGR,MAAGS,MAAGZ,KAAEM,MAAGN,MAAGI,OAAIH,MAAGS,GAAE,OAAMR,MAAGQ,GAAE,QAAOL,MAAGK,GAAE,QAAO,MAAIA,GAAE,UAAQG,MAAEH,GAAE,cAAa,KAAK,qBAAqBH,IAAEG,GAAE,UAAU;AAAG;AAAA,UAAQ;AAAC,UAAAsB,OAAIA,KAAEtB,IAAEsB,GAAE,OAAKhC,GAAE,OAAM,MAAI,EAAE,OAAKU,KAAG,IAAEA,IAAEA,GAAE,OAAKV,GAAE;AAAA,QAAI;AAAA,MAAC;AAAC,MAAAA,KAAEgC,MAAGhC,GAAE;AAAA,IAAI;AAAC,WAAM,EAAC,OAAMC,IAAE,YAAW,KAAK,oBAAoBM,IAAEN,EAAC,GAAE,QAAOC,IAAE,QAAOG,IAAE,aAAYQ,IAAC;AAAA,EAAC;AAAA,EAAC,QAAQV,IAAE;AAAC,UAAMH,KAAE,CAAC,GAAE,EAAC,eAAcC,IAAE,OAAMC,GAAC,IAAEC;AAAE,QAAIE,KAAE,KAAK;AAAM,WAAK,SAAOA,MAAG;AAAC,YAAMF,KAAEE,GAAE,OAAME,KAAEF,GAAE,OAAMG,MAAEH,GAAE;AAAM,UAAGF,MAAGD,IAAE;AAAC,QAAAF,GAAE,KAAKK,EAAC,GAAEA,KAAEA,GAAE;AAAK;AAAA,MAAQ;AAAC,YAAMI,KAAE,KAAK,MAAMN,KAAE,KAAG,CAAC,GAAEO,KAAE,KAAK,OAAOP,KAAE,KAAG,CAAC,GAAEQ,KAAE,IAAER,KAAE,GAAEG,KAAE,MAAI,IAAEG,KAAE,IAAEC,KAAGE,KAAE,MAAI,IAAEH,KAAE,IAAEC,KAAGN,KAAE,GAAG,KAAGE,MAAG,IAAGO,MAAE,GAAG,KAAGD,MAAG,IAAGE,MAAGb,GAAE,MAAIG,OAAIE,IAAES,MAAGd,GAAE,MAAIY,QAAID,IAAEI,MAAGf,GAAE,MAAIG,OAAIE,IAAEW,MAAGhB,GAAE,MAAIY,QAAID,IAAEM,KAAEX,MAAG,IAAEI,KAAE,KAAG,IAAEA,KAAGQ,KAAEX,OAAG,IAAEG,KAAE,KAAG,IAAEA,KAAGS,KAAEF,KAAE,IAAEP,KAAE,KAAG,IAAEA,KAAGU,KAAEF,KAAE,IAAER,KAAE,KAAG,IAAEA,KAAGW,KAAE,KAAK,IAAIJ,IAAEJ,EAAC,GAAES,KAAE,KAAK,IAAIJ,IAAEJ,EAAC,GAAES,KAAE,KAAK,IAAIJ,IAAEJ,EAAC,GAAE,IAAE,KAAK,IAAIK,IAAEJ,EAAC;AAAE,UAAIQ,KAAE,MAAK,IAAE;AAAK,eAAQzB,KAAEuB,IAAEvB,MAAG,GAAEA,KAAI,UAAQG,KAAEmB,IAAEnB,MAAGqB,IAAErB,MAAI;AAAC,cAAMF,MAAEE,KAAEe,MAAGlB,KAAEmB,OAAI,IAAER,KAAE,KAAG,IAAEA,MAAIT,KAAEG,GAAE,SAASJ,GAAC;AAAE,QAAAC,OAAIuB,OAAIA,KAAEvB,IAAEuB,GAAE,OAAKpB,GAAE,OAAM,MAAI,EAAE,OAAKH,KAAG,IAAEA,IAAEA,GAAE,OAAKG,GAAE;AAAA,MAAK;AAAC,MAAAA,KAAEoB,MAAGpB,GAAE;AAAA,IAAI;AAAC,WAAOL;AAAA,EAAC;AAAA,EAAC,cAAcG,IAAE;AAAC,QAAIH,KAAE,MAAKC,KAAE;AAAK,aAAQC,KAAE,GAAEA,MAAGC,GAAE,SAAS,QAAOD,MAAI;AAAC,YAAMG,KAAEF,GAAE,SAASD,EAAC;AAAE,MAAAG,OAAIL,OAAIA,KAAEK,IAAEL,GAAE,OAAKG,GAAE,OAAMF,OAAIA,GAAE,OAAKI,KAAGJ,KAAEI,IAAEA,GAAE,OAAKF,GAAE;AAAA,IAAK;AAAC,WAAOH;AAAA,EAAC;AAAA,EAAC,wBAAwBG,IAAEH,IAAEC,IAAE;AAAC,eAAUC,MAAK,KAAK,kBAAiB;AAAC,YAAMG,KAAEH,GAAE,MAAKK,KAAEL,GAAE,UAAQC,GAAE,cAAcD,GAAE,OAAO,IAAEC,GAAE,0BAA0BD,GAAE,YAAY;AAAE,cAAOA,GAAE,eAAc;AAAA,QAAC,KAAI,OAAM;AAAC,cAAG,MAAMK,EAAC,EAAE;AAAM,cAAG,CAACP,GAAE,WAAWK,EAAC,GAAE;AAAC,YAAAL,GAAE,WAAWK,EAAC,IAAE,EAAC,OAAME,GAAC;AAAE;AAAA,UAAK;AAAC,gBAAMJ,KAAEH,GAAE,WAAWK,EAAC,EAAE;AAAM,UAAAL,GAAE,WAAWK,EAAC,EAAE,QAAM,KAAK,IAAIF,IAAEI,EAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAI,OAAM;AAAC,cAAG,MAAMA,EAAC,EAAE;AAAM,cAAG,CAACP,GAAE,WAAWK,EAAC,GAAE;AAAC,YAAAL,GAAE,WAAWK,EAAC,IAAE,EAAC,OAAME,GAAC;AAAE;AAAA,UAAK;AAAC,gBAAMJ,KAAEH,GAAE,WAAWK,EAAC,EAAE;AAAM,UAAAL,GAAE,WAAWK,EAAC,EAAE,QAAM,KAAK,IAAIF,IAAEI,EAAC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAI;AAAQ;AAAA,QAAM,KAAI;AAAA,QAAM,KAAI,OAAM;AAAC,UAAAP,GAAE,WAAWK,EAAC,MAAIL,GAAE,WAAWK,EAAC,IAAE,EAAC,OAAM,GAAE,UAAS,EAAC;AAAG,gBAAMF,KAAEH,GAAE,WAAWK,EAAC,EAAE,OAAMH,KAAEF,GAAE,WAAWK,EAAC,EAAE,YAAU;AAAE,kBAAME,MAAG,MAAMA,EAAC,IAAEP,GAAE,WAAWK,EAAC,EAAE,WAASH,KAAED,KAAED,GAAE,WAAWK,EAAC,EAAE,QAAMF,KAAEF,KAAEM;AAAE;AAAA,QAAK;AAAA,QAAC,KAAI,aAAY;AAAC,UAAAP,GAAE,WAAWK,EAAC,MAAIL,GAAE,WAAWK,EAAC,IAAE,EAAC,GAAE,GAAE,GAAE,GAAE,UAAS,EAAC;AAAG,gBAAMF,KAAEH,GAAE,WAAWK,EAAC,EAAE,GAAEH,KAAEF,GAAE,WAAWK,EAAC,EAAE,GAAEG,MAAER,GAAE,WAAWK,EAAC,EAAE,YAAU,GAAEI,KAAE,KAAK,KAAG;AAAI,kBAAMF,MAAG,MAAMA,EAAC,IAAEP,GAAE,WAAWK,EAAC,EAAE,WAASG,MAAEP,MAAGD,GAAE,WAAWK,EAAC,EAAE,IAAEF,KAAEF,KAAE,KAAK,IAAIM,KAAEE,EAAC,GAAET,GAAE,WAAWK,EAAC,EAAE,IAAEH,KAAED,KAAE,KAAK,IAAIM,KAAEE,EAAC;AAAG;AAAA,QAAK;AAAA,QAAC,KAAI,QAAO;AAAC,UAAAT,GAAE,WAAWK,EAAC,MAAIL,GAAE,WAAWK,EAAC,IAAE,CAAC;AAAG,gBAAMF,KAAEH,GAAE,WAAWK,EAAC,EAAEE,EAAC,KAAG;AAAE,UAAAP,GAAE,WAAWK,EAAC,EAAEE,EAAC,IAAEJ,KAAEF;AAAE;AAAA,QAAK;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,qBAAqBE,IAAEH,IAAE;AAAC,eAAUC,MAAK,KAAK,kBAAiB;AAAC,YAAMC,KAAED,GAAE;AAAK,cAAOA,GAAE,eAAc;AAAA,QAAC,KAAI,OAAM;AAAC,cAAG,CAACE,GAAED,EAAC,GAAE;AAAC,YAAAC,GAAED,EAAC,IAAE,EAAC,OAAMF,GAAEE,EAAC,EAAE,MAAK;AAAE;AAAA,UAAK;AAAC,gBAAMD,MAAEE,GAAED,EAAC,EAAE;AAAM,UAAAC,GAAED,EAAC,EAAE,QAAM,KAAK,IAAID,KAAED,GAAEE,EAAC,EAAE,KAAK;AAAE;AAAA,QAAK;AAAA,QAAC,KAAI,OAAM;AAAC,cAAG,CAACC,GAAED,EAAC,GAAE;AAAC,YAAAC,GAAED,EAAC,IAAE,EAAC,OAAMF,GAAEE,EAAC,EAAE,MAAK;AAAE;AAAA,UAAK;AAAC,gBAAMD,MAAEE,GAAED,EAAC,EAAE;AAAM,UAAAC,GAAED,EAAC,EAAE,QAAM,KAAK,IAAID,KAAED,GAAEE,EAAC,EAAE,KAAK;AAAE;AAAA,QAAK;AAAA,QAAC,KAAI;AAAQ;AAAA,QAAM,KAAI;AAAA,QAAM,KAAI;AAAA,QAAM,KAAI;AAAA,QAAY,KAAI;AAAO,UAAAC,GAAED,EAAC,MAAIC,GAAED,EAAC,IAAE,CAAC;AAAG,qBAAUD,OAAKD,GAAEE,EAAC,GAAE;AAAC,kBAAMG,KAAEF,GAAED,EAAC,EAAED,GAAC,KAAG;AAAE,YAAAE,GAAED,EAAC,EAAED,GAAC,IAAEI,KAAEL,GAAEE,EAAC,EAAED,GAAC;AAAA,UAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBE,IAAEH,IAAE;AAAC,UAAMC,KAAE,CAAC;AAAE,eAAUC,MAAK,KAAK,kBAAiB;AAAC,YAAMG,KAAEH,GAAE;AAAK,cAAOA,GAAE,eAAc;AAAA,QAAC,KAAI;AAAA,QAAM,KAAI,OAAM;AAAC,gBAAMA,KAAEC,GAAEE,EAAC;AAAE,cAAG,CAACL,MAAG,CAACE,GAAE;AAAM,UAAAD,GAAEI,EAAC,IAAEH,GAAE;AAAM;AAAA,QAAK;AAAA,QAAC,KAAI;AAAQ,cAAG,CAACF,GAAE;AAAM,UAAAC,GAAEI,EAAC,IAAEL;AAAE;AAAA,QAAM,KAAI,OAAM;AAAC,cAAG,CAACA,GAAE;AAAM,gBAAK,EAAC,OAAME,IAAE,UAASK,GAAC,IAAEJ,GAAEE,EAAC;AAAE,cAAG,EAAEL,KAAEO,IAAG;AAAM,UAAAN,GAAEI,EAAC,IAAEH;AAAE;AAAA,QAAK;AAAA,QAAC,KAAI,OAAM;AAAC,cAAG,CAACF,GAAE;AAAM,gBAAK,EAAC,OAAME,IAAE,UAASK,GAAC,IAAEJ,GAAEE,EAAC;AAAE,cAAG,EAAEL,KAAEO,IAAG;AAAM,UAAAN,GAAEI,EAAC,IAAEH,MAAGF,KAAEO;AAAG;AAAA,QAAK;AAAA,QAAC,KAAI,aAAY;AAAC,cAAG,CAACP,GAAE;AAAM,gBAAK,EAAC,GAAEE,IAAE,GAAEK,IAAE,UAASC,IAAC,IAAEL,GAAEE,EAAC;AAAE,cAAG,EAAEL,KAAEQ,KAAG;AAAM,gBAAMC,KAAEP,MAAGF,KAAEQ,MAAGE,KAAEH,MAAGP,KAAEQ,MAAGG,KAAE,MAAI,KAAK,IAAGL,KAAE,KAAK,MAAMI,IAAED,EAAC,IAAEE;AAAE,UAAAV,GAAEI,EAAC,IAAEC;AAAE;AAAA,QAAK;AAAA,QAAC,KAAI,QAAO;AAAC,gBAAMN,KAAEG,GAAEE,EAAC;AAAE,cAAIH,KAAE,GAAEK,KAAE,GAAEC,MAAE;AAAK,qBAAUL,MAAKH,IAAE;AAAC,kBAAMC,MAAED,GAAEG,EAAC;AAAE,YAAAF,QAAIC,KAAEK,MAAG,IAAEN,MAAEC,OAAIA,KAAED,KAAEM,KAAE,GAAEC,MAAEL;AAAA,UAAE;AAAC,UAAAF,GAAEI,EAAC,IAAE,WAASG,OAAGD,KAAE,IAAE,OAAKC;AAAE;AAAA,QAAK;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOP;AAAA,EAAC;AAAC;AAAC,IAAMG,KAAN,MAAO;AAAA,EAAC,YAAYD,IAAEH,IAAEC,IAAEC,IAAE;AAAC,SAAK,QAAM,GAAE,KAAK,SAAO,GAAE,KAAK,SAAO,GAAE,KAAK,aAAW,CAAC,GAAE,KAAK,YAAU,GAAE,KAAK,cAAY,GAAE,KAAK,aAAW,oBAAI,OAAI,KAAK,OAAK,MAAK,KAAK,QAAM,GAAE,KAAK,QAAM,GAAE,KAAK,QAAM,GAAE,KAAK,gBAAc,GAAE,KAAK,gBAAc,GAAE,KAAK,QAAMC,IAAE,KAAK,WAAS,IAAI,MAAM,EAAE;AAAE,aAAQE,KAAE,GAAEA,KAAE,KAAK,SAAS,QAAOA,KAAI,MAAK,SAASA,EAAC,IAAE;AAAK,SAAK,QAAML,IAAE,KAAK,QAAMC,IAAE,KAAK,QAAMC;AAAA,EAAC;AAAA,EAAC,QAAQC,IAAEH,IAAEC,IAAE;AAAC,aAAQC,KAAE,GAAEA,KAAE,KAAK,SAAS,QAAOA,KAAI,MAAK,SAASA,EAAC,IAAE;AAAK,WAAO,KAAK,QAAMC,IAAE,KAAK,QAAMH,IAAE,KAAK,QAAMC,IAAE,KAAK,OAAK,MAAK,KAAK,gBAAc,GAAE,KAAK,gBAAc,GAAE,KAAK,YAAU,GAAE,KAAK,cAAY,GAAE,KAAK,SAAO,GAAE,KAAK,SAAO,GAAE,KAAK,QAAM,GAAE,KAAK,aAAW,CAAC,GAAE,KAAK,WAAW,MAAM,GAAE;AAAA,EAAI;AAAA,EAAC,IAAI,KAAI;AAAC,WAAM,GAAG,KAAK,KAAK,IAAI,KAAK,KAAK;AAAA,EAAE;AAAA,EAAC,IAAIE,IAAE;AAAC,SAAK,WAAW,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,SAAK,WAAW,OAAOA,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,UAAMA,KAAE,KAAK,MAAM,oBAAoB,KAAK,YAAW,KAAK,KAAK;AAAE,WAAOA,GAAE,cAAY,MAAKA,GAAE,cAAY,KAAK,IAAGA,GAAE,iBAAe,KAAK,OAAMA;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAEF,IAAE;AAAC,UAAMC,KAAE,KAAK,gBAAgB,GAAE,CAACO,IAAEH,IAAEM,IAAER,EAAC,IAAEF,IAAEW,MAAEG,GAAE,EAAC,OAAM,CAAC,CAAC,CAACP,IAAEH,EAAC,GAAE,CAACG,IAAEL,EAAC,GAAE,CAACQ,IAAER,EAAC,GAAE,CAACQ,IAAEN,EAAC,GAAE,CAACG,IAAEH,EAAC,CAAC,CAAC,EAAC,GAAEO,GAAE,OAAMV,EAAC,GAAEW,KAAE,EAAE,IAAIX,MAAEU,KAAE,OAAG,KAAE;AAAE,QAAG,EAAEZ,EAAC,GAAE;AAAC,aAAO,GAAE,IAAIE,MAAEW,IAAE,OAAG,OAAG,uBAAsBb,IAAE,OAAG,KAAE;AAAA,IAAC;AAAC,WAAOa;AAAA,EAAC;AAAA,EAAC,oBAAoBX,IAAEF,IAAE;AAAC,UAAMC,KAAE,KAAK,gBAAgB,GAAE,CAACK,IAAED,IAAEM,IAAER,EAAC,IAAEF,IAAEW,MAAEG,GAAE,EAAC,IAAGT,KAAEK,MAAG,GAAE,IAAGN,KAAEF,MAAG,EAAC,GAAES,GAAE,OAAMV,EAAC,GAAEW,KAAEmB,GAAE,IAAI9B,MAAEU,GAAC;AAAE,QAAG,EAAEZ,EAAC,GAAE;AAAC,aAAO,GAAE,IAAIE,MAAEW,IAAE,OAAG,OAAG,qBAAoBb,IAAE,OAAG,KAAE;AAAA,IAAC;AAAC,WAAOa;AAAA,EAAC;AAAA,EAAC,kBAAiB;AAAC,UAAMX,KAAE,KAAK,OAAMH,KAAE,KAAK,KAAKG,KAAE,CAAC,GAAEF,KAAE,KAAK,MAAME,KAAE,CAAC,GAAEE,KAAE,MAAI,IAAEL,KAAE,IAAEC,KAAGM,KAAE,MAAI,IAAEP,KAAE,IAAEC,KAAGO,MAAE,KAAK,SAAOH,IAAEI,KAAE,KAAK,SAAOF;AAAE,WAAO,EAAE,EAAC,UAASC,KAAE,UAASC,GAAC,GAAE,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,KAAKN,IAAEH,IAAEC,IAAEC,IAAEG,IAAEE,IAAE;AAAC,QAAGL,MAAGD,GAAE,QAAO;AAAK,UAAMO,MAAE,IAAEN,KAAE,GAAEO,KAAE,IAAED,MAAE,KAAG,IAAEA,MAAGE,KAAE,IAAEF,MAAE,KAAG,IAAEA,MAAGG,KAAE,KAAGN,KAAEI,IAAEH,KAAE,KAAGC,KAAEG,IAAEE,OAAIT,KAAE,IAAEK,MAAE,KAAG,IAAEA,QAAIG,OAAIA,QAAKX,KAAE,IAAEQ,MAAE,KAAG,IAAEA,QAAIF,OAAIA,OAAI,IAAEE,MAAE,KAAG,IAAEA,OAAIJ,KAAE,KAAK,SAASQ,EAAC;AAAE,WAAO,QAAMR,KAAE,OAAKA,GAAE,KAAKD,IAAEH,IAAEC,IAAEC,KAAE,GAAEG,KAAEI,IAAEF,KAAEG,EAAC;AAAA,EAAC;AAAC;;;ACA75R,IAAMwB,KAAE,EAAE,UAAU,+CAA+C;AAAnE,IAAqEC,KAAE;AAAvE,IAA0EC,KAAE;AAA5E,IAA+EC,KAAEC,GAAE;AAAnF,IAAqF,IAAE;AAAE,SAASC,GAAEC,IAAE;AAAC,SAAO,oBAAkBA;AAAC;AAAC,IAAM,IAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAMH,IAAEE,EAAC,GAAE,KAAK,OAAK,OAAM,KAAK,SAAO,IAAI,KAAE,KAAK,gBAAc,eAAc,KAAK,iBAAe,GAAE,KAAK,gBAAc,GAAE,KAAK,cAAY,CAAC,GAAE,KAAK,eAAaC,IAAE,KAAK,eAAaH,GAAE,cAAa,KAAK,oBAAkBC,IAAE,KAAK,0BAAwBG,GAAEH,IAAEG,GAAE,KAAK,GAAE,KAAK,SAAS,UAAQF,GAAE,UAAUA,GAAE,aAAa,CAAC,GAAE,KAAK,SAAS,WAASA,GAAE,UAAUA,GAAE,aAAa,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,SAAO,KAAK,MAAM,QAAQ;AAAA,EAAC;AAAA,EAAC,IAAI,0BAAyB;AAAC,WAAO,KAAK;AAAA,EAAiB;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,MAAM,aAAaJ,IAAEE,IAAE;AAAC,UAAME,KAAE,KAAK;AAAQ,QAAG;AAAC,YAAM,MAAM,aAAaJ,IAAEE,EAAC,GAAE,MAAM,KAAK;AAAA,IAAuB,SAAOK,IAAE;AAAA,IAAC;AAAC,SAAK,UAAQ,KAAK,QAAQ,OAAO;AAAO,UAAMF,KAAE,EAAED,IAAEF,EAAC;AAAE,IAAAA,OAAI,CAAC,EAAEG,EAAC,KAAGL,GAAE,UAAQA,GAAE,QAAQ,aAAWF,GAAEO,IAAE,eAAe,KAAGP,GAAEO,IAAE,QAAQ,KAAG,CAAC,KAAK,SAAOL,GAAE,YAAU,KAAK,SAAO,KAAK,MAAM,QAAQ,GAAE,KAAK,QAAM,IAAI,EAAE,KAAK,kBAAiB,KAAK,YAAY,GAAE,KAAK,MAAM,YAAU,CAAAA,OAAGA,GAAE,aAAW,KAAK,SAAS,iBAAiBA,GAAE,SAAS,GAAE,KAAK,gBAAc,KAAK,QAAQ,OAAO,eAAc,KAAK,aAAa,GAAE,IAAI,sBAAsB,KAAGN,GAAE,KAAK,kDAAkD,IAAG,IAAI,sBAAsB,KAAGA,GAAE,KAAK,kDAAkD,GAAEM,GAAE,QAAQE,GAAE,IAAI,IAAE,MAAGF,GAAE,OAAK,SAAII,OAAIJ,GAAE,OAAK;AAAA,EAAG;AAAA,EAAC,QAAO;AAAC,SAAK,aAAa;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEE,IAAE;AAAC,SAAK,SAAS,SAAS,WAAY,CAAAC,OAAG;AAAC,UAAG,CAACH,GAAE,IAAIG,EAAC,GAAE;AAAC,cAAMH,KAAEE,GAAE,wBAAwBC,EAAC;AAAE,aAAK,QAAQH,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAEE,IAAEC,IAAE;AAAA,EAAC;AAAA,EAAC,WAAWH,IAAEE,IAAEE,IAAEC,IAAEG,KAAE,MAAG;AAAC,QAAG,CAAC,KAAK,WAAS,EAAEN,GAAE,WAAW,EAAE,QAAOA;AAAE,SAAK,OAAO,KAAK,SAAS;AAAE,UAAMJ,MAAE,KAAK,eAAeE,IAAE,KAAK,iBAAiB;AAAE;AAAC,YAAMA,KAAEE,GAAE,YAAY,UAAU;AAAE,aAAKF,GAAE,KAAK,IAAG,MAAK,QAAQA,IAAEK,EAAC;AAAA,IAAC;AAAC,QAAGH,GAAE,OAAO,QAAM,CAACM,GAAE,QAAON;AAAE,UAAMO,KAAE,IAAI;AAAM,SAAK,gBAAgBA,IAAET,IAAEF,KAAEM,EAAC,GAAEF,GAAE,cAAYD,GAAE,sBAAsBQ,IAAE,EAAC,GAAG,KAAK,cAAa,cAAa,sBAAqB,CAAC,GAAEP,GAAE,YAAY,cAAcE,EAAC,GAAEF,GAAE,MAAI,MAAGA,GAAE,aAAWA,GAAE,QAAM;AAAI;AAAC,YAAMC,KAAED,GAAE,YAAY,UAAU;AAAE,aAAKC,GAAE,KAAK,KAAG;AAAC,cAAMD,KAAEC,GAAE,aAAa;AAAE,aAAK,SAAS,SAAS,MAAMD,EAAC,GAAE,KAAK,sBAAsBE,IAAED,IAAED,IAAEF,GAAE,KAAK;AAAA,MAAC;AAAA,IAAC;AAAC,WAAOE;AAAA,EAAC;AAAA,EAAC,WAAWF,IAAE;AAAC,SAAK,MAAM,QAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,SAAK,MAAM,QAAS,CAAAE,OAAG;AAAC,UAAGA,GAAE,UAAQ,KAAK,cAAc;AAAO,YAAMC,KAAE,KAAK,eAAeD,EAAC,GAAEE,KAAEH,GAAE,aAAa,CAACE,EAAC,GAAE,EAAC,eAAc,KAAK,eAAc,eAAc,MAAK,cAAa,KAAK,aAAa,cAAa,QAAO,KAAK,OAAM,CAAC,EAAE,UAAU;AAAE,MAAAC,GAAE,KAAK,GAAEJ,GAAEI,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBJ,IAAEE,IAAE;AAAA,EAAC;AAAA,EAAC,cAAcF,IAAEE,IAAE;AAAC,UAAK,EAAC,MAAKE,IAAE,MAAKC,GAAC,IAAE,KAAK;AAAa,eAAUG,MAAKR,IAAE;AAAC,YAAMA,KAAE,GAAEH,IAAEW,GAAE,aAAa,GAAEH,IAAED,EAAC;AAAE,QAAEJ,EAAC,KAAGE,GAAEF,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAME,KAAEC,GAAEH,IAAE,IAAE,GAAEG,KAAE,KAAK,MAAM,OAAQ,CAAAH,OAAGA,GAAE,cAAYE,EAAE;AAAE,WAAO,EAAEC,IAAG,CAAAH,OAAG,KAAK,eAAeA,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,WAAO,KAAK,MAAM,UAAW,CAAAA,OAAGA,GAAE,UAAQ,KAAK,aAAc,EAAE,IAAI,KAAK,eAAe,KAAK,IAAI,CAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAME,KAAE,KAAK,MAAM,OAAQ,CAAAA,OAAGA,GAAE,OAAKF,EAAE;AAAE,WAAO,EAAEE,IAAG,CAAAF,OAAGA,GAAE,SAAU;AAAA,EAAC;AAAA,EAAC,iCAAiCA,IAAE;AAAC,UAAME,KAAE,KAAK,MAAM,OAAQ,CAAAA,OAAGA,GAAE,OAAKF,EAAE;AAAE,WAAO,EAAEE,IAAE,CAAC,GAAG,CAAAF,OAAG,MAAM,KAAKA,GAAE,UAAU,CAAE;AAAA,EAAC;AAAA,EAAC,2BAA2BA,IAAE;AAAC,UAAME,KAAE,KAAK,MAAM,OAAQ,CAAAA,OAAG,MAAIA,GAAE,WAAW,QAAMA,GAAE,WAAW,IAAIF,EAAC,CAAE;AAAE,WAAO,EAAEE,IAAG,CAAAF,OAAGA,GAAE,SAAU;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAE;AAAC,UAAME,KAAE,KAAK;AAAkB,WAAM,EAAC,WAAUF,GAAE,WAAU,YAAWA,GAAE,cAAc,GAAE,UAAS,GAAEA,GAAE,YAAYE,EAAC,GAAE,uBAAsB,OAAG,KAAE,GAAE,UAAS,KAAI;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,SAAK,SAAS,SAAS,MAAM,GAAE,KAAK,SAAS,SAAS,MAAM,GAAE,KAAK,SAAO,KAAK,MAAM,MAAM;AAAA,EAAC;AAAA,EAAC,QAAQF,IAAE;AAAC,UAAME,KAAEF,GAAE,aAAa,GAAEG,KAAEH,GAAE,aAAa,GAAEI,KAAEJ,GAAE,aAAa,GAAEK,KAAE,KAAK,YAAY,IAAEH,EAAC,GAAEM,KAAE,KAAK,YAAY,IAAEN,KAAE,CAAC;AAAE,SAAK,SAAS,SAAS,IAAIA,EAAC,MAAI,KAAK,SAAS,SAAS,MAAMA,EAAC,GAAE,KAAK,MAAM,aAAaF,IAAEG,IAAEC,IAAEC,IAAEG,IAAE,KAAK,aAAa;AAAA,EAAE;AAAA,EAAC,QAAQR,IAAEE,IAAE;AAAC,UAAMC,KAAEH,GAAE,aAAa,GAAEI,KAAE,KAAK,SAAS,UAASC,KAAEH,GAAE,UAAUC,EAAC;AAAE,QAAGE,OAAID,GAAE,IAAID,EAAC,EAAE;AAAO,QAAG,CAACE,GAAE,QAAO,KAAK,KAAK,QAAQL,EAAC;AAAE,UAAMQ,KAAER,GAAE,aAAa,GAAEF,MAAEE,GAAE,aAAa;AAAE,QAAG,CAAC,KAAK,YAAYG,IAAEK,IAAEV,GAAC,EAAE;AAAO,UAAMW,KAAE,KAAK,YAAY,IAAEN,EAAC,GAAEI,KAAE,KAAK,YAAY,IAAEJ,KAAE,CAAC;AAAE,SAAK,MAAM,aAAaH,IAAEG,IAAEK,IAAEV,KAAEW,IAAEF,IAAE,KAAK,aAAa,GAAEH,GAAE,IAAID,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYH,IAAEE,IAAEC,IAAE;AAAC,QAAG,KAAK,SAAS,QAAQ,IAAIH,EAAC,EAAE,QAAM;AAAG,UAAMI,KAAE,KAAK;AAAY,QAAG,KAAK,kBAAkB,eAAc;AAAC,YAAMC,KAAEN,GAAEG,KAAEC,GAAE,MAAM,GAAEK,KAAEH,KAAE,MAAI,KAAK,OAAOA,KAAE,OAAK,GAAG,GAAEP,MAAEC,GAAE,KAAK,KAAG,IAAE,IAAE,KAAK,KAAK,KAAK,IAAI,CAACI,KAAEA,GAAE,MAAM,CAAC,CAAC;AAAE,MAAAO,GAAEN,IAAEJ,IAAEF,KAAEU,IAAEb,EAAC;AAAA,IAAC,OAAK;AAAC,YAAMU,KAAEM,GAAE,EAAC,GAAET,IAAE,GAAEC,GAAC,GAAE,KAAK,mBAAkBG,GAAE,KAAK;AAAE,UAAG,CAACD,GAAE,QAAM;AAAG,MAAAK,GAAEN,IAAEJ,IAAEK,GAAE,GAAEA,GAAE,GAAEV,EAAC;AAAA,IAAC;AAAC,WAAO,KAAK,SAAS,QAAQ,IAAIK,EAAC,GAAE;AAAA,EAAE;AAAA,EAAC,gBAAgBA,IAAEE,IAAEC,IAAEC,IAAE;AAAC,QAAG;AAAC,YAAMC,KAAE,KAAK,kBAAkBH,EAAC,GAAEM,KAAE,KAAK,MAAM,QAAQH,EAAC;AAAE,iBAAUH,MAAKM,IAAE;AAAC,QAAAN,GAAE,cAAYA,GAAE,YAAUE,GAAE,gBAAgB,IAAE;AAAG,YAAIC,KAAE;AAAK,cAAMG,KAAEN,GAAE,YAAY,KAAK,mBAAkBC,GAAE,IAAI;AAAE,QAAAK,OAAIH,KAAEH,GAAE,oBAAoB,KAAK,mBAAkBC,GAAE,IAAI;AAAG,cAAML,MAAE,IAAIK,GAAEK,IAAEN,GAAE,cAAc,GAAEG,EAAC;AAAE,QAAAP,IAAE,WAASI,GAAE,IAAGJ,IAAE,YAAUI,GAAE,WAAUF,GAAE,KAAKF,GAAC;AAAA,MAAC;AAAA,IAAC,SAAOO,IAAE;AAAC,aAAO,KAAKX,GAAE,MAAM,+BAA8BQ,GAAE,IAAI,EAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,YAAYF,IAAEE,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAC,UAAS,GAAE,UAAS,EAAC;AAAE,WAAO,EAAEA,IAAEF,IAAEF,IAAEG,EAAC,GAAEC;AAAA,EAAC;AAAA,EAAC,kBAAkBJ,IAAE;AAAC,UAAME,KAAE,KAAK,iBAAiBF,GAAE,IAAI,KAAK,GAAEG,KAAE,CAACH,GAAE,OAAO,MAAKA,GAAE,OAAO,MAAKA,GAAE,OAAO,MAAKA,GAAE,OAAO,IAAI,GAAEI,KAAEQ,GAAE,WAAW,EAAE,WAAWT,IAAE,KAAK,iBAAiB,CAAC,GAAEE,KAAEM,GAAEP,IAAE,KAAK,mBAAkBE,GAAE,OAAM,EAAC,mBAAkBN,GAAE,aAAWJ,GAAC,CAAC,GAAEY,KAAE,EAAE,IAAIN,MAAEG,IAAE,OAAG,KAAE,GAAEP,MAAEU,GAAE,OAAO,OAAQ,CAACR,IAAEE,OAAI,EAAEA,KAAE,EAAG,GAAEO,KAAED,GAAE,OAAO,OAAQ,CAACR,IAAEE,OAAIA,KAAE,CAAE,GAAEK,KAAE,KAAK,IAAI,GAAGT,GAAC,GAAEe,KAAE,KAAK,IAAI,GAAGJ,EAAC,GAAEK,KAAE,KAAK,IAAI,GAAGhB,GAAC,GAAEa,KAAE,KAAK,IAAI,GAAGF,EAAC,GAAEM,KAAE,KAAK,YAAYR,IAAEM,IAAEX,EAAC,GAAED,KAAE,KAAK,YAAYa,IAAEH,IAAET,EAAC;AAAE,WAAM,EAAC,QAAOC,IAAE,eAAc,EAAC,KAAIY,GAAE,UAAS,KAAIA,GAAE,UAAS,KAAId,GAAE,UAAS,KAAIA,GAAE,SAAQ,GAAE,OAAMC,GAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBF,IAAE;AAAC,WAAO,KAAK,QAAQ,OAAO;AAAA,EAAa;AAAA,EAAC,eAAeA,IAAEE,IAAE;AAAC,UAAMC,KAAE,EAAC,gBAAe,aAAY,OAAM,CAACH,GAAE,YAAWA,GAAE,UAAU,GAAE,WAAU,CAACA,GAAE,OAAO,CAAC,GAAEA,GAAE,OAAO,CAAC,CAAC,EAAC,GAAEI,KAAE,EAAEF,EAAC;AAAE,QAAG,CAACE,GAAE,QAAM,EAAC,MAAKD,IAAE,MAAK,MAAK,OAAM,KAAI;AAAE,UAAK,CAACE,IAAEG,EAAC,IAAEJ,GAAE;AAAM,WAAM,EAAC,MAAKD,IAAE,MAAK,EAAC,GAAGA,IAAE,WAAU,CAACK,IAAER,GAAE,OAAO,CAAC,CAAC,EAAC,GAAE,OAAM,EAAC,GAAGG,IAAE,WAAU,CAACE,KAAEG,KAAER,GAAE,OAAO,CAAC,GAAEA,GAAE,OAAO,CAAC,CAAC,EAAC,EAAC;AAAA,EAAC;AAAC;;;ACAxuL,IAAMgB,KAAE;AAAR,IAAWC,KAAE;AAAb,IAAgBC,KAAE;AAAlB,IAAoB,IAAEC,GAAE;AAAE,IAAMC,KAAN,MAAM,WAAUC,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAEC,IAAEC,IAAEJ,IAAE;AAAC,UAAM,IAAIE,GAAE,CAAC,GAAE,CAACA,IAAEC,EAAC,CAAC,GAAEC,IAAE,MAAKH,EAAC,GAAE,KAAK,oBAAkBD;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK,WAAW;AAAA,EAAa;AAAA,EAAC,OAAO,OAAOC,IAAEC,IAAEC,IAAEC,IAAEJ,IAAEF,KAAEO,IAAEC,IAAE;AAAC,UAAMC,KAAE,IAAI,GAAEL,IAAEC,IAAEC,IAAEN,KAAEO,EAAC;AAAE,WAAOE,GAAE,YAAUN,GAAE,gBAAgB,IAAE,GAAEM,GAAE,cAAYD,IAAEC,GAAE,YAAUP,IAAEO;AAAA,EAAC;AAAA,EAAC,OAAON,IAAEC,IAAEC,IAAEC,IAAEJ,IAAEF,KAAE;AAAC,WAAO,KAAK,SAAS,OAAO,CAAC,IAAEG,IAAE,KAAK,SAAS,OAAO,CAAC,IAAEC,IAAE,KAAK,YAAUC,IAAE,KAAK,aAAWC,IAAE,KAAK,oBAAkBJ,IAAE,KAAK,cAAY,MAAK,KAAK,cAAYF,KAAE;AAAA,EAAI;AAAA,EAAC,SAAQ;AAAC,WAAM,EAAC,YAAW,EAAC,GAAG,KAAK,YAAW,aAAY,KAAK,UAAS,aAAY,MAAI,KAAK,WAAW,gBAAc,KAAK,cAAY,KAAI,GAAE,UAAS,EAAC,GAAE,KAAK,SAAS,OAAO,CAAC,GAAE,GAAE,KAAK,SAAS,OAAO,CAAC,EAAC,EAAC;AAAA,EAAC;AAAC;AAAC,SAASU,GAAEP,IAAE;AAAC,SAAO,oBAAkBA;AAAC;AAAC,IAAMQ,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYR,IAAEC,IAAEC,IAAEJ,IAAE;AAAC,UAAME,IAAEE,EAAC,GAAE,KAAK,OAAK,WAAU,KAAK,SAAO,IAAI,KAAE,KAAK,gBAAc,eAAc,KAAK,iBAAe,GAAE,KAAK,gBAAc,GAAE,KAAK,aAAW,GAAE,KAAK,wBAAsB,CAAC,GAAE,KAAK,+BAA6B,OAAG,KAAK,cAAY,CAAC,GAAE,KAAK,YAAU,oBAAI,OAAI,KAAK,SAAO,oBAAI,OAAI,KAAK,eAAaJ,IAAE,KAAK,eAAaE,GAAE,cAAa,KAAK,oBAAkBC,IAAE,KAAK,0BAAwBQ,GAAER,IAAEQ,GAAE,KAAK,GAAE,KAAK,SAAS,UAAQP,GAAE,UAAUA,GAAE,aAAa,CAAC,GAAE,KAAK,SAAS,WAASA,GAAE,UAAUA,GAAE,aAAa,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,MAAM,QAAQ;AAAA,EAAC;AAAA,EAAC,IAAI,0BAAyB;AAAC,WAAO,KAAK;AAAA,EAAiB;AAAA,EAAC,IAAI,SAAQ;AAAC,WAAO,KAAK;AAAA,EAAO;AAAA,EAAC,MAAM,aAAaH,IAAEE,IAAE;AAAC,UAAMC,KAAE,KAAK;AAAQ,QAAG;AAAC,YAAM,MAAM,aAAaH,IAAEE,EAAC,GAAE,MAAM,KAAK;AAAA,IAAuB,SAAOI,IAAE;AAAA,IAAC;AAAC,SAAK,UAAQ,KAAK,QAAQ,OAAO;AAAO,UAAMD,KAAE,EAAEF,IAAED,EAAC;AAAE,IAAAA,OAAI,CAAC,EAAEG,EAAC,KAAGL,GAAE,UAAQA,GAAE,QAAQ,aAAWH,GAAEQ,IAAE,eAAe,KAAG,CAAC,KAAK,SAAOL,GAAE,YAAU,KAAK,SAAO,KAAK,MAAM,QAAQ,GAAE,KAAK,QAAM,IAAI,EAAE,KAAK,kBAAiB,KAAK,YAAY,GAAE,KAAK,aAAa,GAAE,IAAI,sBAAsB,KAAG,QAAQ,MAAM,kDAAkD,IAAG,IAAI,sBAAsB,KAAG,QAAQ,MAAM,mCAAkCK,EAAC,GAAEL,GAAE,QAAQE,GAAE,IAAI,IAAE,MAAGF,GAAE,OAAK,OAAG,KAAK,wBAAsB,CAAC,KAAGG,OAAIH,GAAE,OAAK;AAAA,EAAG;AAAA,EAAC,QAAO;AAAC,SAAK,aAAa;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEC,IAAE;AAAC,SAAK,SAAS,SAAS,WAAY,CAAAC,OAAG;AAAC,UAAG,CAACF,GAAE,IAAIE,EAAC,GAAE;AAAC,cAAMF,KAAEC,GAAE,wBAAwBC,EAAC;AAAE,aAAK,QAAQF,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAEC,IAAEC,IAAE;AAAC,SAAK,UAAU,QAAS,CAACC,IAAEJ,OAAI;AAAC,MAAAI,MAAGA,GAAE,cAAYD,OAAIF,GAAE,iBAAiBG,GAAE,SAAS,GAAEF,GAAE,mBAAmBE,GAAE,SAAS,GAAE,KAAK,UAAU,OAAOJ,EAAC;AAAA,IAAE,CAAE;AAAA,EAAC;AAAA,EAAC,WAAWC,IAAEE,IAAEC,IAAEJ,IAAEF,MAAE,MAAG;AAAC,QAAG,CAAC,KAAK,WAAS,EAAEK,GAAE,WAAW,EAAE,QAAOA;AAAE,SAAK,OAAO,KAAK,SAAS;AAAE,UAAME,KAAE,KAAK,eAAeJ,IAAE,KAAK,iBAAiB;AAAE;AAAC,YAAMA,KAAEE,GAAE,YAAY,UAAU;AAAE,aAAKF,GAAE,KAAK,IAAG,MAAK,QAAQA,IAAED,EAAC;AAAA,IAAC;AAAC,QAAGG,GAAE,OAAO,QAAM,CAACL,IAAE,QAAOK;AAAE,UAAMG,KAAE,IAAI,SAAMC,KAAE,KAAK,QAAQ,OAAO;AAAc,SAAK,oBAAoBD,IAAEL,IAAEM,IAAEH,IAAEC,EAAC,GAAEF,GAAE,cAAYO,GAAE,sBAAsBJ,IAAE,KAAK,YAAY,GAAEH,GAAE,YAAY,cAAcC,EAAC,GAAED,GAAE,QAAM,MAAGA,GAAE,MAAI;AAAG;AAAC,YAAMD,KAAEC,GAAE,YAAY,UAAU;AAAE,aAAKD,GAAE,KAAK,KAAG;AAAC,cAAMC,MAAED,GAAE,aAAa;AAAE,aAAK,SAAS,SAAS,MAAMC,GAAC,GAAE,KAAK,sBAAsBC,IAAEF,IAAEC,KAAEF,GAAE,KAAK;AAAA,MAAC;AAAA,IAAC;AAAC,WAAO,KAAK,gCAA8BE,GAAE,QAAM,KAAK,OAAO,KAAK,sBAAqB,EAAC,aAAY,KAAK,sBAAqB,CAAC,GAAE,KAAK,+BAA6B,QAAIA;AAAA,EAAC;AAAA,EAAC,aAAa,EAAC,OAAMF,IAAE,SAAQC,GAAC,GAAE;AAAC,QAAGD,GAAE,QAAO;AAAC,YAAMC,KAAED,GAAE,CAAC,EAAE;AAAM,WAAK,aAAWC,IAAE,KAAK,iBAAiBA,EAAC;AAAA,IAAC;AAAC,QAAG,CAAC,KAAK,QAAQ;AAAO,UAAMC,KAAE,KAAK,QAAQ,OAAO;AAAc,IAAAD,GAAE,QAAS,CAAAD,OAAG;AAAC,WAAK,OAAO,OAAOA,GAAE,IAAI,EAAE,GAAE,KAAK,6BAA6BA,IAAEE,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,aAAaF,IAAE;AAAC,eAAUC,MAAK,KAAK,UAAU,OAAO,EAAE,OAAIA,MAAA,gBAAAA,GAAG,aAAUK,QAAKN,KAAEM,IAAG,QAAOL,GAAE,OAAO;AAAE,WAAO;AAAA,EAAI;AAAA,EAAC,gBAAe;AAAC,UAAMD,KAAE,CAAC;AAAE,eAAUC,MAAK,KAAK,UAAU,OAAO,EAAE,EAAAA,MAAA,gBAAAA,GAAG,eAAY,KAAK,cAAYD,GAAE,KAAKC,GAAE,OAAO,CAAC;AAAE,WAAOD;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAMC,KAAE,KAAK,UAAU,IAAID,EAAC;AAAE,WAAOC,KAAEA,GAAE,YAAU;AAAA,EAAI;AAAA,EAAC,iCAAiCD,IAAE;AAAC,UAAMC,KAAE,KAAK,UAAU,IAAID,EAAC;AAAE,WAAOC,KAAE,KAAK,MAAM,oBAAoBA,GAAE,iBAAiB,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,2BAA2BD,IAAE;AAAC,eAAUC,MAAK,KAAK,UAAU,OAAO,EAAE,MAAGA,MAAA,gBAAAA,GAAG,iBAAcD,GAAE,QAAOC,GAAE;AAAU,WAAO;AAAA,EAAI;AAAA,EAAC,0BAAyB;AAAC,WAAO,KAAK;AAAA,EAAqB;AAAA,EAAC,QAAQD,IAAE;AAAC,SAAK,UAAU,QAAS,CAAAC,OAAG;AAAC,UAAG,CAACA,GAAE;AAAO,YAAMC,KAAED,GAAE,OAAO,GAAEE,KAAEM,GAAE,aAAa,CAACP,EAAC,GAAE,EAAC,eAAc,KAAK,eAAc,eAAc,MAAK,cAAa,KAAK,aAAa,cAAa,QAAO,KAAK,OAAM,CAAC,EAAE,UAAU;AAAE,MAAAC,GAAE,KAAK,GAAEH,GAAEG,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,gBAAgBH,IAAEC,IAAE;AAAA,EAAC;AAAA,EAAC,cAAcD,IAAEE,IAAE;AAAC,UAAK,EAAC,MAAKC,IAAE,MAAKJ,GAAC,IAAE,KAAK;AAAa,eAAUF,OAAKG,IAAE;AAAC,YAAMA,KAAE,GAAE,GAAEH,IAAE,aAAa,GAAEE,IAAEI,EAAC;AAAE,QAAEH,EAAC,KAAGE,GAAEF,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,OAAM;AAAC,QAAIA,KAAE;AAAE,WAAO,KAAK,QAAS,CAAAC,OAAGD,IAAI,GAAEA;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,SAAK,SAAS,SAAS,MAAM,GAAE,KAAK,SAAS,SAAS,MAAM,GAAE,KAAK,SAAO,KAAK,MAAM,MAAM;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAE;AAAC,UAAMC,KAAED,GAAE,aAAa,GAAEE,KAAEF,GAAE,aAAa,GAAEG,KAAEH,GAAE,aAAa,GAAED,KAAE,KAAK,YAAY,IAAEE,EAAC,GAAEJ,MAAE,KAAK,YAAY,IAAEI,KAAE,CAAC;AAAE,SAAK,SAAS,SAAS,IAAIA,EAAC,MAAI,KAAK,SAAS,SAAS,MAAMA,EAAC,GAAE,KAAK,MAAM,aAAaD,IAAEE,IAAEC,IAAEJ,IAAEF,KAAE,KAAK,aAAa;AAAA,EAAE;AAAA,EAAC,QAAQG,IAAEC,IAAE;AAAC,UAAMC,KAAEF,GAAE,aAAa,GAAEG,KAAE,KAAK,SAAS,UAASJ,KAAEE,GAAE,UAAUC,EAAC;AAAE,QAAGH,OAAII,GAAE,IAAID,EAAC,EAAE;AAAO,QAAG,CAACH,GAAE,QAAO,KAAK,KAAK,QAAQC,EAAC;AAAE,UAAMH,MAAEG,GAAE,aAAa,GAAEI,KAAEJ,GAAE,aAAa;AAAE,QAAG,CAAC,KAAK,YAAYE,IAAEL,KAAEO,EAAC,EAAE;AAAO,UAAMC,KAAE,KAAK,YAAY,IAAEH,EAAC,GAAEI,KAAE,KAAK,YAAY,IAAEJ,KAAE,CAAC;AAAE,SAAK,MAAM,aAAaF,IAAEE,IAAEL,KAAEO,IAAEC,IAAEC,IAAE,KAAK,aAAa,GAAEH,GAAE,IAAID,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYF,IAAEC,IAAEC,IAAE;AAAC,QAAG,KAAK,SAAS,QAAQ,IAAIF,EAAC,EAAE,QAAM;AAAG,UAAMG,KAAE,KAAK;AAAY,QAAG,KAAK,kBAAkB,eAAc;AAAC,YAAMJ,KAAEQ,GAAEN,KAAEC,GAAE,MAAM,GAAEL,MAAEE,KAAE,MAAI,KAAK,OAAOA,KAAE,OAAK,GAAG,GAAEK,KAAEG,GAAE,KAAK,KAAG,IAAE,IAAE,KAAK,KAAK,KAAK,IAAI,CAACL,KAAEA,GAAE,MAAM,CAAC,CAAC;AAAE,MAAAS,GAAER,IAAEH,IAAEI,IAAEP,KAAEH,EAAC;AAAA,IAAC,OAAK;AAAC,YAAMK,KAAEa,GAAE,EAAC,GAAEX,IAAE,GAAEC,GAAC,GAAE,KAAK,mBAAkBQ,GAAE,KAAK;AAAE,UAAG,CAACX,GAAE,QAAM;AAAG,MAAAY,GAAER,IAAEH,IAAED,GAAE,GAAEA,GAAE,GAAEL,EAAC;AAAA,IAAC;AAAC,WAAO,KAAK,SAAS,QAAQ,IAAIM,EAAC,GAAE;AAAA,EAAE;AAAA,EAAC,oBAAoBA,IAAED,IAAEF,KAAEO,IAAEC,IAAEC,KAAE,MAAG;AAAC,UAAMO,KAAE,KAAK,QAAQ,OAAO,oBAAmBC,KAAE,IAAEjB,KAAEe,KAAE,KAAK,KAAK,KAAGb,GAAE,IAAI,QAAMA,KAAEe,EAAC,IAAE,GAAEL,KAAE,KAAK,KAAKI,KAAEC,EAAC,IAAE,GAAEC,KAAE,KAAK,KAAKhB,KAAEe,EAAC,GAAE,EAAC,KAAIE,IAAE,KAAIC,GAAC,IAAElB,GAAE,KAAImB,KAAED,KAAElB,IAAEY,KAAEK,KAAEjB,IAAEoB,KAAE,KAAK,MAAMD,KAAEJ,EAAC,IAAEL,IAAEW,KAAE,KAAK,MAAMT,KAAEG,EAAC,IAAEL,IAAEY,KAAEF,KAAEJ,KAAE,IAAEN,IAAE,IAAEW,KAAEL,KAAE,IAAEN,IAAEa,KAAEvB,GAAE,aAAa,aAAaA,GAAE,IAAI,KAAK;AAAE,aAAQwB,KAAEJ,IAAEI,MAAGF,IAAEE,KAAI,UAAQ1B,MAAEuB,IAAEvB,OAAG,GAAEA,OAAI;AAAC,UAAIgB,KAAEU;AAAE,MAAAD,GAAE,SAAOT,KAAEU,KAAE,IAAEA,KAAEX,KAAEW,KAAEX;AAAG,YAAME,KAAEQ,GAAE,QAAMC,KAAE,GAAEd,MAAEa,GAAE,QAAMC,KAAEX,OAAIW,IAAER,KAAE,KAAK,eAAeX,IAAEkB,IAAEvB,GAAE,IAAI,OAAMc,IAAEhB,KAAEE,EAAC;AAAE,UAAG,EAAEgB,EAAC,GAAE;AAAC,cAAMhB,KAAE,EAAEM,IAAG,CAAAL,OAAGc,KAAEd,GAAE,OAAKS,MAAET,GAAE,QAAMA,GAAE,IAAK;AAAE,YAAGM,MAAG,EAAEP,EAAC,EAAE;AAAS,YAAG,CAACgB,GAAE,MAAM;AAAS,YAAG,EAAEhB,EAAC,KAAGO,IAAE;AAAC,gBAAML,KAAEc,GAAE,SAAS,MAAM;AAAE,cAAIZ,KAAEY,GAAE;AAAW,UAAAd,GAAE,OAAO,CAAC,IAAE,EAAEF,IAAEE,GAAE,OAAO,CAAC,CAAC,GAAEA,GAAE,OAAO,CAAC,IAAE,EAAEF,IAAEE,GAAE,OAAO,CAAC,CAAC,GAAE,MAAIc,GAAE,SAAO,EAAEA,GAAE,WAAW,MAAIZ,KAAE,EAAC,GAAGY,GAAE,YAAW,aAAYA,GAAE,YAAW;AAAG,gBAAMlB,MAAE,IAAIK,GAAED,IAAEE,EAAC;AAAE,UAAAN,IAAE,YAAUkB,GAAE,WAAUf,GAAE,KAAKH,GAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBG,IAAE;AAAC,WAAO,KAAK,IAAI,KAAK,KAAKA,KAAE,IAAE,CAAC,GAAEN,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBM,IAAE;AAAC,UAAMC,KAAE,KAAK,iBAAiBD,EAAC,GAAEE,MAAG,KAAK,MAAMD,KAAEL,EAAC,IAAE,KAAGA,KAAE;AAAE,QAAG,KAAK,kBAAgBM,GAAE,QAAO,KAAK,gBAAcA,IAAE,KAAK,aAAa,GAAE,KAAK,KAAK,SAAS,QAAQ,MAAM;AAAA,EAAC;AAAA,EAAC,eAAeF,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAC,gBAAe,aAAY,OAAM,CAACF,GAAE,YAAWA,GAAE,UAAU,GAAE,WAAU,CAACA,GAAE,OAAO,CAAC,GAAEA,GAAE,OAAO,CAAC,CAAC,EAAC,GAAEG,KAAE,EAAEF,EAAC;AAAE,QAAG,CAACE,GAAE,QAAM,EAAC,MAAKD,IAAE,MAAK,MAAK,OAAM,KAAI;AAAE,UAAK,CAACH,IAAEF,GAAC,IAAEM,GAAE;AAAM,WAAM,EAAC,MAAKD,IAAE,MAAK,EAAC,GAAGA,IAAE,WAAU,CAACL,KAAEG,GAAE,OAAO,CAAC,CAAC,EAAC,GAAE,OAAM,EAAC,GAAGE,IAAE,WAAU,CAACH,KAAEF,MAAEG,GAAE,OAAO,CAAC,GAAEA,GAAE,OAAO,CAAC,CAAC,EAAC,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAEC,IAAEC,IAAE;AAAC,YAAO,KAAGF,OAAI,MAAI,QAAMC,OAAI,KAAG,QAAMC;AAAA,EAAC;AAAA,EAAC,iBAAiBF,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,cAAcH,IAAEC,IAAEC,EAAC;AAAE,SAAK,UAAU,OAAOC,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBH,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,QAAQ,OAAO,eAAcJ,KAAE,IAAEI;AAAE,QAAIN,MAAEK,KAAE,IAAED,KAAEF,KAAEE,KAAEF,KAAEI;AAAE,UAAMC,KAAEF,KAAEH;AAAE,QAAIM,KAAER,MAAEE;AAAE,UAAMO,KAAEF,KAAEL,IAAEc,KAAE,KAAGb,GAAE,QAAMD;AAAE,IAAAC,GAAE,QAAMH,MAAE,MAAIA,MAAE,IAAGG,GAAE,QAAMK,KAAEQ,OAAIR,KAAEQ;AAAG,UAAMC,KAAEjB,MAAEE,IAAEa,KAAER,KAAEL,IAAEU,KAAEJ,KAAEN,IAAEyB,KAAElB,KAAEP;AAAE,WAAM,CAACC,GAAE,cAAcc,EAAC,GAAEd,GAAE,WAAWY,EAAC,GAAEZ,GAAE,cAAcS,EAAC,GAAET,GAAE,WAAWwB,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,YAAYxB,IAAEC,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAC,UAAS,GAAE,UAAS,EAAC;AAAE,WAAO,EAAEA,IAAEF,IAAED,IAAEE,EAAC,GAAEC;AAAA,EAAC;AAAA,EAAC,kBAAkBH,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,iBAAiBF,GAAE,IAAI,KAAK;AAAE,QAAG,KAAK,kBAAkB,eAAc;AAAC,YAAK,CAACA,IAAEG,IAAEJ,IAAEF,GAAC,IAAEI,IAAEG,KAAE,EAAC,GAAEJ,IAAE,GAAEG,GAAC,GAAEE,KAAE,EAAC,GAAEN,IAAE,GAAEF,IAAC;AAAE,UAAIgB,KAAE,GAAED,KAAE,GAAEH,MAAE,GAAEe,MAAE;AAAE;AAAC,cAAMxB,KAAEO,GAAEH,GAAE,IAAEF,GAAE,MAAM;AAAE,QAAAW,KAAEb,KAAE,MAAI,KAAK,OAAOA,KAAE,OAAK,GAAG,GAAEY,KAAEL,GAAE,KAAK,KAAG,IAAE,IAAE,KAAK,KAAK,KAAK,IAAI,CAACH,GAAE,IAAEF,GAAE,MAAM,CAAC,CAAC;AAAA,MAAC;AAAC;AAAC,cAAMF,KAAEO,GAAEF,GAAE,IAAEH,GAAE,MAAM;AAAE,QAAAO,MAAET,KAAE,MAAI,KAAK,OAAOA,KAAE,OAAK,GAAG,GAAEwB,MAAEjB,GAAE,KAAK,KAAG,IAAE,IAAE,KAAK,KAAK,KAAK,IAAI,CAACF,GAAE,IAAEH,GAAE,MAAM,CAAC,CAAC;AAAA,MAAC;AAAC,YAAMQ,MAAE,EAAC,UAAS,GAAE,UAAS,EAAC,GAAEK,KAAE,EAAC,UAAS,GAAE,UAAS,EAAC;AAAE,QAAEL,KAAEE,IAAEC,IAAEX,EAAC,GAAE,EAAEa,IAAES,KAAEf,KAAEP,EAAC;AAAE,aAAM,EAAC,QAAO,CAACF,IAAEG,IAAEJ,IAAEF,GAAC,GAAE,eAAc,EAAC,KAAIa,IAAE,UAAS,KAAIA,IAAE,UAAS,KAAIK,GAAE,UAAS,KAAIA,GAAE,SAAQ,GAAE,OAAMb,GAAC;AAAA,IAAC;AAAC,UAAMC,KAAEoB,GAAE,WAAW,EAAE,WAAWtB,IAAE,KAAK,iBAAiB,CAAC,GAAEF,KAAEa,GAAET,IAAE,KAAK,mBAAkBO,GAAE,OAAM,EAAC,mBAAkBV,GAAE,aAAWL,GAAC,CAAC;AAAE,QAAG,CAACI,GAAE,QAAO;AAAK,UAAMF,MAAE,EAAE,IAAII,MAAEF,IAAE,OAAG,KAAE,GAAEK,KAAEP,IAAE,OAAO,OAAQ,CAACG,IAAEC,OAAI,EAAEA,KAAE,EAAG,GAAEI,KAAER,IAAE,OAAO,OAAQ,CAACG,IAAEC,OAAIA,KAAE,CAAE,GAAEY,KAAE,KAAK,IAAI,GAAGT,EAAC,GAAEQ,KAAE,KAAK,IAAI,GAAGP,EAAC,GAAEI,KAAE,KAAK,IAAI,GAAGL,EAAC,GAAEoB,KAAE,KAAK,IAAI,GAAGnB,EAAC,GAAEK,MAAE,KAAK,YAAYG,IAAED,IAAEV,EAAC,GAAEc,KAAE,KAAK,YAAYP,IAAEe,IAAEtB,EAAC;AAAE,WAAM,EAAC,QAAOD,IAAE,eAAc,EAAC,KAAIS,IAAE,UAAS,KAAIA,IAAE,UAAS,KAAIM,GAAE,UAAS,KAAIA,GAAE,SAAQ,GAAE,OAAMd,GAAC;AAAA,EAAC;AAAA,EAAC,eAAeF,IAAEG,IAAEJ,IAAEF,KAAEO,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,cAAcP,IAAEF,KAAEO,EAAC,GAAES,KAAE,KAAK,UAAU,IAAIP,EAAC,GAAEQ,KAAE,KAAK,kBAAkBX,IAAEN,KAAEO,EAAC,GAAEQ,KAAE,KAAK,kBAAkBP,IAAES,EAAC;AAAE,QAAG,EAAEF,EAAC,EAAE,QAAO;AAAK,UAAMH,KAAE,KAAK,MAAM,oBAAoBG,EAAC,GAAE,EAAC,OAAMY,IAAE,QAAOd,KAAE,QAAOK,IAAE,aAAYC,GAAC,IAAEP,IAAEgB,KAAED,KAAEd,MAAEc,KAAE,GAAEP,KAAEO,KAAET,KAAES,KAAE;AAAE,QAAG,MAAIA,GAAE,QAAO,KAAK,UAAU,IAAIlB,IAAE,IAAI,GAAE;AAAK,UAAMY,KAAE,EAAC,eAAcM,IAAE,GAAGf,GAAE,WAAU,GAAEE,KAAE,EAAEE,EAAC,IAAEA,GAAE,OAAOY,IAAER,IAAElB,IAAEmB,IAAEN,IAAEI,EAAC,IAAElB,GAAE,OAAOE,IAAEM,IAAEmB,IAAER,IAAElB,IAAEmB,IAAEN,IAAEI,EAAC;AAAE,QAAG,MAAIQ,IAAE;AAAC,YAAK,CAACxB,IAAEC,IAAEC,IAAEC,EAAC,IAAEW;AAAE,MAAAH,GAAE,SAAS,OAAO,CAAC,KAAGX,KAAEE,MAAG,GAAES,GAAE,SAAS,OAAO,CAAC,KAAGV,KAAEE,MAAG;AAAA,IAAC;AAAC,WAAO,KAAK,UAAU,IAAIG,IAAEK,EAAC,GAAE,KAAK,qCAAqCA,IAAEA,GAAE,SAAS,GAAEA;AAAA,EAAC;AAAA,EAAC,qCAAqCX,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,sBAAsBD,EAAC,KAAG,EAAC,UAAS,IAAE,GAAE,UAAS,EAAC,GAAEE,KAAED,GAAE,UAASH,KAAEG,GAAE;AAAS,IAAAA,GAAE,WAAS,KAAK,IAAIC,IAAEH,GAAE,KAAK,GAAEE,GAAE,WAAS,KAAK,IAAIH,IAAEC,GAAE,KAAK,GAAE,KAAK,sBAAsBC,EAAC,IAAEC,IAAEC,OAAID,GAAE,YAAUH,OAAIG,GAAE,aAAW,KAAK,+BAA6B;AAAA,EAAG;AAAA,EAAC,6BAA6BF,IAAEC,IAAE;AAAC,UAAMC,KAAE,IAAED,IAAEE,KAAE,KAAK,KAAKJ,KAAEG,EAAC,GAAE,EAAC,KAAIH,IAAE,KAAIF,IAAC,IAAEG,GAAE,KAAII,KAAEP,MAAEE,IAAEM,KAAEN,KAAEA,IAAEO,KAAE,KAAK,MAAMF,KAAEF,EAAC,GAAEW,KAAE,KAAK,MAAMR,KAAEH,EAAC;AAAE,aAAQY,KAAER,IAAEQ,KAAER,KAAEH,IAAEW,KAAI,UAAQb,KAAEY,IAAEZ,KAAEY,KAAEV,IAAEF,KAAI,MAAK,iBAAiBD,GAAE,IAAI,OAAMc,IAAEb,EAAC;AAAA,EAAC;AAAC;;;ACAx0S,IAAMyB,KAAE;AAAR,IAAY,IAAE;AAAd,IAAmEC,KAAE;AAArE,IAA6HC,KAAE;AAA0C,SAASC,GAAEC,IAAE;AAAC,SAAM,yBAAuBA,GAAE;AAAI;AAAC,SAASC,GAAED,IAAE;AAAC,MAAG,CAAC,EAAEA,EAAC,KAAG,CAACD,GAAEC,EAAC,EAAE,OAAMA;AAAC;AAAC,SAASE,GAAEF,IAAE;AAAC,SAAM,cAAYA,GAAE,QAAM,eAAaA,GAAE;AAAI;AAAC,IAAIG,KAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,WAAS,IAAIC,MAAE,KAAK,kBAAgB,KAAK,SAAS,aAAa,GAAE,KAAK,eAAa,YAAY,IAAI,GAAE,KAAK,iBAAe,OAAG,KAAK,eAAa,OAAG,KAAK,kBAAgB,oBAAI,OAAI,KAAK,WAAS,OAAG,KAAK,iBAAe,GAAE,KAAK,YAAU,MAAK,KAAK,SAAO,MAAK,KAAK,YAAU,MAAK,KAAK,eAAa,MAAK,KAAK,UAAQ;AAAA,EAAI;AAAA,EAAC,aAAY;AAAC,SAAK,YAAY,GAAE,KAAK,YAAY,GAAE,KAAK,eAAa,IAAIC,GAAE,EAAC,aAAY,aAAW,KAAK,QAAQ,OAAK,IAAE,GAAE,SAAQ,CAACL,IAAEM,OAAI,KAAK,eAAeN,IAAE,EAAC,QAAOM,GAAC,CAAC,EAAC,CAAC,GAAE,KAAK,WAAW,CAAC,KAAK,UAAU,GAAG,UAAS,KAAK,aAAa,KAAK,IAAI,CAAC,GAAEC,GAAG,MAAI,CAAC,KAAK,UAAW,MAAI,KAAK,OAAO,CAAE,CAAC,CAAC,GAAE,KAAK,iBAAe,YAAa,MAAI,KAAK,aAAa,UAAU,GAAG,GAAG;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,UAAMP,KAAE,KAAK,UAAU,YAAWM,KAAE,MAAI,KAAK,gBAAc,KAAK,aAAa,SAAO,IAAGF,KAAE,CAACJ,IAAEM,QAAK,KAAK,eAAa,MAAG,KAAK,WAAWN,IAAEM,EAAC;AAAG,SAAK,UAAQE,GAAE,KAAK,SAAQ,KAAK,kBAAiBR,IAAEI,IAAEE,IAAE,KAAK,YAAY,GAAE,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,QAAG,aAAW,KAAK,QAAQ,MAAK;AAAC,YAAMN,KAAE,KAAK,QAAQ,QAAOM,KAAE,KAAK;AAAQ,WAAK,WAAW,CAACD,GAAG,MAAIC,GAAE,kBAAmB,CAAAN,OAAG,KAAK,aAAa,OAAO,eAAc,EAAC,cAAa,oBAAmB,OAAMA,GAAC,CAAC,EAAE,MAAMC,EAAC,GAAG,EAAC,SAAQ,KAAE,CAAC,GAAEI,GAAG,MAAIC,GAAE,aAAc,CAAAN,OAAG,KAAK,aAAa,OAAO,eAAc,EAAC,cAAa,eAAc,OAAMA,GAAC,CAAC,EAAE,MAAMC,EAAC,GAAG,EAAC,SAAQ,KAAE,CAAC,GAAED,GAAE,GAAG,iBAAiB,CAAAA,OAAG,KAAK,aAAa,OAAO,aAAY,EAAC,MAAK,iBAAgB,OAAM,EAAC,YAAWA,GAAE,YAAW,UAASA,GAAE,UAAS,UAASA,GAAE,SAAQ,EAAC,CAAC,EAAE,MAAMC,EAAC,CAAE,GAAED,GAAE,GAAG,oBAAoB,CAAAA,OAAG,KAAK,aAAa,OAAO,aAAY,EAAC,MAAK,oBAAmB,OAAMA,GAAC,CAAC,EAAE,MAAMC,EAAC,CAAE,GAAED,GAAE,GAAG,cAAc,CAAAA,OAAG,KAAK,aAAa,OAAO,aAAY,EAAC,MAAK,eAAc,OAAM,EAAC,GAAGA,GAAC,EAAC,CAAC,EAAE,MAAMC,EAAC,CAAE,CAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBD,IAAE;AAAC,SAAK,mBAAiB,KAAK,iBAAe,IAAI,EAAE,EAAC,MAAK,UAAS,YAAW,CAACA,IAAEM,OAAIG,GAAE,KAAK,aAAa,OAAO,GAAET,IAAE,EAAC,QAAOM,GAAC,CAAC,EAAE,MAAML,EAAC,CAAC,GAAE,QAAO,CAACD,IAAEM,OAAIG,GAAE,KAAK,aAAa,OAAOZ,IAAEG,IAAE,EAAC,QAAOM,GAAC,CAAC,EAAE,MAAML,EAAC,CAAC,GAAE,QAAO,CAAAD,OAAGS,GAAE,KAAK,aAAa,OAAOX,IAAE,QAAO,EAAC,QAAOE,GAAC,CAAC,EAAE,MAAMC,EAAC,CAAC,EAAC,GAAED,IAAG,MAAI,KAAK,aAAa,UAAU,CAAE;AAAA,EAAE;AAAA,EAAC,cAAa;AAAC,UAAMA,KAAE,eAAa,KAAK,QAAQ,OAAK,aAAW,aAAYM,KAAE,EAAC,cAAa,EAAC,cAAa,KAAK,QAAQ,cAAa,MAAK,OAAG,MAAK,MAAE,GAAE,kBAAiB,KAAK,kBAAiB,aAAY,KAAK,aAAY,QAAO,KAAK,QAAQ,OAAM;AAAE,SAAK,eAAa,IAAIG,GAAEH,IAAE,KAAK,UAASN,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAE;AAJxoI;AAIyoI,UAAMM,KAAE;AAAK,eAAK,uBAAL,mBAAyB,WAAU,KAAK,qBAAmB,IAAI,GAAE,EAAC,sBAAqBN,GAAE,OAAO,OAAO,wBAAsB,QAAO,QAAO,KAAK,QAAQ,QAAO,cAAa,KAAK,QAAQ,cAAa,eAAc,KAAK,QAAQ,eAAc,MAAK,OAAG,MAAK,OAAG,kBAAiB,KAAK,iBAAiB,OAAO,GAAE,qBAAoB,MAAG,cAAa,KAAK,cAAa,kBAAiB,EAAC,oBAAoBA,IAAE;AAAC,UAAG,EAAEM,GAAE,cAAc,EAAE,QAAM,CAAC;AAAE,aAAOA,GAAE,eAAe,iCAAiCN,EAAC,EAAE,IAAK,CAAAA,OAAGM,GAAE,YAAYN,EAAC,CAAE;AAAA,IAAC,EAAC,GAAE,UAAS,KAAK,QAAQ,SAAQ,CAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BA,IAAEM,IAAE;AAJ5tJ;AAI6tJ,SAAG,UAAK,yBAAL,mBAA2B,WAAU,EAAEN,EAAC,EAAE;AAAO,UAAMU,KAAEJ,GAAE,QAAQ,UAAU,OAAO,OAAO,MAAM;AAAE,SAAK,uBAAqB,IAAI,GAAE,EAAC,sBAAqB,QAAO,QAAOI,IAAE,cAAaV,GAAE,aAAa,cAAa,eAAcA,GAAE,eAAc,MAAKA,GAAE,aAAa,MAAK,MAAKA,GAAE,aAAa,MAAK,kBAAiB,KAAK,iBAAiB,OAAO,GAAE,qBAAoB,OAAG,cAAaA,IAAE,kBAAiB,EAAC,qBAAoB,CAAAA,OAAG,CAAC,EAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAJhpK;AAIipK,SAAK,aAAa,QAAQ,GAAE,KAAK,QAAQ,QAAQ,IAAE,UAAK,uBAAL,mBAAyB,YAAU,UAAK,yBAAL,mBAA2B,YAAU,UAAK,mBAAL,mBAAqB;AAAU,eAAUA,MAAK,KAAK,UAAU,MAAM,MAAK,QAAQ,YAAYA,EAAC;AAAE,kBAAc,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,IAAII,GAAE,KAAK,QAAQ,MAAM;AAAA,EAAC;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK,UAAU,WAAW;AAAA,EAAgB;AAAA,EAAC,IAAI,WAAU;AAAC,WAAO,KAAK,WAAW;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,UAAMJ,KAAE,KAAK,QAAQ,UAASM,KAAE,CAAC,CAAC,KAAK,aAAa,QAAOF,KAAE,CAAC,KAAK,kBAAgB,KAAK,eAAe,WAAW,GAAEM,KAAEV,MAAGM,MAAGF;AAAE,WAAO,IAAI,sBAAsB,KAAG,QAAQ,IAAI,iCAAiCM,EAAC;AAAA,sBAAyBV,EAAC;AAAA,mBAAsBM,EAAC;AAAA,8BAAiCF,EAAC;AAAA,CAAI,GAAEM;AAAA,EAAC;AAAA,EAAC,uBAAuBV,IAAE;AAAC,iBAAW,KAAK,QAAQ,QAAM,KAAK,QAAQ,uBAAuBA,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,SAAK,QAAQ,YAAYA,GAAE,MAAKA,GAAE,KAAK;AAAA,EAAC;AAAA,EAAC,QAAO;AAAC,SAAK,aAAa,MAAM,GAAE,KAAK,aAAa,MAAM;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,aAAa,OAAO;AAAA,EAAC;AAAA,EAAC,cAAa;AAAC,iBAAW,KAAK,QAAQ,QAAM,KAAK,QAAQ,YAAY;AAAA,EAAC;AAAA,EAAC,eAAc;AAAC,iBAAW,KAAK,QAAQ,QAAM,KAAK,QAAQ,aAAa;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,iBAAW,KAAK,QAAQ,QAAM,KAAK,QAAQ,oBAAoBA,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,iBAAW,KAAK,QAAQ,QAAM,KAAK,QAAQ,oBAAoBA,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAJ17M;AAI27M,UAAMM,MAAE,WAAAN,GAAE,OAAO,YAAT,mBAAkB,cAAlB,mBAA6B,MAAKI,KAAE,EAAE,KAAK,QAAQ,CAAAJ,OAAC;AAJv/M,UAAAW,KAAAC;AAIy/M,cAAAA,OAAAD,MAAAX,GAAE,OAAO,YAAT,gBAAAW,IAAkB,cAAlB,gBAAAC,IAA6B;AAAA,KAAK;AAAE,QAAGR,OAAIE,OAAI,EAAE,KAAK,cAAc,MAAI,KAAK,cAAc,oBAAoB,GAAE,KAAK,eAAe,QAAQ,GAAE,KAAK,iBAAe,OAAMA,KAAG;AAAC,cAAOA,IAAE;AAAA,QAAC,KAAI,WAAU;AAAC,gBAAMN,KAAE,EAAC,cAAa,EAAC,cAAa,qBAAoB,MAAK,OAAG,MAAK,MAAE,GAAE,kBAAiB,KAAK,kBAAiB,aAAY,KAAK,aAAY,QAAO,KAAK,QAAQ,OAAM;AAAE,eAAK,iBAAe,IAAIa,GAAEb,IAAE,KAAK,kBAAiB,KAAK,UAAS,KAAK,OAAO,GAAE,KAAK,WAAW,KAAK,eAAe,OAAO,GAAG,sBAAsB,CAAAA,OAAG;AAAC,iBAAK,aAAa,OAAO,aAAY,EAAC,MAAK,sBAAqB,OAAM,EAAC,aAAYA,GAAE,YAAW,EAAC,CAAC,EAAE,MAAMC,EAAC;AAAA,UAAC,CAAE,GAAE,oBAAoB;AAAE;AAAA,QAAK;AAAA,QAAC,KAAI,OAAM;AAAC,gBAAMD,KAAE,EAAC,cAAa,EAAC,cAAa,uBAAsB,MAAK,OAAG,MAAK,MAAE,GAAE,kBAAiB,KAAK,kBAAiB,aAAY,KAAK,aAAY,QAAO,KAAK,QAAQ,OAAM;AAAE,eAAK,iBAAe,IAAI,EAAEA,IAAE,KAAK,kBAAiB,KAAK,UAAS,KAAK,OAAO;AAAE;AAAA,QAAK;AAAA,MAAC;AAAC,WAAK,eAAe,aAAa,EAAC,OAAM,KAAK,UAAU,OAAM,SAAQ,CAAC,EAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,OAAOA,IAAEM,IAAE;AAAC,SAAK,kBAAiB,KAAK,iBAAiBA,EAAC,GAAE,KAAK,oBAAoBA,EAAC,GAAE,KAAK,MAAM,GAAE,MAAM,QAAQ,IAAI,CAAC,KAAK,QAAQ,OAAON,IAAEM,GAAE,OAAO,MAAM,GAAE,KAAK,aAAa,aAAaN,IAAEM,GAAE,OAAO,QAAQ,OAAO,GAAE,KAAK,eAAe,OAAON,IAAEM,EAAC,GAAE,KAAK,eAAe,cAAcN,IAAEM,IAAE,IAAI,CAAC,CAAC,GAAE,KAAK,oBAAoBA,EAAC,GAAE,EAAE,KAAK,cAAc,KAAG,MAAM,KAAK,eAAe,aAAaN,IAAEM,GAAE,OAAO,QAAQ,SAAS,GAAE,KAAK,0BAA0B,KAAK,gBAAeA,GAAE,MAAM,GAAE,IAAI,sBAAsB,KAAGN,GAAE,SAAS,GAAE,KAAK,KAAK,UAASM,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,YAAYN,IAAE;AAAC,IAAAA,GAAE,UAAQ,KAAK,gBAAe,IAAI,sBAAsB,KAAG,QAAQ,MAAM,mBAAmBA,GAAE,OAAO,EAAE,GAAEA,GAAE,QAAM,KAAK,WAAW,GAAE,KAAK,aAAa,OAAO,GAAE,MAAM,KAAK,QAAQ,YAAYA,EAAC,GAAE,KAAK,aAAa,UAAU,GAAE,MAAMH,GAAG,MAAI,CAAC,KAAK,QAAS,GAAE,EAAE,KAAK,cAAc,MAAI,MAAM,EAAE,EAAE,GAAE,MAAMA,GAAG,MAAI,CAAC,KAAK,QAAS;AAAA,EAAE;AAAA,EAAC,MAAM,QAAQ,EAAC,OAAMG,GAAC,GAAE;AAAC,QAAI,sBAAsB,KAAG,QAAQ,MAAM,kBAAiBA,EAAC,GAAE,KAAK,WAAS;AAAG,QAAG;AAAC,YAAMM,KAAEN,GAAE,QAAQ,IAAK,CAAAA,OAAGA,GAAE,YAAU,OAAKA,GAAE,WAASA,GAAE,WAAS,KAAK,0BAA0BA,GAAE,QAAQ,CAAE,GAAEI,KAAEJ,GAAE,cAAc,IAAK,CAAC,EAAC,UAASA,GAAC,MAAIA,EAAE;AAAE,WAAK,aAAa,WAAW,GAAE,MAAM,KAAK,QAAQ,KAAKI,IAAEE,EAAC,GAAE,KAAK,WAAW,GAAE,KAAK,aAAa,UAAU,GAAE,EAAE,KAAK,cAAc,KAAG,KAAK,eAAe,MAAM,GAAE,MAAM,KAAK,QAAQ,OAAO,GAAE,MAAMT,GAAG,MAAI,CAAC,KAAK,QAAS;AAAA,IAAC,SAAOS,IAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQN,IAAE;AAAC,QAAG,CAACA,GAAE,aAAY;AAAC,YAAMA,KAAEM,GAAE,MAAM;AAAE,aAAON,GAAE,QAAQ,UAAQ,MAAG,KAAK,YAAYA,EAAC;AAAA,IAAC;AAAC,SAAK,aAAa,WAAW,GAAE,KAAK,WAAW,GAAE,KAAK,QAAQ,QAAQ,KAAK,gBAAeA,EAAC,GAAE,KAAK,iBAAe,MAAG,KAAK,aAAa,UAAU,GAAE,MAAMH,GAAG,MAAI,CAAC,KAAK,QAAS;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,eAAUG,MAAK,KAAK,UAAU,MAAM,MAAK,UAAU,YAAYA,EAAC;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,MAAE,KAAK,cAAc,KAAG,KAAK,eAAe,aAAaA,EAAC;AAAE,eAAUM,MAAKN,GAAE,MAAM,MAAK,QAAQ,UAAUM,IAAE,KAAK,cAAc,GAAE,KAAK,SAAOA,GAAE;AAAM,eAAUA,MAAKN,GAAE,QAAQ,MAAK,QAAQ,YAAYM,EAAC,GAAE,KAAK,iBAAe,MAAG,KAAK,gBAAgB,IAAIA,GAAE,EAAE,MAAI,KAAK,gBAAgB,IAAIA,GAAE,EAAE,EAAE,QAAQ,GAAE,KAAK,gBAAgB,OAAOA,GAAE,EAAE;AAAG,SAAK,aAAa,UAAU;AAAA,EAAC;AAAA,EAAC,MAAM,SAAQ;AAAC,SAAK,iBAAe,KAAK,eAAa,QAAI,EAAE,KAAK,cAAc,KAAG,cAAY,KAAK,UAAU,SAAO,MAAM,KAAK,yBAAyB,IAAG,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,MAAM,uBAAuB,EAAC,OAAMN,IAAE,QAAOM,GAAC,GAAE;AAAC,WAAO,KAAK,mBAAmB,iCAAiCN,IAAEM,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,gCAAgC,EAAC,OAAMN,IAAE,QAAOM,GAAC,GAAE;AAAC,WAAO,KAAK,qBAAqB,iCAAiCN,IAAEM,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,kBAAkB,EAAC,OAAMN,IAAE,QAAOM,GAAC,GAAE;AAAC,WAAO,KAAK,mBAAmB,4BAA4BN,IAAEM,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,2BAA2B,EAAC,OAAMN,IAAE,QAAOM,GAAC,GAAE;AAAC,WAAO,KAAK,qBAAqB,4BAA4BN,IAAEM,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiB,EAAC,OAAMN,IAAE,QAAOM,GAAC,GAAE;AAAC,WAAO,KAAK,mBAAmB,2BAA2BN,IAAEM,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,0BAA0B,EAAC,OAAMN,IAAE,QAAOM,GAAC,GAAE;AAAC,WAAO,KAAK,qBAAqB,2BAA2BN,IAAEM,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,eAAe,EAAC,OAAMN,IAAE,QAAOM,GAAC,GAAE;AAAC,WAAO,KAAK,mBAAmB,yBAAyBN,IAAEM,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,wBAAwB,EAAC,OAAMN,IAAE,QAAOM,GAAC,GAAE;AAAC,WAAO,KAAK,qBAAqB,yBAAyBN,IAAEM,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYN,IAAE;AAAC,WAAO,KAAK,mBAAmB,sBAAsBA,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAE;AAAC,WAAO,KAAK,qBAAqB,aAAaA,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,WAAO,KAAK,qBAAqB,qBAAqBA,EAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAE;AAAC,WAAO,KAAK,qBAAqB,mBAAmBA,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,WAAO,KAAK,mBAAmB,aAAaA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,qBAAqBA,IAAE;AAAC,UAAMM,KAAE,MAAM,KAAK,mBAAmB,aAAaN,EAAC,GAAEI,KAAEE,GAAE;AAAkB,WAAOA,GAAE,WAASA,GAAE,SAAS,OAAQ,CAAAN,OAAG;AAAC,YAAMM,KAAEN,GAAE,WAAWI,EAAC,GAAEU,KAAE,KAAK,aAAaR,EAAC;AAAE,aAAO,EAAEQ,IAAG,CAAAd,OAAG,KAAK,eAAe,UAAUA,EAAC,CAAE;AAAA,IAAC,CAAE,GAAEM;AAAA,EAAC;AAAA,EAAC,kBAAkBN,IAAE;AAAC,WAAO,KAAK,mBAAmB,qBAAqBA,EAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAE;AAAC,WAAO,KAAK,mBAAmB,kCAAkCA,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAE;AAAC,WAAO,KAAK,mBAAmB,mBAAmBA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,kBAAiB;AAAC,WAAO,KAAK,aAAa;AAAA,EAAe;AAAA,EAAC,YAAYA,IAAE;AAAC,WAAO,KAAK,aAAa,eAAeA,IAAE,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAE;AAAC,QAAG,EAAE,KAAK,cAAc,GAAE;AAAC,YAAMM,KAAE,KAAK,eAAe,aAAaN,EAAC;AAAE,UAAG,EAAEM,EAAC,GAAE;AAAC,cAAMA,KAAE,KAAK,aAAa,gBAAgBN,EAAC;AAAE,eAAO,KAAK,eAAe,2BAA2BM,EAAC;AAAA,MAAC;AAAC,aAAOA;AAAA,IAAC;AAAC,WAAO,KAAK,aAAa,gBAAgBN,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAE;AAAC,UAAMM,KAAE,CAAC,GAAEF,KAAE,CAAC;AAAE,eAAUM,MAAKV,IAAE;AAAC,YAAMA,KAAE,EAAE,KAAK,cAAc,IAAE,KAAK,aAAaU,EAAC,IAAE;AAAK,UAAG,EAAEV,EAAC,EAAE,KAAG,EAAEA,GAAE,WAAW,WAAW,GAAE;AAAC,cAAMI,KAAE,KAAK,WAAWJ,GAAE,WAAW,WAAW;AAAE,UAAEI,EAAC,KAAGE,GAAE,KAAKF,EAAC;AAAA,MAAC,MAAM,CAAAA,GAAE,KAAKJ,EAAC;AAAA,WAAM;AAAC,cAAMA,KAAE,KAAK,WAAWU,EAAC;AAAE,UAAEV,EAAC,KAAGM,GAAE,KAAKN,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,WAAM,EAAC,UAASM,IAAE,YAAWF,GAAC;AAAA,EAAC;AAAA,EAAC,WAAWJ,IAAE;AAAC,UAAMM,KAAE,KAAK,aAAa,yBAAyBN,IAAE,KAAK,QAAQ;AAAE,QAAG,EAAEM,EAAC,EAAE,QAAO;AAAK,UAAMI,KAAEJ,GAAE,qBAAqB,GAAEQ,KAAE,GAAEJ,IAAEJ,GAAE,cAAaA,GAAE,MAAKA,GAAE,IAAI;AAAE,WAAM,EAAC,YAAWA,GAAE,eAAe,GAAE,UAASQ,GAAC;AAAA,EAAC;AAAA,EAAC,aAAad,IAAE;AAAC,WAAO,EAAE,KAAK,cAAc,IAAE,OAAK,KAAK,eAAe,aAAaA,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,WAAO,EAAE,KAAK,cAAc,IAAE,CAAC,IAAE,KAAK,eAAe,cAAc;AAAA,EAAC;AAAA,EAAC,MAAM,aAAaA,IAAE;AAAC,UAAMM,KAAE,EAAEN,GAAE,IAAK,CAAAA,OAAG,KAAK,aAAaA,EAAC,CAAE,CAAC;AAAE,WAAO,KAAK,eAAe,aAAaA,IAAEM,EAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BN,IAAE;AAAC,UAAMM,KAAE,KAAK,QAAQ;AAAc,QAAG,EAAEA,EAAC,EAAE,OAAM,IAAI,MAAM,sCAAsC;AAAE,QAAII,KAAE;AAAK,QAAG,KAAK,aAAa,QAAS,CAAAN,OAAG;AAAC,MAAAJ,OAAII,GAAE,cAAcE,EAAC,MAAII,KAAEN,GAAE,YAAY;AAAA,IAAE,CAAE,GAAE,EAAEM,EAAC,EAAE,OAAM,IAAI,MAAM,4CAA4CV,EAAC,EAAE;AAAE,WAAOU;AAAA,EAAC;AAAA,EAAC,MAAM,2BAA0B;AAAC,UAAMV,KAAE,KAAK,UAAU,MAAM,OAAQ,CAAAA,OAAGA,GAAE,UAAQ,KAAK,MAAO;AAAE,IAAAA,GAAE,IAAK,OAAMA,OAAG,KAAK,WAAW,EAAC,MAAK,UAAS,IAAGA,GAAE,IAAI,IAAG,OAAM,MAAG,aAAY,MAAK,KAAI,MAAE,CAAC,CAAE;AAAE,UAAMM,KAAEN,GAAE,IAAK,OAAMA,OAAG,KAAK,WAAW,EAAC,MAAK,UAAS,IAAGA,GAAE,IAAI,IAAG,aAAYe,GAAE,sBAAsB,CAAC,GAAE,KAAK,OAAO,GAAE,QAAO,CAAC,GAAE,KAAI,MAAG,UAAS,MAAG,QAAOT,GAAE,MAAM,EAAC,CAAC,CAAE;AAAE,UAAM,QAAQ,IAAIA,EAAC;AAAA,EAAC;AAAA,EAAC,qBAAoB;AAAC,gBAAY,IAAI,IAAE,KAAK,eAAaV,MAAG,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,WAAWI,IAAEM,IAAE;AAAC,UAAMF,KAAE,KAAK,aAAa,KAAKJ,IAAEM,EAAC,EAAE,KAAM,MAAI;AAAC,WAAK,aAAa,UAAU;AAAA,IAAC,CAAE,EAAE,MAAO,CAAAN,OAAG;AAAC,WAAK,aAAa,UAAU;AAAA,IAAC,CAAE;AAAE,WAAO,KAAK,aAAa,UAAU,GAAEI;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeJ,IAAEM,IAAE;AAAC,QAAG,EAAEA,EAAC,GAAE,IAAI,sBAAsB,GAAE;AAAC,YAAMA,KAAE,EAAEN,GAAE,aAAa,CAAAA,OAAGA,GAAE,WAAY;AAAE,cAAQ,MAAMA,GAAE,IAAG,2CAA2CA,GAAE,KAAK,SAASA,GAAE,GAAG,eAAeM,EAAC,GAAG;AAAA,IAAC;AAAC,UAAME,MAAE,KAAK,UAAU,IAAIR,GAAE,EAAE;AAAE,QAAG,CAACQ,IAAE;AAAO,QAAGR,GAAE,MAAM,QAAO,KAAK,UAAU,YAAYQ,GAAC;AAAE,UAAMQ,KAAEhB,GAAE;AAAO,SAAK,iBAAe;AAAG,UAAMiB,KAAE,CAAC;AAAE,eAAUb,MAAKJ,GAAE,UAAQ,CAAC,GAAE;AAAC,YAAMA,KAAE,KAAK,aAAa,gBAAgBI,EAAC;AAAE,MAAAJ,MAAGiB,GAAE,KAAKjB,EAAC;AAAA,IAAC;AAAC,IAAAA,GAAE,SAAOiB;AAAE,QAAG;AAAC,UAAG,EAAEjB,GAAE,WAAW,EAAE,QAAO,KAAK,KAAK,UAAU,cAAcQ,KAAE,EAAC,GAAGR,IAAE,aAAY,KAAI,GAAE,EAAE,KAAK,cAAc,GAAEM,EAAC,EAAE,MAAM,CAAC;AAAE,UAAGN,GAAE,YAAY,0BAA0B,KAAK,gBAAgB,GAAE,KAAK,aAAa,YAAYA,GAAE,YAAY,QAAQ,KAAGgB,GAAE,QAAQ,YAAUA,GAAE,QAAQ,UAAQ,MAAG,KAAK,aAAa,WAAWR,KAAER,EAAC,IAAG,CAACgB,GAAE,QAAQ,QAAM,CAACA,GAAE,QAAQ,SAAQ;AAAC,QAAAA,GAAE,QAAQ,OAAK,MAAGA,GAAE,QAAQ,UAAQ,MAAG,KAAK,eAAe,WAAWR,KAAER,EAAC;AAAE,qBAAW,KAAK,QAAQ,QAAM,KAAK,YAAU,MAAM,KAAK,eAAe,YAAY,GAAE,EAAEM,EAAC,KAAG,KAAK,eAAe,YAAY;AAAA,MAAC;AAAC,UAAG,EAAE,KAAK,cAAc,KAAG,CAACU,GAAE,QAAQ,WAAU;AAAC,QAAAA,GAAE,QAAQ,YAAU;AAAG,cAAMV,KAAEJ,GAAE,KAAK,OAAO,KAAG,KAAK,QAAQ,SAAQE,KAAE,CAACF,GAAE,KAAK,OAAO,KAAGI,MAAGN,GAAE;AAAI,YAAG,KAAK,eAAe,WAAWQ,KAAER,IAAE,KAAK,UAAS,KAAK,gBAAeI,EAAC,GAAE,CAACA,GAAE;AAAO,QAAAY,GAAE,SAAO,KAAK,eAAe,WAAWR,KAAER,EAAC,GAAE,MAAM,KAAK,eAAe,YAAY;AAAA,MAAE;AAAC,UAAG,CAACgB,GAAE,MAAK;AAAC,QAAAA,GAAE,OAAK;AAAG,cAAMZ,KAAE,EAAE,KAAK,cAAc,KAAG,cAAY,KAAK,eAAe;AAAK,cAAM,KAAK,UAAU,cAAcI,KAAER,IAAEI,IAAEE,EAAC,GAAE,EAAEA,EAAC;AAAA,MAAC;AAAC,WAAK,mBAAmB;AAAA,IAAC,SAAOY,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAMlB,IAAEM,IAAEF,IAAE;AAAC,UAAMM,MAAG,aAAW,KAAK,SAAS,cAAcV,EAAC,OAAK;AAAG,IAAAA,OAAIM,GAAE,IAAII,EAAC,GAAEN,GAAE,IAAIJ,EAAC;AAAA,EAAE;AAAA,EAAC,gBAAe;AAAC,SAAK,eAAa,YAAY,IAAI;AAAE,QAAG,EAAE,EAAE,cAAY,KAAK,QAAQ,QAAM,eAAa,KAAK,QAAQ,UAAQ,aAAW,KAAK,QAAQ,QAAM,KAAK,iBAAiB;AAAO,SAAK,iBAAe;AAAG,UAAMA,KAAE,KAAK,SAAS,UAAU,KAAK,eAAe,GAAEM,KAAE,oBAAI;AAAI,IAAAN,GAAE,MAAM;AAAE,eAAUI,MAAK,KAAK,UAAU,MAAM,YAAUM,MAAK,KAAK,QAAQ,QAAQN,GAAE,EAAE,GAAE;AAAC,YAAMA,KAAEM,GAAE,UAAU;AAAE,aAAKN,GAAE,KAAK,KAAG;AAAC,YAAIM,MAAEN,GAAE,aAAa;AAAE,YAAG,CAACM,KAAE;AAAC,gBAAMV,KAAEI,GAAE,YAAY;AAAE,UAAAM,MAAE,KAAK,aAAa,gBAAgBV,EAAC;AAAA,QAAC;AAAC,aAAK,MAAMU,KAAEJ,IAAEN,EAAC;AAAA,MAAC;AAAA,IAAC;AAAC,iBAAW,KAAK,UAAU,QAAM,KAAK,UAAU,gBAAiB,CAAAI,OAAG;AAAC,WAAK,MAAMA,IAAEE,IAAEN,EAAC;AAAA,IAAC,CAAE,GAAE,KAAK,aAAa,QAAS,CAAAI,OAAG;AAAC,iBAAUM,MAAKN,GAAE,UAAQ,CAAC,GAAE;AAAC,cAAMA,KAAE,KAAK,aAAa,gBAAgBM,EAAC;AAAE,aAAK,MAAMN,IAAEE,IAAEN,EAAC;AAAA,MAAC;AAAA,IAAC,CAAE,GAAE,EAAE,KAAK,cAAc,MAAI,KAAK,eAAe,cAAcA,IAAE,KAAK,YAAY,GAAE,qBAAoB,KAAK,kBAAgB,KAAK,eAAe,gBAAgB,KAAK,UAAS,KAAK,gBAAe,KAAK,MAAM,IAAG,KAAK,aAAa,cAAcA,IAAE,KAAK,UAAS,KAAK,cAAc,GAAE,KAAK,aAAa,iBAAiBM,EAAC;AAAA,EAAC;AAAC;AAAEN,GAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEG,GAAE,WAAU,aAAY,MAAM,GAAEH,GAAE,CAAC,EAAE,CAAC,GAAEG,GAAE,WAAU,UAAS,MAAM,GAAEH,GAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEG,GAAE,WAAU,eAAc,IAAI,GAAEH,GAAE,CAAC,EAAE,CAAC,GAAEG,GAAE,WAAU,aAAY,MAAM,GAAEH,GAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEG,GAAE,WAAU,gBAAe,MAAM,GAAEH,GAAE,CAAC,EAAE,EAAC,eAAc,KAAE,CAAC,CAAC,GAAEG,GAAE,WAAU,WAAU,MAAM,GAAEH,GAAE,CAAC,EAAE,CAAC,GAAEG,GAAE,WAAU,oBAAmB,IAAI,GAAEH,GAAE,CAAC,EAAE,CAAC,GAAEG,GAAE,WAAU,YAAW,IAAI,GAAEA,KAAEH,GAAE,CAAC,EAAE,+DAA+D,CAAC,GAAEG,EAAC;AAAE,IAAMgB,KAAEhB;;;ACAvvf,IAAIiB,KAAE,cAAc,EAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,aAAW,MAAK,KAAK,YAAU,MAAK,KAAK,eAAa,MAAK,KAAK,YAAU,MAAK,KAAK,UAAQ,MAAK,KAAK,YAAU,MAAK,KAAK,UAAQ,OAAG,KAAK,sBAAoB,CAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,QAAQ,IAAIC,GAAG,MAAI,KAAK,UAAW,CAAAC,OAAG;AAAC,WAAK,aAAa,OAAO,eAAcA,EAAC,EAAE,MAAO,CAAAA,OAAG;AAAA,MAAC,CAAE;AAAA,IAAC,CAAE,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAJrqC;AAIsqC,SAAK,KAAK,IAAE,UAAK,eAAL,mBAAiB,YAAU,UAAK,cAAL,mBAAgB,WAAU,KAAK,aAAW,KAAK,YAAU,KAAK,YAAU,KAAK,eAAa;AAAA,EAAI;AAAA,EAAC,IAAI,WAAU;AAAC,WAAM,CAAC,KAAK,cAAY,KAAK,WAAW;AAAA,EAAQ;AAAA,EAAC,OAAM;AAJl3C;AAIm3C,SAAK,UAAQ,MAAG,MAAM,SAAQ,UAAK,YAAL,mBAAc,MAAM,MAAI,KAAK,QAAQ,OAAO,QAAS,CAAAA,OAAGA,GAAE,MAAM,CAAE,GAAE,KAAK,QAAQ,OAAO,SAAO,KAAG,UAAK,cAAL,mBAAgB,YAAY,EAAC,OAAM,CAAC,GAAE,SAAQ,KAAK,UAAU,MAAM,IAAK,CAAAA,OAAGA,GAAE,EAAG,EAAC,KAAG,UAAK,cAAL,mBAAgB,WAAU,KAAK,YAAU,MAAK,KAAK,oBAAoB,SAAO;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQ,EAAC,SAAQA,IAAE,QAAOC,IAAE,UAASC,IAAE,OAAMC,GAAC,GAAE;AAJ7sD;AAI8sD,QAAG,KAAK,UAAQ,MAAG,MAAM,SAAQ,UAAK,YAAL,mBAAc,MAAM,MAAI,KAAK,QAAQ,OAAO,QAAS,CAAAH,OAAGA,GAAE,MAAM,CAAE,GAAE,KAAK,QAAQ,OAAO,SAAO,IAAG,KAAK,UAAQA,IAAE,CAAC,KAAK,aAAW,CAACI,GAAE,KAAK,UAAU,WAAW,kBAAiBF,GAAE,gBAAgB,GAAE;AAAC,YAAMF,KAAE,IAAI,EAAEK,GAAE,SAASH,EAAC,CAAC;AAAE,MAAAC,GAAE,MAAM,SAAOA,GAAE,QAAQ,SAAO,IAAE,UAAK,cAAL,mBAAgB,YAAY,EAAC,OAAM,CAAC,GAAE,SAAQ,KAAK,UAAU,MAAM,IAAK,CAAAH,OAAGA,GAAE,EAAG,EAAC,KAAG,UAAK,cAAL,mBAAgB,WAAU,KAAK,YAAU,IAAIF,GAAEE,EAAC,GAAE,KAAK,oBAAoB,SAAO;AAAA,IAAC;AAAC,SAAI,MAAM,KAAK,8BAA8BC,EAAC,GAAE,MAAM,KAAK,OAAO,EAAC,QAAOA,GAAC,CAAC,GAAE,KAAK,WAAW,OAAO,GAAE,KAAK,UAAU,MAAM,GAAE,KAAK,UAAU,YAAYE,EAAC,GAAE,KAAK,UAAQ,OAAG,KAAK,oBAAoB,SAAQ,MAAK,UAAU,YAAY,KAAK,oBAAoB,IAAI,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,YAAYH,IAAE;AAJv7E;AAIw7E,SAAK,UAAQ,KAAK,oBAAoB,KAAKA,EAAC,KAAE,UAAK,cAAL,mBAAgB,YAAYA;AAAA,EAAE;AAAA,EAAC,MAAM,OAAO,EAAC,QAAOA,GAAC,GAAE;AAAC,UAAMC,KAAEA,GAAE,MAAM;AAAE,WAAO,MAAM,QAAQ,IAAI,CAAC,KAAK,UAAU,OAAOA,IAAED,EAAC,GAAE,KAAK,WAAW,OAAOC,IAAED,EAAC,CAAC,CAAC,GAAEC,GAAE,OAAO;AAAA,EAAC;AAAA,EAAC,MAAM,YAAYD,IAAE;AAAC,WAAO,KAAK,WAAW,YAAYC,GAAE,OAAOD,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,8BAA8BA,IAAE;AAAC,UAAM,QAAQ,IAAI,CAAC,KAAK,wBAAwBA,EAAC,GAAE,KAAK,uBAAuBA,EAAC,CAAC,CAAC,GAAE,KAAK,WAAW,YAAU,KAAK;AAAA,EAAS;AAAA,EAAC,MAAM,wBAAwBA,IAAE;AAAC,WAAO,KAAK,kBAAkB,KAAK,SAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,uBAAuBA,IAAE;AAAC,WAAO,KAAK,iBAAiB,KAAK,SAAQA,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,kBAAkBA,IAAEC,IAAE;AAAC,SAAK,cAAY,KAAK,WAAW,QAAQ;AAAE,UAAK,EAAC,WAAUC,IAAE,cAAaC,GAAC,IAAE,MAAKG,KAAE,IAAIC,GAAE,EAAC,SAAQP,IAAE,WAAUE,IAAE,cAAaC,GAAC,CAAC;AAAE,WAAO,KAAK,aAAWG,IAAEA;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiBN,IAAEC,IAAE;AAAC,UAAMC,KAAED,GAAE,OAAO,WAAW,CAAC,EAAE,MAAKE,MAAG,MAAMG,GAAEJ,EAAC,GAAG,SAAQ,EAAC,cAAaI,IAAE,WAAUE,GAAC,IAAE,MAAKT,KAAE,IAAII,GAAE,EAAC,SAAQH,IAAE,QAAOC,IAAE,WAAUO,IAAE,cAAaF,GAAC,CAAC;AAAE,WAAO,KAAK,aAAW,KAAK,UAAU,QAAQ,GAAE,KAAK,YAAUP,IAAEA;AAAA,EAAC;AAAC;AAAEC,GAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,cAAa,MAAM,GAAEE,GAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,aAAY,MAAM,GAAEE,GAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,YAAW,IAAI,GAAEE,GAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,aAAY,MAAM,GAAEA,KAAEE,GAAE,CAAC,EAAE,wCAAwC,CAAC,GAAEF,EAAC;AAAE,IAAMW,KAAEX;", "names": ["o", "r", "n", "e", "a", "t", "i", "o", "c", "s", "f", "d", "l", "u", "g", "p", "h", "m", "b", "w", "k", "x", "y", "I", "F", "L", "A", "S", "q", "M", "g", "l", "f", "y", "e", "p", "m", "r", "t", "n", "x", "s", "i", "a", "h", "o", "d", "u", "c", "v", "G", "b", "_", "p", "e", "f", "l", "y", "t", "q", "d", "v", "F", "h", "r", "c", "s", "G", "o", "n", "m", "i", "t", "e", "s", "r", "a", "a", "t", "s", "e", "o", "n", "h", "u", "c", "r", "m", "e", "s", "t", "f", "g", "i", "r", "o", "n", "T", "c", "a", "y", "p", "d", "f", "e", "y", "l", "t", "r", "s", "i", "U", "a", "o", "n", "u", "c", "s", "i", "a", "d", "e", "r", "o", "n", "c", "u", "l", "p", "m", "t", "h", "n", "d", "e", "r", "i", "o", "c", "u", "p", "t", "a", "h", "m", "s", "h", "t", "e", "s", "r", "o", "a", "n", "l", "i", "d", "u", "c", "g", "_", "p", "f", "d", "a", "h", "n", "t", "e", "s", "i", "r", "o", "_", "y", "e", "s", "r", "t", "i", "g", "o", "a", "c", "f", "b", "v", "I", "T", "n", "d", "p", "l", "m", "a", "e", "r", "u", "i", "p", "f", "c", "n", "T", "t", "o", "s", "g", "c", "f", "t", "n", "o", "e", "r", "c", "f", "l", "u", "h", "s", "i", "a", "g", "A", "d", "b", "e", "s", "i", "t", "d", "n", "c", "o", "a", "r", "l", "h", "u", "f", "x", "m", "g", "y", "p", "_", "v", "M", "T", "b", "k", "I", "G", "L", "S", "w", "R", "F", "j", "U", "G", "R", "L", "T", "a", "D", "e", "c", "t", "s", "r", "i", "f", "n", "o", "h", "b", "g", "v", "d", "l", "p", "F", "w", "S", "a", "T", "o", "e", "t", "s", "r", "i", "h", "n", "D", "G", "c", "f", "b", "g", "l", "u", "p", "m", "y", "I", "R", "M", "x", "j", "v", "d", "_", "T", "j", "x", "R", "e", "A", "U", "q", "r", "l", "t", "f", "a", "g", "s", "_a", "_b", "G", "i", "c", "o", "n", "h", "O", "d", "l", "e", "t", "r", "s", "E", "j", "o", "O", "i", "u"]}