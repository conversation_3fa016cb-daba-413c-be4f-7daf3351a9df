import {
  n
} from "./chunk-W42VJAAS.js";
import {
  h as h2
} from "./chunk-X5KH47GX.js";
import "./chunk-73T3NEXA.js";
import {
  v
} from "./chunk-4DICTWL2.js";
import "./chunk-WGU7CS6R.js";
import "./chunk-4JOAXMIS.js";
import "./chunk-UYHYCNVE.js";
import "./chunk-INCFUNSS.js";
import {
  He
} from "./chunk-CM7XD6X4.js";
import "./chunk-YSWCANSA.js";
import "./chunk-ZJC3GHA7.js";
import "./chunk-TVTTDN54.js";
import "./chunk-O4T45CJC.js";
import "./chunk-NNKS4NNY.js";
import "./chunk-LNCHRZJI.js";
import "./chunk-O3LPRA7A.js";
import "./chunk-M5RPNIHK.js";
import "./chunk-KYTIKHPN.js";
import "./chunk-Y7OJSY6H.js";
import "./chunk-6JIS2R4B.js";
import "./chunk-7BSY2CUN.js";
import "./chunk-6RN6WNY4.js";
import "./chunk-SY6DBVDS.js";
import "./chunk-CQJF5YJI.js";
import "./chunk-MLJBD5E5.js";
import "./chunk-DTQ34PEY.js";
import "./chunk-EKOSN3EW.js";
import "./chunk-UVJUTW2U.js";
import "./chunk-RR74IWZB.js";
import "./chunk-KTB2COPC.js";
import "./chunk-FWXA4I6D.js";
import "./chunk-NQ3OACUM.js";
import "./chunk-HTXGAKOK.js";
import "./chunk-7UNBPRRZ.js";
import "./chunk-OQK7L3JR.js";
import "./chunk-5BWF7URZ.js";
import "./chunk-D3MAF4VS.js";
import "./chunk-ND4JUK42.js";
import "./chunk-2WMCP27R.js";
import "./chunk-JV6TBH5W.js";
import {
  _
} from "./chunk-TV6WKLTU.js";
import "./chunk-5VDORDNW.js";
import {
  c
} from "./chunk-FHKOFAQ2.js";
import {
  O
} from "./chunk-XGD5S6QR.js";
import "./chunk-3HW44BD3.js";
import {
  An,
  en,
  rn,
  tn,
  un
} from "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-ZVU4V5QV.js";
import "./chunk-2AZSZWPE.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-P37TUI4J.js";
import "./chunk-Q4VCSCSY.js";
import {
  n as n2
} from "./chunk-LAEW33J6.js";
import {
  t as t3
} from "./chunk-NGPCXWDX.js";
import "./chunk-IXMTA2RC.js";
import "./chunk-65QKNYBL.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import {
  b
} from "./chunk-67MHB3E3.js";
import "./chunk-USWRDFDJ.js";
import "./chunk-XM4RHPJM.js";
import "./chunk-6T5FEO66.js";
import "./chunk-MKG56XBH.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-N4YJNWPS.js";
import {
  y as y2
} from "./chunk-NZB6EMKN.js";
import "./chunk-DHWMTT76.js";
import "./chunk-JEANRG5Q.js";
import "./chunk-45UG5A2F.js";
import "./chunk-ORU3OGKZ.js";
import "./chunk-FCQRDLBQ.js";
import "./chunk-NUZU7NCS.js";
import "./chunk-LHJ7PAFO.js";
import "./chunk-7MQMIP4J.js";
import "./chunk-64RWCMSJ.js";
import "./chunk-GE5PSQPZ.js";
import "./chunk-2ILOD42U.js";
import "./chunk-FRJZOKDL.js";
import "./chunk-DKEAXJKM.js";
import "./chunk-Y4E3DGVA.js";
import "./chunk-4GVJIP3E.js";
import "./chunk-YJWWP4AU.js";
import "./chunk-NOZFLZZL.js";
import "./chunk-MUYX6GXF.js";
import "./chunk-EQYLMJEJ.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-3TXAWGPY.js";
import "./chunk-5EGPPD3R.js";
import "./chunk-L66EKMJP.js";
import "./chunk-3M3FTH72.js";
import "./chunk-XLHYMGQY.js";
import "./chunk-UYJR3ZHF.js";
import "./chunk-52L4L36S.js";
import "./chunk-VTOVDEW2.js";
import "./chunk-UKESBP6X.js";
import "./chunk-HM62IZSE.js";
import "./chunk-CCAF47ZU.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-N7ADFPOO.js";
import {
  g
} from "./chunk-TD7BKFJX.js";
import "./chunk-LNGMNZ4I.js";
import "./chunk-24NZLSKM.js";
import "./chunk-BNFBWJYJ.js";
import "./chunk-BDKNA3OF.js";
import "./chunk-762DBG4V.js";
import "./chunk-23VNIYCK.js";
import "./chunk-AVKOL7OR.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-RFYOGM4H.js";
import {
  D,
  I,
  K,
  f as f2,
  h
} from "./chunk-I7WHRVHF.js";
import {
  S
} from "./chunk-OVIRLBJH.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-CTPXU2ZH.js";
import {
  j
} from "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-FLHLIVG4.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w
} from "./chunk-XTO3XXZ3.js";
import "./chunk-QUHG7NMD.js";
import "./chunk-4YKNTCHK.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import {
  y as y3
} from "./chunk-CGBA4LNQ.js";
import {
  m as m2
} from "./chunk-2RZLOKYO.js";
import "./chunk-VIXZ7ZAD.js";
import "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  E,
  f2 as f
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import {
  m3 as m
} from "./chunk-HHMRK4FX.js";
import "./chunk-QMG7GZIF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-T23PB46T.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-PEEUPDEG.js";
import "./chunk-7SWS36OI.js";
import {
  r as r2
} from "./chunk-6HCWK637.js";
import {
  o as o2
} from "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  t2,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import {
  s
} from "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import {
  o,
  p
} from "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import {
  r,
  t
} from "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/MapNotesLayer.js
function z(e2) {
  return "markup" === e2.featureCollectionType || e2.layers.some((e3) => null != e3.layerDefinition.visibilityField || !U(e3));
}
function U({ layerDefinition: e2, featureSet: t4 }) {
  const r3 = e2.geometryType ?? t4.geometryType;
  return H.find((t5) => {
    var _a, _b, _c;
    return r3 === t5.geometryTypeJSON && ((_c = (_b = (_a = e2.drawingInfo) == null ? void 0 : _a.renderer) == null ? void 0 : _b.symbol) == null ? void 0 : _c.type) === t5.identifyingSymbol.type;
  });
}
function W() {
  return new w({ xmin: -180, ymin: -90, xmax: 180, ymax: 90 });
}
var A = new y2({ name: "OBJECTID", alias: "OBJECTID", type: "oid", nullable: false, editable: false });
var $ = new y2({ name: "title", alias: "Title", type: "string", nullable: true, editable: true });
var q = class extends h2 {
  constructor(e2) {
    super(e2), this.visibilityMode = "inherited";
  }
  initialize() {
    for (const e2 of this.graphics) e2.sourceLayer = this.layer;
    this.graphics.on("after-add", (e2) => {
      e2.item.sourceLayer = this.layer;
    }), this.graphics.on("after-remove", (e2) => {
      e2.item.sourceLayer = null;
    });
  }
  get fullExtent() {
    var _a;
    const e2 = (_a = this.layer) == null ? void 0 : _a.spatialReference, t4 = this.fullBounds;
    return e2 ? t(t4) ? un(W(), e2).geometry : f2(t4, e2) : null;
  }
  get fullBounds() {
    var _a;
    const e2 = (_a = this.layer) == null ? void 0 : _a.spatialReference;
    if (!e2) return null;
    const t4 = D();
    return this.graphics.forEach((r3) => {
      const o3 = r(r3.geometry) ? un(r3.geometry, e2).geometry : null;
      r(o3) && h(t4, "point" === o3.type ? o3 : o3.extent, t4);
    }), I(t4, K) ? null : t4;
  }
  get sublayers() {
    return this.graphics;
  }
};
e([y({ readOnly: true })], q.prototype, "fullExtent", null), e([y({ readOnly: true })], q.prototype, "fullBounds", null), e([y({ readOnly: true })], q.prototype, "sublayers", null), e([y()], q.prototype, "layer", void 0), e([y()], q.prototype, "layerId", void 0), e([y({ readOnly: true })], q.prototype, "visibilityMode", void 0), q = e([a("esri.layers.MapNotesLayer.MapNotesSublayer")], q);
var H = [{ geometryType: "polygon", geometryTypeJSON: "esriGeometryPolygon", id: "polygonLayer", layerId: 0, title: "Polygons", identifyingSymbol: new S().toJSON() }, { geometryType: "polyline", geometryTypeJSON: "esriGeometryPolyline", id: "polylineLayer", layerId: 1, title: "Polylines", identifyingSymbol: new m2().toJSON() }, { geometryType: "multipoint", geometryTypeJSON: "esriGeometryMultipoint", id: "multipointLayer", layerId: 2, title: "Multipoints", identifyingSymbol: new y3().toJSON() }, { geometryType: "point", geometryTypeJSON: "esriGeometryPoint", id: "pointLayer", layerId: 3, title: "Points", identifyingSymbol: new y3().toJSON() }, { geometryType: "point", geometryTypeJSON: "esriGeometryPoint", id: "textLayer", layerId: 4, title: "Text", identifyingSymbol: new m().toJSON() }];
var K2 = class extends n2(t3(c(_(O(b))))) {
  constructor(e2) {
    super(e2), this.capabilities = { operations: { supportsMapNotesEditing: true } }, this.featureCollections = null, this.featureCollectionJSON = null, this.featureCollectionType = "notes", this.legendEnabled = false, this.listMode = "hide-children", this.minScale = 0, this.maxScale = 0, this.spatialReference = f.WGS84, this.sublayers = new j(H.map((e3) => new q({ id: e3.id, layerId: e3.layerId, title: e3.title, layer: this }))), this.title = "Map Notes", this.type = "map-notes", this.visibilityMode = "inherited";
  }
  readCapabilities(e2, t4, r3) {
    return { operations: { supportsMapNotesEditing: !z(t4) && "portal-item" !== (r3 == null ? void 0 : r3.origin) } };
  }
  readFeatureCollections(e2, t4, o3) {
    if (!z(t4)) return null;
    const i = t4.layers.map((e3) => {
      const t5 = new He();
      return t5.read(e3, o3), t5;
    });
    return new j({ items: i });
  }
  readLegacyfeatureCollectionJSON(e2, t4) {
    return z(t4) ? p(t4.featureCollection) : null;
  }
  get fullExtent() {
    var _a;
    const e2 = this.spatialReference, t4 = D();
    if (r(this.sublayers)) this.sublayers.forEach(({ fullBounds: e3 }) => r(e3) ? h(t4, e3, t4) : t4, t4);
    else if ((_a = this.featureCollectionJSON) == null ? void 0 : _a.layers.some((e3) => e3.layerDefinition.extent)) {
      this.featureCollectionJSON.layers.forEach((r3) => {
        const o3 = un(r3.layerDefinition.extent, e2).geometry;
        r(o3) && h(t4, o3, t4);
      });
    }
    return I(t4, K) ? un(W(), e2).geometry : f2(t4, e2);
  }
  readMinScale(e2, t4) {
    for (const r3 of t4.layers) if (null != r3.layerDefinition.minScale) return r3.layerDefinition.minScale;
    return 0;
  }
  readMaxScale(e2, t4) {
    for (const r3 of t4.layers) if (null != r3.layerDefinition.maxScale) return r3.layerDefinition.maxScale;
    return 0;
  }
  get multipointLayer() {
    return this._findSublayer("multipointLayer");
  }
  get pointLayer() {
    return this._findSublayer("pointLayer");
  }
  get polygonLayer() {
    return this._findSublayer("polygonLayer");
  }
  get polylineLayer() {
    return this._findSublayer("polylineLayer");
  }
  readSpatialReference(e2, t4) {
    return t4.layers.length ? f.fromJSON(t4.layers[0].layerDefinition.spatialReference) : f.WGS84;
  }
  readSublayers(e2, o3, i) {
    if (z(o3)) return null;
    const a2 = [];
    let n3 = o3.layers.reduce((e3, t4) => Math.max(e3, t4.layerDefinition.id ?? -1), -1) + 1;
    for (const r3 of o3.layers) {
      const { layerDefinition: e3, featureSet: o4 } = r3, i2 = e3.id ?? n3++, s2 = U(r3);
      if (r(s2)) {
        const r4 = new q({ id: s2.id, title: e3.name, layerId: i2, layer: this, graphics: o4.features.map(({ geometry: e4, symbol: r5, attributes: o5, popupInfo: i3 }) => g.fromJSON({ attributes: o5, geometry: e4, symbol: r5, popupTemplate: i3 })) });
        a2.push(r4);
      }
    }
    return new j(a2);
  }
  writeSublayers(e2, t4, r3, i) {
    var _a;
    const { minScale: n3, maxScale: p2 } = this;
    if (t(e2)) return;
    const y4 = e2.some((e3) => e3.graphics.length > 0);
    if (!this.capabilities.operations.supportsMapNotesEditing) return void (y4 && ((_a = i == null ? void 0 : i.messages) == null ? void 0 : _a.push(new s("map-notes-layer:editing-not-supported", "New map notes cannot be added to this layer"))));
    const m3 = [];
    let u = this.spatialReference.toJSON();
    e: for (const o3 of e2) for (const e3 of o3.graphics) if (r(e3.geometry)) {
      u = e3.geometry.spatialReference.toJSON();
      break e;
    }
    for (const o3 of H) {
      const t5 = e2.find((e3) => o3.id === e3.id);
      this._writeMapNoteSublayer(m3, t5, o3, n3, p2, u, i);
    }
    o("featureCollection.layers", m3, t4);
  }
  get textLayer() {
    return this._findSublayer("textLayer");
  }
  load(e2) {
    return this.addResolvingPromise(this.loadFromPortal({ supportedTypes: ["Feature Collection"] }, e2)), Promise.resolve(this);
  }
  read(e2, t4) {
    "featureCollection" in e2 && (e2 = p(e2), Object.assign(e2, e2.featureCollection)), super.read(e2, t4);
  }
  async beforeSave() {
    if (t(this.sublayers)) return;
    let e2 = null;
    const t4 = [];
    for (const o3 of this.sublayers) for (const r4 of o3.graphics) if (r(r4.geometry)) {
      const o4 = r4.geometry;
      e2 ? E(o4.spatialReference, e2) || (An(o4.spatialReference, e2) || en() || await tn(), r4.geometry = rn(o4, e2)) : e2 = o4.spatialReference, t4.push(r4);
    }
    const r3 = await v(t4.map((e3) => e3.geometry));
    t4.forEach((e3, t5) => e3.geometry = r3[t5]);
  }
  _findSublayer(e2) {
    var _a;
    return t(this.sublayers) ? null : ((_a = this.sublayers) == null ? void 0 : _a.find((t4) => t4.id === e2)) ?? null;
  }
  _writeMapNoteSublayer(e2, t4, r3, o3, l, n3, s2) {
    const p2 = [];
    if (!t(t4)) {
      for (const e3 of t4.graphics) this._writeMapNote(p2, e3, r3.geometryType, s2);
      this._normalizeObjectIds(p2, A), e2.push({ layerDefinition: { name: t4.title, drawingInfo: { renderer: { type: "simple", symbol: p(r3.identifyingSymbol) } }, id: t4.layerId, geometryType: r3.geometryTypeJSON, minScale: o3, maxScale: l, objectIdField: "OBJECTID", fields: [A.toJSON(), $.toJSON()], spatialReference: n3 }, featureSet: { features: p2, geometryType: r3.geometryTypeJSON } });
    }
  }
  _writeMapNote(e2, t4, r3, o3) {
    var _a, _b;
    if (t(t4)) return;
    const { geometry: i, symbol: n3, popupTemplate: s2 } = t4;
    if (t(i)) return;
    if (i.type !== r3) return void ((_a = o3 == null ? void 0 : o3.messages) == null ? void 0 : _a.push(new t2("map-notes-layer:invalid-geometry-type", `Geometry "${i.type}" cannot be saved in "${r3}" layer`, { graphic: t4 })));
    if (t(n3)) return void ((_b = o3 == null ? void 0 : o3.messages) == null ? void 0 : _b.push(new t2("map-notes-layer:no-symbol", "Skipping map notes with no symbol", { graphic: t4 })));
    const y4 = { attributes: { ...t4.attributes }, geometry: i.toJSON(), symbol: n3.toJSON() };
    r(s2) && (y4.popupInfo = s2.toJSON()), e2.push(y4);
  }
  _normalizeObjectIds(e2, t4) {
    const r3 = t4.name;
    let o3 = n(r3, e2) + 1;
    const i = /* @__PURE__ */ new Set();
    for (const l of e2) {
      l.attributes || (l.attributes = {});
      const { attributes: e3 } = l;
      (null == e3[r3] || i.has(e3[r3])) && (e3[r3] = o3++), i.add(e3[r3]);
    }
  }
};
e([y({ readOnly: true })], K2.prototype, "capabilities", void 0), e([o2(["portal-item", "web-map"], "capabilities", ["layers"])], K2.prototype, "readCapabilities", null), e([y({ readOnly: true })], K2.prototype, "featureCollections", void 0), e([o2(["web-map", "portal-item"], "featureCollections", ["layers"])], K2.prototype, "readFeatureCollections", null), e([y({ readOnly: true, json: { origins: { "web-map": { write: { enabled: true, target: "featureCollection" } } } } })], K2.prototype, "featureCollectionJSON", void 0), e([o2(["web-map", "portal-item"], "featureCollectionJSON", ["featureCollection"])], K2.prototype, "readLegacyfeatureCollectionJSON", null), e([y({ readOnly: true, json: { read: true, write: { enabled: true, ignoreOrigin: true } } })], K2.prototype, "featureCollectionType", void 0), e([y({ readOnly: true })], K2.prototype, "fullExtent", null), e([y({ readOnly: true, json: { origins: { "web-map": { write: { target: "featureCollection.showLegend", overridePolicy() {
  return { enabled: null != this.featureCollectionJSON };
} } } } } })], K2.prototype, "legendEnabled", void 0), e([y({ type: ["show", "hide", "hide-children"] })], K2.prototype, "listMode", void 0), e([y({ type: Number, nonNullable: true, json: { write: false } })], K2.prototype, "minScale", void 0), e([o2(["web-map", "portal-item"], "minScale", ["layers"])], K2.prototype, "readMinScale", null), e([y({ type: Number, nonNullable: true, json: { write: false } })], K2.prototype, "maxScale", void 0), e([o2(["web-map", "portal-item"], "maxScale", ["layers"])], K2.prototype, "readMaxScale", null), e([y({ readOnly: true })], K2.prototype, "multipointLayer", null), e([y({ value: "ArcGISFeatureLayer", type: ["ArcGISFeatureLayer"] })], K2.prototype, "operationalLayerType", void 0), e([y({ readOnly: true })], K2.prototype, "pointLayer", null), e([y({ readOnly: true })], K2.prototype, "polygonLayer", null), e([y({ readOnly: true })], K2.prototype, "polylineLayer", null), e([y({ type: f })], K2.prototype, "spatialReference", void 0), e([o2(["web-map", "portal-item"], "spatialReference", ["layers"])], K2.prototype, "readSpatialReference", null), e([y({ readOnly: true, json: { origins: { "web-map": { write: { ignoreOrigin: true } } } } })], K2.prototype, "sublayers", void 0), e([o2("web-map", "sublayers", ["layers"])], K2.prototype, "readSublayers", null), e([r2("web-map", "sublayers")], K2.prototype, "writeSublayers", null), e([y({ readOnly: true })], K2.prototype, "textLayer", null), e([y()], K2.prototype, "title", void 0), e([y({ readOnly: true, json: { read: false } })], K2.prototype, "type", void 0), K2 = e([a("esri.layers.MapNotesLayer")], K2);
var Q = K2;
export {
  Q as default
};
//# sourceMappingURL=MapNotesLayer-CV3OFRHJ.js.map
