{"version": 3, "sources": ["../../@arcgis/core/views/3d/layers/i3s/PointCloudWorkerUtil.js", "../../@arcgis/core/views/3d/layers/PointCloudWorker.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as e,isSome as o}from\"../../../../core/maybe.js\";import r from\"../../../../renderers/PointCloudClassBreaksRenderer.js\";import t from\"../../../../renderers/PointCloudStretchRenderer.js\";import l from\"../../../../renderers/PointCloudUniqueValueRenderer.js\";import{createGeometryIndexFromSchema as n,createTypedView as s,readBinaryAttribute as i}from\"./I3SBinaryReader.js\";import{decodeXYZ as u}from\"./LEPCC.js\";function f(e,o,n,s){const{rendererJSON:i,isRGBRenderer:u}=e;let f=null,c=null;if(o&&u)f=o;else if(o&&\"pointCloudUniqueValueRenderer\"===i?.type){c=l.fromJSON(i);const e=c.colorUniqueValueInfos;f=new Uint8Array(3*s);const r=b(c.fieldTransformType);for(let t=0;t<s;t++){const l=(r?r(o[t]):o[t])+\"\";for(let o=0;o<e.length;o++)if(e[o].values.includes(l)){f[3*t]=e[o].color.r,f[3*t+1]=e[o].color.g,f[3*t+2]=e[o].color.b;break}}}else if(o&&\"pointCloudStretchRenderer\"===i?.type){c=t.fromJSON(i);const e=c.stops;f=new Uint8Array(3*s);const r=b(c.fieldTransformType);for(let t=0;t<s;t++){const l=r?r(o[t]):o[t],n=e.length-1;if(l<e[0].value)f[3*t]=e[0].color.r,f[3*t+1]=e[0].color.g,f[3*t+2]=e[0].color.b;else if(l>=e[n].value)f[3*t]=e[n].color.r,f[3*t+1]=e[n].color.g,f[3*t+2]=e[n].color.b;else for(let o=1;o<e.length;o++)if(l<e[o].value){const r=(l-e[o-1].value)/(e[o].value-e[o-1].value);f[3*t]=e[o].color.r*r+e[o-1].color.r*(1-r),f[3*t+1]=e[o].color.g*r+e[o-1].color.g*(1-r),f[3*t+2]=e[o].color.b*r+e[o-1].color.b*(1-r);break}}}else if(o&&\"pointCloudClassBreaksRenderer\"===i?.type){c=r.fromJSON(i);const e=c.colorClassBreakInfos;f=new Uint8Array(3*s);const t=b(c.fieldTransformType);for(let r=0;r<s;r++){const l=t?t(o[r]):o[r];for(let o=0;o<e.length;o++)if(l>=e[o].minValue&&l<=e[o].maxValue){f[3*r]=e[o].color.r,f[3*r+1]=e[o].color.g,f[3*r+2]=e[o].color.b;break}}}else{f=new Uint8Array(3*s);for(let e=0;e<f.length;e++)f[e]=255}if(n&&c&&c.colorModulation){const e=c.colorModulation.minValue,o=c.colorModulation.maxValue,r=.3;for(let t=0;t<s;t++){const l=n[t],s=l>=o?1:l<=e?r:r+(1-r)*(l-e)/(o-e);f[3*t]=s*f[3*t],f[3*t+1]=s*f[3*t+1],f[3*t+2]=s*f[3*t+2]}}return f}function c(o,r){if(null==o.encoding||\"\"===o.encoding){const t=n(r,o);if(e(t.vertexAttributes.position))return;const l=s(r,t.vertexAttributes.position),i=t.header.fields,u=[i.offsetX,i.offsetY,i.offsetZ],f=[i.scaleX,i.scaleY,i.scaleZ],c=l.length/3,a=new Float64Array(3*c);for(let e=0;e<c;e++)a[3*e]=l[3*e]*f[0]+u[0],a[3*e+1]=l[3*e+1]*f[1]+u[1],a[3*e+2]=l[3*e+2]*f[2]+u[2];return a}if(\"lepcc-xyz\"===o.encoding)return u(r).result}function a(e,r,t){return o(e)&&e.attributeInfo.useElevation?r?d(r,t):null:o(e)&&e.attributeInfo.storageInfo?i(e.attributeInfo.storageInfo,e.buffer,t):null}function d(e,o){const r=new Float64Array(o);for(let t=0;t<o;t++)r[t]=e[3*t+2];return r}function m(e,o,r,t,l){const n=e.length/3;let s=0;for(let i=0;i<n;i++){let n=!0;for(let e=0;e<t.length&&n;e++){const{filterJSON:o}=t[e],r=l[e].values[i];switch(o.type){case\"pointCloudValueFilter\":{const e=\"exclude\"===o.mode;o.values.includes(r)===e&&(n=!1);break}case\"pointCloudBitfieldFilter\":{const e=p(o.requiredSetBits),t=p(o.requiredClearBits);(r&e)===e&&0==(r&t)||(n=!1);break}case\"pointCloudReturnFilter\":{const e=15&r,t=r>>>4&15,l=t>1,s=1===e,i=e===t;let u=!1;for(const r of o.includedReturns)if(\"last\"===r&&i||\"firstOfMany\"===r&&s&&l||\"lastOfMany\"===r&&i&&l||\"single\"===r&&!l){u=!0;break}u||(n=!1);break}}}n&&(r[s]=i,e[3*s]=e[3*i],e[3*s+1]=e[3*i+1],e[3*s+2]=e[3*i+2],o[3*s]=o[3*i],o[3*s+1]=o[3*i+1],o[3*s+2]=o[3*i+2],s++)}return s}function b(e){return null==e||\"none\"===e?null:\"low-four-bit\"===e?e=>15&e:\"high-four-bit\"===e?e=>(240&e)>>4:\"absolute-value\"===e?e=>Math.abs(e):\"modulo-ten\"===e?e=>e%10:null}function p(e){let o=0;for(const r of e||[])o|=1<<r;return o}export{d as elevationFromPositions,f as evaluateRenderer,m as filterInPlace,a as getAttributeValues,c as readGeometry};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t}from\"../../../core/maybe.js\";import{isArrayBuffer as r}from\"../../../core/typedArrayUtil.js\";import{c as e}from\"../../../chunks/quat.js\";import{c as a}from\"../../../chunks/quatf32.js\";import{q as o}from\"../../../chunks/vec3.js\";import{f,c as i}from\"../../../chunks/vec3f32.js\";import{projectBuffer as n}from\"../../../geometry/projection.js\";import s from\"../../../geometry/SpatialReference.js\";import{readGeometry as u,getAttributeValues as l,evaluate<PERSON><PERSON>er as b,filterInPlace as m}from\"./i3s/PointCloudWorkerUtil.js\";class c{transform(e){const a=this._transform(e),o=[a.points.buffer,a.rgb.buffer];t(a.pointIdFilterMap)&&o.push(a.pointIdFilterMap.buffer);for(const t of a.attributes)\"buffer\"in t.values&&r(t.values.buffer)&&t.values.buffer!==a.rgb.buffer&&o.push(t.values.buffer);return Promise.resolve({result:a,transferList:o})}_transform(r){const e=u(r.schema,r.geometryBuffer);let a=e.length/3,o=null;const f=[],i=l(r.primaryAttributeData,e,a);t(r.primaryAttributeData)&&i&&f.push({attributeInfo:r.primaryAttributeData.attributeInfo,values:i});const n=l(r.modulationAttributeData,e,a);t(r.modulationAttributeData)&&n&&f.push({attributeInfo:r.modulationAttributeData.attributeInfo,values:n});let c=b(r.rendererInfo,i,n,a);if(r.filterInfo&&r.filterInfo.length>0&&t(r.filterAttributesData)){const i=r.filterAttributesData.filter(t).map((t=>{const r=l(t,e,a),o={attributeInfo:t.attributeInfo,values:r};return f.push(o),o}));o=new Uint32Array(a),a=m(e,c,o,r.filterInfo,i)}for(const t of r.userAttributesData){const r=l(t,e,a);f.push({attributeInfo:t.attributeInfo,values:r})}3*a<c.length&&(c=new Uint8Array(c.buffer.slice(0,3*a))),this._applyElevationOffsetInPlace(e,a,r.elevationOffset);const p=this._transformCoordinates(e,a,r.obb,s.fromJSON(r.inSR),s.fromJSON(r.outSR));return{obb:r.obb,points:p,rgb:c,attributes:f,pointIdFilterMap:o}}_transformCoordinates(t,r,a,s,u){if(!n(t,s,0,t,u,0,r))throw new Error(\"Can't reproject\");const l=f(a.center[0],a.center[1],a.center[2]),b=i(),m=i();e(p,a.quaternion);const c=new Float32Array(3*r);for(let e=0;e<r;e++)b[0]=t[3*e]-l[0],b[1]=t[3*e+1]-l[1],b[2]=t[3*e+2]-l[2],o(m,b,p),a.halfSize[0]=Math.max(a.halfSize[0],Math.abs(m[0])),a.halfSize[1]=Math.max(a.halfSize[1],Math.abs(m[1])),a.halfSize[2]=Math.max(a.halfSize[2],Math.abs(m[2])),c[3*e]=b[0],c[3*e+1]=b[1],c[3*e+2]=b[2];return c}_applyElevationOffsetInPlace(t,r,e){if(0!==e)for(let a=0;a<r;a++)t[3*a+2]+=e}}const p=a();function h(){return new c}export{h as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIua,SAASA,GAAEC,IAAE,GAAEC,IAAE,GAAE;AAAC,QAAK,EAAC,cAAa,GAAE,eAAc,EAAC,IAAED;AAAE,MAAID,KAAE,MAAKG,KAAE;AAAK,MAAG,KAAG,EAAE,CAAAH,KAAE;AAAA,WAAU,KAAG,qCAAkC,uBAAG,OAAK;AAAC,IAAAG,KAAEC,GAAE,SAAS,CAAC;AAAE,UAAMH,KAAEE,GAAE;AAAsB,IAAAH,KAAE,IAAI,WAAW,IAAE,CAAC;AAAE,UAAMK,KAAE,EAAEF,GAAE,kBAAkB;AAAE,aAAQG,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAMC,MAAGF,KAAEA,GAAE,EAAEC,EAAC,CAAC,IAAE,EAAEA,EAAC,KAAG;AAAG,eAAQE,KAAE,GAAEA,KAAEP,GAAE,QAAOO,KAAI,KAAGP,GAAEO,EAAC,EAAE,OAAO,SAASD,EAAC,GAAE;AAAC,QAAAP,GAAE,IAAEM,EAAC,IAAEL,GAAEO,EAAC,EAAE,MAAM,GAAER,GAAE,IAAEM,KAAE,CAAC,IAAEL,GAAEO,EAAC,EAAE,MAAM,GAAER,GAAE,IAAEM,KAAE,CAAC,IAAEL,GAAEO,EAAC,EAAE,MAAM;AAAE;AAAA,MAAK;AAAA,IAAC;AAAA,EAAC,WAAS,KAAG,iCAA8B,uBAAG,OAAK;AAAC,IAAAL,KAAE,EAAE,SAAS,CAAC;AAAE,UAAMF,KAAEE,GAAE;AAAM,IAAAH,KAAE,IAAI,WAAW,IAAE,CAAC;AAAE,UAAMK,KAAE,EAAEF,GAAE,kBAAkB;AAAE,aAAQG,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAMC,KAAEF,KAAEA,GAAE,EAAEC,EAAC,CAAC,IAAE,EAAEA,EAAC,GAAEJ,KAAED,GAAE,SAAO;AAAE,UAAGM,KAAEN,GAAE,CAAC,EAAE,MAAM,CAAAD,GAAE,IAAEM,EAAC,IAAEL,GAAE,CAAC,EAAE,MAAM,GAAED,GAAE,IAAEM,KAAE,CAAC,IAAEL,GAAE,CAAC,EAAE,MAAM,GAAED,GAAE,IAAEM,KAAE,CAAC,IAAEL,GAAE,CAAC,EAAE,MAAM;AAAA,eAAUM,MAAGN,GAAEC,EAAC,EAAE,MAAM,CAAAF,GAAE,IAAEM,EAAC,IAAEL,GAAEC,EAAC,EAAE,MAAM,GAAEF,GAAE,IAAEM,KAAE,CAAC,IAAEL,GAAEC,EAAC,EAAE,MAAM,GAAEF,GAAE,IAAEM,KAAE,CAAC,IAAEL,GAAEC,EAAC,EAAE,MAAM;AAAA,UAAO,UAAQM,KAAE,GAAEA,KAAEP,GAAE,QAAOO,KAAI,KAAGD,KAAEN,GAAEO,EAAC,EAAE,OAAM;AAAC,cAAMH,MAAGE,KAAEN,GAAEO,KAAE,CAAC,EAAE,UAAQP,GAAEO,EAAC,EAAE,QAAMP,GAAEO,KAAE,CAAC,EAAE;AAAO,QAAAR,GAAE,IAAEM,EAAC,IAAEL,GAAEO,EAAC,EAAE,MAAM,IAAEH,KAAEJ,GAAEO,KAAE,CAAC,EAAE,MAAM,KAAG,IAAEH,KAAGL,GAAE,IAAEM,KAAE,CAAC,IAAEL,GAAEO,EAAC,EAAE,MAAM,IAAEH,KAAEJ,GAAEO,KAAE,CAAC,EAAE,MAAM,KAAG,IAAEH,KAAGL,GAAE,IAAEM,KAAE,CAAC,IAAEL,GAAEO,EAAC,EAAE,MAAM,IAAEH,KAAEJ,GAAEO,KAAE,CAAC,EAAE,MAAM,KAAG,IAAEH;AAAG;AAAA,MAAK;AAAA,IAAC;AAAA,EAAC,WAAS,KAAG,qCAAkC,uBAAG,OAAK;AAAC,IAAAF,KAAE,EAAE,SAAS,CAAC;AAAE,UAAMF,KAAEE,GAAE;AAAqB,IAAAH,KAAE,IAAI,WAAW,IAAE,CAAC;AAAE,UAAMM,KAAE,EAAEH,GAAE,kBAAkB;AAAE,aAAQE,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAME,KAAED,KAAEA,GAAE,EAAED,EAAC,CAAC,IAAE,EAAEA,EAAC;AAAE,eAAQG,KAAE,GAAEA,KAAEP,GAAE,QAAOO,KAAI,KAAGD,MAAGN,GAAEO,EAAC,EAAE,YAAUD,MAAGN,GAAEO,EAAC,EAAE,UAAS;AAAC,QAAAR,GAAE,IAAEK,EAAC,IAAEJ,GAAEO,EAAC,EAAE,MAAM,GAAER,GAAE,IAAEK,KAAE,CAAC,IAAEJ,GAAEO,EAAC,EAAE,MAAM,GAAER,GAAE,IAAEK,KAAE,CAAC,IAAEJ,GAAEO,EAAC,EAAE,MAAM;AAAE;AAAA,MAAK;AAAA,IAAC;AAAA,EAAC,OAAK;AAAC,IAAAR,KAAE,IAAI,WAAW,IAAE,CAAC;AAAE,aAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,KAAI,CAAAD,GAAEC,EAAC,IAAE;AAAA,EAAG;AAAC,MAAGC,MAAGC,MAAGA,GAAE,iBAAgB;AAAC,UAAMF,KAAEE,GAAE,gBAAgB,UAASK,KAAEL,GAAE,gBAAgB,UAASE,KAAE;AAAG,aAAQC,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,YAAMC,KAAEL,GAAEI,EAAC,GAAEG,KAAEF,MAAGC,KAAE,IAAED,MAAGN,KAAEI,KAAEA,MAAG,IAAEA,OAAIE,KAAEN,OAAIO,KAAEP;AAAG,MAAAD,GAAE,IAAEM,EAAC,IAAEG,KAAET,GAAE,IAAEM,EAAC,GAAEN,GAAE,IAAEM,KAAE,CAAC,IAAEG,KAAET,GAAE,IAAEM,KAAE,CAAC,GAAEN,GAAE,IAAEM,KAAE,CAAC,IAAEG,KAAET,GAAE,IAAEM,KAAE,CAAC;AAAA,IAAC;AAAA,EAAC;AAAC,SAAON;AAAC;AAAC,SAASG,GAAE,GAAEE,IAAE;AAAC,MAAG,QAAM,EAAE,YAAU,OAAK,EAAE,UAAS;AAAC,UAAMC,KAAE,EAAED,IAAE,CAAC;AAAE,QAAG,EAAEC,GAAE,iBAAiB,QAAQ,EAAE;AAAO,UAAMC,KAAE,EAAEF,IAAEC,GAAE,iBAAiB,QAAQ,GAAE,IAAEA,GAAE,OAAO,QAAO,IAAE,CAAC,EAAE,SAAQ,EAAE,SAAQ,EAAE,OAAO,GAAEN,KAAE,CAAC,EAAE,QAAO,EAAE,QAAO,EAAE,MAAM,GAAEG,KAAEI,GAAE,SAAO,GAAEH,KAAE,IAAI,aAAa,IAAED,EAAC;AAAE,aAAQF,KAAE,GAAEA,KAAEE,IAAEF,KAAI,CAAAG,GAAE,IAAEH,EAAC,IAAEM,GAAE,IAAEN,EAAC,IAAED,GAAE,CAAC,IAAE,EAAE,CAAC,GAAEI,GAAE,IAAEH,KAAE,CAAC,IAAEM,GAAE,IAAEN,KAAE,CAAC,IAAED,GAAE,CAAC,IAAE,EAAE,CAAC,GAAEI,GAAE,IAAEH,KAAE,CAAC,IAAEM,GAAE,IAAEN,KAAE,CAAC,IAAED,GAAE,CAAC,IAAE,EAAE,CAAC;AAAE,WAAOI;AAAA,EAAC;AAAC,MAAG,gBAAc,EAAE,SAAS,QAAO,EAAEC,EAAC,EAAE;AAAM;AAAC,SAASD,GAAEH,IAAEI,IAAEC,IAAE;AAAC,SAAO,EAAEL,EAAC,KAAGA,GAAE,cAAc,eAAaI,KAAEK,GAAEL,IAAEC,EAAC,IAAE,OAAK,EAAEL,EAAC,KAAGA,GAAE,cAAc,cAAY,EAAEA,GAAE,cAAc,aAAYA,GAAE,QAAOK,EAAC,IAAE;AAAI;AAAC,SAASI,GAAET,IAAE,GAAE;AAAC,QAAMI,KAAE,IAAI,aAAa,CAAC;AAAE,WAAQC,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAD,GAAEC,EAAC,IAAEL,GAAE,IAAEK,KAAE,CAAC;AAAE,SAAOD;AAAC;AAAC,SAAS,EAAEJ,IAAE,GAAEI,IAAEC,IAAEC,IAAE;AAAC,QAAML,KAAED,GAAE,SAAO;AAAE,MAAI,IAAE;AAAE,WAAQ,IAAE,GAAE,IAAEC,IAAE,KAAI;AAAC,QAAIA,KAAE;AAAG,aAAQD,KAAE,GAAEA,KAAEK,GAAE,UAAQJ,IAAED,MAAI;AAAC,YAAK,EAAC,YAAWO,GAAC,IAAEF,GAAEL,EAAC,GAAEI,KAAEE,GAAEN,EAAC,EAAE,OAAO,CAAC;AAAE,cAAOO,GAAE,MAAK;AAAA,QAAC,KAAI,yBAAwB;AAAC,gBAAMP,KAAE,cAAYO,GAAE;AAAK,UAAAA,GAAE,OAAO,SAASH,EAAC,MAAIJ,OAAIC,KAAE;AAAI;AAAA,QAAK;AAAA,QAAC,KAAI,4BAA2B;AAAC,gBAAMD,KAAE,EAAEO,GAAE,eAAe,GAAEF,KAAE,EAAEE,GAAE,iBAAiB;AAAE,WAACH,KAAEJ,QAAKA,MAAG,MAAII,KAAEC,QAAKJ,KAAE;AAAI;AAAA,QAAK;AAAA,QAAC,KAAI,0BAAyB;AAAC,gBAAMD,KAAE,KAAGI,IAAEC,KAAED,OAAI,IAAE,IAAGE,KAAED,KAAE,GAAEG,KAAE,MAAIR,IAAEU,KAAEV,OAAIK;AAAE,cAAI,IAAE;AAAG,qBAAUD,MAAKG,GAAE,gBAAgB,KAAG,WAASH,MAAGM,MAAG,kBAAgBN,MAAGI,MAAGF,MAAG,iBAAeF,MAAGM,MAAGJ,MAAG,aAAWF,MAAG,CAACE,IAAE;AAAC,gBAAE;AAAG;AAAA,UAAK;AAAC,gBAAIL,KAAE;AAAI;AAAA,QAAK;AAAA,MAAC;AAAA,IAAC;AAAC,IAAAA,OAAIG,GAAE,CAAC,IAAE,GAAEJ,GAAE,IAAE,CAAC,IAAEA,GAAE,IAAE,CAAC,GAAEA,GAAE,IAAE,IAAE,CAAC,IAAEA,GAAE,IAAE,IAAE,CAAC,GAAEA,GAAE,IAAE,IAAE,CAAC,IAAEA,GAAE,IAAE,IAAE,CAAC,GAAE,EAAE,IAAE,CAAC,IAAE,EAAE,IAAE,CAAC,GAAE,EAAE,IAAE,IAAE,CAAC,IAAE,EAAE,IAAE,IAAE,CAAC,GAAE,EAAE,IAAE,IAAE,CAAC,IAAE,EAAE,IAAE,IAAE,CAAC,GAAE;AAAA,EAAI;AAAC,SAAO;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAO,QAAMA,MAAG,WAASA,KAAE,OAAK,mBAAiBA,KAAE,CAAAA,OAAG,KAAGA,KAAE,oBAAkBA,KAAE,CAAAA,QAAI,MAAIA,OAAI,IAAE,qBAAmBA,KAAE,CAAAA,OAAG,KAAK,IAAIA,EAAC,IAAE,iBAAeA,KAAE,CAAAA,OAAGA,KAAE,KAAG;AAAI;AAAC,SAAS,EAAEA,IAAE;AAAC,MAAI,IAAE;AAAE,aAAUI,MAAKJ,MAAG,CAAC,EAAE,MAAG,KAAGI;AAAE,SAAO;AAAC;;;ACA3mG,IAAMO,KAAN,MAAO;AAAA,EAAC,UAAUC,IAAE;AAAC,UAAMC,KAAE,KAAK,WAAWD,EAAC,GAAE,IAAE,CAACC,GAAE,OAAO,QAAOA,GAAE,IAAI,MAAM;AAAE,MAAEA,GAAE,gBAAgB,KAAG,EAAE,KAAKA,GAAE,iBAAiB,MAAM;AAAE,eAAUC,MAAKD,GAAE,WAAW,aAAWC,GAAE,UAAQA,GAAEA,GAAE,OAAO,MAAM,KAAGA,GAAE,OAAO,WAASD,GAAE,IAAI,UAAQ,EAAE,KAAKC,GAAE,OAAO,MAAM;AAAE,WAAO,QAAQ,QAAQ,EAAC,QAAOD,IAAE,cAAa,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,WAAWE,IAAE;AAAC,UAAMH,KAAED,GAAEI,GAAE,QAAOA,GAAE,cAAc;AAAE,QAAIF,KAAED,GAAE,SAAO,GAAE,IAAE;AAAK,UAAMI,KAAE,CAAC,GAAE,IAAEH,GAAEE,GAAE,sBAAqBH,IAAEC,EAAC;AAAE,MAAEE,GAAE,oBAAoB,KAAG,KAAGC,GAAE,KAAK,EAAC,eAAcD,GAAE,qBAAqB,eAAc,QAAO,EAAC,CAAC;AAAE,UAAME,KAAEJ,GAAEE,GAAE,yBAAwBH,IAAEC,EAAC;AAAE,MAAEE,GAAE,uBAAuB,KAAGE,MAAGD,GAAE,KAAK,EAAC,eAAcD,GAAE,wBAAwB,eAAc,QAAOE,GAAC,CAAC;AAAE,QAAIN,KAAEK,GAAED,GAAE,cAAa,GAAEE,IAAEJ,EAAC;AAAE,QAAGE,GAAE,cAAYA,GAAE,WAAW,SAAO,KAAG,EAAEA,GAAE,oBAAoB,GAAE;AAAC,YAAMG,KAAEH,GAAE,qBAAqB,OAAO,CAAC,EAAE,IAAK,CAAAD,OAAG;AAAC,cAAMC,KAAEF,GAAEC,IAAEF,IAAEC,EAAC,GAAEM,KAAE,EAAC,eAAcL,GAAE,eAAc,QAAOC,GAAC;AAAE,eAAOC,GAAE,KAAKG,EAAC,GAAEA;AAAA,MAAC,CAAE;AAAE,UAAE,IAAI,YAAYN,EAAC,GAAEA,KAAE,EAAED,IAAED,IAAE,GAAEI,GAAE,YAAWG,EAAC;AAAA,IAAC;AAAC,eAAUJ,MAAKC,GAAE,oBAAmB;AAAC,YAAMA,KAAEF,GAAEC,IAAEF,IAAEC,EAAC;AAAE,MAAAG,GAAE,KAAK,EAAC,eAAcF,GAAE,eAAc,QAAOC,GAAC,CAAC;AAAA,IAAC;AAAC,QAAEF,KAAEF,GAAE,WAASA,KAAE,IAAI,WAAWA,GAAE,OAAO,MAAM,GAAE,IAAEE,EAAC,CAAC,IAAG,KAAK,6BAA6BD,IAAEC,IAAEE,GAAE,eAAe;AAAE,UAAMK,KAAE,KAAK,sBAAsBR,IAAEC,IAAEE,GAAE,KAAI,EAAE,SAASA,GAAE,IAAI,GAAE,EAAE,SAASA,GAAE,KAAK,CAAC;AAAE,WAAM,EAAC,KAAIA,GAAE,KAAI,QAAOK,IAAE,KAAIT,IAAE,YAAWK,IAAE,kBAAiB,EAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBF,IAAEC,IAAEF,IAAE,GAAE,GAAE;AAAC,QAAG,CAAC,GAAEC,IAAE,GAAE,GAAEA,IAAE,GAAE,GAAEC,EAAC,EAAE,OAAM,IAAI,MAAM,iBAAiB;AAAE,UAAMM,KAAEN,GAAEF,GAAE,OAAO,CAAC,GAAEA,GAAE,OAAO,CAAC,GAAEA,GAAE,OAAO,CAAC,CAAC,GAAES,KAAE,EAAE,GAAEC,KAAE,EAAE;AAAE,MAAEH,IAAEP,GAAE,UAAU;AAAE,UAAMF,KAAE,IAAI,aAAa,IAAEI,EAAC;AAAE,aAAQH,KAAE,GAAEA,KAAEG,IAAEH,KAAI,CAAAU,GAAE,CAAC,IAAER,GAAE,IAAEF,EAAC,IAAES,GAAE,CAAC,GAAEC,GAAE,CAAC,IAAER,GAAE,IAAEF,KAAE,CAAC,IAAES,GAAE,CAAC,GAAEC,GAAE,CAAC,IAAER,GAAE,IAAEF,KAAE,CAAC,IAAES,GAAE,CAAC,GAAE,EAAEE,IAAED,IAAEF,EAAC,GAAEP,GAAE,SAAS,CAAC,IAAE,KAAK,IAAIA,GAAE,SAAS,CAAC,GAAE,KAAK,IAAIU,GAAE,CAAC,CAAC,CAAC,GAAEV,GAAE,SAAS,CAAC,IAAE,KAAK,IAAIA,GAAE,SAAS,CAAC,GAAE,KAAK,IAAIU,GAAE,CAAC,CAAC,CAAC,GAAEV,GAAE,SAAS,CAAC,IAAE,KAAK,IAAIA,GAAE,SAAS,CAAC,GAAE,KAAK,IAAIU,GAAE,CAAC,CAAC,CAAC,GAAEZ,GAAE,IAAEC,EAAC,IAAEU,GAAE,CAAC,GAAEX,GAAE,IAAEC,KAAE,CAAC,IAAEU,GAAE,CAAC,GAAEX,GAAE,IAAEC,KAAE,CAAC,IAAEU,GAAE,CAAC;AAAE,WAAOX;AAAA,EAAC;AAAA,EAAC,6BAA6BG,IAAEC,IAAEH,IAAE;AAAC,QAAG,MAAIA,GAAE,UAAQC,KAAE,GAAEA,KAAEE,IAAEF,KAAI,CAAAC,GAAE,IAAED,KAAE,CAAC,KAAGD;AAAA,EAAC;AAAC;AAAC,IAAMQ,KAAE,EAAE;AAAE,SAAS,IAAG;AAAC,SAAO,IAAIT;AAAC;", "names": ["f", "e", "n", "c", "a", "r", "t", "l", "o", "s", "d", "i", "c", "e", "a", "t", "r", "f", "n", "i", "o", "p", "l", "b", "m"]}