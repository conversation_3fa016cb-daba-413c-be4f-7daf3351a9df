{"version": 3, "sources": ["../../@arcgis/core/layers/support/SceneModification.js", "../../@arcgis/core/layers/support/SceneModifications.js", "../../@arcgis/core/layers/IntegratedMeshLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../chunks/tslib.es6.js\";import\"../../geometry.js\";import{JSONSupport as r}from\"../../core/JSONSupport.js\";import{clone as o}from\"../../core/lang.js\";import t from\"../../core/Warning.js\";import{property as s}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import{subclass as p}from\"../../core/accessorSupport/decorators/subclass.js\";import{writer as i}from\"../../core/accessorSupport/decorators/writer.js\";import{persistable as a}from\"../../core/accessorSupport/decorators/persistable.js\";import{canProjectWithoutEngine as c,projectPolygon as n}from\"../../geometry/projection.js\";import l from\"../../geometry/Polygon.js\";var m;let y=m=class extends r{constructor(e){super(e),this.geometry=null,this.type=\"clip\"}writeGeometry(e,r,o,s){if(s.layer&&s.layer.spatialReference&&!s.layer.spatialReference.equals(this.geometry.spatialReference)){if(!c(e.spatialReference,s.layer.spatialReference))return void(s&&s.messages&&s.messages.push(new t(\"scenemodification:unsupported\",\"Scene modifications with incompatible spatial references are not supported\",{modification:this,spatialReference:s.layer.spatialReference,context:s})));const p=new l;n(e,p,s.layer.spatialReference),r[o]=p.toJSON(s)}else r[o]=e.toJSON(s);delete r[o].spatialReference}clone(){return new m({geometry:o(this.geometry),type:this.type})}};e([s({type:l}),a()],y.prototype,\"geometry\",void 0),e([i([\"web-scene\",\"portal-item\"],\"geometry\")],y.prototype,\"writeGeometry\",null),e([s({type:[\"clip\",\"mask\",\"replace\"],nonNullable:!0}),a()],y.prototype,\"type\",void 0),y=m=e([p(\"esri.layers.support.SceneModification\")],y);const f=y;export{f as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../chunks/tslib.es6.js\";import o from\"../../request.js\";import t from\"../../core/Collection.js\";import{JSONSupportMixin as e}from\"../../core/JSONSupport.js\";import{get as s}from\"../../core/maybe.js\";import{urlToObject as i}from\"../../core/urlUtils.js\";import{property as c}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as p}from\"../../core/accessorSupport/decorators/subclass.js\";import m from\"./SceneModification.js\";var a;let n=a=class extends(e(t.ofType(m))){constructor(r){super(r),this.url=null}clone(){return new a({url:this.url,items:this.items.map((r=>r.clone()))})}toJSON(r){return this.toArray().map((o=>o.toJSON(r))).filter((r=>!!r.geometry))}static fromJSON(r,o){const t=new a;for(const e of r)t.add(m.fromJSON(e,o));return t}static async fromUrl(r,t,e){const c={url:i(r),origin:\"service\"},p=await o(r,{responseType:\"json\",signal:s(e,\"signal\")}),n=t.toJSON(),l=[];for(const o of p.data)l.push(m.fromJSON({...o,geometry:{...o.geometry,spatialReference:n}},c));return new a({url:r,items:l})}};r([c({type:String})],n.prototype,\"url\",void 0),n=a=r([p(\"esri.layers.support.SceneModifications\")],n);const l=n;export{l as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import t from\"../core/Error.js\";import r from\"../core/Handles.js\";import{get as o,isSome as i,isNone as s}from\"../core/maybe.js\";import{MultiOriginJSONMixin as a}from\"../core/MultiOriginJSONSupport.js\";import{throwIfAbortError as n}from\"../core/promiseUtils.js\";import{on as p,sync as d}from\"../core/reactiveUtils.js\";import{property as m}from\"../core/accessorSupport/decorators/property.js\";import\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{reader as c}from\"../core/accessorSupport/decorators/reader.js\";import{subclass as y}from\"../core/accessorSupport/decorators/subclass.js\";import{persistable as l}from\"../core/accessorSupport/decorators/persistable.js\";import h from\"./Layer.js\";import{APIKeyMixin as f}from\"./mixins/APIKeyMixin.js\";import{ArcGISService as u}from\"./mixins/ArcGISService.js\";import{OperationalLayer as v}from\"./mixins/OperationalLayer.js\";import{PortalLayer as S}from\"./mixins/PortalLayer.js\";import{ScaleRangeLayer as g}from\"./mixins/ScaleRangeLayer.js\";import{SceneService as j,SaveOperationType as w}from\"./mixins/SceneService.js\";import{elevationInfo as _}from\"./support/commonProperties.js\";import{I3SNodePageDefinition as T,I3SMaterialDefinition as x,I3STextureSetDefinition as L,I3SGeometryDefinition as I}from\"./support/I3SLayerDefinitions.js\";import O from\"./support/SceneModifications.js\";import{f as M}from\"../chunks/persistableUrlUtils.js\";let b=class extends(j(u(v(S(g(a(f(h)))))))){constructor(...e){super(...e),this._handles=new r,this.geometryType=\"mesh\",this.operationalLayerType=\"IntegratedMeshLayer\",this.type=\"integrated-mesh\",this.nodePages=null,this.materialDefinitions=null,this.textureSetDefinitions=null,this.geometryDefinitions=null,this.serviceUpdateTimeStamp=null,this.profile=\"mesh-pyramids\",this.modifications=null,this._modificationsSource=null,this.elevationInfo=null,this.path=null}destroy(){this._handles.destroy()}initialize(){this._handles.add(p((()=>this.modifications),\"after-changes\",(()=>this.modifications=this.modifications),d))}normalizeCtorArgs(e,t){return\"string\"==typeof e?{url:e,...t}:e}readModifications(e,t,r){this._modificationsSource={url:M(e,r),context:r}}async load(e){return this.addResolvingPromise(this._doLoad(e)),this}async _doLoad(e){const t=o(e,\"signal\");try{await this.loadFromPortal({supportedTypes:[\"Scene Service\"]},e)}catch(r){n(r)}if(await this._fetchService(t),i(this._modificationsSource)){const t=await O.fromUrl(this._modificationsSource.url,this.spatialReference,e);this.setAtOrigin(\"modifications\",t,this._modificationsSource.context.origin),this._modificationsSource=null}await this._fetchIndexAndUpdateExtent(this.nodePages,t)}beforeSave(){if(!s(this._modificationsSource))return this.load().then((()=>{}),(()=>{}))}async saveAs(e,t){return this._debouncedSaveOperations(w.SAVE_AS,{...t,getTypeKeywords:()=>this._getTypeKeywords(),portalItemLayerType:\"integrated-mesh\"},e)}async save(){const e={getTypeKeywords:()=>this._getTypeKeywords(),portalItemLayerType:\"integrated-mesh\"};return this._debouncedSaveOperations(w.SAVE,e)}validateLayer(e){if(e.layerType&&\"IntegratedMesh\"!==e.layerType)throw new t(\"integrated-mesh-layer:layer-type-not-supported\",\"IntegratedMeshLayer does not support this layer type\",{layerType:e.layerType});if(isNaN(this.version.major)||isNaN(this.version.minor))throw new t(\"layer:service-version-not-supported\",\"Service version is not supported.\",{serviceVersion:this.version.versionString,supportedVersions:\"1.x\"});if(this.version.major>1)throw new t(\"layer:service-version-too-new\",\"Service version is too new.\",{serviceVersion:this.version.versionString,supportedVersions:\"1.x\"})}_getTypeKeywords(){return[\"IntegratedMeshLayer\"]}};e([m({type:String,readOnly:!0})],b.prototype,\"geometryType\",void 0),e([m({type:[\"show\",\"hide\"]})],b.prototype,\"listMode\",void 0),e([m({type:[\"IntegratedMeshLayer\"]})],b.prototype,\"operationalLayerType\",void 0),e([m({json:{read:!1},readOnly:!0})],b.prototype,\"type\",void 0),e([m({type:T,readOnly:!0})],b.prototype,\"nodePages\",void 0),e([m({type:[x],readOnly:!0})],b.prototype,\"materialDefinitions\",void 0),e([m({type:[L],readOnly:!0})],b.prototype,\"textureSetDefinitions\",void 0),e([m({type:[I],readOnly:!0})],b.prototype,\"geometryDefinitions\",void 0),e([m({readOnly:!0})],b.prototype,\"serviceUpdateTimeStamp\",void 0),e([m({type:O}),l({origins:[\"web-scene\",\"portal-item\"],type:\"resource\",prefix:\"modifications\"})],b.prototype,\"modifications\",void 0),e([c([\"web-scene\",\"portal-item\"],\"modifications\")],b.prototype,\"readModifications\",null),e([m(_)],b.prototype,\"elevationInfo\",void 0),e([m({type:String,json:{origins:{\"web-scene\":{read:!0,write:!0},\"portal-item\":{read:!0,write:!0}},read:!1}})],b.prototype,\"path\",void 0),b=e([y(\"esri.layers.IntegratedMeshLayer\")],b);const A=b;export{A as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI2rB,IAAIA;AAAE,IAAIC,KAAED,KAAE,cAAc,EAAC;AAAA,EAAC,YAAYE,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,WAAS,MAAK,KAAK,OAAK;AAAA,EAAM;AAAA,EAAC,cAAcA,IAAEC,IAAEC,IAAEC,IAAE;AAAC,QAAGA,GAAE,SAAOA,GAAE,MAAM,oBAAkB,CAACA,GAAE,MAAM,iBAAiB,OAAO,KAAK,SAAS,gBAAgB,GAAE;AAAC,UAAG,CAAC,GAAEH,GAAE,kBAAiBG,GAAE,MAAM,gBAAgB,EAAE,QAAO,MAAKA,MAAGA,GAAE,YAAUA,GAAE,SAAS,KAAK,IAAIC,GAAE,iCAAgC,8EAA6E,EAAC,cAAa,MAAK,kBAAiBD,GAAE,MAAM,kBAAiB,SAAQA,GAAC,CAAC,CAAC;AAAG,YAAME,KAAE,IAAI;AAAE,SAAEL,IAAEK,IAAEF,GAAE,MAAM,gBAAgB,GAAEF,GAAEC,EAAC,IAAEG,GAAE,OAAOF,EAAC;AAAA,IAAC,MAAM,CAAAF,GAAEC,EAAC,IAAEF,GAAE,OAAOG,EAAC;AAAE,WAAOF,GAAEC,EAAC,EAAE;AAAA,EAAgB;AAAA,EAAC,QAAO;AAAC,WAAO,IAAIJ,GAAE,EAAC,UAAS,EAAE,KAAK,QAAQ,GAAE,MAAK,KAAK,KAAI,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,CAAC,GAAE,EAAE,CAAC,GAAEC,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAACE,GAAE,CAAC,aAAY,aAAa,GAAE,UAAU,CAAC,GAAEF,GAAE,WAAU,iBAAgB,IAAI,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,QAAO,SAAS,GAAE,aAAY,KAAE,CAAC,GAAE,EAAE,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAED,KAAE,EAAE,CAAC,EAAE,uCAAuC,CAAC,GAAEC,EAAC;AAAE,IAAM,IAAEA;;;ACAxlC,IAAIO;AAAE,IAAI,IAAEA,KAAE,cAAc,EAAE,EAAE,OAAO,CAAC,CAAC,EAAE;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAMA,EAAC,GAAE,KAAK,MAAI;AAAA,EAAI;AAAA,EAAC,QAAO;AAAC,WAAO,IAAID,GAAE,EAAC,KAAI,KAAK,KAAI,OAAM,KAAK,MAAM,IAAK,CAAAC,OAAGA,GAAE,MAAM,CAAE,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAOA,IAAE;AAAC,WAAO,KAAK,QAAQ,EAAE,IAAK,CAAAC,OAAGA,GAAE,OAAOD,EAAC,CAAE,EAAE,OAAQ,CAAAA,OAAG,CAAC,CAACA,GAAE,QAAS;AAAA,EAAC;AAAA,EAAC,OAAO,SAASA,IAAEC,IAAE;AAAC,UAAMC,KAAE,IAAIH;AAAE,eAAUI,MAAKH,GAAE,CAAAE,GAAE,IAAI,EAAE,SAASC,IAAEF,EAAC,CAAC;AAAE,WAAOC;AAAA,EAAC;AAAA,EAAC,aAAa,QAAQF,IAAEE,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAC,KAAI,EAAEJ,EAAC,GAAE,QAAO,UAAS,GAAEK,KAAE,MAAMC,GAAEN,IAAE,EAAC,cAAa,QAAO,QAAO,EAAEG,IAAE,QAAQ,EAAC,CAAC,GAAEI,KAAEL,GAAE,OAAO,GAAEM,KAAE,CAAC;AAAE,eAAUP,MAAKI,GAAE,KAAK,CAAAG,GAAE,KAAK,EAAE,SAAS,EAAC,GAAGP,IAAE,UAAS,EAAC,GAAGA,GAAE,UAAS,kBAAiBM,GAAC,EAAC,GAAEH,EAAC,CAAC;AAAE,WAAO,IAAIL,GAAE,EAAC,KAAIC,IAAE,OAAMQ,GAAC,CAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,OAAM,CAAC,CAAC,GAAE,EAAE,WAAU,OAAM,MAAM,GAAE,IAAET,KAAE,EAAE,CAAC,EAAE,wCAAwC,CAAC,GAAE,CAAC;AAAE,IAAMS,KAAE;;;ACA+M,IAAIC,KAAE,cAAc,EAAEC,GAAEC,GAAE,EAAEC,GAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,eAAeC,IAAE;AAAC,UAAM,GAAGA,EAAC,GAAE,KAAK,WAAS,IAAID,MAAE,KAAK,eAAa,QAAO,KAAK,uBAAqB,uBAAsB,KAAK,OAAK,mBAAkB,KAAK,YAAU,MAAK,KAAK,sBAAoB,MAAK,KAAK,wBAAsB,MAAK,KAAK,sBAAoB,MAAK,KAAK,yBAAuB,MAAK,KAAK,UAAQ,iBAAgB,KAAK,gBAAc,MAAK,KAAK,uBAAqB,MAAK,KAAK,gBAAc,MAAK,KAAK,OAAK;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,SAAK,SAAS,QAAQ;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,SAAS,IAAIE,GAAG,MAAI,KAAK,eAAe,iBAAiB,MAAI,KAAK,gBAAc,KAAK,eAAe,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,kBAAkBD,IAAED,IAAE;AAAC,WAAM,YAAU,OAAOC,KAAE,EAAC,KAAIA,IAAE,GAAGD,GAAC,IAAEC;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAED,IAAEG,IAAE;AAAC,SAAK,uBAAqB,EAAC,KAAI,EAAEF,IAAEE,EAAC,GAAE,SAAQA,GAAC;AAAA,EAAC;AAAA,EAAC,MAAM,KAAKF,IAAE;AAAC,WAAO,KAAK,oBAAoB,KAAK,QAAQA,EAAC,CAAC,GAAE;AAAA,EAAI;AAAA,EAAC,MAAM,QAAQA,IAAE;AAAC,UAAMD,KAAE,EAAEC,IAAE,QAAQ;AAAE,QAAG;AAAC,YAAM,KAAK,eAAe,EAAC,gBAAe,CAAC,eAAe,EAAC,GAAEA,EAAC;AAAA,IAAC,SAAOE,IAAE;AAAC,QAAEA,EAAC;AAAA,IAAC;AAAC,QAAG,MAAM,KAAK,cAAcH,EAAC,GAAE,EAAE,KAAK,oBAAoB,GAAE;AAAC,YAAMA,KAAE,MAAMI,GAAE,QAAQ,KAAK,qBAAqB,KAAI,KAAK,kBAAiBH,EAAC;AAAE,WAAK,YAAY,iBAAgBD,IAAE,KAAK,qBAAqB,QAAQ,MAAM,GAAE,KAAK,uBAAqB;AAAA,IAAI;AAAC,UAAM,KAAK,2BAA2B,KAAK,WAAUA,EAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,QAAG,CAAC,EAAE,KAAK,oBAAoB,EAAE,QAAO,KAAK,KAAK,EAAE,KAAM,MAAI;AAAA,IAAC,GAAI,MAAI;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,MAAM,OAAOC,IAAED,IAAE;AAAC,WAAO,KAAK,yBAAyBK,GAAE,SAAQ,EAAC,GAAGL,IAAE,iBAAgB,MAAI,KAAK,iBAAiB,GAAE,qBAAoB,kBAAiB,GAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,OAAM;AAAC,UAAMA,KAAE,EAAC,iBAAgB,MAAI,KAAK,iBAAiB,GAAE,qBAAoB,kBAAiB;AAAE,WAAO,KAAK,yBAAyBI,GAAE,MAAKJ,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,QAAGA,GAAE,aAAW,qBAAmBA,GAAE,UAAU,OAAM,IAAI,EAAE,kDAAiD,wDAAuD,EAAC,WAAUA,GAAE,UAAS,CAAC;AAAE,QAAG,MAAM,KAAK,QAAQ,KAAK,KAAG,MAAM,KAAK,QAAQ,KAAK,EAAE,OAAM,IAAI,EAAE,uCAAsC,qCAAoC,EAAC,gBAAe,KAAK,QAAQ,eAAc,mBAAkB,MAAK,CAAC;AAAE,QAAG,KAAK,QAAQ,QAAM,EAAE,OAAM,IAAI,EAAE,iCAAgC,+BAA8B,EAAC,gBAAe,KAAK,QAAQ,eAAc,mBAAkB,MAAK,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,WAAM,CAAC,qBAAqB;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,UAAS,KAAE,CAAC,CAAC,GAAEJ,GAAE,WAAU,gBAAe,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,YAAW,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,qBAAqB,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,wBAAuB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAE,GAAE,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKS,IAAE,UAAS,KAAE,CAAC,CAAC,GAAET,GAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAACO,EAAC,GAAE,UAAS,KAAE,CAAC,CAAC,GAAEP,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAACU,EAAC,GAAE,UAAS,KAAE,CAAC,CAAC,GAAEV,GAAE,WAAU,yBAAwB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,GAAE,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,uBAAsB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,0BAAyB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAKO,GAAC,CAAC,GAAE,EAAE,EAAC,SAAQ,CAAC,aAAY,aAAa,GAAE,MAAK,YAAW,QAAO,gBAAe,CAAC,CAAC,GAAEP,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,aAAY,aAAa,GAAE,eAAe,CAAC,GAAEA,GAAE,WAAU,qBAAoB,IAAI,GAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAEA,GAAE,WAAU,iBAAgB,MAAM,GAAE,EAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,SAAQ,EAAC,aAAY,EAAC,MAAK,MAAG,OAAM,KAAE,GAAE,eAAc,EAAC,MAAK,MAAG,OAAM,KAAE,EAAC,GAAE,MAAK,MAAE,EAAC,CAAC,CAAC,GAAEA,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAE,EAAE,CAAC,EAAE,iCAAiC,CAAC,GAAEA,EAAC;AAAE,IAAM,IAAEA;", "names": ["m", "y", "e", "r", "o", "s", "t", "p", "a", "r", "o", "t", "e", "c", "p", "U", "n", "l", "b", "p", "c", "t", "e", "a", "r", "l", "L", "s", "u"]}