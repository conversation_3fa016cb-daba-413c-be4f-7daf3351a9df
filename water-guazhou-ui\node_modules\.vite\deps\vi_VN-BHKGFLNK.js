import {
  r
} from "./chunk-7VG4CGLX.js";
import {
  o
} from "./chunk-EPJSBV4J.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/chunks/vi_VN.js
function r2(e, t) {
  for (var r3 = 0; r3 < t.length; r3++) {
    const o3 = t[r3];
    if ("string" != typeof o3 && !Array.isArray(o3)) {
      for (const t2 in o3) if ("default" !== t2 && !(t2 in e)) {
        const r4 = Object.getOwnPropertyDescriptor(o3, t2);
        r4 && Object.defineProperty(e, t2, r4.get ? r4 : { enumerable: true, get: () => o3[t2] });
      }
    }
  }
  return Object.freeze(Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }));
}
var o2;
var _;
var n = {};
var a = { get exports() {
  return n;
}, set exports(e) {
  n = e;
} };
o2 = a, void 0 !== (_ = function(e, t) {
  Object.defineProperty(t, "__esModule", { value: true }), t.default = { _decimalSeparator: ",", _thousandSeparator: ".", _percentPrefix: null, _percentSuffix: "%", _big_number_suffix_3: "k", _big_number_suffix_6: "M", _big_number_suffix_9: "G", _big_number_suffix_12: "T", _big_number_suffix_15: "P", _big_number_suffix_18: "E", _big_number_suffix_21: "Z", _big_number_suffix_24: "Y", _small_number_suffix_3: "m", _small_number_suffix_6: "μ", _small_number_suffix_9: "n", _small_number_suffix_12: "p", _small_number_suffix_15: "f", _small_number_suffix_18: "a", _small_number_suffix_21: "z", _small_number_suffix_24: "y", _byte_suffix_B: "B", _byte_suffix_KB: "KB", _byte_suffix_MB: "MB", _byte_suffix_GB: "GB", _byte_suffix_TB: "TB", _byte_suffix_PB: "PB", _date_millisecond: "mm:ss SSS", _date_second: "HH:mm:ss", _date_minute: "HH:mm", _date_hour: "HH:mm", _date_day: "MMM dd", _date_week: "ww", _date_month: "MMM", _date_year: "yyyy", _duration_millisecond: "SSS", _duration_millisecond_second: "ss.SSS", _duration_millisecond_minute: "mm:ss SSS", _duration_millisecond_hour: "hh:mm:ss SSS", _duration_millisecond_day: "d'd' mm:ss SSS", _duration_millisecond_week: "d'd' mm:ss SSS", _duration_millisecond_month: "M'm' dd'd' mm:ss SSS", _duration_millisecond_year: "y'y' MM'm' dd'd' mm:ss SSS", _duration_second: "ss", _duration_second_minute: "mm:ss", _duration_second_hour: "hh:mm:ss", _duration_second_day: "d'd' hh:mm:ss", _duration_second_week: "d'd' hh:mm:ss", _duration_second_month: "M'm' dd'd' hh:mm:ss", _duration_second_year: "y'y' MM'm' dd'd' hh:mm:ss", _duration_minute: "mm", _duration_minute_hour: "hh:mm", _duration_minute_day: "d'd' hh:mm", _duration_minute_week: "d'd' hh:mm", _duration_minute_month: "M'm' dd'd' hh:mm", _duration_minute_year: "y'y' MM'm' dd'd' hh:mm", _duration_hour: "hh'h'", _duration_hour_day: "d'd' hh'h'", _duration_hour_week: "d'd' hh'h'", _duration_hour_month: "M'm' dd'd' hh'h'", _duration_hour_year: "y'y' MM'm' dd'd' hh'h'", _duration_day: "d'd'", _duration_day_week: "d'd'", _duration_day_month: "M'm' dd'd'", _duration_day_year: "y'y' MM'm' dd'd'", _duration_week: "w'w'", _duration_week_month: "w'w'", _duration_week_year: "w'w'", _duration_month: "M'm'", _duration_month_year: "y'y' MM'm'", _duration_year: "y'y'", _era_ad: "sau CN", _era_bc: "Trước CN", A: "s", P: "c", AM: "SA", PM: "CH", "A.M.": "SA", "P.M.": "CH", January: "tháng 1", February: "tháng 2", March: "tháng 3", April: "tháng 4", May: "tháng 5", June: "tháng 6", July: "tháng 7", August: "tháng 8", September: "tháng 9", October: "tháng 10", November: "tháng 11", December: "tháng 12", Jan: "thg 1", Feb: "thg 2", Mar: "thg 3", Apr: "thg 4", "May(short)": "thg 5", Jun: "thg 6", Jul: "thg 7", Aug: "thg 8", Sep: "thg 9", Oct: "thg 10", Nov: "thg 11", Dec: "thg 12", Sunday: "Chủ Nhật", Monday: "Thứ Hai", Tuesday: "Thứ Ba", Wednesday: "Thứ Tư", Thursday: "Thứ Năm", Friday: "Thứ Sáu", Saturday: "Thứ Bảy", Sun: "CN", Mon: "Th 2", Tue: "Th 3", Wed: "Th 4", Thu: "Th 5", Fri: "Th 6", Sat: "Th 7", _dateOrd: function(e2) {
    var t2 = "th";
    if (e2 < 11 || e2 > 13) switch (e2 % 10) {
      case 1:
        t2 = "st";
        break;
      case 2:
        t2 = "nd";
        break;
      case 3:
        t2 = "rd";
    }
    return t2;
  }, "Zoom Out": "Thu phóng", Play: "Phát", Stop: "Dừng", Legend: "Chú giải", "Click, tap or press ENTER to toggle": "", Loading: "Đang tải", Home: "Trang chủ", Chart: "", "Serial chart": "", "X/Y chart": "", "Pie chart": "", "Gauge chart": "", "Radar chart": "", "Sankey diagram": "", "Flow diagram": "", "Chord diagram": "", "TreeMap chart": "", "Sliced chart": "", Series: "", "Candlestick Series": "", "OHLC Series": "", "Column Series": "", "Line Series": "", "Pie Slice Series": "", "Funnel Series": "", "Pyramid Series": "", "X/Y Series": "", Map: "", "Press ENTER to zoom in": "", "Press ENTER to zoom out": "", "Use arrow keys to zoom in and out": "", "Use plus and minus keys on your keyboard to zoom in and out": "", Export: "In", Image: "Hình ảnh", Data: "Dữ liệu", Print: "In", "Click, tap or press ENTER to open": "", "Click, tap or press ENTER to print.": "", "Click, tap or press ENTER to export as %1.": "", 'To save the image, right-click this link and choose "Save picture as..."': "", 'To save the image, right-click thumbnail on the left and choose "Save picture as..."': "", "(Press ESC to close this message)": "", "Image Export Complete": "", "Export operation took longer than expected. Something might have gone wrong.": "", "Saved from": "", PNG: "", JPG: "", GIF: "", SVG: "", PDF: "", JSON: "", CSV: "", XLSX: "", "Use TAB to select grip buttons or left and right arrows to change selection": "", "Use left and right arrows to move selection": "", "Use left and right arrows to move left selection": "", "Use left and right arrows to move right selection": "", "Use TAB select grip buttons or up and down arrows to change selection": "", "Use up and down arrows to move selection": "", "Use up and down arrows to move lower selection": "", "Use up and down arrows to move upper selection": "", "From %1 to %2": "Từ %1 đến %2", "From %1": "Từ %1", "To %1": "Đến %1", "No parser available for file: %1": "", "Error parsing file: %1": "", "Unable to load file: %1": "", "Invalid date": "" };
}(r, n)) && (o2.exports = _);
var i = r2({ __proto__: null, default: o(n) }, [n]);
export {
  i as v
};
//# sourceMappingURL=vi_VN-BHKGFLNK.js.map
