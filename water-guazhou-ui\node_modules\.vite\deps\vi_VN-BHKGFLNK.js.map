{"version": 3, "sources": ["../../@arcgis/core/chunks/vi_VN.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{g as e}from\"./_commonjsHelpers.js\";import{c as t}from\"./_commonjs-dynamic-modules.js\";function r(e,t){for(var r=0;r<t.length;r++){const o=t[r];if(\"string\"!=typeof o&&!Array.isArray(o))for(const t in o)if(\"default\"!==t&&!(t in e)){const r=Object.getOwnPropertyDescriptor(o,t);r&&Object.defineProperty(e,t,r.get?r:{enumerable:!0,get:()=>o[t]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}))}var o,_,n={},a={get exports(){return n},set exports(e){n=e}};o=a,void 0!==(_=function(e,t){Object.defineProperty(t,\"__esModule\",{value:!0}),t.default={_decimalSeparator:\",\",_thousandSeparator:\".\",_percentPrefix:null,_percentSuffix:\"%\",_big_number_suffix_3:\"k\",_big_number_suffix_6:\"M\",_big_number_suffix_9:\"G\",_big_number_suffix_12:\"T\",_big_number_suffix_15:\"P\",_big_number_suffix_18:\"E\",_big_number_suffix_21:\"Z\",_big_number_suffix_24:\"Y\",_small_number_suffix_3:\"m\",_small_number_suffix_6:\"μ\",_small_number_suffix_9:\"n\",_small_number_suffix_12:\"p\",_small_number_suffix_15:\"f\",_small_number_suffix_18:\"a\",_small_number_suffix_21:\"z\",_small_number_suffix_24:\"y\",_byte_suffix_B:\"B\",_byte_suffix_KB:\"KB\",_byte_suffix_MB:\"MB\",_byte_suffix_GB:\"GB\",_byte_suffix_TB:\"TB\",_byte_suffix_PB:\"PB\",_date_millisecond:\"mm:ss SSS\",_date_second:\"HH:mm:ss\",_date_minute:\"HH:mm\",_date_hour:\"HH:mm\",_date_day:\"MMM dd\",_date_week:\"ww\",_date_month:\"MMM\",_date_year:\"yyyy\",_duration_millisecond:\"SSS\",_duration_millisecond_second:\"ss.SSS\",_duration_millisecond_minute:\"mm:ss SSS\",_duration_millisecond_hour:\"hh:mm:ss SSS\",_duration_millisecond_day:\"d'd' mm:ss SSS\",_duration_millisecond_week:\"d'd' mm:ss SSS\",_duration_millisecond_month:\"M'm' dd'd' mm:ss SSS\",_duration_millisecond_year:\"y'y' MM'm' dd'd' mm:ss SSS\",_duration_second:\"ss\",_duration_second_minute:\"mm:ss\",_duration_second_hour:\"hh:mm:ss\",_duration_second_day:\"d'd' hh:mm:ss\",_duration_second_week:\"d'd' hh:mm:ss\",_duration_second_month:\"M'm' dd'd' hh:mm:ss\",_duration_second_year:\"y'y' MM'm' dd'd' hh:mm:ss\",_duration_minute:\"mm\",_duration_minute_hour:\"hh:mm\",_duration_minute_day:\"d'd' hh:mm\",_duration_minute_week:\"d'd' hh:mm\",_duration_minute_month:\"M'm' dd'd' hh:mm\",_duration_minute_year:\"y'y' MM'm' dd'd' hh:mm\",_duration_hour:\"hh'h'\",_duration_hour_day:\"d'd' hh'h'\",_duration_hour_week:\"d'd' hh'h'\",_duration_hour_month:\"M'm' dd'd' hh'h'\",_duration_hour_year:\"y'y' MM'm' dd'd' hh'h'\",_duration_day:\"d'd'\",_duration_day_week:\"d'd'\",_duration_day_month:\"M'm' dd'd'\",_duration_day_year:\"y'y' MM'm' dd'd'\",_duration_week:\"w'w'\",_duration_week_month:\"w'w'\",_duration_week_year:\"w'w'\",_duration_month:\"M'm'\",_duration_month_year:\"y'y' MM'm'\",_duration_year:\"y'y'\",_era_ad:\"sau CN\",_era_bc:\"Trước CN\",A:\"s\",P:\"c\",AM:\"SA\",PM:\"CH\",\"A.M.\":\"SA\",\"P.M.\":\"CH\",January:\"tháng 1\",February:\"tháng 2\",March:\"tháng 3\",April:\"tháng 4\",May:\"tháng 5\",June:\"tháng 6\",July:\"tháng 7\",August:\"tháng 8\",September:\"tháng 9\",October:\"tháng 10\",November:\"tháng 11\",December:\"tháng 12\",Jan:\"thg 1\",Feb:\"thg 2\",Mar:\"thg 3\",Apr:\"thg 4\",\"May(short)\":\"thg 5\",Jun:\"thg 6\",Jul:\"thg 7\",Aug:\"thg 8\",Sep:\"thg 9\",Oct:\"thg 10\",Nov:\"thg 11\",Dec:\"thg 12\",Sunday:\"Chủ Nhật\",Monday:\"Thứ Hai\",Tuesday:\"Thứ Ba\",Wednesday:\"Thứ Tư\",Thursday:\"Thứ Năm\",Friday:\"Thứ Sáu\",Saturday:\"Thứ Bảy\",Sun:\"CN\",Mon:\"Th 2\",Tue:\"Th 3\",Wed:\"Th 4\",Thu:\"Th 5\",Fri:\"Th 6\",Sat:\"Th 7\",_dateOrd:function(e){var t=\"th\";if(e<11||e>13)switch(e%10){case 1:t=\"st\";break;case 2:t=\"nd\";break;case 3:t=\"rd\"}return t},\"Zoom Out\":\"Thu phóng\",Play:\"Phát\",Stop:\"Dừng\",Legend:\"Chú giải\",\"Click, tap or press ENTER to toggle\":\"\",Loading:\"Đang tải\",Home:\"Trang chủ\",Chart:\"\",\"Serial chart\":\"\",\"X/Y chart\":\"\",\"Pie chart\":\"\",\"Gauge chart\":\"\",\"Radar chart\":\"\",\"Sankey diagram\":\"\",\"Flow diagram\":\"\",\"Chord diagram\":\"\",\"TreeMap chart\":\"\",\"Sliced chart\":\"\",Series:\"\",\"Candlestick Series\":\"\",\"OHLC Series\":\"\",\"Column Series\":\"\",\"Line Series\":\"\",\"Pie Slice Series\":\"\",\"Funnel Series\":\"\",\"Pyramid Series\":\"\",\"X/Y Series\":\"\",Map:\"\",\"Press ENTER to zoom in\":\"\",\"Press ENTER to zoom out\":\"\",\"Use arrow keys to zoom in and out\":\"\",\"Use plus and minus keys on your keyboard to zoom in and out\":\"\",Export:\"In\",Image:\"Hình ảnh\",Data:\"Dữ liệu\",Print:\"In\",\"Click, tap or press ENTER to open\":\"\",\"Click, tap or press ENTER to print.\":\"\",\"Click, tap or press ENTER to export as %1.\":\"\",'To save the image, right-click this link and choose \"Save picture as...\"':\"\",'To save the image, right-click thumbnail on the left and choose \"Save picture as...\"':\"\",\"(Press ESC to close this message)\":\"\",\"Image Export Complete\":\"\",\"Export operation took longer than expected. Something might have gone wrong.\":\"\",\"Saved from\":\"\",PNG:\"\",JPG:\"\",GIF:\"\",SVG:\"\",PDF:\"\",JSON:\"\",CSV:\"\",XLSX:\"\",\"Use TAB to select grip buttons or left and right arrows to change selection\":\"\",\"Use left and right arrows to move selection\":\"\",\"Use left and right arrows to move left selection\":\"\",\"Use left and right arrows to move right selection\":\"\",\"Use TAB select grip buttons or up and down arrows to change selection\":\"\",\"Use up and down arrows to move selection\":\"\",\"Use up and down arrows to move lower selection\":\"\",\"Use up and down arrows to move upper selection\":\"\",\"From %1 to %2\":\"Từ %1 đến %2\",\"From %1\":\"Từ %1\",\"To %1\":\"Đến %1\",\"No parser available for file: %1\":\"\",\"Error parsing file: %1\":\"\",\"Unable to load file: %1\":\"\",\"Invalid date\":\"\"}}(t,n))&&(o.exports=_);const i=r({__proto__:null,default:e(n)},[n]);export{i as v};\n"], "mappings": ";;;;;;;;;AAI6F,SAASA,GAAE,GAAE,GAAE;AAAC,WAAQA,KAAE,GAAEA,KAAE,EAAE,QAAOA,MAAI;AAAC,UAAMC,KAAE,EAAED,EAAC;AAAE,QAAG,YAAU,OAAOC,MAAG,CAAC,MAAM,QAAQA,EAAC;AAAE,iBAAUC,MAAKD,GAAE,KAAG,cAAYC,MAAG,EAAEA,MAAK,IAAG;AAAC,cAAMF,KAAE,OAAO,yBAAyBC,IAAEC,EAAC;AAAE,QAAAF,MAAG,OAAO,eAAe,GAAEE,IAAEF,GAAE,MAAIA,KAAE,EAAC,YAAW,MAAG,KAAI,MAAIC,GAAEC,EAAC,EAAC,CAAC;AAAA,MAAC;AAAA;AAAA,EAAC;AAAC,SAAO,OAAO,OAAO,OAAO,eAAe,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;AAAC;AAAC,IAAID;AAAJ,IAAM;AAAN,IAAQ,IAAE,CAAC;AAAX,IAAa,IAAE,EAAC,IAAI,UAAS;AAAC,SAAO;AAAC,GAAE,IAAI,QAAQ,GAAE;AAAC,MAAE;AAAC,EAAC;AAAEA,KAAE,GAAE,YAAU,IAAE,SAAS,GAAE,GAAE;AAAC,SAAO,eAAe,GAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAE,EAAE,UAAQ,EAAC,mBAAkB,KAAI,oBAAmB,KAAI,gBAAe,MAAK,gBAAe,KAAI,sBAAqB,KAAI,sBAAqB,KAAI,sBAAqB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,wBAAuB,KAAI,wBAAuB,KAAI,wBAAuB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,gBAAe,KAAI,iBAAgB,MAAK,iBAAgB,MAAK,iBAAgB,MAAK,iBAAgB,MAAK,iBAAgB,MAAK,mBAAkB,aAAY,cAAa,YAAW,cAAa,SAAQ,YAAW,SAAQ,WAAU,UAAS,YAAW,MAAK,aAAY,OAAM,YAAW,QAAO,uBAAsB,OAAM,8BAA6B,UAAS,8BAA6B,aAAY,4BAA2B,gBAAe,2BAA0B,kBAAiB,4BAA2B,kBAAiB,6BAA4B,wBAAuB,4BAA2B,8BAA6B,kBAAiB,MAAK,yBAAwB,SAAQ,uBAAsB,YAAW,sBAAqB,iBAAgB,uBAAsB,iBAAgB,wBAAuB,uBAAsB,uBAAsB,6BAA4B,kBAAiB,MAAK,uBAAsB,SAAQ,sBAAqB,cAAa,uBAAsB,cAAa,wBAAuB,oBAAmB,uBAAsB,0BAAyB,gBAAe,SAAQ,oBAAmB,cAAa,qBAAoB,cAAa,sBAAqB,oBAAmB,qBAAoB,0BAAyB,eAAc,QAAO,oBAAmB,QAAO,qBAAoB,cAAa,oBAAmB,oBAAmB,gBAAe,QAAO,sBAAqB,QAAO,qBAAoB,QAAO,iBAAgB,QAAO,sBAAqB,cAAa,gBAAe,QAAO,SAAQ,UAAS,SAAQ,YAAW,GAAE,KAAI,GAAE,KAAI,IAAG,MAAK,IAAG,MAAK,QAAO,MAAK,QAAO,MAAK,SAAQ,WAAU,UAAS,WAAU,OAAM,WAAU,OAAM,WAAU,KAAI,WAAU,MAAK,WAAU,MAAK,WAAU,QAAO,WAAU,WAAU,WAAU,SAAQ,YAAW,UAAS,YAAW,UAAS,YAAW,KAAI,SAAQ,KAAI,SAAQ,KAAI,SAAQ,KAAI,SAAQ,cAAa,SAAQ,KAAI,SAAQ,KAAI,SAAQ,KAAI,SAAQ,KAAI,SAAQ,KAAI,UAAS,KAAI,UAAS,KAAI,UAAS,QAAO,YAAW,QAAO,WAAU,SAAQ,UAAS,WAAU,UAAS,UAAS,WAAU,QAAO,WAAU,UAAS,WAAU,KAAI,MAAK,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,UAAS,SAASE,IAAE;AAAC,QAAID,KAAE;AAAK,QAAGC,KAAE,MAAIA,KAAE,GAAG,SAAOA,KAAE,IAAG;AAAA,MAAC,KAAK;AAAE,QAAAD,KAAE;AAAK;AAAA,MAAM,KAAK;AAAE,QAAAA,KAAE;AAAK;AAAA,MAAM,KAAK;AAAE,QAAAA,KAAE;AAAA,IAAI;AAAC,WAAOA;AAAA,EAAC,GAAE,YAAW,aAAY,MAAK,QAAO,MAAK,QAAO,QAAO,YAAW,uCAAsC,IAAG,SAAQ,YAAW,MAAK,aAAY,OAAM,IAAG,gBAAe,IAAG,aAAY,IAAG,aAAY,IAAG,eAAc,IAAG,eAAc,IAAG,kBAAiB,IAAG,gBAAe,IAAG,iBAAgB,IAAG,iBAAgB,IAAG,gBAAe,IAAG,QAAO,IAAG,sBAAqB,IAAG,eAAc,IAAG,iBAAgB,IAAG,eAAc,IAAG,oBAAmB,IAAG,iBAAgB,IAAG,kBAAiB,IAAG,cAAa,IAAG,KAAI,IAAG,0BAAyB,IAAG,2BAA0B,IAAG,qCAAoC,IAAG,+DAA8D,IAAG,QAAO,MAAK,OAAM,YAAW,MAAK,WAAU,OAAM,MAAK,qCAAoC,IAAG,uCAAsC,IAAG,8CAA6C,IAAG,4EAA2E,IAAG,wFAAuF,IAAG,qCAAoC,IAAG,yBAAwB,IAAG,gFAA+E,IAAG,cAAa,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,MAAK,IAAG,KAAI,IAAG,MAAK,IAAG,+EAA8E,IAAG,+CAA8C,IAAG,oDAAmD,IAAG,qDAAoD,IAAG,yEAAwE,IAAG,4CAA2C,IAAG,kDAAiD,IAAG,kDAAiD,IAAG,iBAAgB,gBAAe,WAAU,SAAQ,SAAQ,UAAS,oCAAmC,IAAG,0BAAyB,IAAG,2BAA0B,IAAG,gBAAe,GAAE;AAAC,EAAE,GAAE,CAAC,OAAKD,GAAE,UAAQ;AAAG,IAAM,IAAED,GAAE,EAAC,WAAU,MAAK,SAAQ,EAAE,CAAC,EAAC,GAAE,CAAC,CAAC,CAAC;", "names": ["r", "o", "t", "e"]}