<!-- 吨水电耗分析 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card"
      :title="state.activeName==='list'?'吨水电耗分析':'吨水电耗图表'"
    >
      <template #query>
        <el-radio-group
          v-model="state.activeName"
        >
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="clarity:line-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>

      <!-- 列表模式 -->
      <div v-show="state.activeName === 'list'" class="content-container">
        <CardTable
          id="print"
          ref="refTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>

      <!-- 图表模式 -->
      <div v-show="state.activeName === 'echarts'" class="content-container">
        <div class="chart-layout">
          <!-- 左侧图表区域 -->
          <div class="chart-container">
            <VChart
              ref="refChart"
              :theme="useAppStore().isDark?'dark':'light'"
              :option="state.chartOption"
              class="line-chart"
            ></VChart>
          </div>

          <!-- 右侧日期选择区域 -->
          <div class="date-selector">
            <div class="selector-title">选择日期</div>
            <div class="date-buttons">
              <el-button
                v-for="date in state.availableDates"
                :key="date"
                :type="state.selectedDate === date ? 'primary' : 'default'"
                size="small"
                @click="selectDate(date)"
                class="date-btn"
              >
                {{ formatDateButton(date) }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </SLCard>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import { Download, Printer, Search } from '@element-plus/icons-vue'
import { Icon } from '@iconify/vue'
import { getFormatTreeNodeDeepestChild, objectLookup } from '@/utils/GlobalHelper'
import { getWaterSupplyAndEnergyDataDetail } from '@/api/headwatersManage/statisticalAnalysis'
import useStation from '@/hooks/station/useStation'
import { printJSON } from '@/utils/printUtils'
import { reportType } from '@/views/secondSupplyManage/statisticalAnalysis/data/data'

import { IECharts } from '@/plugins/echart'
import { useAppStore } from '@/store'
import { SLMessage } from '@/utils/Message'

const { getStationTree } = useStation()
const state = reactive<{
  type: 'date' | 'month' | 'year';
  treeDataType: string;
  stationId: string;
  sumsRow: any,
  title: string,
  activeName: string,
  chartOption: any,
  availableDates: string[];
  selectedDate: string;
  chartData: any;
}>({
  type: 'date',
  treeDataType: 'Station',
  stationId: '',
  sumsRow: {},
  title: '',
  activeName: 'list',
  chartOption: null,
  availableDates: [],
  selectedDate: '',
  chartData: null
})

const today = dayjs().date()

const refTable = ref()
const cardSearch = ref()
const refChart = ref<IECharts>()

// 监听模式切换
watch(() => state.activeName, () => {
  if (state.activeName === 'echarts') {
    nextTick(() => {
      setTimeout(() => {
        generateLineChart()
      }, 100) // 延迟确保DOM渲染完成
    })
  }
})

// 监听报表类型变化，重新获取数据
watch(() => cardSearch.value?.queryParams?.type, (newType) => {
  if (newType && TreeData.currentProject?.id) {
    nextTick(() => {
      refreshData()
    })
  }
})

// 水源站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  currentProject: {}
})

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    type: 'day',
    year: [dayjs().format(), dayjs().format()],
    month: [dayjs().format(), dayjs().format()],
    day: [dayjs().date(today - 6).format('YYYY-MM-DD'), dayjs().date(today).format('YYYY-MM-DD')]
  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      checkStrictly: true,
      defaultExpandAll: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: key => {
        const val = objectLookup(TreeData.data, 'children', 'id', key)
        if (val && val.id) {
          TreeData.currentProject = val
          state.treeDataType = val.data?.type || 'Station'
          state.stationId = val.id as string
          // 站点改变时自动刷新数据
          nextTick(() => {
            refreshData()
          })
        }
      }
    },
    {
      type: 'radio-button',
      field: 'type',
      options: [
        { label: '日报', value: 'day' },
        { label: '月报', value: 'month' },
        { label: '年报', value: 'year' }
      ],
      label: '报告类型'
    },
    {
      hidden: true,
      type: 'daterange',
      label: '选择时间',
      field: 'day',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'year'
      }
    },
    {
      type: 'monthrange',
      label: '选择时间',
      field: 'month',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'day' || params.type === 'year'
      }
    },
    {
      type: 'yearrange',
      label: '选择时间',
      field: 'year',
      clearable: false,
      handleHidden: (params: any, query: any, formItem: IFormItem) => {
        formItem.hidden = params.type === 'month' || params.type === 'day'
      }
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          svgIcon: shallowRef(Search)
        },
        {
          text: '导出',
          perm: true,
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => _exportReport()
        },
        {
          perm: true,
          text: '打印',
          type: 'success',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [],
  operations: [],
  operationWidth: '150px',
  pagination: {
    hide: true
  }
})

// 刷新列表数据
const refreshData = () => {
  cardTableConfig.loading = true
  const stationId = TreeData.currentProject?.id
  const queryParams = cardSearch.value?.queryParams as any || {}
  const date = queryParams[queryParams.type]
  const type = reportType.find(type => type.value === queryParams.type)

  if (!stationId) {
    cardTableConfig.loading = false
    console.log('等待站点数据加载...')
    return
  }

  if (!date || !date[0] || !date[1]) {
    cardTableConfig.loading = false
    console.log('等待时间范围设置...')
    return
  }

  state.title = TreeData.currentProject.label + '吨水电耗分析' + '(' + type?.label + dayjs(date[0]).format(type?.data || 'YYYY-MM-DD') + '至' + dayjs(date[1]).format(type?.data || 'YYYY-MM-DD') + ')'

  const params = {
    stationId,
    queryType: queryParams.type,
    start: dayjs(date[0]).startOf(queryParams.type).valueOf(),
    end: dayjs(date[1]).endOf(queryParams.type).valueOf()
  }

  // 使用耗电量详情接口获取时间序列数据
  getWaterSupplyAndEnergyDataDetail(params).then(res => {
    const data = res.data.data

    // 生成表格数据 - 基于时间序列
    const flowList = data.flowList || []
    const energyList = data.energyList || []
    const unitConsumption = data.unitConsumption || []

    // 生成时间轴
    const timeLabels: string[] = []
    for (let i = 0; i < flowList.length; i++) {
      if (queryParams.type === 'day') {
        timeLabels.push(i + '时')
      } else if (queryParams.type === 'month') {
        timeLabels.push((i + 1) + '日')
      } else {
        timeLabels.push((i + 1) + '月')
      }
    }

    // 设置表格列结构
    const columns = [
      { prop: 'time', label: '时间', minWidth: 120, align: 'center' as const },
      { prop: 'unitConsumption', label: '吨水电耗', unit: '(kWh/m³)', minWidth: 180, align: 'center' as const },
      { prop: 'totalFlow', label: '取水量', unit: '(m³)', minWidth: 180, align: 'center' as const },
      { prop: 'energy', label: '用电量', unit: '(kW)', minWidth: 180, align: 'center' as const }
    ]

    cardTableConfig.columns = columns

    // 转换数据为表格格式
    const tableData = timeLabels.map((time, index) => ({
      time,
      unitConsumption: unitConsumption[index]?.value || 0,
      totalFlow: flowList[index] || 0,
      energy: energyList[index]?.value || 0
    }))

    cardTableConfig.dataList = tableData
    cardTableConfig.loading = false

    // 存储原始数据用于图表
    state.chartData = data

    // 为图表模式准备日期选择 - 根据查询类型生成可选日期
    if (queryParams.type === 'day') {
      // 日报模式：生成日期范围内的每一天
      const startDate = dayjs(date[0])
      const endDate = dayjs(date[1])
      const dateList: string[] = []
      let currentDate = startDate
      while (currentDate.isBefore(endDate) || currentDate.isSame(endDate)) {
        dateList.push(currentDate.format('YYYY-MM-DD'))
        currentDate = currentDate.add(1, 'day')
      }
      state.availableDates = dateList
    } else if (queryParams.type === 'month') {
      // 月报模式：生成月份范围内的每一个月
      const startMonth = dayjs(date[0])
      const endMonth = dayjs(date[1])
      const monthList: string[] = []
      let currentMonth = startMonth
      while (currentMonth.isBefore(endMonth) || currentMonth.isSame(endMonth)) {
        monthList.push(currentMonth.format('YYYY-MM'))
        currentMonth = currentMonth.add(1, 'month')
      }
      state.availableDates = monthList
    } else {
      // 年报模式：生成年份范围内的每一年
      const startYear = dayjs(date[0])
      const endYear = dayjs(date[1])
      const yearList: string[] = []
      let currentYear = startYear
      while (currentYear.isBefore(endYear) || currentYear.isSame(endYear)) {
        yearList.push(currentYear.format('YYYY'))
        currentYear = currentYear.add(1, 'year')
      }
      state.availableDates = yearList
    }

    // 设置默认选中第一个日期
    state.selectedDate = state.availableDates[0] || ''

    // 如果是图表模式，生成图表
    if (state.activeName === 'echarts') {
      nextTick(() => {
        setTimeout(() => {
          // 如果有多个日期可选，需要重新查询第一个日期的数据
          if (state.availableDates.length > 1) {
            refreshSelectedDateData(state.selectedDate)
          } else {
            generateLineChart()
          }
        }, 200)
      })
    }
  }).catch((error) => {
    console.error('获取数据失败:', error)
    cardTableConfig.loading = false
    SLMessage.error('获取数据失败，请稍后重试')
  })
}

// 导出报告
const _exportReport = () => {
  refTable.value?.exportTable()
}

// 打印报表
const handlePrint = () => {
  printJSON({ title: state.title, data: cardTableConfig.dataList, titleList: cardTableConfig.columns })
}

// 生成混合图表
const generateLineChart = () => {
  if (!state.chartData) {
    return
  }

  const data = state.chartData
  const queryParams = cardSearch.value?.queryParams as any || {}
  const reportType = queryParams.type || 'day'

  // 生成时间轴标签
  const flowList = data.flowList || []
  const xAxisData: string[] = []
  for (let i = 0; i < flowList.length; i++) {
    if (reportType === 'day') {
      xAxisData.push(i + '时')
    } else if (reportType === 'month') {
      xAxisData.push((i + 1) + '日')
    } else {
      xAxisData.push((i + 1) + '月')
    }
  }

  // 获取数据
  const unitConsumptionData = (data.unitConsumption || []).map((item: any) => item.value || 0)
  const flowData = data.flowList || []
  const energyData = (data.energyList || []).map((item: any) => item.value || 0)

  // 设置图表配置
  let titleText = ''
  if (reportType === 'day') {
    titleText = `${dayjs(state.selectedDate).format('M月D日')} ${TreeData.currentProject.label || ''}生产图`
  } else if (reportType === 'month') {
    titleText = `${dayjs(state.selectedDate).format('YYYY年M月')} ${TreeData.currentProject.label || ''}生产图`
  } else {
    titleText = `${dayjs(state.selectedDate).format('YYYY年')} ${TreeData.currentProject.label || ''}生产图`
  }

  const chartConfig = {
    title: {
      text: titleText,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function(params: any) {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          let unit = ''
          if (param.seriesName.includes('吨水电耗')) {
            unit = 'kWh/m³'
          } else if (param.seriesName.includes('取水量')) {
            unit = 'm³'
          } else if (param.seriesName.includes('用电量')) {
            unit = 'kW'
          }
          result += `${param.marker} ${param.seriesName}: ${param.value} ${unit}<br/>`
        })
        return result
      }
    },
    legend: {
      top: 30,
      data: ['吨水电耗', '取水量', '用电量'],
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '10%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      name: reportType === 'day' ? '时间' : reportType === 'month' ? '日期' : '月份',
      nameTextStyle: {
        fontSize: 12
      },
      axisLabel: {
        fontSize: 11,
        rotate: 0
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '取水量(m³)/用电量(kW)',
        position: 'left',
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          fontSize: 11,
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '吨水电耗(kWh/m³)',
        position: 'right',
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          fontSize: 11,
          formatter: '{value}'
        }
      }
    ],
    series: [
      {
        name: '取水量',
        type: 'bar',
        yAxisIndex: 0,
        data: flowData,
        itemStyle: {
          color: '#91CC75'
        },
        barWidth: '20%'
      },
      {
        name: '用电量',
        type: 'bar',
        yAxisIndex: 0,
        data: energyData,
        itemStyle: {
          color: '#C0C0C0'
        },
        barWidth: '20%'
      },
      {
        name: '吨水电耗',
        type: 'line',
        yAxisIndex: 1,
        data: unitConsumptionData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          width: 3,
          color: '#5CB85C'
        },
        itemStyle: {
          color: '#5CB85C'
        }
      }
    ]
  }

  state.chartOption = chartConfig

  // 延迟调整图表大小
  nextTick(() => {
    setTimeout(() => {
      refChart.value?.resize()
    }, 200)
  })
}

// 选择日期
const selectDate = (date: string) => {
  state.selectedDate = date
  // 重新查询选中日期的数据
  refreshSelectedDateData(date)
}

// 查询选中日期的数据
const refreshSelectedDateData = (selectedDate: string) => {
  const stationId = TreeData.currentProject?.id
  const queryParams = cardSearch.value?.queryParams as any || {}

  if (!stationId || !selectedDate) {
    return
  }

  let start: number, end: number

  if (queryParams.type === 'day') {
    // 日报：查询选中日期的24小时数据
    start = dayjs(selectedDate).startOf('day').valueOf()
    end = dayjs(selectedDate).endOf('day').valueOf()
  } else if (queryParams.type === 'month') {
    // 月报：查询选中月份的每日数据
    start = dayjs(selectedDate).startOf('month').valueOf()
    end = dayjs(selectedDate).endOf('month').valueOf()
  } else {
    // 年报：查询选中年份的每月数据
    start = dayjs(selectedDate).startOf('year').valueOf()
    end = dayjs(selectedDate).endOf('year').valueOf()
  }

  const params = {
    stationId,
    queryType: queryParams.type,
    start,
    end
  }

  getWaterSupplyAndEnergyDataDetail(params).then(res => {
    const data = res.data.data
    state.chartData = data
    generateLineChart()
  }).catch((error) => {
    console.error('获取选中日期数据失败:', error)
    SLMessage.error('获取数据失败，请稍后重试')
  })
}

// 格式化日期按钮显示
const formatDateButton = (date: string) => {
  // 将 2025-06-10 格式转换为 06-10 显示
  const parts = date.split('-')
  if (parts.length === 3) {
    return `${parts[1]}-${parts[2]}`
  }
  return date
}

onMounted(async () => {
  const treeData = await getStationTree('水源地')
  TreeData.data = treeData

  // 自动选择第一个可用的站点
  const firstStation = getFormatTreeNodeDeepestChild(TreeData.data)
  if (firstStation && firstStation.id) {
    TreeData.currentProject = firstStation
    state.treeDataType = firstStation.data?.type || 'Station'
    state.stationId = firstStation.id
  }

  cardSearchConfig.defaultParams = { ...cardSearchConfig.defaultParams, treeData: TreeData.currentProject }
  cardSearch.value?.resetForm()

  // 延迟执行refreshData，确保组件完全初始化
  nextTick(() => {
    setTimeout(() => {
      refreshData()
    }, 100)
  })

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    setTimeout(() => {
      refChart.value?.resize()
    }, 100)
  })
})

onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener('resize', () => {
    refChart.value?.resize()
  })
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.card {
  height: calc(100% - 80px);
}

.content-container {
  width: 100%;
  height: calc(100vh - 254px);
  min-height: 500px;
}

.chart-layout {
  display: flex;
  height: 100%;
  gap: 20px;
}

.chart-container {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.date-selector {
  width: 160px;
  min-width: 160px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.selector-title {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
  flex-shrink: 0;
}

.date-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: stretch;
}

.date-btn {
  width: 100% !important;
  height: 32px !important;
  margin: 0 !important;
  padding: 0 8px !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  border-radius: 4px !important;
  font-size: 13px !important;
  box-sizing: border-box !important;
  flex-shrink: 0 !important;
}

.chart-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  text-align: center;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 12px;
  flex-shrink: 0;
}

.line-chart {
  flex: 1;
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.card-table {
  height: 100%;
  width: 100%;

  :deep(.el-table) {
    .el-table__header-wrapper {
      .el-table__header {
        th {
          background-color: #f8f9fa;
          font-weight: 600;
          color: #333;
          padding: 16px 12px;
          border-bottom: 2px solid #e9ecef;
        }
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        tr {
          &:hover {
            background-color: #f5f7fa;
          }

          td {
            padding: 16px 12px;
            border-bottom: 1px solid #ebeef5;
            font-size: 14px;

            .cell {
              padding: 0 8px;
              line-height: 1.5;
            }
          }
        }
      }
    }

    // 表格行间距
    .el-table__row {
      height: auto;
    }

    // 表格边框
    &.el-table--border {
      border: 1px solid #ebeef5;

      &::after {
        background-color: #ebeef5;
      }

      &::before {
        background-color: #ebeef5;
      }
    }
  }
}

// 暗色主题适配
:deep(.el-card.is-dark) {
  .chart-container {
    background: #2d2d2d;

    .chart-title {
      color: #fff;
      border-bottom-color: #404040;
    }
  }
}

// 让表格更宽松美观
:deep(.el-card__body) {
  padding: 20px;
}

// 强制日期按钮对齐
:deep(.date-buttons .el-button) {
  width: 100% !important;
  margin: 0 !important;
  display: block !important;
}

:deep(.date-buttons .el-button + .el-button) {
  margin-left: 0 !important;
}
</style>
