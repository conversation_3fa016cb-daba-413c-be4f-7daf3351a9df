<!-- 比泵分析 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card"
      :title="state.activeName==='list'?'比泵分析':'比泵分析图表'"
    >
      <template #query>
        <el-radio-group
          v-model="state.activeName"
        >
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="clarity:bar-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>

      <!-- 列表模式 -->
      <div v-show="state.activeName === 'list'" class="content-container">
        <CardTable
          id="print"
          ref="refTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>

      <!-- 图表模式 -->
      <div v-show="state.activeName === 'echarts'" class="content-container">
        <div class="chart-container">
          <VChart
            ref="refChart"
            :theme="useAppStore().isDark?'dark':'light'"
            :option="state.chartOption"
            class="comparison-chart"
          ></VChart>
        </div>
      </div>
    </SLCard>
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { getWaterOutletAndInletReport } from '@/api/headwatersManage/statisticalAnalysis'
import { ICardSearchIns } from '@/components/type'
import {
  getFormatTreeNodeDeepestChild,
  objectLookup
} from '@/utils/GlobalHelper'
import useStation from '@/hooks/station/useStation'
import { printJSON } from '@/utils/printUtils'

import { IECharts } from '@/plugins/echart'
import { useAppStore } from '@/store'
import { SLMessage } from '@/utils/Message'

const { getStationTree } = useStation()
const today = dayjs().date()
const cardSearch = ref<ICardSearchIns>()
const refTable = ref()
const refChart = ref<IECharts>()

// 状态管理
const state = reactive<{
  activeName: string;
  chartOption: any;
}>({
  activeName: 'list',
  chartOption: null
})

// 水源站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  checkedKeys: [],
  checkedNodes: []
})

const totalLoading = ref<boolean>(false)
// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    date: [
      dayjs()
        .date(today - 3)
        .format('YYYY-MM-DD'),
      dayjs().date(today).format('YYYY-MM-DD')
    ]
  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      defaultExpandAll: true,
      multiple: true,
      options: computed(() => TreeData.data) as any,
      label: '站点选择',
      onChange: key => {
        TreeData.checkedKeys = key || []
        TreeData.checkedNodes = []
        for (const i in key) {
          const val = objectLookup(TreeData.data, 'children', 'id', i)
          TreeData.checkedNodes.push(val)
        }
        refreshData()
      }
    },
    { type: 'daterange', label: '选择时间', field: 'date' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          icon: 'iconfont icon-chaxun'
        }
      ]
    }
  ]
})

// 定义动态表头初始化数据
// let weekDate = reactive<IFormTableColumn[]>([])

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [
    { prop: 'name', label: '泵房名称', minWidth: 200, align: 'center' as const },
    { prop: 'outletTotalFlow', label: '出水量', unit: '(m³)', minWidth: 150, align: 'center' as const },
    { prop: 'inletTotalFlow', label: '进水量', unit: '(m³)', minWidth: 150, align: 'center' as const },
    { prop: 'differenceTotalFlow', label: '进出水差值', unit: '(m³)', minWidth: 160, align: 'center' as const },
    { prop: 'differenceRate', label: '差值率', unit: '(%)', minWidth: 120, align: 'center' as const },
    { prop: 'efficiency', label: '泵效率', unit: '(%)', minWidth: 120, align: 'center' as const }
  ],
  operations: [
    {
      text: '导出',
      perm: true,
      click: (row: any) => {
        printJSON([row], `${row.name}_比泵分析数据`)
      }
    }
  ],
  showSummary: true,
  summaryMethod: (param: any) => {
    const { columns, data } = param
    const sums: string[] = []
    columns.forEach((column: any, index: number) => {
      if (index === 0) {
        sums[index] = '合计'
        return
      }
      if (column.property === 'name') {
        sums[index] = '合计'
        return
      }
      const values = data.map((item: any) => Number(item[column.property]))
      if (!values.every((value: any) => isNaN(value))) {
        if (column.property === 'differenceRate' || column.property === 'efficiency') {
          // 百分比字段计算平均值
          sums[index] = (values.reduce((prev: any, curr: any) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0) / values.length).toFixed(2) + '%'
        } else {
          // 其他字段计算总和
          sums[index] = values.reduce((prev: any, curr: any) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0).toFixed(2)
        }
      } else {
        sums[index] = 'N/A'
      }
    })
    return sums
  },
  operationWidth: '100px',
  indexVisible: true,
  pagination: {
    hide: true
  }
})

// 刷新列表数据
const refreshData = () => {
  if (!TreeData.checkedKeys || TreeData.checkedKeys.length === 0) {
    SLMessage.warning('请至少选择一个水源地进行对比分析')
    return
  }

  cardTableConfig.loading = true
  const queryParams = cardSearch.value?.queryParams as any || {}
  const stationIdList = TreeData.checkedKeys as any[]
  const params: any = {
    stationIdList: stationIdList.join(','),
    start: dayjs(queryParams.date[0]).valueOf(),
    end: dayjs(queryParams.date[1]).valueOf()
  }

  getWaterOutletAndInletReport(params).then(res => {
    const data = res.data.data || []

    // 处理数据，添加计算字段
    const processedData = data.map((item: any) => {
      const outletFlow = parseFloat(item.outletTotalFlow) || 0
      const inletFlow = parseFloat(item.inletTotalFlow) || 0
      const difference = inletFlow - outletFlow
      const differenceRate = inletFlow > 0 ? ((difference / inletFlow) * 100) : 0
      const efficiency = inletFlow > 0 ? ((outletFlow / inletFlow) * 100) : 0

      return {
        ...item,
        outletTotalFlow: outletFlow.toFixed(2),
        inletTotalFlow: inletFlow.toFixed(2),
        differenceTotalFlow: difference.toFixed(2),
        differenceRate: differenceRate.toFixed(2),
        efficiency: efficiency.toFixed(2)
      }
    })

    cardTableConfig.dataList = processedData
    cardTableConfig.loading = false

    // 如果是图表模式，生成图表
    if (state.activeName === 'echarts') {
      nextTick(() => {
        setTimeout(() => {
          generateComparisonChart(processedData)
        }, 200)
      })
    }
  }).catch((error) => {
    console.error('获取数据失败:', error)
    cardTableConfig.loading = false
    SLMessage.error('获取数据失败，请稍后重试')
  })
}

// 生成对比图表
const generateComparisonChart = (data: any[]) => {
  if (!data || data.length === 0) {
    return
  }

  const stationNames = data.map(item => item.name)
  const outletFlowData = data.map(item => parseFloat(item.outletTotalFlow))
  const inletFlowData = data.map(item => parseFloat(item.inletTotalFlow))
  const efficiencyData = data.map(item => parseFloat(item.efficiency))

  const chartConfig = {
    title: {
      text: '水源地比泵分析对比图',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function(params: any) {
        let result = `${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          let unit = ''
          if (param.seriesName.includes('水量')) {
            unit = 'm³'
          } else if (param.seriesName.includes('效率')) {
            unit = '%'
          }
          result += `${param.marker} ${param.seriesName}: ${param.value} ${unit}<br/>`
        })
        return result
      }
    },
    legend: {
      top: 30,
      data: ['进水量', '出水量', '泵效率'],
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '15%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: stationNames,
      name: '水源地',
      nameTextStyle: {
        fontSize: 12
      },
      axisLabel: {
        fontSize: 11,
        rotate: 45,
        interval: 0
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '水量(m³)',
        position: 'left',
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          fontSize: 11,
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '效率(%)',
        position: 'right',
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          fontSize: 11,
          formatter: '{value}%'
        },
        max: 100
      }
    ],
    series: [
      {
        name: '进水量',
        type: 'bar',
        yAxisIndex: 0,
        data: inletFlowData,
        itemStyle: {
          color: '#5470C6'
        },
        barWidth: '25%'
      },
      {
        name: '出水量',
        type: 'bar',
        yAxisIndex: 0,
        data: outletFlowData,
        itemStyle: {
          color: '#91CC75'
        },
        barWidth: '25%'
      },
      {
        name: '泵效率',
        type: 'line',
        yAxisIndex: 1,
        data: efficiencyData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: '#FAC858'
        },
        itemStyle: {
          color: '#FAC858'
        }
      }
    ]
  }

  state.chartOption = chartConfig

  // 延迟调整图表大小
  nextTick(() => {
    setTimeout(() => {
      refChart.value?.resize()
    }, 200)
  })
}

// 监听模式切换
watch(() => state.activeName, (newMode) => {
  if (newMode === 'echarts' && cardTableConfig.dataList.length > 0) {
    nextTick(() => {
      setTimeout(() => {
        generateComparisonChart(cardTableConfig.dataList)
      }, 200)
    })
  }
})

onMounted(async () => {
  const treeData = await getStationTree('水源地')
  TreeData.data = treeData
  TreeData.currentProject = getFormatTreeNodeDeepestChild(TreeData.data)
  cardSearchConfig.defaultParams = { ...cardSearchConfig.defaultParams, treeData: [TreeData.currentProject.id] }
  cardSearch.value?.resetForm()
  TreeData.checkedKeys = [TreeData.currentProject.id]
  refreshData()

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    setTimeout(() => {
      refChart.value?.resize()
    }, 100)
  })
})

</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card {
  height: calc(100% - 80px);
}

.content-container {
  width: 100%;
  height: calc(100vh - 254px);
  min-height: 500px;
}

.chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.comparison-chart {
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.card-table {
  height: 100%;
}

// 深色模式适配
:deep(.el-table) {
  background-color: transparent;
}

:deep(.el-table__header-wrapper) {
  background-color: transparent;
}

:deep(.el-table__body-wrapper) {
  background-color: transparent;
}

// 表格样式优化
:deep(.el-table .cell) {
  padding: 8px 12px;
}

:deep(.el-table th) {
  background-color: var(--el-table-header-bg-color);
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid var(--el-table-border-color);
}

// 汇总行样式
:deep(.el-table__footer-wrapper .el-table__footer .cell) {
  font-weight: 600;
  color: var(--el-color-primary);
}
</style>
