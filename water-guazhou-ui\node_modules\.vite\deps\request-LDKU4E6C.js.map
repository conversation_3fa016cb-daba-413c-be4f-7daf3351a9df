{"version": 3, "sources": ["../../@arcgis/core/core/workers/request.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../Error.js\";import{unwrap as t}from\"../maybe.js\";let s;function r(r,a){let n=a.responseType;n?\"array-buffer\"!==n&&\"blob\"!==n&&\"json\"!==n&&\"native\"!==n&&\"native-request-init\"!==n&&\"text\"!==n&&(n=\"text\"):n=\"json\",a.responseType=n;const o=t(a.signal);return delete a.signal,globalThis.invokeStaticMessage(\"request\",{url:r,options:a},{signal:o}).then((async t=>{let i,l,u,c,p;if(t.data)if(t.data instanceof ArrayBuffer){if(!(\"json\"!==n&&\"text\"!==n&&\"blob\"!==n||(i=new Blob([t.data]),\"json\"!==n&&\"text\"!==n||(s||(s=new FileReaderSync),c=s.readAsText(i),\"json\"!==n)))){try{l=JSON.parse(c||null)}catch(b){const t={...b,url:r,requestOptions:a};throw new e(\"request:server\",b.message,t)}if(l.error){const t={...l.error,url:r,requestOptions:a};throw new e(\"request:server\",l.error.message,t)}}}else\"native\"===n&&(t.data.signal=o,u=await fetch(t.data.url,t.data),t.httpStatus=u.status);switch(n){case\"blob\":p=i;break;case\"json\":p=l;break;case\"native\":p=u;break;case\"text\":p=c;break;default:p=t.data}return{data:p,httpStatus:t.httpStatus,requestOptions:a,ssl:t.ssl,url:r}}))}export{r as execute};\n"], "mappings": ";;;;;;;;;;;;;;AAIgE,IAAIA;AAAE,SAAS,EAAEC,IAAE,GAAE;AAAC,MAAI,IAAE,EAAE;AAAa,MAAE,mBAAiB,KAAG,WAAS,KAAG,WAAS,KAAG,aAAW,KAAG,0BAAwB,KAAG,WAAS,MAAI,IAAE,UAAQ,IAAE,QAAO,EAAE,eAAa;AAAE,QAAM,IAAE,EAAE,EAAE,MAAM;AAAE,SAAO,OAAO,EAAE,QAAO,WAAW,oBAAoB,WAAU,EAAC,KAAIA,IAAE,SAAQ,EAAC,GAAE,EAAC,QAAO,EAAC,CAAC,EAAE,KAAM,OAAM,MAAG;AAAC,QAAI,GAAE,GAAE,GAAE,GAAE;AAAE,QAAG,EAAE,KAAK,KAAG,EAAE,gBAAgB,aAAY;AAAC,UAAG,EAAE,WAAS,KAAG,WAAS,KAAG,WAAS,MAAI,IAAE,IAAI,KAAK,CAAC,EAAE,IAAI,CAAC,GAAE,WAAS,KAAG,WAAS,MAAID,OAAIA,KAAE,IAAI,mBAAgB,IAAEA,GAAE,WAAW,CAAC,GAAE,WAAS,MAAK;AAAC,YAAG;AAAC,cAAE,KAAK,MAAM,KAAG,IAAI;AAAA,QAAC,SAAO,GAAE;AAAC,gBAAME,KAAE,EAAC,GAAG,GAAE,KAAID,IAAE,gBAAe,EAAC;AAAE,gBAAM,IAAI,EAAE,kBAAiB,EAAE,SAAQC,EAAC;AAAA,QAAC;AAAC,YAAG,EAAE,OAAM;AAAC,gBAAMA,KAAE,EAAC,GAAG,EAAE,OAAM,KAAID,IAAE,gBAAe,EAAC;AAAE,gBAAM,IAAI,EAAE,kBAAiB,EAAE,MAAM,SAAQC,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,MAAK,cAAW,MAAI,EAAE,KAAK,SAAO,GAAE,IAAE,MAAM,MAAM,EAAE,KAAK,KAAI,EAAE,IAAI,GAAE,EAAE,aAAW,EAAE;AAAQ,YAAO,GAAE;AAAA,MAAC,KAAI;AAAO,YAAE;AAAE;AAAA,MAAM,KAAI;AAAO,YAAE;AAAE;AAAA,MAAM,KAAI;AAAS,YAAE;AAAE;AAAA,MAAM,KAAI;AAAO,YAAE;AAAE;AAAA,MAAM;AAAQ,YAAE,EAAE;AAAA,IAAI;AAAC,WAAM,EAAC,MAAK,GAAE,YAAW,EAAE,YAAW,gBAAe,GAAE,KAAI,EAAE,KAAI,KAAID,GAAC;AAAA,EAAC,CAAE;AAAC;", "names": ["s", "r", "t"]}