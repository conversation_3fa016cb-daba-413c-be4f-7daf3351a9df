{"version": 3, "sources": ["../../@arcgis/core/rest/query/operations/pbfDehydratedFeatureSet.js", "../../@arcgis/core/views/3d/support/PBFDecoderWorker.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{neverReached as t}from\"../../../core/compilerUtils.js\";import{isNone as e}from\"../../../core/maybe.js\";import{generateUID as r}from\"../../../core/uid.js\";import s from\"../../../geometry/SpatialReference.js\";import{getGeometryZScaler as o}from\"../../../geometry/support/zscale.js\";import{DehydratedFeatureSetClass as i,DehydratedFeatureClass as n}from\"../../../layers/graphics/dehydratedFeatures.js\";import{unquantizeOptimizedGeometry as a,convertToGeometry as h}from\"../../../layers/graphics/featureConversionUtils.js\";import l from\"../../../layers/support/Field.js\";function u(t,e){return e}function p(t,e,r,s){switch(r){case 0:return m(t,e+s,0);case 1:return\"lowerLeft\"===t.originPosition?m(t,e+s,1):y(t,e+s,1)}}function c(t,e,r,s){return 2===r?m(t,e,2):p(t,e,r,s)}function d(t,e,r,s){return 2===r?m(t,e,3):p(t,e,r,s)}function f(t,e,r,s){return 3===r?m(t,e,3):c(t,e,r,s)}function m({translate:t,scale:e},r,s){return t[s]+r*e[s]}function y({translate:t,scale:e},r,s){return t[s]-r*e[s]}class _{constructor(t){this._options=t,this.geometryTypes=[\"point\",\"multipoint\",\"polyline\",\"polygon\"],this._previousCoordinate=[0,0],this._transform=null,this._applyTransform=u,this._lengths=[],this._currentLengthIndex=0,this._toAddInCurrentPath=0,this._vertexDimension=0,this._coordinateBuffer=null,this._coordinateBufferPtr=0,this._attributesConstructor=class{}}createFeatureResult(){return new i}finishFeatureResult(t){if(this._options.applyTransform&&(t.transform=null),this._attributesConstructor=class{},this._coordinateBuffer=null,this._lengths.length=0,!t.hasZ)return;const r=o(t.geometryType,this._options.sourceSpatialReference,t.spatialReference);if(!e(r))for(const e of t.features)r(e.geometry)}createSpatialReference(){return new s}addField(t,e){t.fields.push(l.fromJSON(e));const r=t.fields.map((t=>t.name));this._attributesConstructor=function(){for(const t of r)this[t]=null}}addFeature(t,e){const r=this._options.maxStringAttributeLength?this._options.maxStringAttributeLength:0;if(r>0)for(const s in e.attributes){const t=e.attributes[s];\"string\"==typeof t&&t.length>r&&(e.attributes[s]=\"\")}t.features.push(e)}addQueryGeometry(t,e){const{queryGeometry:r,queryGeometryType:s}=e,o=a(r.clone(),r,!1,!1,this._transform),i=h(o,s,!1,!1);let n=null;switch(s){case\"esriGeometryPoint\":n=\"point\";break;case\"esriGeometryPolygon\":n=\"polygon\";break;case\"esriGeometryPolyline\":n=\"polyline\";break;case\"esriGeometryMultipoint\":n=\"multipoint\"}i.type=n,t.queryGeometryType=s,t.queryGeometry=i}prepareFeatures(e){switch(this._transform=e.transform??null,this._options.applyTransform&&e.transform&&(this._applyTransform=this._deriveApplyTransform(e)),this._vertexDimension=2,e.hasZ&&this._vertexDimension++,e.hasM&&this._vertexDimension++,e.geometryType){case\"point\":this.addCoordinate=(t,e,r)=>this.addCoordinatePoint(t,e,r),this.createGeometry=t=>this.createPointGeometry(t);break;case\"polygon\":this.addCoordinate=(t,e,r)=>this._addCoordinatePolygon(t,e,r),this.createGeometry=t=>this._createPolygonGeometry(t);break;case\"polyline\":this.addCoordinate=(t,e,r)=>this._addCoordinatePolyline(t,e,r),this.createGeometry=t=>this._createPolylineGeometry(t);break;case\"multipoint\":this.addCoordinate=(t,e,r)=>this._addCoordinateMultipoint(t,e,r),this.createGeometry=t=>this._createMultipointGeometry(t);break;case\"mesh\":case\"extent\":break;default:t(e.geometryType)}}createFeature(){return this._lengths.length=0,this._currentLengthIndex=0,this._previousCoordinate[0]=0,this._previousCoordinate[1]=0,new n(r(),null,new this._attributesConstructor)}allocateCoordinates(){const t=this._lengths.reduce(((t,e)=>t+e),0);this._coordinateBuffer=new Float64Array(t*this._vertexDimension),this._coordinateBufferPtr=0}addLength(t,e){0===this._lengths.length&&(this._toAddInCurrentPath=e),this._lengths.push(e)}createPointGeometry(t){const e={type:\"point\",x:0,y:0,spatialReference:t.spatialReference,hasZ:!!t.hasZ,hasM:!!t.hasM};return e.hasZ&&(e.z=0),e.hasM&&(e.m=0),e}addCoordinatePoint(t,e,r){const s=this._transform?this._applyTransform(this._transform,e,r,0):e;if(null!=s)switch(r){case 0:t.x=s;break;case 1:t.y=s;break;case 2:t.hasZ?t.z=s:t.m=s;break;case 3:t.m=s}}_transformPathLikeValue(t,e){let r=0;return e<=1&&(r=this._previousCoordinate[e],this._previousCoordinate[e]+=t),this._transform?this._applyTransform(this._transform,t,e,r):t}_addCoordinatePolyline(t,e,r){this._dehydratedAddPointsCoordinate(t.paths,e,r)}_addCoordinatePolygon(t,e,r){this._dehydratedAddPointsCoordinate(t.rings,e,r)}_addCoordinateMultipoint(t,e,r){0===r&&t.points.push([]);const s=this._transformPathLikeValue(e,r);t.points[t.points.length-1].push(s)}_createPolygonGeometry(t){return{type:\"polygon\",rings:[[]],spatialReference:t.spatialReference,hasZ:!!t.hasZ,hasM:!!t.hasM}}_createPolylineGeometry(t){return{type:\"polyline\",paths:[[]],spatialReference:t.spatialReference,hasZ:!!t.hasZ,hasM:!!t.hasM}}_createMultipointGeometry(t){return{type:\"multipoint\",points:[],spatialReference:t.spatialReference,hasZ:!!t.hasZ,hasM:!!t.hasM}}_dehydratedAddPointsCoordinate(t,e,r){0===r&&0==this._toAddInCurrentPath--&&(t.push([]),this._toAddInCurrentPath=this._lengths[++this._currentLengthIndex]-1,this._previousCoordinate[0]=0,this._previousCoordinate[1]=0);const s=this._transformPathLikeValue(e,r),o=t[t.length-1],i=this._coordinateBuffer;if(i){if(0===r){const t=this._coordinateBufferPtr*Float64Array.BYTES_PER_ELEMENT;o.push(new Float64Array(i.buffer,t,this._vertexDimension))}i[this._coordinateBufferPtr++]=s}}_deriveApplyTransform(t){const{hasZ:e,hasM:r}=t;return e&&r?f:e?c:r?d:p}}export{_ as DehydratedFeatureSetParserContext};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{DehydratedFeatureSetParserContext as e}from\"../../../rest/query/operations/pbfDehydratedFeatureSet.js\";import{parsePBFFeatureQuery as r}from\"../../../rest/query/operations/pbfQueryUtils.js\";class t{_parseFeatureQuery(t){const s=r(t.buffer,new e(t.options)),o={...s,spatialReference:s.spatialReference?.toJSON(),fields:s.fields?s.fields.map((e=>e.toJSON())):void 0};return Promise.resolve(o)}}function s(){return new t}export{s as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI8jB,SAAS,EAAEA,IAAEC,IAAE;AAAC,SAAOA;AAAC;AAAC,SAAS,EAAED,IAAEC,IAAE,GAAEC,IAAE;AAAC,UAAO,GAAE;AAAA,IAAC,KAAK;AAAE,aAAO,EAAEF,IAAEC,KAAEC,IAAE,CAAC;AAAA,IAAE,KAAK;AAAE,aAAM,gBAAcF,GAAE,iBAAe,EAAEA,IAAEC,KAAEC,IAAE,CAAC,IAAEC,GAAEH,IAAEC,KAAEC,IAAE,CAAC;AAAA,EAAC;AAAC;AAAC,SAAS,EAAEF,IAAEC,IAAE,GAAEC,IAAE;AAAC,SAAO,MAAI,IAAE,EAAEF,IAAEC,IAAE,CAAC,IAAE,EAAED,IAAEC,IAAE,GAAEC,EAAC;AAAC;AAAC,SAASE,GAAEJ,IAAEC,IAAE,GAAEC,IAAE;AAAC,SAAO,MAAI,IAAE,EAAEF,IAAEC,IAAE,CAAC,IAAE,EAAED,IAAEC,IAAE,GAAEC,EAAC;AAAC;AAAC,SAASG,GAAEL,IAAEC,IAAE,GAAEC,IAAE;AAAC,SAAO,MAAI,IAAE,EAAEF,IAAEC,IAAE,CAAC,IAAE,EAAED,IAAEC,IAAE,GAAEC,EAAC;AAAC;AAAC,SAAS,EAAE,EAAC,WAAUF,IAAE,OAAMC,GAAC,GAAE,GAAEC,IAAE;AAAC,SAAOF,GAAEE,EAAC,IAAE,IAAED,GAAEC,EAAC;AAAC;AAAC,SAASC,GAAE,EAAC,WAAUH,IAAE,OAAMC,GAAC,GAAE,GAAEC,IAAE;AAAC,SAAOF,GAAEE,EAAC,IAAE,IAAED,GAAEC,EAAC;AAAC;AAAC,IAAM,IAAN,MAAO;AAAA,EAAC,YAAYF,IAAE;AAAC,SAAK,WAASA,IAAE,KAAK,gBAAc,CAAC,SAAQ,cAAa,YAAW,SAAS,GAAE,KAAK,sBAAoB,CAAC,GAAE,CAAC,GAAE,KAAK,aAAW,MAAK,KAAK,kBAAgB,GAAE,KAAK,WAAS,CAAC,GAAE,KAAK,sBAAoB,GAAE,KAAK,sBAAoB,GAAE,KAAK,mBAAiB,GAAE,KAAK,oBAAkB,MAAK,KAAK,uBAAqB,GAAE,KAAK,yBAAuB,MAAK;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,WAAO,IAAI;AAAA,EAAC;AAAA,EAAC,oBAAoBA,IAAE;AAAC,QAAG,KAAK,SAAS,mBAAiBA,GAAE,YAAU,OAAM,KAAK,yBAAuB,MAAK;AAAA,IAAC,GAAE,KAAK,oBAAkB,MAAK,KAAK,SAAS,SAAO,GAAE,CAACA,GAAE,KAAK;AAAO,UAAM,IAAE,EAAEA,GAAE,cAAa,KAAK,SAAS,wBAAuBA,GAAE,gBAAgB;AAAE,QAAG,CAAC,EAAE,CAAC,EAAE,YAAUC,MAAKD,GAAE,SAAS,GAAEC,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,yBAAwB;AAAC,WAAO,IAAI;AAAA,EAAC;AAAA,EAAC,SAASD,IAAEC,IAAE;AAAC,IAAAD,GAAE,OAAO,KAAK,EAAE,SAASC,EAAC,CAAC;AAAE,UAAM,IAAED,GAAE,OAAO,IAAK,CAAAA,OAAGA,GAAE,IAAK;AAAE,SAAK,yBAAuB,WAAU;AAAC,iBAAUA,MAAK,EAAE,MAAKA,EAAC,IAAE;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,WAAWA,IAAEC,IAAE;AAAC,UAAM,IAAE,KAAK,SAAS,2BAAyB,KAAK,SAAS,2BAAyB;AAAE,QAAG,IAAE,EAAE,YAAUC,MAAKD,GAAE,YAAW;AAAC,YAAMD,KAAEC,GAAE,WAAWC,EAAC;AAAE,kBAAU,OAAOF,MAAGA,GAAE,SAAO,MAAIC,GAAE,WAAWC,EAAC,IAAE;AAAA,IAAG;AAAC,IAAAF,GAAE,SAAS,KAAKC,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBD,IAAEC,IAAE;AAAC,UAAK,EAAC,eAAc,GAAE,mBAAkBC,GAAC,IAAED,IAAEK,KAAE,GAAE,EAAE,MAAM,GAAE,GAAE,OAAG,OAAG,KAAK,UAAU,GAAE,IAAE,GAAEA,IAAEJ,IAAE,OAAG,KAAE;AAAE,QAAIK,KAAE;AAAK,YAAOL,IAAE;AAAA,MAAC,KAAI;AAAoB,QAAAK,KAAE;AAAQ;AAAA,MAAM,KAAI;AAAsB,QAAAA,KAAE;AAAU;AAAA,MAAM,KAAI;AAAuB,QAAAA,KAAE;AAAW;AAAA,MAAM,KAAI;AAAyB,QAAAA,KAAE;AAAA,IAAY;AAAC,MAAE,OAAKA,IAAEP,GAAE,oBAAkBE,IAAEF,GAAE,gBAAc;AAAA,EAAC;AAAA,EAAC,gBAAgBC,IAAE;AAAC,YAAO,KAAK,aAAWA,GAAE,aAAW,MAAK,KAAK,SAAS,kBAAgBA,GAAE,cAAY,KAAK,kBAAgB,KAAK,sBAAsBA,EAAC,IAAG,KAAK,mBAAiB,GAAEA,GAAE,QAAM,KAAK,oBAAmBA,GAAE,QAAM,KAAK,oBAAmBA,GAAE,cAAa;AAAA,MAAC,KAAI;AAAQ,aAAK,gBAAc,CAACD,IAAEC,IAAE,MAAI,KAAK,mBAAmBD,IAAEC,IAAE,CAAC,GAAE,KAAK,iBAAe,CAAAD,OAAG,KAAK,oBAAoBA,EAAC;AAAE;AAAA,MAAM,KAAI;AAAU,aAAK,gBAAc,CAACA,IAAEC,IAAE,MAAI,KAAK,sBAAsBD,IAAEC,IAAE,CAAC,GAAE,KAAK,iBAAe,CAAAD,OAAG,KAAK,uBAAuBA,EAAC;AAAE;AAAA,MAAM,KAAI;AAAW,aAAK,gBAAc,CAACA,IAAEC,IAAE,MAAI,KAAK,uBAAuBD,IAAEC,IAAE,CAAC,GAAE,KAAK,iBAAe,CAAAD,OAAG,KAAK,wBAAwBA,EAAC;AAAE;AAAA,MAAM,KAAI;AAAa,aAAK,gBAAc,CAACA,IAAEC,IAAE,MAAI,KAAK,yBAAyBD,IAAEC,IAAE,CAAC,GAAE,KAAK,iBAAe,CAAAD,OAAG,KAAK,0BAA0BA,EAAC;AAAE;AAAA,MAAM,KAAI;AAAA,MAAO,KAAI;AAAS;AAAA,MAAM;AAAQ,UAAEC,GAAE,YAAY;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,WAAO,KAAK,SAAS,SAAO,GAAE,KAAK,sBAAoB,GAAE,KAAK,oBAAoB,CAAC,IAAE,GAAE,KAAK,oBAAoB,CAAC,IAAE,GAAE,IAAI,EAAE,EAAE,GAAE,MAAK,IAAI,KAAK,wBAAsB;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,UAAMD,KAAE,KAAK,SAAS,OAAQ,CAACA,IAAEC,OAAID,KAAEC,IAAG,CAAC;AAAE,SAAK,oBAAkB,IAAI,aAAaD,KAAE,KAAK,gBAAgB,GAAE,KAAK,uBAAqB;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAEC,IAAE;AAAC,UAAI,KAAK,SAAS,WAAS,KAAK,sBAAoBA,KAAG,KAAK,SAAS,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBD,IAAE;AAAC,UAAMC,KAAE,EAAC,MAAK,SAAQ,GAAE,GAAE,GAAE,GAAE,kBAAiBD,GAAE,kBAAiB,MAAK,CAAC,CAACA,GAAE,MAAK,MAAK,CAAC,CAACA,GAAE,KAAI;AAAE,WAAOC,GAAE,SAAOA,GAAE,IAAE,IAAGA,GAAE,SAAOA,GAAE,IAAE,IAAGA;AAAA,EAAC;AAAA,EAAC,mBAAmBD,IAAEC,IAAE,GAAE;AAAC,UAAMC,KAAE,KAAK,aAAW,KAAK,gBAAgB,KAAK,YAAWD,IAAE,GAAE,CAAC,IAAEA;AAAE,QAAG,QAAMC,GAAE,SAAO,GAAE;AAAA,MAAC,KAAK;AAAE,QAAAF,GAAE,IAAEE;AAAE;AAAA,MAAM,KAAK;AAAE,QAAAF,GAAE,IAAEE;AAAE;AAAA,MAAM,KAAK;AAAE,QAAAF,GAAE,OAAKA,GAAE,IAAEE,KAAEF,GAAE,IAAEE;AAAE;AAAA,MAAM,KAAK;AAAE,QAAAF,GAAE,IAAEE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,wBAAwBF,IAAEC,IAAE;AAAC,QAAI,IAAE;AAAE,WAAOA,MAAG,MAAI,IAAE,KAAK,oBAAoBA,EAAC,GAAE,KAAK,oBAAoBA,EAAC,KAAGD,KAAG,KAAK,aAAW,KAAK,gBAAgB,KAAK,YAAWA,IAAEC,IAAE,CAAC,IAAED;AAAA,EAAC;AAAA,EAAC,uBAAuBA,IAAEC,IAAE,GAAE;AAAC,SAAK,+BAA+BD,GAAE,OAAMC,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBD,IAAEC,IAAE,GAAE;AAAC,SAAK,+BAA+BD,GAAE,OAAMC,IAAE,CAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBD,IAAEC,IAAE,GAAE;AAAC,UAAI,KAAGD,GAAE,OAAO,KAAK,CAAC,CAAC;AAAE,UAAME,KAAE,KAAK,wBAAwBD,IAAE,CAAC;AAAE,IAAAD,GAAE,OAAOA,GAAE,OAAO,SAAO,CAAC,EAAE,KAAKE,EAAC;AAAA,EAAC;AAAA,EAAC,uBAAuBF,IAAE;AAAC,WAAM,EAAC,MAAK,WAAU,OAAM,CAAC,CAAC,CAAC,GAAE,kBAAiBA,GAAE,kBAAiB,MAAK,CAAC,CAACA,GAAE,MAAK,MAAK,CAAC,CAACA,GAAE,KAAI;AAAA,EAAC;AAAA,EAAC,wBAAwBA,IAAE;AAAC,WAAM,EAAC,MAAK,YAAW,OAAM,CAAC,CAAC,CAAC,GAAE,kBAAiBA,GAAE,kBAAiB,MAAK,CAAC,CAACA,GAAE,MAAK,MAAK,CAAC,CAACA,GAAE,KAAI;AAAA,EAAC;AAAA,EAAC,0BAA0BA,IAAE;AAAC,WAAM,EAAC,MAAK,cAAa,QAAO,CAAC,GAAE,kBAAiBA,GAAE,kBAAiB,MAAK,CAAC,CAACA,GAAE,MAAK,MAAK,CAAC,CAACA,GAAE,KAAI;AAAA,EAAC;AAAA,EAAC,+BAA+BA,IAAEC,IAAE,GAAE;AAAC,UAAI,KAAG,KAAG,KAAK,0BAAwBD,GAAE,KAAK,CAAC,CAAC,GAAE,KAAK,sBAAoB,KAAK,SAAS,EAAE,KAAK,mBAAmB,IAAE,GAAE,KAAK,oBAAoB,CAAC,IAAE,GAAE,KAAK,oBAAoB,CAAC,IAAE;AAAG,UAAME,KAAE,KAAK,wBAAwBD,IAAE,CAAC,GAAEK,KAAEN,GAAEA,GAAE,SAAO,CAAC,GAAE,IAAE,KAAK;AAAkB,QAAG,GAAE;AAAC,UAAG,MAAI,GAAE;AAAC,cAAMA,KAAE,KAAK,uBAAqB,aAAa;AAAkB,QAAAM,GAAE,KAAK,IAAI,aAAa,EAAE,QAAON,IAAE,KAAK,gBAAgB,CAAC;AAAA,MAAC;AAAC,QAAE,KAAK,sBAAsB,IAAEE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBF,IAAE;AAAC,UAAK,EAAC,MAAKC,IAAE,MAAK,EAAC,IAAED;AAAE,WAAOC,MAAG,IAAEI,KAAEJ,KAAE,IAAE,IAAEG,KAAE;AAAA,EAAC;AAAC;;;ACA7tK,IAAMI,KAAN,MAAO;AAAA,EAAC,mBAAmBA,IAAE;AAJlO;AAImO,UAAMC,KAAED,GAAEA,GAAE,QAAO,IAAI,EAAEA,GAAE,OAAO,CAAC,GAAEE,KAAE,EAAC,GAAGD,IAAE,mBAAiB,KAAAA,GAAE,qBAAF,mBAAoB,UAAS,QAAOA,GAAE,SAAOA,GAAE,OAAO,IAAK,CAAAE,OAAGA,GAAE,OAAO,CAAE,IAAE,OAAM;AAAE,WAAO,QAAQ,QAAQD,EAAC;AAAA,EAAC;AAAC;AAAC,SAAS,IAAG;AAAC,SAAO,IAAIF;AAAC;", "names": ["t", "e", "s", "y", "d", "f", "o", "n", "t", "s", "o", "e"]}