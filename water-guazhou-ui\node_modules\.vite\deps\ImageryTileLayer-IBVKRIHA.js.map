{"version": 3, "sources": ["../../@arcgis/core/layers/support/rasterDatasets/BaseRaster.js", "../../@arcgis/core/layers/support/rasterDatasets/FunctionRaster.js", "../../@arcgis/core/layers/mixins/ImageryTileMixin.js", "../../@arcgis/core/layers/support/rasterDatasets/DBFParser.js", "../../@arcgis/core/layers/support/rasterDatasets/CloudRaster.js", "../../@arcgis/core/layers/support/rasterDatasets/InMemoryRaster.js", "../../@arcgis/core/layers/support/rasterDatasets/xmlUtilities.js", "../../@arcgis/core/layers/support/rasterDatasets/pamParser.js", "../../@arcgis/core/layers/support/rasterDatasets/ImageAuxRaster.js", "../../@arcgis/core/layers/support/rasterDatasets/ImageServerRaster.js", "../../@arcgis/core/layers/support/rasterDatasets/MRFRaster.js", "../../@arcgis/core/layers/support/rasterDatasets/TIFFRaster.js", "../../@arcgis/core/layers/support/rasterDatasets/RasterFactory.js", "../../@arcgis/core/layers/ImageryTileLayer.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import\"../../../geometry.js\";import t from\"../../../request.js\";import i from\"../../../core/Error.js\";import{JSONSupport as o}from\"../../../core/JSONSupport.js\";import r from\"../../../core/Logger.js\";import{isSome as n,isNone as s,unwrap as a,unwrapOrThrow as l}from\"../../../core/maybe.js\";import{EsriPromiseMixin as c}from\"../../../core/Promise.js\";import{onAbort as m}from\"../../../core/promiseUtils.js\";import{property as f}from\"../../../core/accessorSupport/decorators/property.js\";import{ensureClass as h}from\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as u}from\"../../../core/accessorSupport/decorators/subclass.js\";import{sanitizeUrl as p}from\"../arcgisLayerUrl.js\";import{url as d}from\"../commonProperties.js\";import x from\"../DimensionalDefinition.js\";import y from\"../LOD.js\";import g from\"../RasterStorageInfo.js\";import I from\"../TileInfo.js\";import{isMultiSliceOrRangeDefinition as R,getSliceIndex as w,createSlices as k,getSliceIds as S}from\"./multidimensionalUtils.js\";import{getRasterId as v,getBlock as T,putBlock as b,deleteBlock as B,decreaseRefCount as P}from\"./RawBlockCache.js\";import{convertNoDataToMask as M}from\"../rasterFormats/pixelRangeUtils.js\";import{decode as W}from\"../rasterFormats/RasterCodec.js\";import{mosaic as _,approximateTransform as C,getLocalArithmeticNorthRotations as j}from\"../rasterFunctions/pixelUtils.js\";import{load as H,getWorldWidth as L,projectPoint as E,snapPyramid as D,snapExtent as z,getWorldWrapCount as A,projectExtent as F,getProjectionOffsetGrid as q,getRasterDatasetAlignmentInfo as O,projectResolution as G}from\"../rasterFunctions/rasterProjectionHelper.js\";import{convertToLocalDirections as J,uvComponentToVector as N}from\"../rasterFunctions/vectorFieldUtils.js\";import V from\"../../../geometry/Extent.js\";import U from\"../../../geometry/Point.js\";const $=8,X=256;let K=class extends(c(o)){constructor(){super(...arguments),this.rasterJobHandler=null,this.datasetName=null,this.datasetFormat=null,this.hasUniqueSourceStorageInfo=!0,this.rasterInfo=null,this.ioConfig={sampling:\"closest\"}}async init(){const e=H();this.addResolvingPromise(e),await this.when()}normalizeCtorArgs(e){return e&&e.ioConfig&&(e={...e,ioConfig:{resolution:null,bandIds:null,sampling:\"closest\",tileInfo:I.create(),...e.ioConfig}}),e}get _isGlobalWrappableSource(){const{rasterInfo:e}=this,t=L(e.spatialReference);return n(t)&&e.extent.width>=t/2}set url(e){this._set(\"url\",p(e,r.getLogger(this.declaredClass)))}async open(e){throw new i(\"BaseRaster:open-not-implemented\",\"open() is not implemented\")}async fetchTile(e,t,i,o={}){const r=o.tileInfo||this.rasterInfo.storageInfo.tileInfo,n=this.getTileExtentFromTileInfo(e,t,i,r);return this.fetchPixels(n,r.size[0],r.size[1],o)}async identify(e,t={}){e=h(U,e).clone().normalize();const{multidimensionalDefinition:i,timeExtent:o}=t,{rasterInfo:r}=this,{hasMultidimensionalTranspose:l,multidimensionalInfo:c}=r;let{transposedVariableName:m}=t;const f=n(c)&&l&&(null!=o||R(i));if(f&&!m){m=n(i)&&i.length>0?i[0].variableName??void 0:c.variables[0].name,t={...t,transposedVariableName:m}}t=this._getRequestOptionsWithSliceId(t);const{spatialReference:u,extent:p}=r,{datumTransformation:d}=t;let x=E(e,u,d);if(!p.intersects(x))return{location:x,value:null};if(n(r.transform)){const e=r.transform.inverseTransform(x);if(!r.nativeExtent.intersects(e))return{location:e,value:null};x=e}let y=0;const g=n(m)&&n(c)&&r.hasMultidimensionalTranspose;if(\"Function\"===this.datasetFormat){const e=this.primaryRasters.rasters[0];if(g)return e.identify(x,t);const{pixelSize:i}=r,o=3,a=i.x*o/2,l=i.y*o/2,c=new V({xmin:x.x-a,xmax:x.x+a,ymin:x.y-l,ymax:x.y+l,spatialReference:u}),m={interpolation:\"nearest\"},{pixelBlock:f}=await e.fetchPixels(c,o,o,m),{pixelBlock:h}=await this.fetchPixels(c,o,o,m);if(s(f))return{location:x,value:null};const p=Math.floor(o*o*.5),d=!f.mask||f.mask[p]?f.pixels.map((e=>e[p])):null;let y;return n(h)&&(y=!h.mask||h.mask[p]?h.pixels.map((e=>e[p])):void 0),{location:x,value:d,processedValue:y,pyramidLevel:0}}if(!g)if(t.srcResolution){y=D(t.srcResolution,r,this.ioConfig.sampling).pyramidLevel}else if(y=await this.computeBestPyramidLevelForLocation(e,t),null==y)return{location:x,value:null};const I=this.identifyPixelLocation(x,y,null,g);if(null===I)return{location:x,value:null};const{row:w,col:k,rowOffset:S,colOffset:B,blockWidth:P}=I,M=m??a(t.sliceId),W=v(this.url,M),_=`${y}/${w}/${k}`;let C=T(W,null,_);s(C)&&(C=this.fetchRawTile(y,w,k,t),b(W,null,_,C));const j=await C;if(s(j)||!j.pixels?.length)return{location:x,value:null};const H=S*P+B;return this._processIdentifyResult(j,{srcLocation:x,position:H,pyramidLevel:y,useTransposedTile:!!g,requestSomeSlices:f,identifyOptions:t})}async fetchPixels(e,t,i,o={}){if(e=z(e),(o=this._getRequestOptionsWithSliceId(o)).requestRawData)return this._fetchPixels(e,t,i,o);const r=L(e.spatialReference),n=A(e);if(s(r)||0===n||1===n&&this._isGlobalWrappableSource)return this._fetchPixels(e,t,i,o);if(n>=3)return{extent:e,pixelBlock:null};const a=[],{xmin:l,xmax:c}=e,m=Math.round(r/(c-l)*t),f=m-Math.round((r/2-l)/(c-l)*t);let h=0;const u=[];for(let s=0;s<=n;s++){const p=new V({xmin:0===s?l:-r/2,xmax:s===n?c-r*s:r/2,ymin:e.ymin,ymax:e.ymax,spatialReference:e.spatialReference}),d=0===s?m-f:s===n?t-h:m;h+=d,u.push(d);const x=o.disableWrapAround&&s>0?null:this._fetchPixels(p,d,i,o);a.push(x)}const p=(await Promise.all(a)).map((e=>e?.pixelBlock));let d=null;const x={width:t,height:i};if(this.rasterJobHandler){d=(await this.rasterJobHandler.mosaicAndTransform({srcPixelBlocks:p,srcMosaicSize:x,destDimension:null,coefs:null,sampleSpacing:null,interpolation:\"nearest\",alignmentInfo:null,blockWidths:u},o)).pixelBlock}else d=_(p,x,{blockWidths:u});return{extent:e,srcExtent:F(e,this.rasterInfo.spatialReference,o.datumTransformation),pixelBlock:d}}async fetchRawPixels(e,t,i,o={}){t={x:Math.floor(t.x),y:Math.floor(t.y)};const r=await this._fetchRawTiles(e,t,i,o),{nativeExtent:s,nativePixelSize:a,storageInfo:l}=this.rasterInfo,c=2**e,m=a.x*c,f=a.y*c,h=new V({xmin:s.xmin+m*t.x,xmax:s.xmin+m*(t.x+i.width-1),ymin:s.ymax-f*(t.y+i.height-1),ymax:s.ymax-f*t.y,spatialReference:s.spatialReference});if(!r)return{extent:h,srcExtent:h,pixelBlock:null};const{pixelBlocks:u,mosaicSize:p}=r;if(1===u.length&&n(u[0])&&u[0].width===i.width&&u[0].height===i.height)return{extent:h,srcExtent:h,pixelBlock:r.pixelBlocks[0]};const d=e>0?l.pyramidBlockWidth:l.blockWidth,x=e>0?l.pyramidBlockHeight:l.blockHeight,y={x:t.x%d,y:t.y%x};let g;if(this.rasterJobHandler){g=(await this.rasterJobHandler.mosaicAndTransform({srcPixelBlocks:u,srcMosaicSize:p,destDimension:i,clipOffset:y,clipSize:i,coefs:null,sampleSpacing:null,interpolation:o.interpolation,alignmentInfo:null,blockWidths:null},o)).pixelBlock}else g=_(u,p,{clipOffset:y,clipSize:i});return{extent:h,srcExtent:h,pixelBlock:g}}fetchRawTile(e,t,o,r){throw new i(\"BaseRaster:read-not-implemented\",\"fetchRawTile() is not implemented\")}computeExtent(e){return F(this.rasterInfo.extent,e)}decodePixelBlock(e,t){return!this.rasterJobHandler||t.useCanvas?W(e,t):this.rasterJobHandler.decode({data:e,options:t})}async request(e,i,o=0){const{customFetchParameters:r}=this.ioConfig,{range:n,query:s,headers:a}=i;o=o??i.retryCount??this.ioConfig.retryCount;const l=n?{Range:`bytes=${n.from}-${n.to}`}:null;try{return await t(e,{...i,query:{...s,...r},headers:{...a,...l}})}catch(c){if(o>0)return o--,this.request(e,i,o);throw c}}getSliceIndex(e){const{multidimensionalInfo:t}=this.rasterInfo;return s(t)||s(e)||0===e.length?null:w(e,t)}getTileExtentFromTileInfo(e,t,i,o){const r=l(o.lodAt(e));return this.getTileExtent({x:r.resolution,y:r.resolution},t,i,o.origin,o.spatialReference,o.size)}updateTileInfo(){const{storageInfo:e,spatialReference:t,extent:i,pixelSize:o}=this.rasterInfo;if(!e.tileInfo){const r=[],n=e.maximumPyramidLevel||0;let s=Math.max(o.x,o.y),a=1/.0254*96*s;for(let e=0;e<=n;e++)r.push(new y({level:n-e,resolution:s,scale:a})),s*=2,a*=2;const l=new U({x:i.xmin,y:i.ymax,spatialReference:t});e.tileInfo=new I({origin:l,size:[e.blockWidth,e.blockHeight],spatialReference:t,lods:r}),e.isVirtualTileInfo=!0}}createRemoteDatasetStorageInfo(e,t=512,i=512,o){const{width:r,height:n,nativeExtent:s,pixelSize:a,spatialReference:l}=e,c=new U({x:s.xmin,y:s.ymax,spatialReference:l});null==o&&(o=Math.max(0,Math.round(Math.log(Math.max(r,n))/Math.LN2-8)));const m=this.computeBlockBoundary(s,512,512,{x:s.xmin,y:s.ymax},[a],o);e.storageInfo=new g({blockWidth:t,blockHeight:i,pyramidBlockWidth:t,pyramidBlockHeight:i,origin:c,firstPyramidLevel:1,maximumPyramidLevel:o,blockBoundary:m})}async computeBestPyramidLevelForLocation(e,t={}){return 0}computeBlockBoundary(e,t,i,o,r,n=0,s=2){if(1===r.length&&n>0){r=[...r];let{x:e,y:t}=r[0];for(let i=0;i<n;i++)e*=s,t*=s,r.push({x:e,y:t})}const a=[],{x:l,y:c}=o;for(let m=0;m<r.length;m++){const{x:o,y:n}=r[m];a.push({minCol:Math.floor((e.xmin-l+.1*o)/t/o),maxCol:Math.floor((e.xmax-l-.1*o)/t/o),minRow:Math.floor((c-e.ymax+.1*n)/i/n),maxRow:Math.floor((c-e.ymin-.1*n)/i/n)})}return a}getPyramidPixelSize(e){const{nativePixelSize:t}=this.rasterInfo,{pyramidResolutions:i,pyramidScalingFactor:o}=this.rasterInfo.storageInfo;if(0===e)return t;if(n(i)&&i.length)return i[e-1];const r=o**e;return{x:t.x*r,y:t.y*r}}identifyPixelLocation(e,t,i,o){const{spatialReference:r,nativeExtent:s,storageInfo:a}=this.rasterInfo,{maximumPyramidLevel:l,origin:c,transposeInfo:m}=a,f=o&&n(m)?m.tileSize[0]:a.blockWidth,h=o&&n(m)?m.tileSize[1]:a.blockHeight,u=E(e,r,i);if(!s.intersects(u))return null;if(t<0||t>l)return null;const p=this.getPyramidPixelSize(t),{x:d,y:x}=p,y=(c.y-u.y)/x/h,g=(u.x-c.x)/d/f,I=Math.min(h-1,Math.floor((y-Math.floor(y))*h)),R=Math.min(f-1,Math.floor((g-Math.floor(g))*f));return{pyramidLevel:t,row:Math.floor(y),col:Math.floor(g),rowOffset:I,colOffset:R,blockWidth:f,srcLocation:u}}getTileExtent(e,t,i,o,r,n){const[s,a]=n,l=o.x+i*s*e.x,c=l+s*e.x,m=o.y-t*a*e.y,f=m-a*e.y;return new V({xmin:l,xmax:c,ymin:f,ymax:m,spatialReference:r})}getBlockWidthHeight(e){return{blockWidth:e>0?this.rasterInfo.storageInfo.pyramidBlockWidth:this.rasterInfo.storageInfo.blockWidth,blockHeight:e>0?this.rasterInfo.storageInfo.pyramidBlockHeight:this.rasterInfo.storageInfo.blockHeight}}isBlockOutside(e,t,i){const o=this.rasterInfo.storageInfo.blockBoundary[e];return!o||o.maxRow<t||o.maxCol<i||o.minRow>t||o.minCol>i}async _fetchPixels(e,t,i,o={}){let r=A(e);if(r>=2)return{extent:e,pixelBlock:null};const s=this._getSourceDataInfo(e,t,i,o),{pyramidLevel:l,pyramidResolution:c,srcResolution:m,srcExtent:f,srcWidth:h,srcHeight:u}=s;if(0===h||0===u)return{extent:e,srcExtent:f,pixelBlock:null};const p=a(this.rasterInfo.transform),d=\"gcs-shift\"===p?.type,x=n(L(e.spatialReference));!d&&x||(r=A(s.srcExtent,d));const y=this.rasterInfo.storageInfo,g={x:Math.floor((f.xmin-y.origin.x)/c.x+.1),y:Math.floor((y.origin.y-f.ymax)/c.y+.1)},I=await this._fetchRawTiles(l,g,{width:h,height:u,wrapCount:r},o);if(!I)return{extent:e,srcExtent:f,pixelBlock:null};const R=l>0?y.pyramidBlockWidth:y.blockWidth,w=l>0?y.pyramidBlockHeight:y.blockHeight,k=R===h&&w===u&&g.x%R==0&&g.y%w==0,S=new U({x:(e.xmax-e.xmin)/t,y:(e.ymax-e.ymin)/i,spatialReference:e.spatialReference}),v=!e.spatialReference.equals(this.rasterInfo.spatialReference),{datumTransformation:T}=o;if(!v&&k&&1===I.pixelBlocks.length&&R===t&&w===i&&m.x===S.x&&m.y===S.y)return{extent:e,srcExtent:f,pixelBlock:I.pixelBlocks[0]};const b=x&&n(L(f.spatialReference)),B=o.requestProjectedLocalDirections&&this.rasterInfo.dataType.startsWith(\"vector\");B&&!this.rasterJobHandler&&await H();const P=this.rasterJobHandler?await this.rasterJobHandler.getProjectionOffsetGrid({projectedExtent:e,srcBufferExtent:I.extent,pixelSize:S.toJSON(),datumTransformation:T,rasterTransform:p,hasWrapAround:r>0||b,isAdaptive:!1!==this.ioConfig.optimizeProjectionAccuracy,includeGCSGrid:B},o):q({projectedExtent:e,srcBufferExtent:I.extent,pixelSize:S,datumTransformation:T,rasterTransform:p,hasWrapAround:r>0||b,isAdaptive:!1,includeGCSGrid:B});let M;const W=!o.requestRawData,E={rows:P.spacing[0],cols:P.spacing[1]},D=a(this._getRasterTileAlignmentInfo(l,I.extent.xmin)),{pixelBlocks:z,mosaicSize:F,isPartiallyFilled:O}=I;let G=null;if(this.rasterJobHandler){const e=await this.rasterJobHandler.mosaicAndTransform({srcPixelBlocks:z,srcMosaicSize:F,destDimension:W?{width:t,height:i}:null,coefs:W?P.coefficients:null,sampleSpacing:W?E:null,projectDirections:B,gcsGrid:B?P.gcsGrid:null,isUV:\"vector-uv\"===this.rasterInfo.dataType,interpolation:o.interpolation,alignmentInfo:D,blockWidths:null},o);({pixelBlock:M,localNorthDirections:G}=e)}else{const e=_(z,F,{alignmentInfo:D});M=W?C(e,{width:t,height:i},P.coefficients,E,o.interpolation):e,B&&P.gcsGrid&&(G=j({width:t,height:i},P.gcsGrid),M=J(M,this.rasterInfo.dataType,G))}return o.requestRawData||B?{srcExtent:f,pixelBlock:M,transformGrid:P,localNorthDirections:G,extent:e,isPartiallyFilled:O}:{srcExtent:f,extent:e,pixelBlock:M}}async _fetchRawTiles(e,t,i,o){const{origin:r,blockBoundary:s}=this.rasterInfo.storageInfo,{blockWidth:a,blockHeight:l}=this.getBlockWidthHeight(e);let{x:c,y:m}=t,{width:f,height:h,wrapCount:u}=i;const p=this._getRasterTileAlignmentInfo(e,0);o.buffer&&(c-=o.buffer.cols,m-=o.buffer.rows,f+=2*o.buffer.cols,h+=2*o.buffer.rows);let d=0,x=0,y=0;if(u&&n(p)){({worldColumnCountFromOrigin:x,originColumnOffset:y,rightPadding:d}=p);x*p.blockWidth-d>=c+f&&(d=0)}const g=Math.floor(c/a),I=Math.floor(m/l),R=Math.floor((c+f+d-1)/a),w=Math.floor((m+h+d-1)/l),k=s[e];if(!k)return null;const{minRow:S,minCol:v,maxCol:T,maxRow:b}=k;if(0===u&&(w<S||R<v||I>b||g>T))return null;const B=new Array;let P=!1;const M=null==this.ioConfig.allowPartialFill?o.allowPartialFill:this.ioConfig.allowPartialFill;for(let E=I;E<=w;E++)for(let t=g;t<=R;t++){let i=t;if(!o.disableWrapAround&&u&&n(p)&&x<=t&&(i=t-x-y),E>=S&&i>=v&&b>=E&&T>=i){const t=this._fetchRawTile(e,E,i,o);M?B.push(new Promise((e=>{t.then((t=>e(t))).catch((()=>{P=!0,e(null)}))}))):B.push(t)}else B.push(Promise.resolve(null))}if(0===B.length)return null;const W=await Promise.all(B),_={height:(w-I+1)*l,width:(R-g+1)*a},{spatialReference:C}=this.rasterInfo,j=this.getPyramidPixelSize(e),{x:H,y:L}=j;return{extent:new V({xmin:r.x+g*a*H,xmax:r.x+(R+1)*a*H,ymin:r.y-(w+1)*l*L,ymax:r.y-I*l*L,spatialReference:C}),pixelBlocks:W,mosaicSize:_,isPartiallyFilled:P}}_fetchRawTile(e,t,i,o){const r=this.rasterInfo.storageInfo.blockBoundary[e];if(!r)return Promise.resolve(null);const{minRow:n,minCol:a,maxCol:l,maxRow:c}=r;if(t<n||i<a||t>c||i>l)return Promise.resolve(null);const f=v(this.url,o.sliceId),h=`${e}/${t}/${i}`;let u=T(f,o.registryId,h);if(s(u)){const r=new AbortController;u=this.fetchRawTile(e,t,i,{...o,signal:r.signal}),b(f,o.registryId,h,u,r),u.catch((()=>B(f,o.registryId,h)))}return o.signal&&m(o,(()=>{P(f,o.registryId,h)})),u}_computeMagDirValues(e){const{bandCount:t,dataType:i}=this.rasterInfo;if(!(2===t&&\"vector-magdir\"===i||\"vector-uv\"===i)||2!==e?.length||!e[0]?.length)return null;const o=e[0].length;if(\"vector-magdir\"===i){const t=e[1].map((e=>(e+360)%360));return[e[0],t]}const[r,n]=e,s=[],a=[];for(let l=0;l<o;l++){const[e,t]=N([r[l],n[l]]);s.push(e),a.push(t)}return[s,a]}_getRasterTileAlignmentInfo(e,t){return null==this._rasterTileAlighmentInfo&&(this._rasterTileAlighmentInfo=O(this.rasterInfo)),s(this._rasterTileAlighmentInfo.pyramidsInfo)?null:{startX:t,halfWorldWidth:this._rasterTileAlighmentInfo.halfWorldWidth,hasGCSSShiftTransform:this._rasterTileAlighmentInfo.hasGCSSShiftTransform,...this._rasterTileAlighmentInfo.pyramidsInfo[e]}}_getSourceDataInfo(e,t,i,o={}){const r={datumTransformation:o.datumTransformation,pyramidLevel:0,pyramidResolution:null,srcExtent:null,srcHeight:0,srcResolution:null,srcWidth:0};o.srcResolution&&(r.srcResolution=o.srcResolution,this._updateSourceDataInfo(e,r));const n=this.rasterInfo.storageInfo.maximumPyramidLevel||0,{srcWidth:s,srcHeight:a,pyramidLevel:l}=r,c=s/t,m=a/i,f=l<n&&c*m>=16,h=l===n&&this._requireTooManySrcTiles(s,a,t,i);if(f||h||(0===s||0===a)){const s=new U({x:(e.xmax-e.xmin)/t,y:(e.ymax-e.ymin)/i,spatialReference:e.spatialReference});let a=G(s,this.rasterInfo.spatialReference,e,r.datumTransformation);const h=!a||o.srcResolution&&a.x+a.y<o.srcResolution.x+o.srcResolution.y;if(f&&o.srcResolution&&h){const e=Math.round(Math.log(Math.max(c,m))/Math.LN2)-1;if(n-l+3>=e){const t=2**e;a={x:o.srcResolution.x*t,y:o.srcResolution.y*t}}}a&&(r.srcResolution=a,this._updateSourceDataInfo(e,r))}return this._requireTooManySrcTiles(r.srcWidth,r.srcHeight,t,i)&&(r.srcWidth=0,r.srcHeight=0),r}_requireTooManySrcTiles(e,t,i,o){const{tileInfo:r}=this.rasterInfo.storageInfo;return Math.ceil(e/r.size[0])*Math.ceil(t/r.size[1])>=X||e/i>$||t/o>$}_updateSourceDataInfo(e,t){t.srcWidth=0,t.srcHeight=0;const i=this.rasterInfo.spatialReference,{srcResolution:o,datumTransformation:r}=t,{pyramidLevel:n,pyramidResolution:s,excessiveReading:l}=D(o,this.rasterInfo,this.ioConfig.sampling);if(l)return;let c=t.srcExtent||F(e,i,r);if(null==c)return;const m=a(this.rasterInfo.transform);m&&(c=m.inverseTransform(c)),t.srcExtent=c;const f=Math.ceil((c.xmax-c.xmin)/s.x-.1),h=Math.ceil((c.ymax-c.ymin)/s.y-.1);t.pyramidLevel=n,t.pyramidResolution=s,t.srcWidth=f,t.srcHeight=h}_getRequestOptionsWithSliceId(e){return n(this.rasterInfo.multidimensionalInfo)&&null==e.sliceId&&(e={...e,sliceId:this.getSliceIndex(e.multidimensionalDefinition)}),e}_processIdentifyResult(e,t){const{srcLocation:i,position:o,pyramidLevel:r,useTransposedTile:l}=t,c=e.pixels[0].length/e.width/e.height;if(!(!e.mask||e.mask[o]))return{location:i,value:null};const{multidimensionalInfo:m}=this.rasterInfo;if(s(m)||!l){const t=e.pixels.map((e=>e[o])),n={location:i,value:t,pyramidLevel:r},s=this._computeMagDirValues(t.map((e=>[e])));return s?.length&&(n.magdirValue=s.map((e=>e[0]))),n}let f=e.pixels.map((e=>e.slice(o*c,o*c+c))),h=this._computeMagDirValues(f);const{requestSomeSlices:u,identifyOptions:p}=t;let d=k(m,p.transposedVariableName);if(u){const e=S(d,a(p.multidimensionalDefinition),a(p.timeExtent));f=f.map((t=>e.map((e=>t[e])))),h=h?.map((t=>e.map((e=>t[e])))),d=e.map((e=>d[e]))}const y=e.noDataValues||this.rasterInfo.noDataValue,g={pixels:f,pixelType:e.pixelType};let I;n(y)&&(M(g,y),I=g.mask);return{location:i,value:null,dataSeries:d.map(((e,t)=>{const i={value:0===I?.[t]?null:f.map((e=>e[t])),multidimensionalDefinition:e.multidimensionalDefinition.map((e=>new x({...e,isSlice:!0})))};return h?.length&&(i.magdirValue=[h[0][t],h[1][t]]),i})),pyramidLevel:r}}};e([f()],K.prototype,\"_rasterTileAlighmentInfo\",void 0),e([f({readOnly:!0})],K.prototype,\"_isGlobalWrappableSource\",null),e([f(d)],K.prototype,\"url\",null),e([f({type:String,json:{write:!0}})],K.prototype,\"datasetName\",void 0),e([f({type:String,json:{write:!0}})],K.prototype,\"datasetFormat\",void 0),e([f()],K.prototype,\"hasUniqueSourceStorageInfo\",void 0),e([f()],K.prototype,\"rasterInfo\",void 0),e([f()],K.prototype,\"ioConfig\",void 0),e([f()],K.prototype,\"sourceJSON\",void 0),K=e([u(\"esri.layers.support.rasterDatasets.BaseRaster\")],K);const Q=K;export{Q as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as r}from\"../../../chunks/tslib.es6.js\";import t from\"../../../core/Error.js\";import{isNone as e,isSome as s}from\"../../../core/maybe.js\";import{property as a}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as o}from\"../../../core/accessorSupport/decorators/subclass.js\";import i from\"./BaseRaster.js\";let n=class extends i{constructor(){super(...arguments),this.datasetFormat=\"Function\",this.tileType=\"Raster\",this.rasterFunction=null}async open(r){await this.init();const{rasterFunction:e}=this;this.primaryRasters?.rasters?.length?e.sourceRasters=this.primaryRasters.rasters:this.primaryRasters=e.getPrimaryRasters();const{rasters:s,rasterIds:a}=this.primaryRasters,o=s.map((t=>t.rasterInfo?void 0:t.open(r)));await Promise.all(o);const i=s.map((({rasterInfo:r})=>r)),n=e.bind({rasterInfos:i,rasterIds:a});if(!n.success||0===i.length)throw new t(\"raster-function:open\",`cannot bind the function: ${n.error??\"\"}`);await this.syncJobHandler();const c=i[0];this.hasUniqueSourceStorageInfo=1===i.length||i.slice(1).every((r=>this._hasSameStorageInfo(r,c))),this.set(\"sourceJSON\",s[0].sourceJSON),this.set(\"rasterInfo\",e.rasterInfo)}async syncJobHandler(){return this.rasterJobHandler?.updateRasterFunction(this.rasterFunction)}async fetchPixels(r,t,a,o={}){const{rasters:i,rasterIds:n}=this.primaryRasters,c=i.map((e=>e.fetchPixels(r,t,a,o))),p=await Promise.all(c),l=p.map((r=>r.pixelBlock));if(o.skipRasterFunction||l.every((r=>e(r))))return p[0];const m=p.find((r=>s(r.pixelBlock)))?.extent??r,u=this.rasterJobHandler?await this.rasterJobHandler.process({extent:m,primaryPixelBlocks:l,primaryRasterIds:n}):this.rasterFunction.process({extent:m,primaryPixelBlocks:l,primaryRasterIds:n});return{...p[0],pixelBlock:u}}_hasSameStorageInfo(r,t){const{storageInfo:e,pixelSize:s,spatialReference:a,extent:o}=r,{storageInfo:i,pixelSize:n,spatialReference:c,extent:p}=t;return s.x===n.x&&s.y===n.y&&a.equals(c)&&o.equals(p)&&e.blockHeight===i.blockHeight&&e.blockWidth===i.blockWidth&&e.maximumPyramidLevel===i.maximumPyramidLevel}};r([a({type:String,json:{write:!0}})],n.prototype,\"datasetFormat\",void 0),r([a()],n.prototype,\"tileType\",void 0),r([a()],n.prototype,\"rasterFunction\",void 0),r([a()],n.prototype,\"primaryRasters\",void 0),n=r([o(\"esri.layers.support.rasterDatasets.FunctionRaster\")],n);const c=n;export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../chunks/tslib.es6.js\";import\"../../geometry.js\";import{rasterRendererTypes as e}from\"../../rasterRenderers.js\";import r from\"../../request.js\";import i from\"../../core/Error.js\";import s from\"../../core/Logger.js\";import{isNone as n,isSome as o}from\"../../core/maybe.js\";import{property as a}from\"../../core/accessorSupport/decorators/property.js\";import\"../../core/accessorSupport/ensureType.js\";import\"../../core/arrayUtils.js\";import{subclass as l}from\"../../core/accessorSupport/decorators/subclass.js\";import{getInfo as u}from\"../../geometry/support/spatialReferenceUtils.js\";import{sanitizeUrl as m}from\"../support/arcgisLayerUrl.js\";import{url as d}from\"../support/commonProperties.js\";import c from\"../support/DimensionalDefinition.js\";import h from\"../support/MultidimensionalSubset.js\";import f from\"../support/RasterFunction.js\";import p from\"../support/RasterJobHandler.js\";import y from\"../support/TileInfo.js\";import b from\"../support/rasterDatasets/FunctionRaster.js\";import{getDefaultMultidimensionalDefinition as g,hasExcludedVariableOrDimension as J,getDefaultVariablInfo as _,isMultiSliceOrRangeDefinition as F,getSubsetVariablesFromMdInfo as I}from\"../support/rasterDatasets/multidimensionalUtils.js\";import{create as R}from\"../support/rasterFunctions/rasterFunctionHelper.js\";import{convertVectorFieldData as S}from\"../support/rasterFunctions/vectorFieldUtils.js\";import{normalizeRendererJSON as v,getVariableRasterInfo as x,getDefaultInterpolation as D,getDefaultBandCombination as j,createDefaultRenderer as H}from\"../../renderers/support/rasterRendererHelper.js\";import T from\"../../renderers/support/RasterSymbolizer.js\";import{createFlowMesh as w}from\"../../views/2d/engine/flow/dataUtils.js\";import N from\"../../geometry/SpatialReference.js\";const O=s.getLogger(\"esri.layers.mixins.ImageryTileMixin\"),z=s=>{let z=class extends s{constructor(...t){super(...t),this._isConstructedFromFunctionRaster=!1,this._rasterJobHandler={instance:null,refCount:0,connectionPromise:null},this.bandIds=null,this.copyright=null,this.interpolation=\"nearest\",this.multidimensionalSubset=null,this.raster=null,this.rasterFunction=null,this.rasterInfo=null,this.sourceJSON=null,this.spatialReference=null,this.symbolizer=null,this._isConstructedFromFunctionRaster=\"Function\"===t[0]?.raster?.datasetFormat}get fullExtent(){return this.rasterInfo?.extent}set multidimensionalDefinition(t){this._set(\"multidimensionalDefinition\",t),this.updateRenderer()}get tileInfo(){return this.rasterInfo?.storageInfo.tileInfo}set url(t){this._set(\"url\",m(t,O))}set renderer(t){this._set(\"renderer\",t),this.updateRenderer()}async convertVectorFieldData(t,e){if(n(t)||!this.rasterInfo)return null;const r=this._rasterJobHandler.instance,i=this.rasterInfo.dataType;return r?r.convertVectorFieldData({pixelBlock:t,dataType:i},e):S(t,i)}async createFlowMesh(t,e){const r=this._rasterJobHandler.instance;return r?r.createFlowMesh(t,e):w(t.meshType,t.simulationSettings,t.flowData,o(e.signal)?e.signal:(new AbortController).signal)}normalizeRasterFetchOptions(t){const{multidimensionalInfo:e}=this.rasterInfo??{};if(n(e))return t;let r=t.multidimensionalDefinition||this.multidimensionalDefinition;!n(r)&&r.length||(r=g(this.raster.rasterInfo,{multidimensionalSubset:this.multidimensionalSubset}));const i=t.timeExtent||this.timeExtent;if(o(r)&&o(i)&&(o(i.start)||o(i.end))){r=r.map((t=>t.clone()));const s=e.variables.find((({name:t})=>t===r[0].variableName))?.dimensions?.find((({name:t})=>\"StdTime\"===t)),a=r.find((({dimensionName:t})=>\"StdTime\"===t));if(!s||!a)return{...t,multidimensionalDefinition:null};const{start:l,end:u}=i,m=n(l)?null:l.getTime(),d=n(u)?null:u.getTime(),c=m??d,h=d??m;if(o(s.values)){const t=s.values.filter((t=>{if(Array.isArray(t)){if(c===h)return t[0]<=c&&t[1]>=c;const e=t[0]<=c&&t[1]>c||t[0]<h&&t[1]>=h,r=t[0]>=c&&t[1]<=h||t[0]<c&&t[1]>h;return e||r}return c===h?t===c:t>=c&&t<=h}));if(t.length){const e=t.sort(((t,e)=>{if(c===h)return(t[0]??t)-(e[0]??e);return Math.abs((t[1]??t)-h)-Math.abs((e[1]??e)-h)}))[0];a.values=[e]}else r=null}else if(s.hasRegularIntervals&&s.extent){const[t,e]=s.extent;c>e||h<t?r=null:a.values=c===h?[c]:[Math.max(t,c),Math.min(e,h)]}}return o(r)&&J(r,this.multidimensionalSubset)?{...t,multidimensionalDefinition:null}:{...t,multidimensionalDefinition:r}}async updateRasterFunction(){if(\"imagery-tile\"!==this.type||!this.rasterFunction&&!this._cachedRasterFunctionJson||JSON.stringify(this.rasterFunction)===JSON.stringify(this._cachedRasterFunctionJson))return;if(this._isConstructedFromFunctionRaster&&\"Function\"===this.raster.datasetFormat){const t=this.raster.rasterFunction.toJSON();return!this.rasterFunction&&t&&this._set(\"rasterFunction\",f.fromJSON(t)),void(this._cachedRasterFunctionJson=this.rasterFunction?.toJSON())}let t,e=this.raster,r=!1;\"Function\"===e.datasetFormat?(t=e.primaryRasters.rasters,e=t[0],r=!0):t=[e];const{rasterFunction:i}=this;if(i){const r={raster:e};t.length>1&&t.forEach((t=>r[t.url]=t));const s=R(i.rasterFunctionDefinition??i.toJSON(),r),n=new b({rasterFunction:s});n.rasterJobHandler=this._rasterJobHandler.instance,await n.open(),this._cachedRasterFunctionJson=this.rasterFunction?.toJSON(),this.raster=n}else this.raster=e,this._cachedRasterFunctionJson=null;if(this._cachedRendererJson=null,!r&&!i)return;const{bandIds:s}=this,{bandCount:n}=this.raster.rasterInfo,o=s?.length?s.some((t=>t>=n)):n>=3;s&&(o||\"raster-stretch\"!==this.renderer.type)&&this._set(\"bandIds\",null),this._configDefaultRenderer(\"auto\")}async updateRenderer(){const{loaded:t,symbolizer:e}=this;if(!t||!e)return;const{rasterInfo:r}=this.raster,i=_(r,{multidimensionalDefinition:this.multidimensionalDefinition,multidimensionalSubset:this.multidimensionalSubset})?.name,s=v({...this.renderer.toJSON(),variableName:i});if(JSON.stringify(this._cachedRendererJson)===JSON.stringify(s))return;const n=this._rasterJobHandler.instance;n&&(e.rasterInfo=x(r,i),e.rendererJSON=s,e.bind(),await n.updateSymbolizer(e),this._cachedRendererJson=s)}async applyRenderer(t,e){const r=t&&t.pixelBlock;if(!(o(r)&&r.pixels&&r.pixels.length>0))return null;let i;await this.updateRenderer();const s=this._rasterJobHandler.instance,n=this.bandIds??[];return i=s?await s.symbolize({...t,simpleStretchParams:e,bandIds:n}):this.symbolizer.symbolize({...t,simpleStretchParams:e,bandIds:n}),i}getTileUrl(t,e,r){return\"RasterTileServer\"===this.raster.datasetFormat?`${this.url}/tile/${t}/${e}/${r}`:\"\"}getCompatibleTileInfo(t,e,r=!1){if(!this.loaded||n(e))return null;if(r&&t.equals(this.spatialReference))return this.tileInfo;const i=u(t);return y.create({size:256,spatialReference:t,origin:i?{x:i.origin[0],y:i.origin[1]}:{x:e.xmin,y:e.ymax}})}getCompatibleFullExtent(t){return this.loaded?(this._compatibleFullExtent&&this._compatibleFullExtent.spatialReference.equals(t)||(this._compatibleFullExtent=this.raster.computeExtent(t)),this._compatibleFullExtent):null}async fetchTile(t,e,i,s={}){if(C(this),s.requestAsImageElement){const n=this.getTileUrl(t,e,i);return r(n,{responseType:\"image\",query:{...this.refreshParameters,...this.raster.ioConfig.customFetchParameters},signal:s.signal}).then((t=>t.data))}const{rasterInfo:a}=this;if(o(a.multidimensionalInfo)&&(s=this.normalizeRasterFetchOptions(s),n(s.multidimensionalDefinition))){const r=s.tileInfo||a.storageInfo.tileInfo;return{extent:this.raster.getTileExtentFromTileInfo(t,e,i,r),pixelBlock:null}}return await this._initJobHandler(),await this.updateRasterFunction(),\"raster-shaded-relief\"===this.renderer.type&&(s={...s,buffer:{cols:1,rows:1}}),this.raster.fetchTile(t,e,i,s)}async fetchPixels(t,e,r,i={}){return o(this.rasterInfo.multidimensionalInfo)&&(i=this.normalizeRasterFetchOptions(i),n(i.multidimensionalDefinition))?{extent:t,pixelBlock:null}:(await this._initJobHandler(),await this.updateRasterFunction(),this.raster.fetchPixels(t,e,r,i))}async identify(t,e={}){const{raster:r,rasterInfo:s}=this;if(o(s.multidimensionalInfo)){if(!(s.hasMultidimensionalTranspose&&!!(F(e.multidimensionalDefinition)||e.transposedVariableName||e.timeExtent))&&(e=this.normalizeRasterFetchOptions(e),n(e.multidimensionalDefinition)))return{location:t,value:null}}const a=this.multidimensionalSubset?.areaOfInterest;if(a&&!a.contains(t))throw new i(\"imagery-tile-mixin:identify\",\"the request cannot be fulfilled when falling outside of the multidimensional subset\");return r.identify(t,e)}increaseRasterJobHandlerUsage(){this._rasterJobHandler.refCount++}decreaseRasterJobHandlerUsage(){this._rasterJobHandler.refCount--,this._rasterJobHandler.refCount<=0&&this._shutdownJobHandler()}hasStandardTime(){const t=this.rasterInfo?.multidimensionalInfo;if(n(t)||\"standard-time\"!==this.rasterInfo?.dataType)return!1;const e=this.multidimensionalDefinition,r=e?.[0]?.variableName;return t.variables.some((t=>t.name===r&&(!e?.[0].dimensionName||t.dimensions.some((t=>\"StdTime\"===t.name)))))}getStandardTimeValue(t){return new Date(24*(t-25569)*3600*1e3).toString()}getMultidimensionalSubsetVariables(t){const e=t??this.rasterInfo?.multidimensionalInfo;return I(this.multidimensionalSubset,e)}_configDefaultSettings(){this._configDefaultInterpolation(),this.multidimensionalDefinition||(this.multidimensionalDefinition=g(this.raster.rasterInfo,{multidimensionalSubset:this.multidimensionalSubset})),this._configDefaultRenderer()}_initJobHandler(){if(null!=this._rasterJobHandler.connectionPromise)return this._rasterJobHandler.connectionPromise;const t=new p;return this._rasterJobHandler.connectionPromise=t.initialize().then((()=>{C(this),this._rasterJobHandler.instance=t,this.raster.rasterJobHandler=t,this.renderer&&this.updateRenderer(),\"Function\"===this.raster.datasetFormat&&this.raster.syncJobHandler()})).catch((()=>{})),this._rasterJobHandler.connectionPromise}_shutdownJobHandler(){this._rasterJobHandler.instance&&this._rasterJobHandler.instance.destroy(),this._rasterJobHandler.instance=null,this._rasterJobHandler.connectionPromise=null,this._rasterJobHandler.refCount=0,this._cachedRendererJson=null,this.raster&&(this.raster.rasterJobHandler=null)}_configDefaultInterpolation(){if(null==this.interpolation){C(this);const{raster:t}=this,e=D(t.rasterInfo,t.tileType,this.sourceJSON?.defaultResamplingMethod);this._set(\"interpolation\",e)}}_configDefaultRenderer(t=\"no\"){C(this);const{rasterInfo:e}=this.raster;!this.bandIds&&e.bandCount>1&&(this.bandIds=j(e));const r=_(e,{multidimensionalDefinition:this.multidimensionalDefinition,multidimensionalSubset:this.multidimensionalSubset})?.name;if(!this.renderer||\"override\"===t){const t=H(e,{bandIds:this.bandIds,variableName:r});\"WCSServer\"===this.raster.datasetFormat&&\"raster-stretch\"===t.type&&((e.statistics?.[0].max??0)>1e24||(e.statistics?.[0].min??0)<-1e24)&&(t.dynamicRangeAdjustment=!0,t.statistics=null,\"none\"===t.stretchType&&(t.stretchType=\"min-max\")),this.renderer=t}const i=v({...this.renderer.toJSON(),variableName:r}),s=x(e,r);this.symbolizer?(this.symbolizer.rendererJSON=i,this.symbolizer.rasterInfo=s):this.symbolizer=new T({rendererJSON:i,rasterInfo:s});const n=this.symbolizer.bind();if(n.success){if(\"auto\"===t){const{colormap:t}=this.raster.rasterInfo,e=this.renderer;if(o(t))if(\"raster-colormap\"!==e.type)this._configDefaultRenderer(\"override\");else{const t=H(this.raster.rasterInfo);JSON.stringify(t)!==JSON.stringify(e)&&this._configDefaultRenderer(\"override\")}else if(\"raster-stretch\"===e.type){const t=this.bandIds?.length,r=e.statistics?.length;!e.dynamicRangeAdjustment&&r&&t&&r!==t&&this._configDefaultRenderer(\"override\")}}}else O.warn(\"imagery-tile-mixin\",n.error||\"The given renderer is not supported by the layer.\"),\"auto\"===t&&this._configDefaultRenderer(\"override\")}};function C(t){if(!t.raster||!t.rasterInfo)throw new i(\"imagery-tile\",\"no raster\")}return t([a()],z.prototype,\"_cachedRendererJson\",void 0),t([a()],z.prototype,\"_cachedRasterFunctionJson\",void 0),t([a()],z.prototype,\"_compatibleFullExtent\",void 0),t([a()],z.prototype,\"_isConstructedFromFunctionRaster\",void 0),t([a()],z.prototype,\"_rasterJobHandler\",void 0),t([a()],z.prototype,\"bandIds\",void 0),t([a({json:{origins:{service:{read:{source:\"copyrightText\"}}}}})],z.prototype,\"copyright\",void 0),t([a({json:{read:!1}})],z.prototype,\"fullExtent\",null),t([a()],z.prototype,\"interpolation\",void 0),t([a()],z.prototype,\"ioConfig\",void 0),t([a({type:[c],json:{write:!0}})],z.prototype,\"multidimensionalDefinition\",null),t([a({type:h,json:{write:!0}})],z.prototype,\"multidimensionalSubset\",void 0),t([a()],z.prototype,\"raster\",void 0),t([a({type:f})],z.prototype,\"rasterFunction\",void 0),t([a()],z.prototype,\"rasterInfo\",void 0),t([a()],z.prototype,\"sourceJSON\",void 0),t([a({readOnly:!0,type:N,json:{read:!1}})],z.prototype,\"spatialReference\",void 0),t([a({json:{read:!1}})],z.prototype,\"tileInfo\",null),t([a(d)],z.prototype,\"url\",null),t([a({types:e})],z.prototype,\"renderer\",null),t([a()],z.prototype,\"symbolizer\",void 0),z=t([l(\"esri.layers.ImageryTileMixin\")],z),z};export{z as ImageryTileMixin};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{bytesToUTF8 as e}from\"./byteStreamUtils.js\";function t(e){const t=e.fields,r=e.records,n=t.some((e=>\"oid\"===e.name.toLowerCase()))?\"OBJECTID\":\"OID\",i=[{name:n,type:\"esriFieldTypeOID\",alias:\"OID\"}].concat(t.map((e=>({name:e.name,type:\"esriFieldType\"+e.typeName,alias:e.name})))),s=i.map((e=>e.name)),a=[];let o=0,l=0;return r.forEach((e=>{const t={};for(t[n]=o++,l=1;l<s.length;l++)t[s[l]]=e[l-1];a.push({attributes:t})})),{displayFieldName:\"\",fields:i,features:a}}class r{static get supportedVersions(){return[5]}static parse(r){const n=new DataView(r),i=3&n.getUint8(0);if(3!==i)return{header:{version:i},recordSet:null};const s=n.getUint32(4,!0),a=n.getUint16(8,!0),o=n.getUint16(10,!0),l={version:i,recordCount:s,headerByteCount:a,recordByteCount:o};let p=32;const g=[],u=[];let d;if(3===i){for(;13!==n.getUint8(p);)d=String.fromCharCode(n.getUint8(p+11)).trim(),g.push({name:e(new Uint8Array(r,p,11)),type:d,typeName:[\"String\",\"Date\",\"Double\",\"Boolean\",\"String\",\"Integer\"][[\"C\",\"D\",\"F\",\"L\",\"M\",\"N\"].indexOf(d)],length:n.getUint8(p+16)}),p+=32;if(p+=1,g.length>0)for(;u.length<s&&r.byteLength-p>o;){const t=[];32===n.getUint8(p)?(p+=1,g.forEach((n=>{if(\"C\"===n.type)t.push(e(new Uint8Array(r,p,n.length)).trim());else if(\"N\"===n.type)t.push(parseInt(String.fromCharCode.apply(null,new Uint8Array(r,p,n.length)).trim(),10));else if(\"F\"===n.type)t.push(parseFloat(String.fromCharCode.apply(null,new Uint8Array(r,p,n.length)).trim()));else if(\"D\"===n.type){const e=String.fromCharCode.apply(null,new Uint8Array(r,p,n.length)).trim();t.push(new Date(parseInt(e.substring(0,4),10),parseInt(e.substring(4,6),10)-1,parseInt(e.substring(6,8),10)))}p+=n.length})),u.push(t)):p+=o}}return{header:l,fields:g,records:u,recordSet:t({fields:g,records:u})}}}export{r as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import\"../../../geometry.js\";import t from\"../../../core/Error.js\";import{isNone as r,isSome as o}from\"../../../core/maybe.js\";import{property as i}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as s}from\"../../../core/accessorSupport/decorators/subclass.js\";import a from\"../LOD.js\";import n from\"../RasterInfo.js\";import l from\"../RasterStorageInfo.js\";import f from\"../TileInfo.js\";import m from\"./BaseRaster.js\";import c from\"./DBFParser.js\";import{isTransformSupported as p,readTransform as d}from\"../rasterTransforms/utils.js\";import u from\"../../../rest/support/FeatureSet.js\";import h from\"../../../geometry/SpatialReference.js\";import g from\"../../../geometry/Extent.js\";import y from\"../../../geometry/Point.js\";const x=new Map;x.set(\"int16\",\"esriFieldTypeSmallInteger\"),x.set(\"int32\",\"esriFieldTypeInteger\"),x.set(\"int64\",\"esriFieldTypeInteger\"),x.set(\"float32\",\"esriFieldTypeSingle\"),x.set(\"float64\",\"esriFieldTypeDouble\"),x.set(\"text\",\"esriFieldTypeString\");const S=8;let I=class extends m{constructor(){super(...arguments),this.storageInfo=null,this.datasetFormat=\"CRF\"}async open(e){await this.init();const{data:r}=await this.request(this.url+\"/conf.json\",{signal:e?.signal});if(!this._validateHeader(r))throw new t(\"cloudraster:open\",\"Invalid or unsupported conf.json.\");this.datasetName=this.url.slice(this.url.lastIndexOf(\"/\")+1);const{storageInfo:o,rasterInfo:i}=this._parseHeader(r);if(\"thematic\"===i.dataType){const e=await this._fetchAuxiliaryInformation();i.attributeTable=e}this._set(\"storageInfo\",o),this._set(\"rasterInfo\",i),this.ioConfig.retryCount=this.ioConfig.retryCount||0}async fetchRawTile(e,t,r,o={}){const{transposeInfo:i}=this.rasterInfo.storageInfo,{transposedVariableName:s}=o,a=!(!i||!s),n=a?0:this.rasterInfo.storageInfo.maximumPyramidLevel-e;if(n<0)return null;const l=this._buildCacheFilePath(n,t,r,o.multidimensionalDefinition,s),f=this._getIndexRecordFromBundle(t,r,a),m=await this.request(l,{range:{from:0,to:this.storageInfo.headerSize-1},responseType:\"array-buffer\",signal:o.signal});if(!m)return null;const c=new Uint8Array(m.data),p=this._getTileEndAndContentType(c,f);if(0===p.recordSize)return null;const d=await this.request(l,{range:{from:p.position,to:p.position+p.recordSize},responseType:\"array-buffer\",signal:o.signal});if(!d)return null;const[u,h]=this._getTileSize(a);return this.decodePixelBlock(d.data,{width:u,height:h,planes:null,pixelType:null,returnInterleaved:a})}_validateHeader(e){const t=[\"origin\",\"extent\",\"geodataXform\",\"LODInfos\",\"blockWidth\",\"blockHeight\",\"bandCount\",\"pixelType\",\"pixelSizeX\",\"pixelSizeY\",\"format\",\"packetSize\"];return e&&\"RasterInfo\"===e.type&&!t.some((t=>!e[t]))}_parseHeader(e){const t=[\"u1\",\"u2\",\"u4\",\"u8\",\"s8\",\"u16\",\"s16\",\"u32\",\"s32\",\"f32\",\"f64\"][e.pixelType],{bandCount:r,histograms:o,colormap:i,blockWidth:s,blockHeight:m,firstPyramidLevel:c,maximumPyramidLevel:p}=e,d=e.statistics&&e.statistics.map((e=>({min:e.min,max:e.max,avg:e.mean,stddev:e.standardDeviation,median:e.median,mode:e.mode}))),u=e.extent.spatialReference,x=e.geodataXform?.spatialReference,I=new h(u?.wkid||u?.wkt?u:x);let w=new g({xmin:e.extent.xmin,ymin:e.extent.ymin,xmax:e.extent.xmax,ymax:e.extent.ymax,spatialReference:I});const _=new y({x:e.pixelSizeX,y:e.pixelSizeY,spatialReference:I}),v=Math.round((w.xmax-w.xmin)/_.x),b=Math.round((w.ymax-w.ymin)/_.y),z=this._parseTransform(e.geodataXform),T=z?w:null;z&&(w=z.forwardTransform(w),_.x=(w.xmax-w.xmin)/v,_.y=(w.ymax-w.ymin)/b);const k=e.properties??{},j=e.format.toLowerCase().replace(\"cache/\",\"\"),C=new y(e.origin.x,e.origin.y,I);let R,F,P,H;if(i&&i.colors)for(R=[],F=0;F<i.colors.length;F++)P=i.colors[F],H=i.values?i.values[F]:F,R.push([H,255&P,P<<16>>>24,P<<8>>>24,P>>>24]);const D=e.LODInfos,L=[];for(F=0;F<D.levels.length;F++)L.push(new a({level:D.levels[F],resolution:D.resolutions[F],scale:96/.0254*D.resolutions[F]}));const M=new f({dpi:96,lods:L,format:j,origin:C,size:[s,m],spatialReference:I}),O={recordSize:S,packetSize:e.packetSize,headerSize:e.packetSize*e.packetSize*S+64},B=[{maxCol:Math.ceil(v/s)-1,maxRow:Math.ceil(b/m)-1,minCol:0,minRow:0}];let $=2;if(p>0)for(F=0;F<p;F++)B.push({maxCol:Math.ceil(v/$/s)-1,maxRow:Math.ceil(b/$/m)-1,minCol:0,minRow:0}),$*=2;const N=e.mdInfo;let q=null;if(N&&k._yxs){const e=k._yxs;q={packetSize:e.PacketSize,tileSize:[e.TileXSize,e.TileYSize]}}return{storageInfo:O,rasterInfo:new n({width:v,height:b,pixelType:t,bandCount:r,extent:w,nativeExtent:T,transform:z,spatialReference:I,pixelSize:_,keyProperties:k,statistics:d,histograms:o,multidimensionalInfo:N,colormap:R,storageInfo:new l({blockWidth:s,blockHeight:m,pyramidBlockWidth:s,pyramidBlockHeight:m,origin:C,tileInfo:M,transposeInfo:q,firstPyramidLevel:c,maximumPyramidLevel:p,blockBoundary:B})})}}_parseTransform(e){if(!p(e))throw new t(\"cloudraster:open\",\"the data contains unsupported geodata transform types\");const r=d(e);if(\"identity\"===r.type)return null;if(\"polynomial\"!==r.type||!r.forwardCoefficients?.length||!r.inverseCoefficients?.length)throw new t(\"cloudraster:open\",\"the data contains unsupported geodata transforms - both forward and inverse coefficients are required currently\");return r}async _fetchAuxiliaryInformation(e){const t=this.request(this.url+\"/conf.vat.json\",{signal:e}).then((e=>e.data)).catch((()=>null)),r=this.request(this.url+\"/conf.vat.dbf\",{responseType:\"array-buffer\",signal:e}).then((e=>e.data)).catch((()=>null)),o=await Promise.all([t,r]);let i;if(o[0]){let e=o[0].fields;const t=o[0].values;if(e&&t){e=e.map((e=>({type:\"OID\"===e.name?\"esriFieldTypeOID\":x.get(e.type),name:e.name,alias:e.alias||e.name})));const r=t.map((e=>({attributes:e})));e&&t&&(i={fields:e,features:r})}}if(!i&&o[1]){i=c.parse(o[1]).recordSet}return u.fromJSON(i)}_buildCacheFilePath(e,t,o,i,s){const a=this._getPackageSize(!!s),n=Math.floor(t/a)*a,l=Math.floor(o/a)*a,f=\"R\"+this._toHexString4(n)+\"C\"+this._toHexString4(l);let m=\"L\";m+=e>=10?e.toString():\"0\"+e.toString();const{multidimensionalInfo:c}=this.rasterInfo,p=i?.[0];if(r(c)||!p)return`${this.url}/_alllayers/${m}/${f}.bundle`;let d=\"_yxs\";if(!s){d=c.variables.find((e=>e.name===p.variableName)).dimensions[0].values.indexOf(p.values[0]).toString(16);const e=4-d.length;for(let t=0;t<e;t++)d=\"0\"+d;d=\"S\"+d}const u=this._getVariableFolderName(s||p.variableName);return`${this.url}/_alllayers/${u}/${d}/${m}/${f}.bundle`}_getPackageSize(e=!1){const{transposeInfo:t}=this.rasterInfo.storageInfo;return e&&o(t)?t.packetSize??0:this.storageInfo.packetSize}_getTileSize(e=!1){const{storageInfo:t}=this.rasterInfo,{transposeInfo:r}=t;return e&&o(r)?r.tileSize:t.tileInfo.size}_getVariableFolderName(e){return\"\"===(e=e.trim())?\"_v\":e.replace(/[\\{|\\}\\-]/g,\"_\").replace(\"\\\\*\",\"_v\")}_getIndexRecordFromBundle(e,t,r=!1){const o=this._getPackageSize(r),i=o*(e%o)+t%o;if(i<0)throw new Error(\"Invalid level / row / col\");return 20+i*this.storageInfo.recordSize+44}_getTileEndAndContentType(e,t){const r=e.subarray(t,t+8);let o,i=0;for(o=0;o<5;o++)i|=(255&r[o])<<8*o;const s=0xffffffffff&i;for(i=0,o=5;o<8;o++)i|=(255&r[o])<<8*(o-5);return{position:s,recordSize:0xffffffffff&i}}_toHexString4(e){let t=e.toString(16);if(4!==t.length){let e=4-t.length;for(;e-- >0;)t=\"0\"+t}return t}};e([i({readOnly:!0})],I.prototype,\"storageInfo\",void 0),e([i({type:String,json:{write:!0}})],I.prototype,\"datasetFormat\",void 0),I=e([s(\"esri.layers.support.rasterDatasets.CloudRaster\")],I);const w=I;export{w as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import\"../../../geometry.js\";import e from\"../../../core/Error.js\";import{isSome as s}from\"../../../core/maybe.js\";import{eachAlways as r}from\"../../../core/promiseUtils.js\";import{property as i}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as o}from\"../../../core/accessorSupport/decorators/subclass.js\";import a from\"../RasterInfo.js\";import m from\"./BaseRaster.js\";import{split as n}from\"../rasterFunctions/pixelUtils.js\";import{estimateStatisticsHistograms as l}from\"../rasterFunctions/stretchUtils.js\";import p from\"../../../geometry/Extent.js\";import c from\"../../../geometry/SpatialReference.js\";let h=class extends m{constructor(){super(...arguments),this.datasetFormat=\"MEMORY\",this.data=null}async open(t){await this.init();const e=this.data,{pixelBlock:s,statistics:r,histograms:i,name:o,keyProperties:m,nativeExtent:n,transform:l}=this.data,{width:h,height:f,pixelType:d}=s,u=e.extent??new p({xmin:-.5,ymin:.5,xmax:h-.5,ymax:f-.5,spatialReference:new c({wkid:3857})}),y=e.isPseudoSpatialReference??!e.extent,x={x:u.width/h,y:u.height/f},g=new a({width:h,height:f,pixelType:d,extent:u,nativeExtent:n,transform:l,pixelSize:x,spatialReference:u.spatialReference,bandCount:s.pixels.length,keyProperties:m||{},statistics:r,isPseudoSpatialReference:y,histograms:i});this.createRemoteDatasetStorageInfo(g,512,512),this._set(\"rasterInfo\",g),this.updateTileInfo(),await this._buildInMemoryRaster(s,{width:512,height:512},t),this.datasetName=o,this.url=\"/InMemory/\"+o}fetchRawTile(t,e,s,r={}){const i=this._pixelBlockTiles.get(`${t}/${e}/${s}`);return Promise.resolve(i)}async _buildInMemoryRaster(t,i,o){const a=this.rasterInfo.storageInfo.maximumPyramidLevel,m=this.rasterJobHandler?this.rasterJobHandler.split({pixelBlock:t,tileSize:i,maximumPyramidLevel:a},o):Promise.resolve(n(t,i,a)),p=s(this.rasterInfo.statistics),c=s(this.rasterInfo.histograms),h=p?Promise.resolve({statistics:null,histograms:null}):this.rasterJobHandler?this.rasterJobHandler.estimateStatisticsHistograms({pixelBlock:t},o):Promise.resolve(l(t)),f=await r([m,h]);if(!f[0].value&&f[1].value)throw new e(\"inmemory-raster:open\",\"failed to build in memory raster\");this._pixelBlockTiles=f[0].value,p||(this.rasterInfo.statistics=f[1].value?.statistics),c||(this.rasterInfo.histograms=f[1].value?.histograms)}};t([i({type:String,json:{write:!0}})],h.prototype,\"datasetFormat\",void 0),t([i()],h.prototype,\"data\",void 0),h=t([o(\"esri.layers.support.rasterDatasets.InMemoryRaster\")],h);const f=h;export{f as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nfunction n(e,t){if(!e||!t)return[];let l=t;t.includes(\"/\")?(l=t.slice(0,t.indexOf(\"/\")),t=t.slice(t.indexOf(\"/\")+1)):t=\"\";const r=[];if(t){const u=n(e,l);for(let e=0;e<u.length;e++){n(u[e],t).forEach((n=>r.push(n)))}return r}const u=e.getElementsByTagNameNS(\"*\",l);if(!u||0===u.length)return[];for(let n=0;n<u.length;n++)r.push(u[n]||u.item[n]);return r}function e(t,l){if(!t||!l)return null;let r=l;l.includes(\"/\")?(r=l.slice(0,l.indexOf(\"/\")),l=l.slice(l.indexOf(\"/\")+1)):l=\"\";const u=n(t,r);return u.length>0?l?e(u[0],l):u[0]:null}function t(n,t=null){const l=t?e(n,t):n;let r;return l?(r=l.textContent||l.nodeValue,r?r.trim():null):null}function l(e,t){const l=n(e,t),r=[];let u;for(let n=0;n<l.length;n++)u=l[n].textContent||l[n].nodeValue,u&&(u=u.trim(),\"\"!==u&&r.push(u));return r}function r(n,e=null){return t(n,e)?.split(\" \").map((n=>Number(n)))??[]}function u(n,e){return l(n,e).map((n=>Number(n)))}function o(n,e){const l=t(n,e);return Number(l)}function i(n,e){const t=n?.nodeName?.toLowerCase(),l=e.toLowerCase();return t.slice(t.lastIndexOf(\":\")+1)===l}function c(n){return n.nodeName.slice(n.nodeName.lastIndexOf(\":\")+1)}export{e as getElement,o as getElementNumericValue,u as getElementNumericValues,t as getElementValue,l as getElementValues,n as getElements,c as getNodeNameIgnoreNS,r as getSpaceDelimitedNumericValues,i as isSameTagIgnoreNS};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../geometry.js\";import{isSome as e}from\"../../../core/maybe.js\";import{isSameTagIgnoreNS as t,getElementValue as n,getElements as r,getElement as s,getElementNumericValue as a,getElementNumericValues as i}from\"./xmlUtilities.js\";import l from\"../rasterTransforms/PolynomialTransform.js\";import o from\"../../../geometry/SpatialReference.js\";function f(e,t){if(!e||!t)return null;const n=[];for(let r=0;r<e.length;r++)n.push(e[r]),n.push(t[r]);return n}function u(e){const t=s(e,\"GeodataXform\"),r=m(a(t,\"SpatialReference/WKID\")||n(t,\"SpatialReference/WKT\"));if(\"typens:PolynomialXform\"!==t.getAttribute(\"xsi:type\"))return{spatialReference:r,transform:null};const o=a(t,\"PolynomialOrder\")??1,u=i(t,\"CoeffX/Double\"),c=i(t,\"CoeffY/Double\"),d=i(t,\"InverseCoeffX/Double\"),p=i(t,\"InverseCoeffY/Double\"),S=f(u,c),C=f(d,p);return{spatialReference:r,transform:S&&C&&S.length&&C.length?new l({spatialReference:r,polynomialOrder:o,forwardCoefficients:S,inverseCoefficients:C}):null}}function c(e){const t=a(e,\"NoDataValue\"),i=s(e,\"Histograms/HistItem\"),l=a(i,\"HistMin\"),o=a(i,\"HistMax\"),f=a(i,\"BucketCount\"),u=n(i,\"HistCounts\")?.split(\"|\").map((e=>Number(e)));let c,m,d,p;r(e,\"Metadata/MDI\").forEach((e=>{const t=Number(e.textContent??e.nodeValue);switch(e.getAttribute(\"key\").toUpperCase()){case\"STATISTICS_MINIMUM\":c=t;break;case\"STATISTICS_MAXIMUM\":m=t;break;case\"STATISTICS_MEAN\":d=t;break;case\"STATISTICS_STDDEV\":p=t}}));const S=a(e,\"Metadata/SourceBandIndex\");return{noDataValue:t,histogram:u?.length&&null!=l&&null!=o?{min:l,max:o,size:f||u.length,counts:u}:null,sourceBandIndex:S,statistics:null!=c&&null!=m?{min:c,max:m,avg:d,stddev:p}:null}}function m(e){if(!e)return null;let t=Number(e);if(!isNaN(t)&&0!==t)return new o({wkid:t});if((e=String(e)).startsWith(\"COMPD_CS\")){if(!e.includes(\"VERTCS\")||!e.includes(\"GEOGCS\")&&!e.startsWith(\"PROJCS\"))return null;const n=e.indexOf(\"VERTCS\"),r=e.indexOf(\"PROJCS\"),s=r>-1?r:e.indexOf(\"GEOGCS\");if(-1===s)return null;const a=e.slice(s,e.lastIndexOf(\"]\",n)+1).trim(),i=e.slice(n,e.lastIndexOf(\"]\")).trim();t=d(a);const l=new o(t?{wkid:t}:{wkt:a}),f=d(i);return f&&(l.vcsWkid=f),l}return e.startsWith(\"GEOGCS\")||e.startsWith(\"PROJCS\")?(t=d(e),new o(0!==t?{wkid:t}:{wkt:e})):null}function d(e){const t=e.replace(/\\]/g,\"[\").replace(/\\\"/g,\"\").split(\"[\").map((e=>e.trim())).filter((e=>\"\"!==e)),n=t[t.length-1].split(\",\"),r=n[0]?.toLowerCase();if((\"epsg\"===r||\"esri\"===r)&&e.endsWith('\"]]')){const e=Number(n[1]);if(!isNaN(e)&&0!==e)return e}return 0}function p(s){if(\"pamdataset\"!==s?.documentElement.tagName?.toLowerCase())return{};const a={spatialReference:null,transform:null,metadata:{},rasterBands:[],statistics:null,histograms:null};s.documentElement.childNodes.forEach((e=>{if(1===e.nodeType)if(t(e,\"SRS\")){if(!a.spatialReference){const t=n(e);a.spatialReference=m(t)}}else if(t(e,\"Metadata\"))if(\"xml:ESRI\"===e.getAttribute(\"domain\")){const{spatialReference:t,transform:n}=u(e);a.transform=n,a.spatialReference||(a.spatialReference=t)}else{r(e,\"MDI\").forEach((e=>a.metadata[e.getAttribute(\"key\")]=n(e)))}else if(t(e,\"PAMRasterBand\")){const t=c(e);null!=t.sourceBandIndex&&null==a.rasterBands[t.sourceBandIndex]?a.rasterBands[t.sourceBandIndex]=t:a.rasterBands.push(t)}}));const i=a.rasterBands;if(i.length){const t=!!i[0].statistics;a.statistics=t?i.map((e=>e.statistics)).filter(e):null;const n=!!i[0].histogram;a.histograms=n?i.map((e=>e.histogram)).filter(e):null}return a}export{p as parsePAMInfo,m as parseSpatialReference};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import\"../../../geometry.js\";import e from\"../../../core/Error.js\";import has from\"../../../core/has.js\";import{unwrap as s}from\"../../../core/maybe.js\";import{eachAlways as r,createAbortError as a}from\"../../../core/promiseUtils.js\";import{property as o}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as i}from\"../../../core/accessorSupport/decorators/subclass.js\";import n from\"./BaseRaster.js\";import m from\"./InMemoryRaster.js\";import{parsePAMInfo as l}from\"./pamParser.js\";import{getFormat as p}from\"../rasterFormats/RasterCodec.js\";import{estimateStatisticsFromHistograms as c}from\"../rasterFunctions/stretchUtils.js\";import f from\"../rasterTransforms/PolynomialTransform.js\";import u from\"../../../geometry/SpatialReference.js\";import h from\"../../../geometry/Extent.js\";let d=class extends n{async open(t){await this.init();const e=await this._fetchData(t);let{spatialReference:s,statistics:r,histograms:a,transform:o}=await this._fetchAuxiliaryData(t);const i=!s;i&&(s=new u({wkid:3857})),a?.length&&null==r&&(r=c(a));const{width:n,height:l}=e;let p=new h({xmin:-.5,ymin:.5-l,xmax:n-.5,ymax:.5,spatialReference:s});const f=o?o.forwardTransform(p):p;let d=!0;if(o){const t=o.forwardCoefficients;d=t&&0===t[1]&&0===t[2],d&&(o=null,p=f)}const w=new m({data:{extent:f,nativeExtent:p,transform:o,pixelBlock:e,statistics:r,histograms:a,keyProperties:{DateType:\"Processed\"},isPseudoSpatialReference:i}});await w.open(),w.data=null,this._set(\"rasterInfo\",w.rasterInfo),this._inMemoryRaster=w}fetchRawTile(t,e,s,r={}){return this._inMemoryRaster.fetchRawTile(t,e,s,r)}async _fetchData(t){const{data:s}=await this.request(this.url,{responseType:\"array-buffer\",signal:t?.signal}),r=p(s).toUpperCase();if(\"JPG\"!==r&&\"PNG\"!==r&&\"GIF\"!==r&&\"BMP\"!==r)throw new e(\"image-aux-raster:open\",\"the data is not a supported format\");this._set(\"datasetFormat\",r);const a=r.toLowerCase(),o=\"gif\"===a||\"bmp\"===a||!has(\"ios\"),i=await this.decodePixelBlock(s,{format:a,useCanvas:o,hasNoZlibMask:!0});if(null==i)throw new e(\"image-aux-raster:open\",\"the data cannot be decoded\");return i}async _fetchAuxiliaryData(t){const e=s(t?.signal),o=this.ioConfig.skipExtensions??[],i=o.includes(\"aux.xml\")?null:this.request(this.url+\".aux.xml\",{responseType:\"xml\",signal:e}),n=this.datasetFormat,m=\"JPG\"===n?\"jgw\":\"PNG\"===n?\"pgw\":\"BMP\"===n?\"bpw\":null,p=m&&o.includes(m)?null:this.request(this.url.slice(0,this.url.lastIndexOf(\".\"))+\".\"+m,{responseType:\"text\",signal:e}),c=await r([i,p]);if(e?.aborted)throw a();const u=l(c[0].value?.data);if(!u.transform){const t=c[1].value?c[1].value.data.split(\"\\n\").slice(0,6).map((t=>Number(t))):null;u.transform=6===t?.length?new f({forwardCoefficients:[t[4],t[5],t[0],-t[1],t[2],-t[3]]}):null}return u}};t([o({type:String,json:{write:!0}})],d.prototype,\"datasetFormat\",void 0),d=t([i(\"esri.layers.support.rasterDatasets.ImageAuxRaster\")],d);const w=d;export{w as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import\"../../../geometry.js\";import t from\"../../../core/Error.js\";import{isNone as i,assertIsSome as s,isSome as r,unwrap as a}from\"../../../core/maybe.js\";import{urlToObject as l}from\"../../../core/urlUtils.js\";import{property as o}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as n}from\"../../../core/accessorSupport/decorators/subclass.js\";import c from\"../RasterInfo.js\";import m from\"../RasterStorageInfo.js\";import{readServiceTileInfo as h}from\"../serviceTileInfoProperty.js\";import u from\"../TileInfo.js\";import{TilemapCache as f}from\"../TilemapCache.js\";import p from\"./BaseRaster.js\";import{setValidBoundary as y}from\"../rasterFunctions/pixelUtils.js\";import d from\"../rasterTransforms/GCSShiftTransform.js\";import{fetchServiceRasterInfo as x}from\"../../../rest/imageService/fetchRasterInfo.js\";import g from\"../../../geometry/SpatialReference.js\";import v from\"../../../geometry/Point.js\";import S from\"../../../geometry/Extent.js\";let I=class extends p{constructor(){super(...arguments),this._levelOffset=0,this._tilemapCache=null,this._slices=null,this.datasetFormat=\"RasterTileServer\",this.tileType=null}async open(e){await this.init();const r=e&&e.signal,a=this.sourceJSON?{data:this.sourceJSON}:await this.request(this.url,{query:{f:\"json\"},signal:r});a.ssl&&(this.url=this.url.replace(/^http:/i,\"https:\"));const o=a.data;if(this.sourceJSON=o,!o)throw new t(\"imageserverraster:open\",\"cannot initialize tiled image service, missing service info\");if(!o.tileInfo)throw new t(\"imageserverraster:open\",\"use ImageryLayer to open non-tiled image services\");this._fixScaleInServiceInfo();const n=[\"jpg\",\"jpeg\",\"png\",\"png8\",\"png24\",\"png32\",\"mixed\"];this.tileType=o.cacheType,null==this.tileType&&(n.includes(o.tileInfo.format.toLowerCase())?this.tileType=\"Map\":\"lerc\"===o.tileInfo.format.toLowerCase()?this.tileType=\"Elevation\":this.tileType=\"Raster\"),this.datasetName=o.name?.slice(o.name.indexOf(\"/\")+1)??\"\";const c=await this._fetchRasterInfo({signal:r});if(i(c))throw new t(\"image-server-raster:open\",\"cannot initialize image service\");const p=\"Map\"===this.tileType?h(o.tileInfo,o):u.fromJSON(o.tileInfo);s(p);const[y,d]=this._computeMinMaxLOD(c,p),{extent:x,pixelSize:g}=c,v=.5/c.width*g.x,S=Math.max(g.x,g.y),{lods:I}=p;(\"Map\"!==this.tileType&&0!==o.maxScale||Math.abs(g.x-g.y)>v||!I.some((e=>Math.abs(e.resolution-S)<v)))&&(g.x=g.y=y.resolution,c.width=Math.ceil((x.xmax-x.xmin)/g.x-.1),c.height=Math.ceil((x.ymax-x.ymin)/g.y-.1));const w=y.level-d.level,[j,T]=p.size,b=[],M=[];I.forEach(((e,t)=>{e.level>=d.level&&e.level<=y.level&&b.push({x:e.resolution,y:e.resolution}),t<I.length-1&&M.push(Math.round(10*e.resolution/I[t+1].resolution)/10)})),b.sort(((e,t)=>e.x-t.x));const _=this.computeBlockBoundary(x,j,T,p.origin,b,w),R=b.length>1?b.slice(1):null;let z;o.transposeInfo&&(z={tileSize:[o.transposeInfo.rows,o.transposeInfo.cols],packetSize:c.keyProperties?._yxs.PacketSize??0});const P=M.length<=1||M.length>=3&&M.slice(0,M.length-1).every((e=>e===M[0]))?M[0]??2:Math.round(10/(d.resolution/y.resolution)**(-1/w))/10;if(c.storageInfo=new m({blockWidth:p.size[0],blockHeight:p.size[1],pyramidBlockWidth:p.size[0],pyramidBlockHeight:p.size[1],pyramidResolutions:R,pyramidScalingFactor:P,compression:p.format,origin:p.origin,firstPyramidLevel:1,maximumPyramidLevel:w,tileInfo:p,transposeInfo:z,blockBoundary:_}),this._fixGCSShift(c),this._set(\"rasterInfo\",c),o.capabilities.toLowerCase().includes(\"tilemap\")){const e={tileInfo:c.storageInfo.tileInfo,parsedUrl:l(this.url),url:this.url,tileServers:[],type:\"tile\"};this._tilemapCache=new f({layer:e})}}async fetchRawTile(e,t,i,s={}){const{storageInfo:l,extent:o}=this.rasterInfo,{transposeInfo:n}=l,c=r(n)&&!!s.transposedVariableName;if(this._slices&&!c&&null==s.sliceId)return null;const m=c?0:l.maximumPyramidLevel-e+this._levelOffset,h=`${this.url}/tile/${m}/${t}/${i}`,u=this._slices?c?{variable:s.transposedVariableName}:{sliceId:s.sliceId||0}:null,{data:f}=await this.request(h,{query:u,responseType:\"array-buffer\",signal:s.signal});if(!f)return null;const p=c?n.tileSize:l.tileInfo.size,d=await this.decodePixelBlock(f,{width:p[0],height:p[1],planes:null,pixelType:null,isPoint:\"Elevation\"===this.tileType,returnInterleaved:c,noDataValue:a(this.rasterInfo.noDataValue)});if(null==d)return null;const x=l.blockBoundary[e];if(\"jpg\"!==l.compression||i>x.minCol&&i<x.maxCol&&t>x.minRow&&t<x.maxRow)return d;const{origin:g,blockWidth:v,blockHeight:S}=l,{x:I,y:w}=this.getPyramidPixelSize(e),j=Math.round((o.xmin-g.x)/I)%v,T=Math.round((o.xmax-g.x)/I)%v||v,b=Math.round((g.y-o.ymax)/w)%S,M=Math.round((g.y-o.ymin)/w)%S||S,_=i===x.minCol?j:0,R=t===x.minRow?b:0,z=i===x.maxCol?T:v,P=t===x.maxRow?M:S;return y(d,{x:_,y:R},{width:z-_,height:P-R}),d}getSliceIndex(e){if(!this._slices||i(e)||0===e.length)return null;const t=e;for(let i=0;i<this._slices.length;i++){const e=this._slices[i].multidimensionalDefinition;if(e.length===t.length&&!e.some((e=>{const i=t.find((t=>e.variableName===t.variableName&&t.dimensionName===e.dimensionName));if(!i)return!0;return(Array.isArray(e.values[0])?`${e.values[0][0]}-${e.values[0][1]}`:e.values[0])!==(Array.isArray(i.values[0])?`${i.values[0][0]}-${i.values[0][1]}`:i.values[0])})))return i}return null}async fetchVariableStatisticsHistograms(e,t){const i=this.request(this.url+\"/statistics\",{query:{variable:e,f:\"json\"},signal:t}).then((e=>e.data?.statistics)),s=this.request(this.url+\"/histograms\",{query:{variable:e,f:\"json\"},signal:t}).then((e=>e.data?.histograms)),r=await Promise.all([i,s]);return r[0]&&r[0].forEach((e=>{e.avg=e.mean,e.stddev=e.standardDeviation})),{statistics:r[0]||null,histograms:r[1]||null}}async computeBestPyramidLevelForLocation(e,t={}){if(!this._tilemapCache)return 0;let i=this.identifyPixelLocation(e,0,a(t.datumTransformation));if(null===i)return null;let s=0;const{maximumPyramidLevel:r}=this.rasterInfo.storageInfo;let l=r-s+this._levelOffset;const o=i.srcLocation;for(;l>=0;){try{if(\"available\"===await this._tilemapCache.fetchAvailability(l,i.row,i.col,t))break}catch{}if(l--,s++,i=this.identifyPixelLocation(o,s,a(t.datumTransformation)),null===i)return null}return-1===l||null==i?null:s}async _fetchRasterInfo(e){const t=this.sourceJSON;if(\"Map\"===this.tileType){const e=t.fullExtent||t.extent,i=Math.ceil((e.xmax-e.xmin)/t.pixelSizeX-.1),s=Math.ceil((e.ymax-e.ymin)/t.pixelSizeY-.1),r=g.fromJSON(t.spatialReference||e.spatialReference),a=new v({x:t.pixelSizeX,y:t.pixelSizeY,spatialReference:r});return new c({width:i,height:s,bandCount:3,extent:S.fromJSON(e),spatialReference:r,pixelSize:a,pixelType:\"u8\",statistics:null,keyProperties:{DataType:\"processed\"}})}const{signal:i}=e,s=x(this.url,this.sourceJSON,{signal:i,query:this.ioConfig.customFetchParameters}),r=t.hasMultidimensions?this.request(`${this.url}/slices`,{query:{f:\"json\"},signal:i}).then((e=>e.data&&e.data.slices)).catch((()=>null)):null,a=await Promise.all([s,r]);return this._slices=a[1],a[0]}_fixScaleInServiceInfo(){const{sourceJSON:e}=this;e.minScale&&e.minScale<0&&(e.minScale=0),e.maxScale&&e.maxScale<0&&(e.maxScale=0)}_fixGCSShift(e){const{extent:t,spatialReference:i}=e;t.xmin>-1&&t.xmax>181&&i?.wkid&&i.isGeographic&&(e.nativeExtent=e.extent,e.transform=new d,e.extent=e.transform.forwardTransform(t))}_computeMinMaxLOD(e,t){const{pixelSize:i}=e,s=.5/e.width*i.x,{lods:r}=t,a=t.lodAt(Math.max.apply(null,r.map((e=>e.level)))),l=t.lodAt(Math.min.apply(null,r.map((e=>e.level)))),{tileType:o}=this;if(\"Map\"===o)return this._levelOffset=r[0].level,[a,l];if(\"Raster\"===o){return[r.find((e=>e.resolution===i.x))??a,l]}const{minScale:n,maxScale:c}=this.sourceJSON;let m=a;c>0&&(m=r.find((e=>Math.abs(e.scale-c)<s)),m||(m=r.filter((e=>e.scale>c)).sort(((e,t)=>e.scale>t.scale?1:-1))[0]??a));let h=l;return n>0&&(h=r.find((e=>Math.abs(e.scale-n)<s))??l,this._levelOffset=h.level-l.level),[m,h]}};e([o({type:String,json:{write:!0}})],I.prototype,\"datasetFormat\",void 0),e([o()],I.prototype,\"tileType\",void 0),I=e([n(\"esri.layers.support.rasterDatasets.ImageServerRaster\")],I);const w=I;export{w as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import\"../../../geometry.js\";import e from\"../../../core/Error.js\";import{unwrap as r,isNone as s,isSome as o}from\"../../../core/maybe.js\";import{property as i}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as a}from\"../../../core/accessorSupport/decorators/subclass.js\";import n from\"../PixelBlock.js\";import l from\"../RasterInfo.js\";import f from\"../RasterStorageInfo.js\";import c from\"./BaseRaster.js\";import{parseSpatialReference as m,parsePAMInfo as p}from\"./pamParser.js\";import{getElement as h,getElementValue as u}from\"./xmlUtilities.js\";import{isPlatformLittleEndian as g}from\"../rasterFormats/utils.js\";import{estimateStatisticsFromHistograms as d}from\"../rasterFunctions/stretchUtils.js\";import y from\"../../../geometry/SpatialReference.js\";import x from\"../../../geometry/Extent.js\";import w from\"../../../geometry/Point.js\";const I=new Map;I.set(\"Int8\",\"s8\"),I.set(\"UInt8\",\"u8\"),I.set(\"Int16\",\"s16\"),I.set(\"UInt16\",\"u16\"),I.set(\"Int32\",\"s32\"),I.set(\"UInt32\",\"u32\"),I.set(\"Float32\",\"f32\"),I.set(\"Float64\",\"f32\"),I.set(\"Double64\",\"f32\");const b=new Map;b.set(\"none\",{blobExtension:\".til\",isOneSegment:!0,decoderFormat:\"bip\"}),b.set(\"lerc\",{blobExtension:\".lrc\",isOneSegment:!1,decoderFormat:\"lerc\"}),b.set(\"deflate\",{blobExtension:\".pzp\",isOneSegment:!0,decoderFormat:\"deflate\"}),b.set(\"jpeg\",{blobExtension:\".pjg\",isOneSegment:!0,decoderFormat:\"jpg\"});let A=class extends c{constructor(){super(...arguments),this._files=null,this._storageIndex=null,this.datasetFormat=\"MRF\"}async open(t){await this.init(),this.datasetName=this.url.slice(this.url.lastIndexOf(\"/\")+1);const e=t?r(t.signal):null,o=await this.request(this.url,{responseType:\"xml\",signal:e}),{rasterInfo:i,files:a}=this._parseHeader(o.data);if(-1===this.ioConfig.skipExtensions?.indexOf(\"aux.xml\")){const e=await this._fetchAuxiliaryData(t);null!=e&&(i.statistics=e.statistics??i.statistics,i.histograms=e.histograms,e.histograms&&s(i.statistics)&&(i.statistics=d(e.histograms)))}this._set(\"rasterInfo\",i),this._files=a;const n=await this.request(a.index,{responseType:\"array-buffer\",signal:e});this._storageIndex=this._parseIndex(n.data);const{blockWidth:l,blockHeight:f}=this.rasterInfo.storageInfo,c=this.rasterInfo.storageInfo.pyramidScalingFactor,{width:m,height:p}=this.rasterInfo,h=[],u=this._getBandSegmentCount();let g=0,y=-1;for(;g<this._storageIndex.length;){y++;const t=Math.ceil(m/l/c**y)-1,e=Math.ceil(p/f/c**y)-1;g+=(t+1)*(e+1)*u*4,h.push({maxRow:e,maxCol:t,minCol:0,minRow:0})}this.rasterInfo.storageInfo.blockBoundary=h,y>0&&(this.rasterInfo.storageInfo.firstPyramidLevel=1,this.rasterInfo.storageInfo.maximumPyramidLevel=y),this.updateTileInfo()}async fetchRawTile(t,e,r,s={}){const{blockWidth:i,blockHeight:a,blockBoundary:l}=this.rasterInfo.storageInfo,f=l[t];if(!f||f.maxRow<e||f.maxCol<r||f.minRow>e||f.minCol>r)return null;const{bandCount:c,pixelType:m}=this.rasterInfo,{ranges:p,actualTileWidth:h,actualTileHeight:u}=this._getTileLocation(t,e,r);if(!p||0===p.length)return null;if(0===p[0].from&&0===p[0].to){const t=new Uint8Array(i*a);return new n({width:i,height:a,pixels:null,mask:t,validPixelCount:0})}const{bandIds:g}=this.ioConfig,d=this._getBandSegmentCount(),y=[];let x=0;for(x=0;x<d;x++)(!g||g.indexOf[x]>-1)&&y.push(this.request(this._files.data,{range:{from:p[x].from,to:p[x].to},responseType:\"array-buffer\",signal:s.signal}));const w=await Promise.all(y),I=w.map((t=>t.data.byteLength)).reduce(((t,e)=>t+e)),A=new Uint8Array(I);let F=0;for(x=0;x<d;x++)A.set(new Uint8Array(w[x].data),F),F+=w[x].data.byteLength;const _=b.get(this.rasterInfo.storageInfo.compression).decoderFormat,R=await this.decodePixelBlock(A.buffer,{width:i,height:a,format:_,planes:g?.length||c,pixelType:m});if(null==R)return null;if(o(this.rasterInfo.noDataValue)&&\"lerc\"!==_&&!R.mask){const t=this.rasterInfo.noDataValue[0];if(null!=t){const e=R.width*R.height,r=new Uint8Array(e);if(Math.abs(t)>1e24)for(x=0;x<e;x++)Math.abs((R.pixels[0][x]-t)/t)>1e-6&&(r[x]=1);else for(x=0;x<e;x++)R.pixels[0][x]!==t&&(r[x]=1);R.mask=r}}let S=0,j=0;if(h!==i||u!==a){let t=R.mask;if(t)for(x=0;x<a;x++)if(j=x*i,x<u)for(S=h;S<i;S++)t[j+S]=0;else for(S=0;S<i;S++)t[j+S]=0;else for(t=new Uint8Array(i*a),R.mask=t,x=0;x<u;x++)for(j=x*i,S=0;S<h;S++)t[j+S]=1}return R}_parseIndex(t){if(t.byteLength%16>0)throw new Error(\"invalid array buffer must be multiples of 16\");let e,r,s,o,i,a;if(g){for(r=new Uint8Array(t),o=new ArrayBuffer(t.byteLength),s=new Uint8Array(o),i=0;i<t.byteLength/4;i++)for(a=0;a<4;a++)s[4*i+a]=r[4*i+3-a];e=new Uint32Array(o)}else e=new Uint32Array(t);return e}_getBandSegmentCount(){return b.get(this.rasterInfo.storageInfo.compression).isOneSegment?1:this.rasterInfo.bandCount}_getTileLocation(t,e,r){const{blockWidth:s,blockHeight:o,pyramidScalingFactor:i}=this.rasterInfo.storageInfo,{width:a,height:n}=this.rasterInfo,l=this._getBandSegmentCount();let f,c,m,p=0,h=0;for(m=0;m<t;m++)h=i**m,f=Math.ceil(a/s/h),c=Math.ceil(n/o/h),p+=f*c;h=i**t,f=Math.ceil(a/s/h),c=Math.ceil(n/o/h),p+=e*f+r,p*=4*l;const u=this._storageIndex.subarray(p,p+4*l);let g=0,d=0;const y=[];for(let x=0;x<l;x++)g=u[4*x+0]*2**32+u[4*x+1],d=g+u[4*x+2]*2**32+u[4*x+3],y.push({from:g,to:d});return{ranges:y,actualTileWidth:r<f-1?s:Math.ceil(a/h)-s*(f-1),actualTileHeight:e<c-1?o:Math.ceil(n/h)-o*(c-1)}}_parseHeader(t){const r=h(t,\"MRF_META/Raster\");if(!r)throw new e(\"mrf:open\",\"not a valid MRF format\");const s=h(r,\"Size\"),o=parseInt(s.getAttribute(\"x\"),10),i=parseInt(s.getAttribute(\"y\"),10),a=parseInt(s.getAttribute(\"c\"),10),n=(u(r,\"Compression\")||\"none\").toLowerCase();if(!b.has(n))throw new e(\"mrf:open\",\"currently does not support compression \"+n);const c=u(r,\"DataType\")||\"UInt8\",p=I.get(c);if(null==p)throw new e(\"mrf:open\",\"currently does not support pixel type \"+c);const g=h(r,\"PageSize\"),d=parseInt(g.getAttribute(\"x\"),10),A=parseInt(g.getAttribute(\"y\"),10),F=h(r,\"DataValues\");let _,R;F&&(R=F.getAttribute(\"NoData\"),null!=R&&(_=R.trim().split(\" \").map((t=>parseFloat(t)))));if(h(t,\"MRF_META/CachedSource\"))throw new e(\"mrf:open\",\"currently does not support MRF referencing other data files\");const S=h(t,\"MRF_META/GeoTags\"),j=h(S,\"BoundingBox\");let k,M=!1;if(null!=j){const t=parseFloat(j.getAttribute(\"minx\")),e=parseFloat(j.getAttribute(\"miny\")),r=parseFloat(j.getAttribute(\"maxx\")),s=parseFloat(j.getAttribute(\"maxy\")),o=u(S,\"Projection\")||\"\";let i=y.WGS84;if(\"LOCAL_CS[]\"!==o)if(o.toLowerCase().startsWith(\"epsg:\")){const t=Number(o.slice(5));isNaN(t)||0===t||(i=new y({wkid:t}))}else i=m(o)??y.WGS84;else M=!0,i=new y({wkid:3857});k=new x(t,e,r,s),k.spatialReference=i}else M=!0,k=new x({xmin:-.5,ymin:.5-i,xmax:o-.5,ymax:.5,spatialReference:new y({wkid:3857})});const T=h(t,\"MRF_META/Rsets\"),C=parseInt(T&&T.getAttribute(\"scale\")||\"2\",10),U=k.spatialReference,B=new f({origin:new w({x:k.xmin,y:k.ymax,spatialReference:U}),blockWidth:d,blockHeight:A,pyramidBlockWidth:d,pyramidBlockHeight:A,compression:n,pyramidScalingFactor:C}),E=new w({x:k.width/o,y:k.height/i,spatialReference:U}),L=new l({width:o,height:i,extent:k,isPseudoSpatialReference:M,spatialReference:U,bandCount:a,pixelType:p,pixelSize:E,noDataValue:_,storageInfo:B}),P=u(t,\"datafile\"),O=u(t,\"IndexFile\");return{rasterInfo:L,files:{mrf:this.url,index:O||this.url.replace(\".mrf\",\".idx\"),data:P||this.url.replace(\".mrf\",b.get(n).blobExtension)}}}async _fetchAuxiliaryData(t){try{const{data:e}=await this.request(this.url+\".aux.xml\",{responseType:\"xml\",signal:t?.signal});return p(e)}catch{return null}}};t([i()],A.prototype,\"_files\",void 0),t([i()],A.prototype,\"_storageIndex\",void 0),t([i({type:String,json:{write:!0}})],A.prototype,\"datasetFormat\",void 0),A=t([a(\"esri.layers.support.rasterIO.MRFRaster\")],A);const F=A;export{F as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../../../chunks/tslib.es6.js\";import\"../../../geometry.js\";import t from\"../../../core/Error.js\";import{unwrap as r,isSome as i,isNone as s}from\"../../../core/maybe.js\";import{property as a}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as n}from\"../../../core/accessorSupport/decorators/subclass.js\";import o from\"../RasterInfo.js\";import f from\"../RasterStorageInfo.js\";import l from\"./BaseRaster.js\";import u from\"./DBFParser.js\";import{parseSpatialReference as c,parsePAMInfo as p}from\"./pamParser.js\";import{parseSignature as m,getPyramidIFDs as h,getMaskIFDs as d,getImageInfo as y,parseIFD as g,parseFieldValues as T,isBSQConfig as x}from\"../rasterFormats/TiffDecoder.js\";import I from\"../rasterFormats/TiffTags.js\";import{estimateStatisticsFromHistograms as w}from\"../rasterFunctions/stretchUtils.js\";import _ from\"../rasterTransforms/PolynomialTransform.js\";import b from\"../../../rest/support/FeatureSet.js\";import F from\"../../../geometry/SpatialReference.js\";import S from\"../../../geometry/Extent.js\";import R from\"../../../geometry/Point.js\";const E=(e,t)=>e.get(t)?.values,k=(e,t)=>e.get(t)?.values?.[0];let D=class extends l{constructor(){super(...arguments),this._files=null,this._headerInfo=null,this._bufferSize=1048576,this.datasetFormat=\"TIFF\"}async open(e){await this.init();const s=e?r(e.signal):null,{data:a}=await this.request(this.url,{range:{from:0,to:this._bufferSize},responseType:\"array-buffer\",signal:s});if(!a)throw new t(\"tiffraster:open\",\"failed to open url \"+this.url);this.datasetName=this.url.slice(this.url.lastIndexOf(\"/\")+1,this.url.lastIndexOf(\".\"));const{littleEndian:n,firstIFDPos:o,isBigTiff:f}=m(a),l=[];await this._readIFDs(l,a,n,o,0,f?8:4,s);const{imageInfo:u,rasterInfo:c}=this._parseIFDs(l),p=h(l),y=d(l);if(this._headerInfo={littleEndian:n,isBigTiff:f,ifds:l,pyramidIFDs:p,maskIFDs:y,...u},this._set(\"rasterInfo\",c),!u.isSupported)throw new t(\"tiffraster:open\",\"this tiff is not supported: \"+u.message);if(!u.tileWidth)throw new t(\"tiffraster:open\",\"none-tiled tiff is not optimized for access, convert to COG and retry.\");const{skipExtensions:g=[]}=this.ioConfig;if(!g.includes(\"aux.xml\")){const t=await this._fetchAuxiliaryMetaData(e);null!=t&&this._processPAMInfo(t,c)}g.includes(\"vat.dbf\")||1!==c.bandCount||\"u8\"!==c.pixelType||(c.attributeTable=await this._fetchAuxiliaryTable(e),i(c.attributeTable)&&(c.keyProperties.DataType=\"thematic\")),this.updateTileInfo()}async fetchRawTile(e,t,r,s={}){if(!this._headerInfo?.isSupported||this.isBlockOutside(e,t,r))return null;const a=await this._fetchRawTiffTile(e,t,r,!1,s);if(i(a)&&this._headerInfo.hasMaskBand){const n=await this._fetchRawTiffTile(e,t,r,!0,s);i(n)&&n.pixels[0]instanceof Uint8Array&&(a.mask=n.pixels[0])}return a}_parseIFDs(e){const t=y(e),{width:r,height:i,tileWidth:s,tileHeight:a,planes:n,pixelType:l,compression:u,firstPyramidLevel:p,maximumPyramidLevel:m,pyramidBlockWidth:h,pyramidBlockHeight:d,tileBoundary:g,affine:T,metadata:x}=t,I=t.extent.spatialReference?.wkt||t.extent.spatialReference?.wkid;let w=c(I),b=!!t.isPseudoGeographic;null==w&&(b=!0,w=new F({wkid:3857}));const D=new S({...t.extent,spatialReference:w}),v=new R(D?{x:D.xmin,y:D.ymax,spatialReference:w}:{x:0,y:0}),P=new f({blockWidth:s,blockHeight:a,pyramidBlockWidth:h,pyramidBlockHeight:d,compression:u,origin:v,firstPyramidLevel:p,maximumPyramidLevel:m,blockBoundary:g}),O=new R({x:(D.xmax-D.xmin)/r,y:(D.ymax-D.ymin)/i,spatialReference:w}),j=x?{BandProperties:x.bandProperties,DataType:x.dataType}:{};let B=null;const L=k(e[0],\"PHOTOMETRICINTERPRETATION\"),A=E(e[0],\"COLORMAP\");if(L<=3&&A?.length>3&&A.length%3==0){B=[];const e=A.length/3;for(let t=0;t<e;t++)B.push([t,A[t]>>>8,A[t+e]>>>8,A[t+2*e]>>>8])}const z=new o({width:r,height:i,bandCount:n,pixelType:l,pixelSize:O,storageInfo:P,spatialReference:w,isPseudoSpatialReference:b,keyProperties:j,extent:D,colormap:B,statistics:x?x.statistics:null});return T?.length&&(z.nativeExtent=new S({xmin:-.5,ymin:.5-i,xmax:r-.5,ymax:.5,spatialReference:w}),z.transform=new _({polynomialOrder:1,forwardCoefficients:[T[2]+T[0]/2,T[5]-T[3]/2,T[0],T[3],-T[1],-T[4]]}),z.extent=z.transform.forwardTransform(z.nativeExtent),z.pixelSize=new R({x:(D.xmax-D.xmin)/r,y:(D.ymax-D.ymin)/i,spatialReference:w}),P.origin.x=-.5,P.origin.y=.5),{imageInfo:t,rasterInfo:z}}_processPAMInfo(e,t){if(t.statistics=e.statistics??t.statistics,t.histograms=e.histograms,e.histograms&&s(t.statistics)&&(t.statistics=w(e.histograms)),e.transform&&s(t.transform)){t.transform=e.transform,t.nativeExtent=t.extent;const r=t.transform.forwardTransform(t.nativeExtent);t.pixelSize=new R({x:(r.xmax-r.xmin)/t.width,y:(r.ymax-r.ymin)/t.height,spatialReference:t.spatialReference}),t.extent=r}t.isPseudoSpatialReference&&e.spatialReference&&(t.spatialReference=e.spatialReference)}async _readIFDs(e,t,r,i,s,a=4,n){if(!i)return null;if(i>=t.byteLength||i<0){t=(await this.request(this.url,{range:{from:i+s,to:i+s+this._bufferSize},responseType:\"array-buffer\",signal:n})).data,s=i+s,i=0}const o=await this._readIFD(t,r,i,s,I.TIFF_TAGS,a,n);if(e.push(o.ifd),!o.nextIFD)return null;await this._readIFDs(e,t,r,o.nextIFD-s,s,a,n)}async _readIFD(e,t,r,s,a=I.TIFF_TAGS,n=4,o){if(!e)return null;const f=g(e,t,r,s,a,n);if(f.success){const r=[];if(f.ifd?.forEach((e=>{e.values||r.push(e)})),r.length>0){const a=r.map((e=>e.offlineOffsetSize)).filter(i),n=Math.min.apply(null,a.map((e=>e[0])));if(Math.min.apply(null,a.map((e=>e[0]+e[1])))-n<=this._bufferSize){const{data:i}=await this.request(this.url,{range:{from:n,to:n+this._bufferSize},responseType:\"array-buffer\",signal:o});e=i,s=n,r.forEach((r=>T(e,t,r,s)))}}if(f.ifd?.has(\"GEOKEYDIRECTORY\")){const r=f.ifd.get(\"GEOKEYDIRECTORY\"),i=r?.values;if(i&&i.length>4){const a=i[0]+\".\"+i[1]+\".\"+i[2],n=await this._readIFD(e,t,r.valueOffset+6-s,s,I.GEO_KEYS,2,o);r.data=n.ifd,r.data&&r.data.set(\"GEOTIFFVersion\",{id:0,type:2,valueCount:1,valueOffset:null,values:[a]})}}return f}if(f.requiredBufferSize&&f.requiredBufferSize!==e.byteLength){const r=await this.request(this.url,{range:{from:s,to:s+f.requiredBufferSize+4},responseType:\"array-buffer\",signal:o});return(e=r.data).byteLength<f.requiredBufferSize?null:this._readIFD(e,t,0,s,I.TIFF_TAGS,4,o)}}async _fetchRawTiffTile(e,t,r,i,s={}){const a=this._getTileLocation(e,t,r,i);if(!a)return null;const{ranges:n,actualTileWidth:o,actualTileHeight:f,ifd:l}=a,u=n.map((e=>this.request(this.url,{range:e,responseType:\"array-buffer\",signal:s.signal}))),c=await Promise.all(u),p=c.map((e=>e.data.byteLength)).reduce(((e,t)=>e+t)),m=1===c.length?c[0].data:new ArrayBuffer(p),h=[0],d=[0];if(c.length>1){const e=new Uint8Array(m);for(let t=0,r=0;t<c.length;t++){const i=c[t].data;e.set(new Uint8Array(i),r),h[t]=r,r+=i.byteLength,d[t]=i.byteLength}}const{blockWidth:y,blockHeight:g}=this.getBlockWidthHeight(e),T=await this.decodePixelBlock(m,{format:\"tiff\",customOptions:{headerInfo:this._headerInfo,ifd:l,offsets:h,sizes:d},width:y,height:g,planes:null,pixelType:null});if(null==T)return null;let x,I,w;if(o!==y||f!==g){let e=T.mask;if(e)for(x=0;x<g;x++)if(w=x*y,x<f)for(I=o;I<y;I++)e[w+I]=0;else for(I=0;I<y;I++)e[w+I]=0;else for(e=new Uint8Array(y*g),T.mask=e,x=0;x<f;x++)for(w=x*y,I=0;I<o;I++)e[w+I]=1}return T}_getTileLocation(e,t,r,i=!1){const{firstPyramidLevel:s,blockBoundary:a}=this.rasterInfo.storageInfo,n=0===e?0:e-(s-1),{_headerInfo:o}=this;if(!o)return null;const f=i?o.maskIFDs[n]:0===n?o?.ifds[0]:o?.pyramidIFDs[n-1];if(!f)return null;const l=x(f,o),u=E(f,\"TILEOFFSETS\");if(void 0===u)return null;const c=E(f,\"TILEBYTECOUNTS\"),{minRow:p,minCol:m,maxRow:h,maxCol:d}=a[n];if(t>h||r>d||t<p||r<m)return null;const y=k(f,\"IMAGEWIDTH\"),g=k(f,\"IMAGELENGTH\"),T=k(f,\"TILEWIDTH\"),I=k(f,\"TILELENGTH\"),w=l?this.rasterInfo.bandCount:1,_=w*t*(d+1)+r,b=[{from:u[_],to:u[_+w-1]+c[_+w-1]-1}];if(l){let e=!0;for(let t=0;t<w;t++)if(u[_+t]+c[_+t]!==u[_+t+1]){e=!1;break}if(!e)for(let t=0;t<w;t++)b[t]={from:u[_+t],to:u[_+t]+c[_+t]-1}}const F=u[_],S=c[_];if(null==F||null==S)return null;return{ranges:b,ifd:f,actualTileWidth:r===d&&y%T||T,actualTileHeight:t===h&&g%I||I}}async _fetchAuxiliaryMetaData(e){try{const{data:t}=await this.request(this.url+\".aux.xml\",{responseType:\"xml\",signal:e?.signal});return p(t)}catch{return null}}async _fetchAuxiliaryTable(e){try{const{data:t}=await this.request(this.url+\".vat.dbf\",{responseType:\"array-buffer\",signal:e?.signal}),r=u.parse(t);return r?.recordSet?b.fromJSON(r.recordSet):null}catch{return null}}};e([a()],D.prototype,\"_files\",void 0),e([a()],D.prototype,\"_headerInfo\",void 0),e([a()],D.prototype,\"_bufferSize\",void 0),e([a({type:String,json:{write:!0}})],D.prototype,\"datasetFormat\",void 0),D=e([n(\"esri.layers.support.rasterDatasets.TIFFRaster\")],D);const v=D;export{v as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport t from\"../../../core/Error.js\";import e from\"./CloudRaster.js\";import r from\"./ImageAuxRaster.js\";import s from\"./ImageServerRaster.js\";import o from\"./MRFRaster.js\";import a from\"./TIFFRaster.js\";const c=new Map;c.set(\"CRF\",{desc:\"Cloud Raster Format\",constructor:e}),c.set(\"MRF\",{desc:\"Meta Raster Format\",constructor:o}),c.set(\"TIFF\",{desc:\"GeoTIFF\",constructor:a}),c.set(\"RasterTileServer\",{desc:\"Raster Tile Server\",constructor:s}),c.set(\"JPG\",{desc:\"JPG Raster Format\",constructor:r}),c.set(\"PNG\",{desc:\"PNG Raster Format\",constructor:r}),c.set(\"GIF\",{desc:\"GIF Raster Format\",constructor:r}),c.set(\"BMP\",{desc:\"BMP Raster Format\",constructor:r});class n{static get supportedFormats(){const t=new Set;return c.forEach(((e,r)=>t.add(r))),t}static async open(e){const{url:r,ioConfig:s,sourceJSON:o}=e;let a=e.datasetFormat;null==a&&r.lastIndexOf(\".\")&&(a=r.slice(r.lastIndexOf(\".\")+1).toUpperCase()),\"OVR\"===a||\"TIF\"===a?a=\"TIFF\":\"JPG\"!==a&&\"JPEG\"!==a&&\"JFIF\"!==a||(a=\"JPG\"),r.toLowerCase().includes(\"/imageserver\")&&!r.toLowerCase().includes(\"/wcsserver\")&&(a=\"RasterTileServer\");const n={url:r,sourceJSON:o,datasetFormat:a,ioConfig:s??{bandIds:null,sampling:null}};let l,i;if(a&&this.supportedFormats.has(a)){if(\"CRF\"===a&&!s?.enableCRF)throw new t(\"rasterfactory:open\",`cannot open raster: ${r}`);return l=c.get(a).constructor,i=new l(n),await i.open({signal:e.signal}),i}if(a)throw new t(\"rasterfactory:open\",\"not a supported format \"+a);const u=Array.from(c.keys());let F=0;const m=()=>(a=u[F++],a&&(\"CRF\"!==a||s?.enableCRF)?(l=c.get(a).constructor,i=new l(n),i.open({signal:e.signal}).then((()=>i)).catch((()=>m()))):null);return m()}static register(t,e,r){c.has(t.toUpperCase())||c.set(t.toUpperCase(),{desc:e,constructor:r})}}export{n as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as e}from\"../chunks/tslib.es6.js\";import r from\"../PopupTemplate.js\";import{read as t,rasterRendererTypes as s,websceneRasterRendererTypes as i}from\"../rasterRenderers.js\";import o from\"../core/Error.js\";import a from\"../core/Logger.js\";import{isSome as n}from\"../core/maybe.js\";import{MultiOriginJSONMixin as p}from\"../core/MultiOriginJSONSupport.js\";import{throwIfAbortError as l}from\"../core/promiseUtils.js\";import{watch as m}from\"../core/reactiveUtils.js\";import{property as d}from\"../core/accessorSupport/decorators/property.js\";import{ensureClass as u,Integer as c}from\"../core/accessorSupport/ensureType.js\";import\"../core/arrayUtils.js\";import{enumeration as h}from\"../core/accessorSupport/decorators/enumeration.js\";import{reader as y}from\"../core/accessorSupport/decorators/reader.js\";import{subclass as f}from\"../core/accessorSupport/decorators/subclass.js\";import g from\"./Layer.js\";import{ArcGISService as v}from\"./mixins/ArcGISService.js\";import{BlendLayer as j}from\"./mixins/BlendLayer.js\";import{CustomParametersMixin as R}from\"./mixins/CustomParametersMixin.js\";import{ImageryTileMixin as S}from\"./mixins/ImageryTileMixin.js\";import{OperationalLayer as b}from\"./mixins/OperationalLayer.js\";import{PortalLayer as w}from\"./mixins/PortalLayer.js\";import{RefreshableLayer as T}from\"./mixins/RefreshableLayer.js\";import{ScaleRangeLayer as I}from\"./mixins/ScaleRangeLayer.js\";import{TemporalLayer as P}from\"./mixins/TemporalLayer.js\";import{legendEnabled as O}from\"./support/commonProperties.js\";import _ from\"./support/Field.js\";import{interpolationKebab as F}from\"./support/rasterEnums.js\";import x from\"./support/RasterFunction.js\";import L from\"./support/rasterDatasets/FunctionRaster.js\";import N from\"./support/rasterDatasets/RasterFactory.js\";import{create as J,getPrimaryRasterUrls as D}from\"./support/rasterFunctions/rasterFunctionHelper.js\";import{createPopupTemplate as M}from\"../support/popupUtils.js\";let C=class extends(j(I(b(w(R(S(P(v(T(p(g))))))))))){constructor(...e){super(...e),this._primaryRasters=null,this.bandIds=null,this.interpolation=null,this.legendEnabled=!0,this.isReference=null,this.listMode=\"show\",this.sourceJSON=null,this.version=null,this.type=\"imagery-tile\",this.operationalLayerType=\"ArcGISTiledImageServiceLayer\",this.popupEnabled=!0,this.popupTemplate=null,this.fields=null}normalizeCtorArgs(e,r){return\"string\"==typeof e?{url:e,...r}:e}load(e){const r=n(e)?e.signal:null;return this.addResolvingPromise(this.loadFromPortal({supportedTypes:[\"Image Service\"]},e).catch(l).then((()=>this._openRaster(r)))),Promise.resolve(this)}get defaultPopupTemplate(){return this.createPopupTemplate()}get rasterFields(){let e=[new _({name:\"Raster.ServicePixelValue\",alias:\"Pixel Value\",domain:null,editable:!1,length:50,type:\"string\"})];const{rasterInfo:r}=this,t=r?.attributeTable,s=n(t)?t.fields:null,i=\"Raster.\";if(s){const r=s.filter((e=>\"oid\"!==e.type&&\"value\"!==e.name.toLowerCase())).map((e=>{const r=e.clone();return r.name=i+e.name,r}));e=e.concat(r)}const o=r?.dataType,a=r?.multidimensionalInfo;if((\"vector-magdir\"===o||\"vector-uv\"===o)&&n(a)){const r=a.variables[0].unit?.trim(),t=\"Magnitude\"+(r?` (${r})`:\"\");e.push(new _({name:\"Raster.Magnitude\",alias:t,domain:null,editable:!1,type:\"double\"})),e.push(new _({name:\"Raster.Direction\",alias:\"Direction (°)\",domain:null,editable:!1,type:\"double\"}))}return e}set renderer(e){this._set(\"renderer\",e),this.updateRenderer()}readRenderer(e,r,s){const i=r&&r.layerDefinition&&r.layerDefinition.drawingInfo&&r.layerDefinition.drawingInfo.renderer,o=t(i,s)||void 0;if(null!=o)return o}createPopupTemplate(e){return M({fields:this.rasterFields,title:this.title},e)}async generateRasterInfo(e,r){if(!(e=u(x,e)))return this._primaryRasters[0].rasterInfo;try{const t={raster:this._primaryRasters[0]};this._primaryRasters.length>1&&this._primaryRasters.forEach((e=>t[e.url]=e));const s=J(e.toJSON(),t),i=new L({rasterFunction:s});return await i.open(r),i.rasterInfo}catch{return null}}write(e,r){const{raster:t}=this;if(this.loaded?\"RasterTileServer\"===t.datasetFormat&&(\"Raster\"===t.tileType||\"Map\"===t.tileType):this.url&&/\\/ImageServer(\\/|\\/?$)/i.test(this.url))return super.write(e,r);if(r&&r.messages){const e=`${r.origin}/${r.layerContainerType||\"operational-layers\"}`;r.messages.push(new o(\"layer:unsupported\",`Layers (${this.title}, ${this.id}) of type '${this.declaredClass}' are not supported in the context of '${e}'`,{layer:this}))}return null}async _openRaster(e){let r=!1;if(this.raster)this.raster.rasterInfo||await this.raster.open(),\"Function\"===this.raster.datasetFormat?(r=!0,this._primaryRasters=this.raster.primaryRasters.rasters):this._primaryRasters=[this.raster],this.url=this.raster.url;else{const{rasterFunction:r}=this,t=[this.url];r&&D(r.toJSON(),t);const i=await Promise.all(t.map((r=>N.open({url:r,sourceJSON:this.sourceJSON,ioConfig:{sampling:\"closest\",...this.ioConfig,customFetchParameters:this.customParameters},signal:e})))),n=i.findIndex((e=>null==e));if(n>-1)throw new o(\"imagery-tile-layer:open\",`cannot open raster: ${t[n]}`);if(this._primaryRasters=i,r){const e={raster:this._primaryRasters[0]};this._primaryRasters.length>1&&this._primaryRasters.forEach((r=>e[r.url]=r));const t=J(r.rasterFunctionDefinition??r.toJSON(),e),n=new L({rasterFunction:t});try{await n.open(),this.raster=n}catch(s){const e=a.getLogger(this.declaredClass);s instanceof o&&e.error(\"imagery-tile-layer:open\",s.message),e.warn(\"imagery-tile-layer:open\",\"the raster function cannot be applied and is removed\"),this._set(\"rasterFunction\",null),this.raster=i[0]}}else this.raster=i[0]}const t=this.raster.rasterInfo;if(!t)throw new o(\"imagery-tile-layer:load\",\"cannot load resources on \"+this.url);if(this._set(\"rasterInfo\",r?t:this._primaryRasters[0].rasterInfo),this._set(\"spatialReference\",t.spatialReference),this.sourceJSON=this.sourceJSON||this.raster.sourceJSON,null!=this.sourceJSON){const e=\"Map\"===this.raster.tileType&&null!=this.sourceJSON.minLOD&&null!=this.sourceJSON.maxLOD?this.sourceJSON:{...this.sourceJSON,minScale:0,maxScale:0};this.read(e,{origin:\"service\"})}this.title||(this.title=this.raster.datasetName),\"Map\"===this.raster.tileType&&(this.popupEnabled=!1),this._configDefaultSettings(),this.addHandles(m((()=>this.customParameters),(e=>{this.raster&&(this.raster.ioConfig.customFetchParameters=e)})))}};e([d()],C.prototype,\"_primaryRasters\",void 0),e([d({type:[c],json:{write:{overridePolicy(){return{enabled:!this.loaded||\"Raster\"===this.raster.tileType||\"0,1,2\"!==this.bandIds?.join(\",\")}}}}})],C.prototype,\"bandIds\",void 0),e([d({json:{write:{overridePolicy(){return{enabled:!this.loaded||\"Raster\"===this.raster.tileType||\"bilinear\"!==this.interpolation}}}}}),h(F)],C.prototype,\"interpolation\",void 0),e([d(O)],C.prototype,\"legendEnabled\",void 0),e([d({type:Boolean,json:{read:!1,write:{enabled:!0,overridePolicy:()=>({enabled:!1})}}})],C.prototype,\"isReference\",void 0),e([d({type:[\"show\",\"hide\"]})],C.prototype,\"listMode\",void 0),e([d({json:{read:!0,write:!0}})],C.prototype,\"blendMode\",void 0),e([d()],C.prototype,\"sourceJSON\",void 0),e([d({readOnly:!0,json:{origins:{service:{read:{source:\"currentVersion\"}}}}})],C.prototype,\"version\",void 0),e([d({readOnly:!0,json:{read:!1}})],C.prototype,\"type\",void 0),e([d({type:[\"ArcGISTiledImageServiceLayer\"]})],C.prototype,\"operationalLayerType\",void 0),e([d({type:Boolean,value:!0,json:{read:{source:\"disablePopup\",reader:(e,r)=>!r.disablePopup},write:{target:\"disablePopup\",overridePolicy(){return{enabled:!this.loaded||\"Raster\"===this.raster.tileType}},writer(e,r,t){r[t]=!e}}}})],C.prototype,\"popupEnabled\",void 0),e([d({type:r,json:{read:{source:\"popupInfo\"},write:{target:\"popupInfo\",overridePolicy(){return{enabled:!this.loaded||\"Raster\"===this.raster.tileType}}}}})],C.prototype,\"popupTemplate\",void 0),e([d({readOnly:!0})],C.prototype,\"defaultPopupTemplate\",null),e([d({readOnly:!0,type:[_]})],C.prototype,\"fields\",void 0),e([d({readOnly:!0,type:[_]})],C.prototype,\"rasterFields\",null),e([d({types:s,json:{name:\"layerDefinition.drawingInfo.renderer\",write:{overridePolicy(){const e=\"raster-stretch\"===this.renderer?.type&&\"none\"===this.renderer.stretchType&&!this.renderer.useGamma;return{enabled:!this.loaded||\"Raster\"===this.raster.tileType||!e}}},origins:{\"web-scene\":{types:i,name:\"layerDefinition.drawingInfo.renderer\",write:{overridePolicy:e=>({enabled:e&&\"vector-field\"!==e.type&&\"flow\"!==e.type})}}}}})],C.prototype,\"renderer\",null),e([y(\"renderer\")],C.prototype,\"readRenderer\",null),C=e([f(\"esri.layers.ImageryTileLayer\")],C);const E=C;export{E as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI43D,IAAMA,KAAE;AAAR,IAAU,IAAE;AAAI,IAAI,IAAE,cAAc,EAAE,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,mBAAiB,MAAK,KAAK,cAAY,MAAK,KAAK,gBAAc,MAAK,KAAK,6BAA2B,MAAG,KAAK,aAAW,MAAK,KAAK,WAAS,EAAC,UAAS,UAAS;AAAA,EAAC;AAAA,EAAC,MAAM,OAAM;AAAC,UAAMC,KAAEC,GAAE;AAAE,SAAK,oBAAoBD,EAAC,GAAE,MAAM,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,kBAAkBA,IAAE;AAAC,WAAOA,MAAGA,GAAE,aAAWA,KAAE,EAAC,GAAGA,IAAE,UAAS,EAAC,YAAW,MAAK,SAAQ,MAAK,UAAS,WAAU,UAAS,EAAE,OAAO,GAAE,GAAGA,GAAE,SAAQ,EAAC,IAAGA;AAAA,EAAC;AAAA,EAAC,IAAI,2BAA0B;AAAC,UAAK,EAAC,YAAWA,GAAC,IAAE,MAAKE,KAAEC,GAAEH,GAAE,gBAAgB;AAAE,WAAO,EAAEE,EAAC,KAAGF,GAAE,OAAO,SAAOE,KAAE;AAAA,EAAC;AAAA,EAAC,IAAI,IAAIF,IAAE;AAAC,SAAK,KAAK,OAAM,EAAEA,IAAE,EAAE,UAAU,KAAK,aAAa,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,KAAKA,IAAE;AAAC,UAAM,IAAII,GAAE,mCAAkC,2BAA2B;AAAA,EAAC;AAAA,EAAC,MAAM,UAAUJ,IAAEE,IAAEG,IAAEC,KAAE,CAAC,GAAE;AAAC,UAAMC,KAAED,GAAE,YAAU,KAAK,WAAW,YAAY,UAASE,KAAE,KAAK,0BAA0BR,IAAEE,IAAEG,IAAEE,EAAC;AAAE,WAAO,KAAK,YAAYC,IAAED,GAAE,KAAK,CAAC,GAAEA,GAAE,KAAK,CAAC,GAAED,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,SAASN,IAAEE,KAAE,CAAC,GAAE;AAJzxF;AAI0xF,IAAAF,KAAE,EAAES,IAAET,EAAC,EAAE,MAAM,EAAE,UAAU;AAAE,UAAK,EAAC,4BAA2BK,IAAE,YAAWC,GAAC,IAAEJ,IAAE,EAAC,YAAWK,GAAC,IAAE,MAAK,EAAC,8BAA6BG,IAAE,sBAAqBC,IAAC,IAAEJ;AAAE,QAAG,EAAC,wBAAuBK,GAAC,IAAEV;AAAE,UAAMW,KAAE,EAAEF,GAAC,KAAGD,OAAI,QAAMJ,MAAG,EAAED,EAAC;AAAG,QAAGQ,MAAG,CAACD,IAAE;AAAC,MAAAA,KAAE,EAAEP,EAAC,KAAGA,GAAE,SAAO,IAAEA,GAAE,CAAC,EAAE,gBAAc,SAAOM,IAAE,UAAU,CAAC,EAAE,MAAKT,KAAE,EAAC,GAAGA,IAAE,wBAAuBU,GAAC;AAAA,IAAC;AAAC,IAAAV,KAAE,KAAK,8BAA8BA,EAAC;AAAE,UAAK,EAAC,kBAAiBY,IAAE,QAAOC,GAAC,IAAER,IAAE,EAAC,qBAAoBS,GAAC,IAAEd;AAAE,QAAIe,KAAEC,GAAElB,IAAEc,IAAEE,EAAC;AAAE,QAAG,CAACD,GAAE,WAAWE,EAAC,EAAE,QAAM,EAAC,UAASA,IAAE,OAAM,KAAI;AAAE,QAAG,EAAEV,GAAE,SAAS,GAAE;AAAC,YAAMP,KAAEO,GAAE,UAAU,iBAAiBU,EAAC;AAAE,UAAG,CAACV,GAAE,aAAa,WAAWP,EAAC,EAAE,QAAM,EAAC,UAASA,IAAE,OAAM,KAAI;AAAE,MAAAiB,KAAEjB;AAAA,IAAC;AAAC,QAAImB,KAAE;AAAE,UAAMC,KAAE,EAAER,EAAC,KAAG,EAAED,GAAC,KAAGJ,GAAE;AAA6B,QAAG,eAAa,KAAK,eAAc;AAAC,YAAMP,KAAE,KAAK,eAAe,QAAQ,CAAC;AAAE,UAAGoB,GAAE,QAAOpB,GAAE,SAASiB,IAAEf,EAAC;AAAE,YAAK,EAAC,WAAUG,GAAC,IAAEE,IAAED,KAAE,GAAEe,KAAEhB,GAAE,IAAEC,KAAE,GAAEI,KAAEL,GAAE,IAAEC,KAAE,GAAEK,MAAE,IAAIF,GAAE,EAAC,MAAKQ,GAAE,IAAEI,IAAE,MAAKJ,GAAE,IAAEI,IAAE,MAAKJ,GAAE,IAAEP,IAAE,MAAKO,GAAE,IAAEP,IAAE,kBAAiBI,GAAC,CAAC,GAAEF,MAAE,EAAC,eAAc,UAAS,GAAE,EAAC,YAAWC,GAAC,IAAE,MAAMb,GAAE,YAAYW,KAAEL,IAAEA,IAAEM,GAAC,GAAE,EAAC,YAAWU,GAAC,IAAE,MAAM,KAAK,YAAYX,KAAEL,IAAEA,IAAEM,GAAC;AAAE,UAAG,EAAEC,EAAC,EAAE,QAAM,EAAC,UAASI,IAAE,OAAM,KAAI;AAAE,YAAMF,KAAE,KAAK,MAAMT,KAAEA,KAAE,GAAE,GAAEU,KAAE,CAACH,GAAE,QAAMA,GAAE,KAAKE,EAAC,IAAEF,GAAE,OAAO,IAAK,CAAAb,OAAGA,GAAEe,EAAC,CAAE,IAAE;AAAK,UAAII;AAAE,aAAO,EAAEG,EAAC,MAAIH,KAAE,CAACG,GAAE,QAAMA,GAAE,KAAKP,EAAC,IAAEO,GAAE,OAAO,IAAK,CAAAtB,OAAGA,GAAEe,EAAC,CAAE,IAAE,SAAQ,EAAC,UAASE,IAAE,OAAMD,IAAE,gBAAeG,IAAE,cAAa,EAAC;AAAA,IAAC;AAAC,QAAG,CAACC;AAAE,UAAGlB,GAAE,eAAc;AAAC,QAAAiB,KAAE,GAAEjB,GAAE,eAAcK,IAAE,KAAK,SAAS,QAAQ,EAAE;AAAA,MAAY,WAASY,KAAE,MAAM,KAAK,mCAAmCnB,IAAEE,EAAC,GAAE,QAAMiB,GAAE,QAAM,EAAC,UAASF,IAAE,OAAM,KAAI;AAAA;AAAE,UAAMM,KAAE,KAAK,sBAAsBN,IAAEE,IAAE,MAAKC,EAAC;AAAE,QAAG,SAAOG,GAAE,QAAM,EAAC,UAASN,IAAE,OAAM,KAAI;AAAE,UAAK,EAAC,KAAIR,IAAE,KAAIe,IAAE,WAAUC,IAAE,WAAUC,IAAE,YAAW,EAAC,IAAEH,IAAE,IAAEX,MAAG,EAAEV,GAAE,OAAO,GAAE,IAAEmB,GAAE,KAAK,KAAI,CAAC,GAAEM,KAAE,GAAGR,EAAC,IAAIV,EAAC,IAAIe,EAAC;AAAG,QAAII,KAAEX,GAAE,GAAE,MAAKU,EAAC;AAAE,MAAEC,EAAC,MAAIA,KAAE,KAAK,aAAaT,IAAEV,IAAEe,IAAEtB,EAAC,GAAEoB,GAAE,GAAE,MAAKK,IAAEC,EAAC;AAAG,UAAMV,KAAE,MAAMU;AAAE,QAAG,EAAEV,EAAC,KAAG,GAAC,KAAAA,GAAE,WAAF,mBAAU,QAAO,QAAM,EAAC,UAASD,IAAE,OAAM,KAAI;AAAE,UAAM,IAAEQ,KAAE,IAAEC;AAAE,WAAO,KAAK,uBAAuBR,IAAE,EAAC,aAAYD,IAAE,UAAS,GAAE,cAAaE,IAAE,mBAAkB,CAAC,CAACC,IAAE,mBAAkBP,IAAE,iBAAgBX,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,YAAYF,IAAEE,IAAEG,IAAEC,KAAE,CAAC,GAAE;AAAC,QAAGN,KAAE,GAAEA,EAAC,IAAGM,KAAE,KAAK,8BAA8BA,EAAC,GAAG,eAAe,QAAO,KAAK,aAAaN,IAAEE,IAAEG,IAAEC,EAAC;AAAE,UAAMC,KAAEJ,GAAEH,GAAE,gBAAgB,GAAEQ,KAAE,EAAER,EAAC;AAAE,QAAG,EAAEO,EAAC,KAAG,MAAIC,MAAG,MAAIA,MAAG,KAAK,yBAAyB,QAAO,KAAK,aAAaR,IAAEE,IAAEG,IAAEC,EAAC;AAAE,QAAGE,MAAG,EAAE,QAAM,EAAC,QAAOR,IAAE,YAAW,KAAI;AAAE,UAAMqB,KAAE,CAAC,GAAE,EAAC,MAAKX,IAAE,MAAKC,IAAC,IAAEX,IAAEY,KAAE,KAAK,MAAML,MAAGI,MAAED,MAAGR,EAAC,GAAEW,KAAED,KAAE,KAAK,OAAOL,KAAE,IAAEG,OAAIC,MAAED,MAAGR,EAAC;AAAE,QAAIoB,KAAE;AAAE,UAAMR,KAAE,CAAC;AAAE,aAAQV,KAAE,GAAEA,MAAGI,IAAEJ,MAAI;AAAC,YAAMW,KAAE,IAAIN,GAAE,EAAC,MAAK,MAAIL,KAAEM,KAAE,CAACH,KAAE,GAAE,MAAKH,OAAII,KAAEG,MAAEJ,KAAEH,KAAEG,KAAE,GAAE,MAAKP,GAAE,MAAK,MAAKA,GAAE,MAAK,kBAAiBA,GAAE,iBAAgB,CAAC,GAAEgB,KAAE,MAAIZ,KAAEQ,KAAEC,KAAET,OAAII,KAAEN,KAAEoB,KAAEV;AAAE,MAAAU,MAAGN,IAAEF,GAAE,KAAKE,EAAC;AAAE,YAAMC,KAAEX,GAAE,qBAAmBF,KAAE,IAAE,OAAK,KAAK,aAAaW,IAAEC,IAAEX,IAAEC,EAAC;AAAE,MAAAe,GAAE,KAAKJ,EAAC;AAAA,IAAC;AAAC,UAAMF,MAAG,MAAM,QAAQ,IAAIM,EAAC,GAAG,IAAK,CAAArB,OAAGA,MAAA,gBAAAA,GAAG,UAAW;AAAE,QAAIgB,KAAE;AAAK,UAAMC,KAAE,EAAC,OAAMf,IAAE,QAAOG,GAAC;AAAE,QAAG,KAAK,kBAAiB;AAAC,MAAAW,MAAG,MAAM,KAAK,iBAAiB,mBAAmB,EAAC,gBAAeD,IAAE,eAAcE,IAAE,eAAc,MAAK,OAAM,MAAK,eAAc,MAAK,eAAc,WAAU,eAAc,MAAK,aAAYH,GAAC,GAAER,EAAC,GAAG;AAAA,IAAU,MAAM,CAAAU,KAAEa,GAAEd,IAAEE,IAAE,EAAC,aAAYH,GAAC,CAAC;AAAE,WAAM,EAAC,QAAOd,IAAE,WAAU,EAAEA,IAAE,KAAK,WAAW,kBAAiBM,GAAE,mBAAmB,GAAE,YAAWU,GAAC;AAAA,EAAC;AAAA,EAAC,MAAM,eAAehB,IAAEE,IAAEG,IAAEC,KAAE,CAAC,GAAE;AAAC,IAAAJ,KAAE,EAAC,GAAE,KAAK,MAAMA,GAAE,CAAC,GAAE,GAAE,KAAK,MAAMA,GAAE,CAAC,EAAC;AAAE,UAAMK,KAAE,MAAM,KAAK,eAAeP,IAAEE,IAAEG,IAAEC,EAAC,GAAE,EAAC,cAAaF,IAAE,iBAAgBiB,IAAE,aAAYX,GAAC,IAAE,KAAK,YAAWC,MAAE,KAAGX,IAAEY,KAAES,GAAE,IAAEV,KAAEE,KAAEQ,GAAE,IAAEV,KAAEW,KAAE,IAAIb,GAAE,EAAC,MAAKL,GAAE,OAAKQ,KAAEV,GAAE,GAAE,MAAKE,GAAE,OAAKQ,MAAGV,GAAE,IAAEG,GAAE,QAAM,IAAG,MAAKD,GAAE,OAAKS,MAAGX,GAAE,IAAEG,GAAE,SAAO,IAAG,MAAKD,GAAE,OAAKS,KAAEX,GAAE,GAAE,kBAAiBE,GAAE,iBAAgB,CAAC;AAAE,QAAG,CAACG,GAAE,QAAM,EAAC,QAAOe,IAAE,WAAUA,IAAE,YAAW,KAAI;AAAE,UAAK,EAAC,aAAYR,IAAE,YAAWC,GAAC,IAAER;AAAE,QAAG,MAAIO,GAAE,UAAQ,EAAEA,GAAE,CAAC,CAAC,KAAGA,GAAE,CAAC,EAAE,UAAQT,GAAE,SAAOS,GAAE,CAAC,EAAE,WAAST,GAAE,OAAO,QAAM,EAAC,QAAOiB,IAAE,WAAUA,IAAE,YAAWf,GAAE,YAAY,CAAC,EAAC;AAAE,UAAMS,KAAEhB,KAAE,IAAEU,GAAE,oBAAkBA,GAAE,YAAWO,KAAEjB,KAAE,IAAEU,GAAE,qBAAmBA,GAAE,aAAYS,KAAE,EAAC,GAAEjB,GAAE,IAAEc,IAAE,GAAEd,GAAE,IAAEe,GAAC;AAAE,QAAIG;AAAE,QAAG,KAAK,kBAAiB;AAAC,MAAAA,MAAG,MAAM,KAAK,iBAAiB,mBAAmB,EAAC,gBAAeN,IAAE,eAAcC,IAAE,eAAcV,IAAE,YAAWc,IAAE,UAASd,IAAE,OAAM,MAAK,eAAc,MAAK,eAAcC,GAAE,eAAc,eAAc,MAAK,aAAY,KAAI,GAAEA,EAAC,GAAG;AAAA,IAAU,MAAM,CAAAc,KAAES,GAAEf,IAAEC,IAAE,EAAC,YAAWI,IAAE,UAASd,GAAC,CAAC;AAAE,WAAM,EAAC,QAAOiB,IAAE,WAAUA,IAAE,YAAWF,GAAC;AAAA,EAAC;AAAA,EAAC,aAAapB,IAAEE,IAAEI,IAAEC,IAAE;AAAC,UAAM,IAAIH,GAAE,mCAAkC,mCAAmC;AAAA,EAAC;AAAA,EAAC,cAAcJ,IAAE;AAAC,WAAO,EAAE,KAAK,WAAW,QAAOA,EAAC;AAAA,EAAC;AAAA,EAAC,iBAAiBA,IAAEE,IAAE;AAAC,WAAM,CAAC,KAAK,oBAAkBA,GAAE,YAAUuB,GAAEzB,IAAEE,EAAC,IAAE,KAAK,iBAAiB,OAAO,EAAC,MAAKF,IAAE,SAAQE,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQF,IAAEK,IAAEC,KAAE,GAAE;AAAC,UAAK,EAAC,uBAAsBC,GAAC,IAAE,KAAK,UAAS,EAAC,OAAMC,IAAE,OAAMJ,IAAE,SAAQiB,GAAC,IAAEhB;AAAE,IAAAC,KAAEA,MAAGD,GAAE,cAAY,KAAK,SAAS;AAAW,UAAMK,KAAEF,KAAE,EAAC,OAAM,SAASA,GAAE,IAAI,IAAIA,GAAE,EAAE,GAAE,IAAE;AAAK,QAAG;AAAC,aAAO,MAAM,EAAER,IAAE,EAAC,GAAGK,IAAE,OAAM,EAAC,GAAGD,IAAE,GAAGG,GAAC,GAAE,SAAQ,EAAC,GAAGc,IAAE,GAAGX,GAAC,EAAC,CAAC;AAAA,IAAC,SAAOC,KAAE;AAAC,UAAGL,KAAE,EAAE,QAAOA,MAAI,KAAK,QAAQN,IAAEK,IAAEC,EAAC;AAAE,YAAMK;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,cAAcX,IAAE;AAAC,UAAK,EAAC,sBAAqBE,GAAC,IAAE,KAAK;AAAW,WAAO,EAAEA,EAAC,KAAG,EAAEF,EAAC,KAAG,MAAIA,GAAE,SAAO,OAAKmB,GAAEnB,IAAEE,EAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BF,IAAEE,IAAEG,IAAEC,IAAE;AAAC,UAAMC,KAAE,EAAED,GAAE,MAAMN,EAAC,CAAC;AAAE,WAAO,KAAK,cAAc,EAAC,GAAEO,GAAE,YAAW,GAAEA,GAAE,WAAU,GAAEL,IAAEG,IAAEC,GAAE,QAAOA,GAAE,kBAAiBA,GAAE,IAAI;AAAA,EAAC;AAAA,EAAC,iBAAgB;AAAC,UAAK,EAAC,aAAYN,IAAE,kBAAiBE,IAAE,QAAOG,IAAE,WAAUC,GAAC,IAAE,KAAK;AAAW,QAAG,CAACN,GAAE,UAAS;AAAC,YAAMO,KAAE,CAAC,GAAEC,KAAER,GAAE,uBAAqB;AAAE,UAAII,KAAE,KAAK,IAAIE,GAAE,GAAEA,GAAE,CAAC,GAAEe,KAAE,IAAE,SAAM,KAAGjB;AAAE,eAAQJ,KAAE,GAAEA,MAAGQ,IAAER,KAAI,CAAAO,GAAE,KAAK,IAAI,EAAE,EAAC,OAAMC,KAAER,IAAE,YAAWI,IAAE,OAAMiB,GAAC,CAAC,CAAC,GAAEjB,MAAG,GAAEiB,MAAG;AAAE,YAAMX,KAAE,IAAID,GAAE,EAAC,GAAEJ,GAAE,MAAK,GAAEA,GAAE,MAAK,kBAAiBH,GAAC,CAAC;AAAE,MAAAF,GAAE,WAAS,IAAI,EAAE,EAAC,QAAOU,IAAE,MAAK,CAACV,GAAE,YAAWA,GAAE,WAAW,GAAE,kBAAiBE,IAAE,MAAKK,GAAC,CAAC,GAAEP,GAAE,oBAAkB;AAAA,IAAE;AAAA,EAAC;AAAA,EAAC,+BAA+BA,IAAEE,KAAE,KAAIG,KAAE,KAAIC,IAAE;AAAC,UAAK,EAAC,OAAMC,IAAE,QAAOC,IAAE,cAAaJ,IAAE,WAAUiB,IAAE,kBAAiBX,GAAC,IAAEV,IAAEW,MAAE,IAAIF,GAAE,EAAC,GAAEL,GAAE,MAAK,GAAEA,GAAE,MAAK,kBAAiBM,GAAC,CAAC;AAAE,YAAMJ,OAAIA,KAAE,KAAK,IAAI,GAAE,KAAK,MAAM,KAAK,IAAI,KAAK,IAAIC,IAAEC,EAAC,CAAC,IAAE,KAAK,MAAI,CAAC,CAAC;AAAG,UAAMI,KAAE,KAAK,qBAAqBR,IAAE,KAAI,KAAI,EAAC,GAAEA,GAAE,MAAK,GAAEA,GAAE,KAAI,GAAE,CAACiB,EAAC,GAAEf,EAAC;AAAE,IAAAN,GAAE,cAAY,IAAIQ,GAAE,EAAC,YAAWN,IAAE,aAAYG,IAAE,mBAAkBH,IAAE,oBAAmBG,IAAE,QAAOM,KAAE,mBAAkB,GAAE,qBAAoBL,IAAE,eAAcM,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,MAAM,mCAAmCZ,IAAEE,KAAE,CAAC,GAAE;AAAC,WAAO;AAAA,EAAC;AAAA,EAAC,qBAAqBF,IAAEE,IAAEG,IAAEC,IAAEC,IAAEC,KAAE,GAAEJ,KAAE,GAAE;AAAC,QAAG,MAAIG,GAAE,UAAQC,KAAE,GAAE;AAAC,MAAAD,KAAE,CAAC,GAAGA,EAAC;AAAE,UAAG,EAAC,GAAEP,IAAE,GAAEE,GAAC,IAAEK,GAAE,CAAC;AAAE,eAAQF,KAAE,GAAEA,KAAEG,IAAEH,KAAI,CAAAL,MAAGI,IAAEF,MAAGE,IAAEG,GAAE,KAAK,EAAC,GAAEP,IAAE,GAAEE,GAAC,CAAC;AAAA,IAAC;AAAC,UAAMmB,KAAE,CAAC,GAAE,EAAC,GAAEX,IAAE,GAAEC,IAAC,IAAEL;AAAE,aAAQM,KAAE,GAAEA,KAAEL,GAAE,QAAOK,MAAI;AAAC,YAAK,EAAC,GAAEN,IAAE,GAAEE,IAAC,IAAED,GAAEK,EAAC;AAAE,MAAAS,GAAE,KAAK,EAAC,QAAO,KAAK,OAAOrB,GAAE,OAAKU,KAAE,MAAGJ,MAAGJ,KAAEI,EAAC,GAAE,QAAO,KAAK,OAAON,GAAE,OAAKU,KAAE,MAAGJ,MAAGJ,KAAEI,EAAC,GAAE,QAAO,KAAK,OAAOK,MAAEX,GAAE,OAAK,MAAGQ,OAAGH,KAAEG,GAAC,GAAE,QAAO,KAAK,OAAOG,MAAEX,GAAE,OAAK,MAAGQ,OAAGH,KAAEG,GAAC,EAAC,CAAC;AAAA,IAAC;AAAC,WAAOa;AAAA,EAAC;AAAA,EAAC,oBAAoBrB,IAAE;AAAC,UAAK,EAAC,iBAAgBE,GAAC,IAAE,KAAK,YAAW,EAAC,oBAAmBG,IAAE,sBAAqBC,GAAC,IAAE,KAAK,WAAW;AAAY,QAAG,MAAIN,GAAE,QAAOE;AAAE,QAAG,EAAEG,EAAC,KAAGA,GAAE,OAAO,QAAOA,GAAEL,KAAE,CAAC;AAAE,UAAMO,KAAED,MAAGN;AAAE,WAAM,EAAC,GAAEE,GAAE,IAAEK,IAAE,GAAEL,GAAE,IAAEK,GAAC;AAAA,EAAC;AAAA,EAAC,sBAAsBP,IAAEE,IAAEG,IAAEC,IAAE;AAAC,UAAK,EAAC,kBAAiBC,IAAE,cAAaH,IAAE,aAAYiB,GAAC,IAAE,KAAK,YAAW,EAAC,qBAAoBX,IAAE,QAAOC,KAAE,eAAcC,GAAC,IAAES,IAAER,KAAEP,MAAG,EAAEM,EAAC,IAAEA,GAAE,SAAS,CAAC,IAAES,GAAE,YAAWC,KAAEhB,MAAG,EAAEM,EAAC,IAAEA,GAAE,SAAS,CAAC,IAAES,GAAE,aAAYP,KAAEI,GAAElB,IAAEO,IAAEF,EAAC;AAAE,QAAG,CAACD,GAAE,WAAWU,EAAC,EAAE,QAAO;AAAK,QAAGZ,KAAE,KAAGA,KAAEQ,GAAE,QAAO;AAAK,UAAMK,KAAE,KAAK,oBAAoBb,EAAC,GAAE,EAAC,GAAEc,IAAE,GAAEC,GAAC,IAAEF,IAAEI,MAAGR,IAAE,IAAEG,GAAE,KAAGG,KAAEK,IAAEF,MAAGN,GAAE,IAAEH,IAAE,KAAGK,KAAEH,IAAEU,KAAE,KAAK,IAAID,KAAE,GAAE,KAAK,OAAOH,KAAE,KAAK,MAAMA,EAAC,KAAGG,EAAC,CAAC,GAAEQ,KAAE,KAAK,IAAIjB,KAAE,GAAE,KAAK,OAAOO,KAAE,KAAK,MAAMA,EAAC,KAAGP,EAAC,CAAC;AAAE,WAAM,EAAC,cAAaX,IAAE,KAAI,KAAK,MAAMiB,EAAC,GAAE,KAAI,KAAK,MAAMC,EAAC,GAAE,WAAUG,IAAE,WAAUO,IAAE,YAAWjB,IAAE,aAAYC,GAAC;AAAA,EAAC;AAAA,EAAC,cAAcd,IAAEE,IAAEG,IAAEC,IAAEC,IAAEC,IAAE;AAAC,UAAK,CAACJ,IAAEiB,EAAC,IAAEb,IAAEE,KAAEJ,GAAE,IAAED,KAAED,KAAEJ,GAAE,GAAEW,MAAED,KAAEN,KAAEJ,GAAE,GAAEY,KAAEN,GAAE,IAAEJ,KAAEmB,KAAErB,GAAE,GAAEa,KAAED,KAAES,KAAErB,GAAE;AAAE,WAAO,IAAIS,GAAE,EAAC,MAAKC,IAAE,MAAKC,KAAE,MAAKE,IAAE,MAAKD,IAAE,kBAAiBL,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBP,IAAE;AAAC,WAAM,EAAC,YAAWA,KAAE,IAAE,KAAK,WAAW,YAAY,oBAAkB,KAAK,WAAW,YAAY,YAAW,aAAYA,KAAE,IAAE,KAAK,WAAW,YAAY,qBAAmB,KAAK,WAAW,YAAY,YAAW;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAEE,IAAEG,IAAE;AAAC,UAAMC,KAAE,KAAK,WAAW,YAAY,cAAcN,EAAC;AAAE,WAAM,CAACM,MAAGA,GAAE,SAAOJ,MAAGI,GAAE,SAAOD,MAAGC,GAAE,SAAOJ,MAAGI,GAAE,SAAOD;AAAA,EAAC;AAAA,EAAC,MAAM,aAAaL,IAAEE,IAAEG,IAAEC,KAAE,CAAC,GAAE;AAAC,QAAIC,KAAE,EAAEP,EAAC;AAAE,QAAGO,MAAG,EAAE,QAAM,EAAC,QAAOP,IAAE,YAAW,KAAI;AAAE,UAAMI,KAAE,KAAK,mBAAmBJ,IAAEE,IAAEG,IAAEC,EAAC,GAAE,EAAC,cAAaI,IAAE,mBAAkBC,KAAE,eAAcC,IAAE,WAAUC,IAAE,UAASS,IAAE,WAAUR,GAAC,IAAEV;AAAE,QAAG,MAAIkB,MAAG,MAAIR,GAAE,QAAM,EAAC,QAAOd,IAAE,WAAUa,IAAE,YAAW,KAAI;AAAE,UAAME,KAAE,EAAE,KAAK,WAAW,SAAS,GAAEC,KAAE,iBAAcD,MAAA,gBAAAA,GAAG,OAAKE,KAAE,EAAEd,GAAEH,GAAE,gBAAgB,CAAC;AAAE,KAACgB,MAAGC,OAAIV,KAAE,EAAEH,GAAE,WAAUY,EAAC;AAAG,UAAMG,KAAE,KAAK,WAAW,aAAYC,KAAE,EAAC,GAAE,KAAK,OAAOP,GAAE,OAAKM,GAAE,OAAO,KAAGR,IAAE,IAAE,GAAE,GAAE,GAAE,KAAK,OAAOQ,GAAE,OAAO,IAAEN,GAAE,QAAMF,IAAE,IAAE,GAAE,EAAC,GAAEY,KAAE,MAAM,KAAK,eAAeb,IAAEU,IAAE,EAAC,OAAME,IAAE,QAAOR,IAAE,WAAUP,GAAC,GAAED,EAAC;AAAE,QAAG,CAACiB,GAAE,QAAM,EAAC,QAAOvB,IAAE,WAAUa,IAAE,YAAW,KAAI;AAAE,UAAMiB,KAAEpB,KAAE,IAAES,GAAE,oBAAkBA,GAAE,YAAWV,KAAEC,KAAE,IAAES,GAAE,qBAAmBA,GAAE,aAAYK,KAAEM,OAAIR,MAAGb,OAAIK,MAAGM,GAAE,IAAEU,MAAG,KAAGV,GAAE,IAAEX,MAAG,GAAEgB,KAAE,IAAIhB,GAAE,EAAC,IAAGT,GAAE,OAAKA,GAAE,QAAME,IAAE,IAAGF,GAAE,OAAKA,GAAE,QAAMK,IAAE,kBAAiBL,GAAE,iBAAgB,CAAC,GAAE+B,KAAE,CAAC/B,GAAE,iBAAiB,OAAO,KAAK,WAAW,gBAAgB,GAAE,EAAC,qBAAoBC,GAAC,IAAEK;AAAE,QAAG,CAACyB,MAAGP,MAAG,MAAID,GAAE,YAAY,UAAQO,OAAI5B,MAAGO,OAAIJ,MAAGO,GAAE,MAAIa,GAAE,KAAGb,GAAE,MAAIa,GAAE,EAAE,QAAM,EAAC,QAAOzB,IAAE,WAAUa,IAAE,YAAWU,GAAE,YAAY,CAAC,EAAC;AAAE,UAAMM,KAAEZ,MAAG,EAAEd,GAAEU,GAAE,gBAAgB,CAAC,GAAEa,KAAEpB,GAAE,mCAAiC,KAAK,WAAW,SAAS,WAAW,QAAQ;AAAE,IAAAoB,MAAG,CAAC,KAAK,oBAAkB,MAAMzB,GAAE;AAAE,UAAM,IAAE,KAAK,mBAAiB,MAAM,KAAK,iBAAiB,wBAAwB,EAAC,iBAAgBD,IAAE,iBAAgBuB,GAAE,QAAO,WAAUE,GAAE,OAAO,GAAE,qBAAoBxB,IAAE,iBAAgBc,IAAE,eAAcR,KAAE,KAAGsB,IAAE,YAAW,UAAK,KAAK,SAAS,4BAA2B,gBAAeH,GAAC,GAAEpB,EAAC,IAAEP,GAAE,EAAC,iBAAgBC,IAAE,iBAAgBuB,GAAE,QAAO,WAAUE,IAAE,qBAAoBxB,IAAE,iBAAgBc,IAAE,eAAcR,KAAE,KAAGsB,IAAE,YAAW,OAAG,gBAAeH,GAAC,CAAC;AAAE,QAAI;AAAE,UAAM,IAAE,CAACpB,GAAE,gBAAe0B,KAAE,EAAC,MAAK,EAAE,QAAQ,CAAC,GAAE,MAAK,EAAE,QAAQ,CAAC,EAAC,GAAEC,KAAE,EAAE,KAAK,4BAA4BvB,IAAEa,GAAE,OAAO,IAAI,CAAC,GAAE,EAAC,aAAYW,IAAE,YAAWC,IAAE,mBAAkBC,GAAC,IAAEb;AAAE,QAAI,IAAE;AAAK,QAAG,KAAK,kBAAiB;AAAC,YAAMvB,KAAE,MAAM,KAAK,iBAAiB,mBAAmB,EAAC,gBAAekC,IAAE,eAAcC,IAAE,eAAc,IAAE,EAAC,OAAMjC,IAAE,QAAOG,GAAC,IAAE,MAAK,OAAM,IAAE,EAAE,eAAa,MAAK,eAAc,IAAE2B,KAAE,MAAK,mBAAkBN,IAAE,SAAQA,KAAE,EAAE,UAAQ,MAAK,MAAK,gBAAc,KAAK,WAAW,UAAS,eAAcpB,GAAE,eAAc,eAAc2B,IAAE,aAAY,KAAI,GAAE3B,EAAC;AAAE,OAAC,EAAC,YAAW,GAAE,sBAAqB,EAAC,IAAEN;AAAA,IAAE,OAAK;AAAC,YAAMA,KAAE6B,GAAEK,IAAEC,IAAE,EAAC,eAAcF,GAAC,CAAC;AAAE,UAAE,IAAE,EAAEjC,IAAE,EAAC,OAAME,IAAE,QAAOG,GAAC,GAAE,EAAE,cAAa2B,IAAE1B,GAAE,aAAa,IAAEN,IAAE0B,MAAG,EAAE,YAAU,IAAE,EAAE,EAAC,OAAMxB,IAAE,QAAOG,GAAC,GAAE,EAAE,OAAO,GAAE,IAAEO,GAAE,GAAE,KAAK,WAAW,UAAS,CAAC;AAAA,IAAE;AAAC,WAAON,GAAE,kBAAgBoB,KAAE,EAAC,WAAUb,IAAE,YAAW,GAAE,eAAc,GAAE,sBAAqB,GAAE,QAAOb,IAAE,mBAAkBoC,GAAC,IAAE,EAAC,WAAUvB,IAAE,QAAOb,IAAE,YAAW,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeA,IAAEE,IAAEG,IAAEC,IAAE;AAAC,UAAK,EAAC,QAAOC,IAAE,eAAcH,GAAC,IAAE,KAAK,WAAW,aAAY,EAAC,YAAWiB,IAAE,aAAYX,GAAC,IAAE,KAAK,oBAAoBV,EAAC;AAAE,QAAG,EAAC,GAAEW,KAAE,GAAEC,GAAC,IAAEV,IAAE,EAAC,OAAMW,IAAE,QAAOS,IAAE,WAAUR,GAAC,IAAET;AAAE,UAAMU,KAAE,KAAK,4BAA4Bf,IAAE,CAAC;AAAE,IAAAM,GAAE,WAASK,OAAGL,GAAE,OAAO,MAAKM,MAAGN,GAAE,OAAO,MAAKO,MAAG,IAAEP,GAAE,OAAO,MAAKgB,MAAG,IAAEhB,GAAE,OAAO;AAAM,QAAIU,KAAE,GAAEC,KAAE,GAAEE,KAAE;AAAE,QAAGL,MAAG,EAAEC,EAAC,GAAE;AAAC,OAAC,EAAC,4BAA2BE,IAAE,oBAAmBE,IAAE,cAAaH,GAAC,IAAED;AAAG,MAAAE,KAAEF,GAAE,aAAWC,MAAGL,MAAEE,OAAIG,KAAE;AAAA,IAAE;AAAC,UAAMI,KAAE,KAAK,MAAMT,MAAEU,EAAC,GAAEE,KAAE,KAAK,MAAMX,KAAEF,EAAC,GAAEoB,KAAE,KAAK,OAAOnB,MAAEE,KAAEG,KAAE,KAAGK,EAAC,GAAEZ,KAAE,KAAK,OAAOG,KAAEU,KAAEN,KAAE,KAAGN,EAAC,GAAEc,KAAEpB,GAAEJ,EAAC;AAAE,QAAG,CAACwB,GAAE,QAAO;AAAK,UAAK,EAAC,QAAOC,IAAE,QAAOM,IAAE,QAAO9B,IAAE,QAAO4B,GAAC,IAAEL;AAAE,QAAG,MAAIV,OAAIL,KAAEgB,MAAGK,KAAEC,MAAGR,KAAEM,MAAGT,KAAEnB,IAAG,QAAO;AAAK,UAAMyB,KAAE,IAAI;AAAM,QAAI,IAAE;AAAG,UAAM,IAAE,QAAM,KAAK,SAAS,mBAAiBpB,GAAE,mBAAiB,KAAK,SAAS;AAAiB,aAAQ0B,KAAET,IAAES,MAAGvB,IAAEuB,KAAI,UAAQ9B,KAAEkB,IAAElB,MAAG4B,IAAE5B,MAAI;AAAC,UAAIG,KAAEH;AAAE,UAAG,CAACI,GAAE,qBAAmBQ,MAAG,EAAEC,EAAC,KAAGE,MAAGf,OAAIG,KAAEH,KAAEe,KAAEE,KAAGa,MAAGP,MAAGpB,MAAG0B,MAAGF,MAAGG,MAAG/B,MAAGI,IAAE;AAAC,cAAMH,KAAE,KAAK,cAAcF,IAAEgC,IAAE3B,IAAEC,EAAC;AAAE,YAAEoB,GAAE,KAAK,IAAI,QAAS,CAAA1B,OAAG;AAAC,UAAAE,GAAE,KAAM,CAAAA,OAAGF,GAAEE,EAAC,CAAE,EAAE,MAAO,MAAI;AAAC,gBAAE,MAAGF,GAAE,IAAI;AAAA,UAAC,CAAE;AAAA,QAAC,CAAE,CAAC,IAAE0B,GAAE,KAAKxB,EAAC;AAAA,MAAC,MAAM,CAAAwB,GAAE,KAAK,QAAQ,QAAQ,IAAI,CAAC;AAAA,IAAC;AAAC,QAAG,MAAIA,GAAE,OAAO,QAAO;AAAK,UAAM,IAAE,MAAM,QAAQ,IAAIA,EAAC,GAAEC,KAAE,EAAC,SAAQlB,KAAEc,KAAE,KAAGb,IAAE,QAAOoB,KAAEV,KAAE,KAAGC,GAAC,GAAE,EAAC,kBAAiBO,GAAC,IAAE,KAAK,YAAWV,KAAE,KAAK,oBAAoBlB,EAAC,GAAE,EAAC,GAAE,GAAE,GAAEqC,GAAC,IAAEnB;AAAE,WAAM,EAAC,QAAO,IAAIT,GAAE,EAAC,MAAKF,GAAE,IAAEa,KAAEC,KAAE,GAAE,MAAKd,GAAE,KAAGuB,KAAE,KAAGT,KAAE,GAAE,MAAKd,GAAE,KAAGE,KAAE,KAAGC,KAAE2B,IAAE,MAAK9B,GAAE,IAAEgB,KAAEb,KAAE2B,IAAE,kBAAiBT,GAAC,CAAC,GAAE,aAAY,GAAE,YAAWD,IAAE,mBAAkB,EAAC;AAAA,EAAC;AAAA,EAAC,cAAc3B,IAAEE,IAAEG,IAAEC,IAAE;AAAC,UAAMC,KAAE,KAAK,WAAW,YAAY,cAAcP,EAAC;AAAE,QAAG,CAACO,GAAE,QAAO,QAAQ,QAAQ,IAAI;AAAE,UAAK,EAAC,QAAOC,IAAE,QAAOa,IAAE,QAAOX,IAAE,QAAOC,IAAC,IAAEJ;AAAE,QAAGL,KAAEM,MAAGH,KAAEgB,MAAGnB,KAAES,OAAGN,KAAEK,GAAE,QAAO,QAAQ,QAAQ,IAAI;AAAE,UAAMG,KAAEQ,GAAE,KAAK,KAAIf,GAAE,OAAO,GAAEgB,KAAE,GAAGtB,EAAC,IAAIE,EAAC,IAAIG,EAAC;AAAG,QAAIS,KAAEG,GAAEJ,IAAEP,GAAE,YAAWgB,EAAC;AAAE,QAAG,EAAER,EAAC,GAAE;AAAC,YAAMP,KAAE,IAAI;AAAgB,MAAAO,KAAE,KAAK,aAAad,IAAEE,IAAEG,IAAE,EAAC,GAAGC,IAAE,QAAOC,GAAE,OAAM,CAAC,GAAEe,GAAET,IAAEP,GAAE,YAAWgB,IAAER,IAAEP,EAAC,GAAEO,GAAE,MAAO,MAAIE,GAAEH,IAAEP,GAAE,YAAWgB,EAAC,CAAE;AAAA,IAAC;AAAC,WAAOhB,GAAE,UAAQyB,GAAEzB,IAAG,MAAI;AAAC,MAAAM,GAAEC,IAAEP,GAAE,YAAWgB,EAAC;AAAA,IAAC,CAAE,GAAER;AAAA,EAAC;AAAA,EAAC,qBAAqBd,IAAE;AAJrkd;AAIskd,UAAK,EAAC,WAAUE,IAAE,UAASG,GAAC,IAAE,KAAK;AAAW,QAAG,EAAE,MAAIH,MAAG,oBAAkBG,MAAG,gBAAcA,OAAI,OAAIL,MAAA,gBAAAA,GAAG,WAAQ,GAAC,KAAAA,GAAE,CAAC,MAAH,mBAAM,QAAO,QAAO;AAAK,UAAMM,KAAEN,GAAE,CAAC,EAAE;AAAO,QAAG,oBAAkBK,IAAE;AAAC,YAAMH,KAAEF,GAAE,CAAC,EAAE,IAAK,CAAAA,QAAIA,KAAE,OAAK,GAAI;AAAE,aAAM,CAACA,GAAE,CAAC,GAAEE,EAAC;AAAA,IAAC;AAAC,UAAK,CAACK,IAAEC,EAAC,IAAER,IAAEI,KAAE,CAAC,GAAEiB,KAAE,CAAC;AAAE,aAAQX,KAAE,GAAEA,KAAEJ,IAAEI,MAAI;AAAC,YAAK,CAACV,IAAEE,EAAC,IAAEW,GAAE,CAACN,GAAEG,EAAC,GAAEF,GAAEE,EAAC,CAAC,CAAC;AAAE,MAAAN,GAAE,KAAKJ,EAAC,GAAEqB,GAAE,KAAKnB,EAAC;AAAA,IAAC;AAAC,WAAM,CAACE,IAAEiB,EAAC;AAAA,EAAC;AAAA,EAAC,4BAA4BrB,IAAEE,IAAE;AAAC,WAAO,QAAM,KAAK,6BAA2B,KAAK,2BAAyBoC,GAAE,KAAK,UAAU,IAAG,EAAE,KAAK,yBAAyB,YAAY,IAAE,OAAK,EAAC,QAAOpC,IAAE,gBAAe,KAAK,yBAAyB,gBAAe,uBAAsB,KAAK,yBAAyB,uBAAsB,GAAG,KAAK,yBAAyB,aAAaF,EAAC,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAEE,IAAEG,IAAEC,KAAE,CAAC,GAAE;AAAC,UAAMC,KAAE,EAAC,qBAAoBD,GAAE,qBAAoB,cAAa,GAAE,mBAAkB,MAAK,WAAU,MAAK,WAAU,GAAE,eAAc,MAAK,UAAS,EAAC;AAAE,IAAAA,GAAE,kBAAgBC,GAAE,gBAAcD,GAAE,eAAc,KAAK,sBAAsBN,IAAEO,EAAC;AAAG,UAAMC,KAAE,KAAK,WAAW,YAAY,uBAAqB,GAAE,EAAC,UAASJ,IAAE,WAAUiB,IAAE,cAAaX,GAAC,IAAEH,IAAEI,MAAEP,KAAEF,IAAEU,KAAES,KAAEhB,IAAEQ,KAAEH,KAAEF,MAAGG,MAAEC,MAAG,IAAGU,KAAEZ,OAAIF,MAAG,KAAK,wBAAwBJ,IAAEiB,IAAEnB,IAAEG,EAAC;AAAE,QAAGQ,MAAGS,OAAI,MAAIlB,MAAG,MAAIiB,KAAG;AAAC,YAAMjB,KAAE,IAAIK,GAAE,EAAC,IAAGT,GAAE,OAAKA,GAAE,QAAME,IAAE,IAAGF,GAAE,OAAKA,GAAE,QAAMK,IAAE,kBAAiBL,GAAE,iBAAgB,CAAC;AAAE,UAAIqB,KAAE,EAAEjB,IAAE,KAAK,WAAW,kBAAiBJ,IAAEO,GAAE,mBAAmB;AAAE,YAAMe,KAAE,CAACD,MAAGf,GAAE,iBAAee,GAAE,IAAEA,GAAE,IAAEf,GAAE,cAAc,IAAEA,GAAE,cAAc;AAAE,UAAGO,MAAGP,GAAE,iBAAegB,IAAE;AAAC,cAAMtB,KAAE,KAAK,MAAM,KAAK,IAAI,KAAK,IAAIW,KAAEC,EAAC,CAAC,IAAE,KAAK,GAAG,IAAE;AAAE,YAAGJ,KAAEE,KAAE,KAAGV,IAAE;AAAC,gBAAME,KAAE,KAAGF;AAAE,UAAAqB,KAAE,EAAC,GAAEf,GAAE,cAAc,IAAEJ,IAAE,GAAEI,GAAE,cAAc,IAAEJ,GAAC;AAAA,QAAC;AAAA,MAAC;AAAC,MAAAmB,OAAId,GAAE,gBAAcc,IAAE,KAAK,sBAAsBrB,IAAEO,EAAC;AAAA,IAAE;AAAC,WAAO,KAAK,wBAAwBA,GAAE,UAASA,GAAE,WAAUL,IAAEG,EAAC,MAAIE,GAAE,WAAS,GAAEA,GAAE,YAAU,IAAGA;AAAA,EAAC;AAAA,EAAC,wBAAwBP,IAAEE,IAAEG,IAAEC,IAAE;AAAC,UAAK,EAAC,UAASC,GAAC,IAAE,KAAK,WAAW;AAAY,WAAO,KAAK,KAAKP,KAAEO,GAAE,KAAK,CAAC,CAAC,IAAE,KAAK,KAAKL,KAAEK,GAAE,KAAK,CAAC,CAAC,KAAG,KAAGP,KAAEK,KAAEN,MAAGG,KAAEI,KAAEP;AAAA,EAAC;AAAA,EAAC,sBAAsBC,IAAEE,IAAE;AAAC,IAAAA,GAAE,WAAS,GAAEA,GAAE,YAAU;AAAE,UAAMG,KAAE,KAAK,WAAW,kBAAiB,EAAC,eAAcC,IAAE,qBAAoBC,GAAC,IAAEL,IAAE,EAAC,cAAaM,IAAE,mBAAkBJ,IAAE,kBAAiBM,GAAC,IAAE,GAAEJ,IAAE,KAAK,YAAW,KAAK,SAAS,QAAQ;AAAE,QAAGI,GAAE;AAAO,QAAIC,MAAET,GAAE,aAAW,EAAEF,IAAEK,IAAEE,EAAC;AAAE,QAAG,QAAMI,IAAE;AAAO,UAAMC,KAAE,EAAE,KAAK,WAAW,SAAS;AAAE,IAAAA,OAAID,MAAEC,GAAE,iBAAiBD,GAAC,IAAGT,GAAE,YAAUS;AAAE,UAAME,KAAE,KAAK,MAAMF,IAAE,OAAKA,IAAE,QAAMP,GAAE,IAAE,GAAE,GAAEkB,KAAE,KAAK,MAAMX,IAAE,OAAKA,IAAE,QAAMP,GAAE,IAAE,GAAE;AAAE,IAAAF,GAAE,eAAaM,IAAEN,GAAE,oBAAkBE,IAAEF,GAAE,WAASW,IAAEX,GAAE,YAAUoB;AAAA,EAAC;AAAA,EAAC,8BAA8BtB,IAAE;AAAC,WAAO,EAAE,KAAK,WAAW,oBAAoB,KAAG,QAAMA,GAAE,YAAUA,KAAE,EAAC,GAAGA,IAAE,SAAQ,KAAK,cAAcA,GAAE,0BAA0B,EAAC,IAAGA;AAAA,EAAC;AAAA,EAAC,uBAAuBA,IAAEE,IAAE;AAAC,UAAK,EAAC,aAAYG,IAAE,UAASC,IAAE,cAAaC,IAAE,mBAAkBG,GAAC,IAAER,IAAES,MAAEX,GAAE,OAAO,CAAC,EAAE,SAAOA,GAAE,QAAMA,GAAE;AAAO,QAAG,EAAE,CAACA,GAAE,QAAMA,GAAE,KAAKM,EAAC,GAAG,QAAM,EAAC,UAASD,IAAE,OAAM,KAAI;AAAE,UAAK,EAAC,sBAAqBO,GAAC,IAAE,KAAK;AAAW,QAAG,EAAEA,EAAC,KAAG,CAACF,IAAE;AAAC,YAAMR,KAAEF,GAAE,OAAO,IAAK,CAAAA,OAAGA,GAAEM,EAAC,CAAE,GAAEE,KAAE,EAAC,UAASH,IAAE,OAAMH,IAAE,cAAaK,GAAC,GAAEH,KAAE,KAAK,qBAAqBF,GAAE,IAAK,CAAAF,OAAG,CAACA,EAAC,CAAE,CAAC;AAAE,cAAOI,MAAA,gBAAAA,GAAG,YAASI,GAAE,cAAYJ,GAAE,IAAK,CAAAJ,OAAGA,GAAE,CAAC,CAAE,IAAGQ;AAAA,IAAC;AAAC,QAAIK,KAAEb,GAAE,OAAO,IAAK,CAAAA,OAAGA,GAAE,MAAMM,KAAEK,KAAEL,KAAEK,MAAEA,GAAC,CAAE,GAAEW,KAAE,KAAK,qBAAqBT,EAAC;AAAE,UAAK,EAAC,mBAAkBC,IAAE,iBAAgBC,GAAC,IAAEb;AAAE,QAAIc,KAAEK,GAAET,IAAEG,GAAE,sBAAsB;AAAE,QAAGD,IAAE;AAAC,YAAMd,KAAEI,GAAEY,IAAE,EAAED,GAAE,0BAA0B,GAAE,EAAEA,GAAE,UAAU,CAAC;AAAE,MAAAF,KAAEA,GAAE,IAAK,CAAAX,OAAGF,GAAE,IAAK,CAAAA,OAAGE,GAAEF,EAAC,CAAE,CAAE,GAAEsB,KAAEA,MAAA,gBAAAA,GAAG,IAAK,CAAApB,OAAGF,GAAE,IAAK,CAAAA,OAAGE,GAAEF,EAAC,CAAE,IAAIgB,KAAEhB,GAAE,IAAK,CAAAA,OAAGgB,GAAEhB,EAAC,CAAE;AAAA,IAAC;AAAC,UAAMmB,KAAEnB,GAAE,gBAAc,KAAK,WAAW,aAAYoB,KAAE,EAAC,QAAOP,IAAE,WAAUb,GAAE,UAAS;AAAE,QAAIuB;AAAE,MAAEJ,EAAC,MAAI,EAAEC,IAAED,EAAC,GAAEI,KAAEH,GAAE;AAAM,WAAM,EAAC,UAASf,IAAE,OAAM,MAAK,YAAWW,GAAE,IAAK,CAAChB,IAAEE,OAAI;AAAC,YAAMG,KAAE,EAAC,OAAM,OAAIkB,MAAA,gBAAAA,GAAIrB,OAAG,OAAKW,GAAE,IAAK,CAAAb,OAAGA,GAAEE,EAAC,CAAE,GAAE,4BAA2BF,GAAE,2BAA2B,IAAK,CAAAA,OAAG,IAAIe,GAAE,EAAC,GAAGf,IAAE,SAAQ,KAAE,CAAC,CAAE,EAAC;AAAE,cAAOsB,MAAA,gBAAAA,GAAG,YAASjB,GAAE,cAAY,CAACiB,GAAE,CAAC,EAAEpB,EAAC,GAAEoB,GAAE,CAAC,EAAEpB,EAAC,CAAC,IAAGG;AAAA,IAAC,CAAE,GAAE,cAAaE,GAAC;AAAA,EAAC;AAAC;AAAEP,GAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,4BAA2B,MAAM,GAAEA,GAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,4BAA2B,IAAI,GAAEA,GAAE,CAAC,EAAEa,EAAC,CAAC,GAAE,EAAE,WAAU,OAAM,IAAI,GAAEb,GAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAEA,GAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,8BAA6B,MAAM,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,YAAW,MAAM,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,cAAa,MAAM,GAAE,IAAEA,GAAE,CAAC,EAAE,+CAA+C,CAAC,GAAE,CAAC;AAAE,IAAMuC,KAAE;;;ACApwkB,IAAIC,KAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,gBAAc,YAAW,KAAK,WAAS,UAAS,KAAK,iBAAe;AAAA,EAAI;AAAA,EAAC,MAAM,KAAKC,IAAE;AAJ7jB;AAI8jB,UAAM,KAAK,KAAK;AAAE,UAAK,EAAC,gBAAeC,GAAC,IAAE;AAAK,sBAAK,mBAAL,mBAAqB,YAArB,mBAA8B,UAAOA,GAAE,gBAAc,KAAK,eAAe,UAAQ,KAAK,iBAAeA,GAAE,kBAAkB;AAAE,UAAK,EAAC,SAAQC,IAAE,WAAUC,GAAC,IAAE,KAAK,gBAAeC,KAAEF,GAAE,IAAK,CAAAG,OAAGA,GAAE,aAAW,SAAOA,GAAE,KAAKL,EAAC,CAAE;AAAE,UAAM,QAAQ,IAAII,EAAC;AAAE,UAAME,KAAEJ,GAAE,IAAK,CAAC,EAAC,YAAWF,GAAC,MAAIA,EAAE,GAAEF,KAAEG,GAAE,KAAK,EAAC,aAAYK,IAAE,WAAUH,GAAC,CAAC;AAAE,QAAG,CAACL,GAAE,WAAS,MAAIQ,GAAE,OAAO,OAAM,IAAIJ,GAAE,wBAAuB,6BAA6BJ,GAAE,SAAO,EAAE,EAAE;AAAE,UAAM,KAAK,eAAe;AAAE,UAAMS,MAAED,GAAE,CAAC;AAAE,SAAK,6BAA2B,MAAIA,GAAE,UAAQA,GAAE,MAAM,CAAC,EAAE,MAAO,CAAAN,OAAG,KAAK,oBAAoBA,IAAEO,GAAC,CAAE,GAAE,KAAK,IAAI,cAAaL,GAAE,CAAC,EAAE,UAAU,GAAE,KAAK,IAAI,cAAaD,GAAE,UAAU;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAgB;AAJ7vC;AAI8vC,YAAO,UAAK,qBAAL,mBAAuB,qBAAqB,KAAK;AAAA,EAAe;AAAA,EAAC,MAAM,YAAYD,IAAEK,IAAEF,IAAEC,KAAE,CAAC,GAAE;AAJn2C;AAIo2C,UAAK,EAAC,SAAQE,IAAE,WAAUR,GAAC,IAAE,KAAK,gBAAeS,MAAED,GAAE,IAAK,CAAAL,OAAGA,GAAE,YAAYD,IAAEK,IAAEF,IAAEC,EAAC,CAAE,GAAEI,KAAE,MAAM,QAAQ,IAAID,GAAC,GAAEE,KAAED,GAAE,IAAK,CAAAR,OAAGA,GAAE,UAAW;AAAE,QAAGI,GAAE,sBAAoBK,GAAE,MAAO,CAAAT,OAAG,EAAEA,EAAC,CAAE,EAAE,QAAOQ,GAAE,CAAC;AAAE,UAAME,OAAE,KAAAF,GAAE,KAAM,CAAAR,OAAG,EAAEA,GAAE,UAAU,CAAE,MAA3B,mBAA8B,WAAQA,IAAEW,KAAE,KAAK,mBAAiB,MAAM,KAAK,iBAAiB,QAAQ,EAAC,QAAOD,IAAE,oBAAmBD,IAAE,kBAAiBX,GAAC,CAAC,IAAE,KAAK,eAAe,QAAQ,EAAC,QAAOY,IAAE,oBAAmBD,IAAE,kBAAiBX,GAAC,CAAC;AAAE,WAAM,EAAC,GAAGU,GAAE,CAAC,GAAE,YAAWG,GAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBX,IAAEK,IAAE;AAAC,UAAK,EAAC,aAAYJ,IAAE,WAAUC,IAAE,kBAAiBC,IAAE,QAAOC,GAAC,IAAEJ,IAAE,EAAC,aAAYM,IAAE,WAAUR,IAAE,kBAAiBS,KAAE,QAAOC,GAAC,IAAEH;AAAE,WAAOH,GAAE,MAAIJ,GAAE,KAAGI,GAAE,MAAIJ,GAAE,KAAGK,GAAE,OAAOI,GAAC,KAAGH,GAAE,OAAOI,EAAC,KAAGP,GAAE,gBAAcK,GAAE,eAAaL,GAAE,eAAaK,GAAE,cAAYL,GAAE,wBAAsBK,GAAE;AAAA,EAAmB;AAAC;AAAEL,GAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEH,GAAE,WAAU,iBAAgB,MAAM,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,YAAW,MAAM,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,kBAAiB,MAAM,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,kBAAiB,MAAM,GAAEA,KAAEG,GAAE,CAAC,EAAE,mDAAmD,CAAC,GAAEH,EAAC;AAAE,IAAMS,KAAET;;;ACAvnB,IAAMc,KAAE,EAAE,UAAU,qCAAqC;AAAzD,IAA2DC,KAAE,CAAAC,OAAG;AAAC,MAAID,KAAE,cAAcC,GAAC;AAAA,IAAC,eAAeC,IAAE;AAJz2D;AAI02D,YAAM,GAAGA,EAAC,GAAE,KAAK,mCAAiC,OAAG,KAAK,oBAAkB,EAAC,UAAS,MAAK,UAAS,GAAE,mBAAkB,KAAI,GAAE,KAAK,UAAQ,MAAK,KAAK,YAAU,MAAK,KAAK,gBAAc,WAAU,KAAK,yBAAuB,MAAK,KAAK,SAAO,MAAK,KAAK,iBAAe,MAAK,KAAK,aAAW,MAAK,KAAK,aAAW,MAAK,KAAK,mBAAiB,MAAK,KAAK,aAAW,MAAK,KAAK,mCAAiC,iBAAa,WAAAA,GAAE,CAAC,MAAH,mBAAM,WAAN,mBAAc;AAAA,IAAa;AAAA,IAAC,IAAI,aAAY;AAJ/yE;AAIgzE,cAAO,UAAK,eAAL,mBAAiB;AAAA,IAAM;AAAA,IAAC,IAAI,2BAA2BA,IAAE;AAAC,WAAK,KAAK,8BAA6BA,EAAC,GAAE,KAAK,eAAe;AAAA,IAAC;AAAA,IAAC,IAAI,WAAU;AAJ/7E;AAIg8E,cAAO,UAAK,eAAL,mBAAiB,YAAY;AAAA,IAAQ;AAAA,IAAC,IAAI,IAAIA,IAAE;AAAC,WAAK,KAAK,OAAM,EAAEA,IAAEH,EAAC,CAAC;AAAA,IAAC;AAAA,IAAC,IAAI,SAASG,IAAE;AAAC,WAAK,KAAK,YAAWA,EAAC,GAAE,KAAK,eAAe;AAAA,IAAC;AAAA,IAAC,MAAM,uBAAuBA,IAAEC,IAAE;AAAC,UAAG,EAAED,EAAC,KAAG,CAAC,KAAK,WAAW,QAAO;AAAK,YAAME,KAAE,KAAK,kBAAkB,UAASC,KAAE,KAAK,WAAW;AAAS,aAAOD,KAAEA,GAAE,uBAAuB,EAAC,YAAWF,IAAE,UAASG,GAAC,GAAEF,EAAC,IAAE,EAAED,IAAEG,EAAC;AAAA,IAAC;AAAA,IAAC,MAAM,eAAeH,IAAEC,IAAE;AAAC,YAAMC,KAAE,KAAK,kBAAkB;AAAS,aAAOA,KAAEA,GAAE,eAAeF,IAAEC,EAAC,IAAEG,GAAEJ,GAAE,UAASA,GAAE,oBAAmBA,GAAE,UAAS,EAAEC,GAAE,MAAM,IAAEA,GAAE,SAAQ,IAAI,kBAAiB,MAAM;AAAA,IAAC;AAAA,IAAC,4BAA4BD,IAAE;AAJ9/F;AAI+/F,YAAK,EAAC,sBAAqBC,GAAC,IAAE,KAAK,cAAY,CAAC;AAAE,UAAG,EAAEA,EAAC,EAAE,QAAOD;AAAE,UAAIE,KAAEF,GAAE,8BAA4B,KAAK;AAA2B,OAAC,EAAEE,EAAC,KAAGA,GAAE,WAASA,KAAE,EAAE,KAAK,OAAO,YAAW,EAAC,wBAAuB,KAAK,uBAAsB,CAAC;AAAG,YAAMC,KAAEH,GAAE,cAAY,KAAK;AAAW,UAAG,EAAEE,EAAC,KAAG,EAAEC,EAAC,MAAI,EAAEA,GAAE,KAAK,KAAG,EAAEA,GAAE,GAAG,IAAG;AAAC,QAAAD,KAAEA,GAAE,IAAK,CAAAF,OAAGA,GAAE,MAAM,CAAE;AAAE,cAAMD,MAAE,WAAAE,GAAE,UAAU,KAAM,CAAC,EAAC,MAAKD,GAAC,MAAIA,OAAIE,GAAE,CAAC,EAAE,YAAa,MAApD,mBAAuD,eAAvD,mBAAmE,KAAM,CAAC,EAAC,MAAKF,GAAC,MAAI,cAAYA,KAAIK,KAAEH,GAAE,KAAM,CAAC,EAAC,eAAcF,GAAC,MAAI,cAAYA,EAAE;AAAE,YAAG,CAACD,MAAG,CAACM,GAAE,QAAM,EAAC,GAAGL,IAAE,4BAA2B,KAAI;AAAE,cAAK,EAAC,OAAMM,IAAE,KAAIC,GAAC,IAAEJ,IAAEK,KAAE,EAAEF,EAAC,IAAE,OAAKA,GAAE,QAAQ,GAAEG,KAAE,EAAEF,EAAC,IAAE,OAAKA,GAAE,QAAQ,GAAEG,MAAEF,MAAGC,IAAEE,KAAEF,MAAGD;AAAE,YAAG,EAAET,GAAE,MAAM,GAAE;AAAC,gBAAMC,KAAED,GAAE,OAAO,OAAQ,CAAAC,OAAG;AAAC,gBAAG,MAAM,QAAQA,EAAC,GAAE;AAAC,kBAAGU,QAAIC,GAAE,QAAOX,GAAE,CAAC,KAAGU,OAAGV,GAAE,CAAC,KAAGU;AAAE,oBAAMT,KAAED,GAAE,CAAC,KAAGU,OAAGV,GAAE,CAAC,IAAEU,OAAGV,GAAE,CAAC,IAAEW,MAAGX,GAAE,CAAC,KAAGW,IAAET,KAAEF,GAAE,CAAC,KAAGU,OAAGV,GAAE,CAAC,KAAGW,MAAGX,GAAE,CAAC,IAAEU,OAAGV,GAAE,CAAC,IAAEW;AAAE,qBAAOV,MAAGC;AAAA,YAAC;AAAC,mBAAOQ,QAAIC,KAAEX,OAAIU,MAAEV,MAAGU,OAAGV,MAAGW;AAAA,UAAC,CAAE;AAAE,cAAGX,GAAE,QAAO;AAAC,kBAAMC,KAAED,GAAE,KAAM,CAACA,IAAEC,OAAI;AAAC,kBAAGS,QAAIC,GAAE,SAAOX,GAAE,CAAC,KAAGA,OAAIC,GAAE,CAAC,KAAGA;AAAG,qBAAO,KAAK,KAAKD,GAAE,CAAC,KAAGA,MAAGW,EAAC,IAAE,KAAK,KAAKV,GAAE,CAAC,KAAGA,MAAGU,EAAC;AAAA,YAAC,CAAE,EAAE,CAAC;AAAE,YAAAN,GAAE,SAAO,CAACJ,EAAC;AAAA,UAAC,MAAM,CAAAC,KAAE;AAAA,QAAI,WAASH,GAAE,uBAAqBA,GAAE,QAAO;AAAC,gBAAK,CAACC,IAAEC,EAAC,IAAEF,GAAE;AAAO,UAAAW,MAAET,MAAGU,KAAEX,KAAEE,KAAE,OAAKG,GAAE,SAAOK,QAAIC,KAAE,CAACD,GAAC,IAAE,CAAC,KAAK,IAAIV,IAAEU,GAAC,GAAE,KAAK,IAAIT,IAAEU,EAAC,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC,aAAO,EAAET,EAAC,KAAGM,GAAEN,IAAE,KAAK,sBAAsB,IAAE,EAAC,GAAGF,IAAE,4BAA2B,KAAI,IAAE,EAAC,GAAGA,IAAE,4BAA2BE,GAAC;AAAA,IAAC;AAAA,IAAC,MAAM,uBAAsB;AAJjwI;AAIkwI,UAAG,mBAAiB,KAAK,QAAM,CAAC,KAAK,kBAAgB,CAAC,KAAK,6BAA2B,KAAK,UAAU,KAAK,cAAc,MAAI,KAAK,UAAU,KAAK,yBAAyB,EAAE;AAAO,UAAG,KAAK,oCAAkC,eAAa,KAAK,OAAO,eAAc;AAAC,cAAMF,KAAE,KAAK,OAAO,eAAe,OAAO;AAAE,eAAM,CAAC,KAAK,kBAAgBA,MAAG,KAAK,KAAK,kBAAiBY,GAAE,SAASZ,EAAC,CAAC,GAAE,MAAK,KAAK,6BAA0B,UAAK,mBAAL,mBAAqB;AAAA,MAAS;AAAC,UAAIA,IAAEC,KAAE,KAAK,QAAOC,KAAE;AAAG,qBAAaD,GAAE,iBAAeD,KAAEC,GAAE,eAAe,SAAQA,KAAED,GAAE,CAAC,GAAEE,KAAE,QAAIF,KAAE,CAACC,EAAC;AAAE,YAAK,EAAC,gBAAeE,GAAC,IAAE;AAAK,UAAGA,IAAE;AAAC,cAAMD,KAAE,EAAC,QAAOD,GAAC;AAAE,QAAAD,GAAE,SAAO,KAAGA,GAAE,QAAS,CAAAA,OAAGE,GAAEF,GAAE,GAAG,IAAEA,EAAE;AAAE,cAAMD,KAAEc,GAAEV,GAAE,4BAA0BA,GAAE,OAAO,GAAED,EAAC,GAAEY,MAAE,IAAIJ,GAAE,EAAC,gBAAeX,GAAC,CAAC;AAAE,QAAAe,IAAE,mBAAiB,KAAK,kBAAkB,UAAS,MAAMA,IAAE,KAAK,GAAE,KAAK,6BAA0B,UAAK,mBAAL,mBAAqB,UAAS,KAAK,SAAOA;AAAA,MAAC,MAAM,MAAK,SAAOb,IAAE,KAAK,4BAA0B;AAAK,UAAG,KAAK,sBAAoB,MAAK,CAACC,MAAG,CAACC,GAAE;AAAO,YAAK,EAAC,SAAQJ,GAAC,IAAE,MAAK,EAAC,WAAUe,GAAC,IAAE,KAAK,OAAO,YAAWC,MAAEhB,MAAA,gBAAAA,GAAG,UAAOA,GAAE,KAAM,CAAAC,OAAGA,MAAGc,EAAE,IAAEA,MAAG;AAAE,MAAAf,OAAIgB,MAAG,qBAAmB,KAAK,SAAS,SAAO,KAAK,KAAK,WAAU,IAAI,GAAE,KAAK,uBAAuB,MAAM;AAAA,IAAC;AAAA,IAAC,MAAM,iBAAgB;AAJp6K;AAIq6K,YAAK,EAAC,QAAOf,IAAE,YAAWC,GAAC,IAAE;AAAK,UAAG,CAACD,MAAG,CAACC,GAAE;AAAO,YAAK,EAAC,YAAWC,GAAC,IAAE,KAAK,QAAOC,MAAE,KAAAM,GAAEP,IAAE,EAAC,4BAA2B,KAAK,4BAA2B,wBAAuB,KAAK,uBAAsB,CAAC,MAAnH,mBAAsH,MAAKH,KAAE,EAAE,EAAC,GAAG,KAAK,SAAS,OAAO,GAAE,cAAaI,GAAC,CAAC;AAAE,UAAG,KAAK,UAAU,KAAK,mBAAmB,MAAI,KAAK,UAAUJ,EAAC,EAAE;AAAO,YAAMe,KAAE,KAAK,kBAAkB;AAAS,MAAAA,OAAIb,GAAE,aAAWe,GAAEd,IAAEC,EAAC,GAAEF,GAAE,eAAaF,IAAEE,GAAE,KAAK,GAAE,MAAMa,GAAE,iBAAiBb,EAAC,GAAE,KAAK,sBAAoBF;AAAA,IAAE;AAAA,IAAC,MAAM,cAAcC,IAAEC,IAAE;AAAC,YAAMC,KAAEF,MAAGA,GAAE;AAAW,UAAG,EAAE,EAAEE,EAAC,KAAGA,GAAE,UAAQA,GAAE,OAAO,SAAO,GAAG,QAAO;AAAK,UAAIC;AAAE,YAAM,KAAK,eAAe;AAAE,YAAMJ,KAAE,KAAK,kBAAkB,UAASe,KAAE,KAAK,WAAS,CAAC;AAAE,aAAOX,KAAEJ,KAAE,MAAMA,GAAE,UAAU,EAAC,GAAGC,IAAE,qBAAoBC,IAAE,SAAQa,GAAC,CAAC,IAAE,KAAK,WAAW,UAAU,EAAC,GAAGd,IAAE,qBAAoBC,IAAE,SAAQa,GAAC,CAAC,GAAEX;AAAA,IAAC;AAAA,IAAC,WAAWH,IAAEC,IAAEC,IAAE;AAAC,aAAM,uBAAqB,KAAK,OAAO,gBAAc,GAAG,KAAK,GAAG,SAASF,EAAC,IAAIC,EAAC,IAAIC,EAAC,KAAG;AAAA,IAAE;AAAA,IAAC,sBAAsBF,IAAEC,IAAEC,KAAE,OAAG;AAAC,UAAG,CAAC,KAAK,UAAQ,EAAED,EAAC,EAAE,QAAO;AAAK,UAAGC,MAAGF,GAAE,OAAO,KAAK,gBAAgB,EAAE,QAAO,KAAK;AAAS,YAAMG,KAAE,EAAEH,EAAC;AAAE,aAAO,EAAE,OAAO,EAAC,MAAK,KAAI,kBAAiBA,IAAE,QAAOG,KAAE,EAAC,GAAEA,GAAE,OAAO,CAAC,GAAE,GAAEA,GAAE,OAAO,CAAC,EAAC,IAAE,EAAC,GAAEF,GAAE,MAAK,GAAEA,GAAE,KAAI,EAAC,CAAC;AAAA,IAAC;AAAA,IAAC,wBAAwBD,IAAE;AAAC,aAAO,KAAK,UAAQ,KAAK,yBAAuB,KAAK,sBAAsB,iBAAiB,OAAOA,EAAC,MAAI,KAAK,wBAAsB,KAAK,OAAO,cAAcA,EAAC,IAAG,KAAK,yBAAuB;AAAA,IAAI;AAAA,IAAC,MAAM,UAAUA,IAAEC,IAAEE,IAAEJ,KAAE,CAAC,GAAE;AAAC,UAAGc,GAAE,IAAI,GAAEd,GAAE,uBAAsB;AAAC,cAAMe,KAAE,KAAK,WAAWd,IAAEC,IAAEE,EAAC;AAAE,eAAO,EAAEW,IAAE,EAAC,cAAa,SAAQ,OAAM,EAAC,GAAG,KAAK,mBAAkB,GAAG,KAAK,OAAO,SAAS,sBAAqB,GAAE,QAAOf,GAAE,OAAM,CAAC,EAAE,KAAM,CAAAC,OAAGA,GAAE,IAAK;AAAA,MAAC;AAAC,YAAK,EAAC,YAAWK,GAAC,IAAE;AAAK,UAAG,EAAEA,GAAE,oBAAoB,MAAIN,KAAE,KAAK,4BAA4BA,EAAC,GAAE,EAAEA,GAAE,0BAA0B,IAAG;AAAC,cAAMG,KAAEH,GAAE,YAAUM,GAAE,YAAY;AAAS,eAAM,EAAC,QAAO,KAAK,OAAO,0BAA0BL,IAAEC,IAAEE,IAAED,EAAC,GAAE,YAAW,KAAI;AAAA,MAAC;AAAC,aAAO,MAAM,KAAK,gBAAgB,GAAE,MAAM,KAAK,qBAAqB,GAAE,2BAAyB,KAAK,SAAS,SAAOH,KAAE,EAAC,GAAGA,IAAE,QAAO,EAAC,MAAK,GAAE,MAAK,EAAC,EAAC,IAAG,KAAK,OAAO,UAAUC,IAAEC,IAAEE,IAAEJ,EAAC;AAAA,IAAC;AAAA,IAAC,MAAM,YAAYC,IAAEC,IAAEC,IAAEC,KAAE,CAAC,GAAE;AAAC,aAAO,EAAE,KAAK,WAAW,oBAAoB,MAAIA,KAAE,KAAK,4BAA4BA,EAAC,GAAE,EAAEA,GAAE,0BAA0B,KAAG,EAAC,QAAOH,IAAE,YAAW,KAAI,KAAG,MAAM,KAAK,gBAAgB,GAAE,MAAM,KAAK,qBAAqB,GAAE,KAAK,OAAO,YAAYA,IAAEC,IAAEC,IAAEC,EAAC;AAAA,IAAE;AAAA,IAAC,MAAM,SAASH,IAAEC,KAAE,CAAC,GAAE;AAJhtP;AAIitP,YAAK,EAAC,QAAOC,IAAE,YAAWH,GAAC,IAAE;AAAK,UAAG,EAAEA,GAAE,oBAAoB,GAAE;AAAC,YAAG,EAAEA,GAAE,gCAA8B,CAAC,EAAE,EAAEE,GAAE,0BAA0B,KAAGA,GAAE,0BAAwBA,GAAE,iBAAeA,KAAE,KAAK,4BAA4BA,EAAC,GAAE,EAAEA,GAAE,0BAA0B,GAAG,QAAM,EAAC,UAASD,IAAE,OAAM,KAAI;AAAA,MAAC;AAAC,YAAMK,MAAE,UAAK,2BAAL,mBAA6B;AAAe,UAAGA,MAAG,CAACA,GAAE,SAASL,EAAC,EAAE,OAAM,IAAID,GAAE,+BAA8B,qFAAqF;AAAE,aAAOG,GAAE,SAASF,IAAEC,EAAC;AAAA,IAAC;AAAA,IAAC,gCAA+B;AAAC,WAAK,kBAAkB;AAAA,IAAU;AAAA,IAAC,gCAA+B;AAAC,WAAK,kBAAkB,YAAW,KAAK,kBAAkB,YAAU,KAAG,KAAK,oBAAoB;AAAA,IAAC;AAAA,IAAC,kBAAiB;AAJ/5Q;AAIg6Q,YAAMD,MAAE,UAAK,eAAL,mBAAiB;AAAqB,UAAG,EAAEA,EAAC,KAAG,sBAAkB,UAAK,eAAL,mBAAiB,UAAS,QAAM;AAAG,YAAMC,KAAE,KAAK,4BAA2BC,MAAE,KAAAD,MAAA,gBAAAA,GAAI,OAAJ,mBAAQ;AAAa,aAAOD,GAAE,UAAU,KAAM,CAAAA,OAAGA,GAAE,SAAOE,OAAI,EAACD,MAAA,gBAAAA,GAAI,GAAG,kBAAeD,GAAE,WAAW,KAAM,CAAAA,OAAG,cAAYA,GAAE,IAAK,EAAG;AAAA,IAAC;AAAA,IAAC,qBAAqBA,IAAE;AAAC,aAAO,IAAI,KAAK,MAAIA,KAAE,SAAO,OAAK,GAAG,EAAE,SAAS;AAAA,IAAC;AAAA,IAAC,mCAAmCA,IAAE;AAJxyR;AAIyyR,YAAMC,KAAED,QAAG,UAAK,eAAL,mBAAiB;AAAqB,aAAOiB,GAAE,KAAK,wBAAuBhB,EAAC;AAAA,IAAC;AAAA,IAAC,yBAAwB;AAAC,WAAK,4BAA4B,GAAE,KAAK,+BAA6B,KAAK,6BAA2B,EAAE,KAAK,OAAO,YAAW,EAAC,wBAAuB,KAAK,uBAAsB,CAAC,IAAG,KAAK,uBAAuB;AAAA,IAAC;AAAA,IAAC,kBAAiB;AAAC,UAAG,QAAM,KAAK,kBAAkB,kBAAkB,QAAO,KAAK,kBAAkB;AAAkB,YAAMD,KAAE,IAAIc;AAAE,aAAO,KAAK,kBAAkB,oBAAkBd,GAAE,WAAW,EAAE,KAAM,MAAI;AAAC,QAAAa,GAAE,IAAI,GAAE,KAAK,kBAAkB,WAASb,IAAE,KAAK,OAAO,mBAAiBA,IAAE,KAAK,YAAU,KAAK,eAAe,GAAE,eAAa,KAAK,OAAO,iBAAe,KAAK,OAAO,eAAe;AAAA,MAAC,CAAE,EAAE,MAAO,MAAI;AAAA,MAAC,CAAE,GAAE,KAAK,kBAAkB;AAAA,IAAiB;AAAA,IAAC,sBAAqB;AAAC,WAAK,kBAAkB,YAAU,KAAK,kBAAkB,SAAS,QAAQ,GAAE,KAAK,kBAAkB,WAAS,MAAK,KAAK,kBAAkB,oBAAkB,MAAK,KAAK,kBAAkB,WAAS,GAAE,KAAK,sBAAoB,MAAK,KAAK,WAAS,KAAK,OAAO,mBAAiB;AAAA,IAAK;AAAA,IAAC,8BAA6B;AAJ32T;AAI42T,UAAG,QAAM,KAAK,eAAc;AAAC,QAAAa,GAAE,IAAI;AAAE,cAAK,EAAC,QAAOb,GAAC,IAAE,MAAKC,KAAE,EAAED,GAAE,YAAWA,GAAE,WAAS,UAAK,eAAL,mBAAiB,uBAAuB;AAAE,aAAK,KAAK,iBAAgBC,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA,IAAC,uBAAuBD,KAAE,MAAK;AAJxiU;AAIyiU,MAAAa,GAAE,IAAI;AAAE,YAAK,EAAC,YAAWZ,GAAC,IAAE,KAAK;AAAO,OAAC,KAAK,WAASA,GAAE,YAAU,MAAI,KAAK,UAAQiB,GAAEjB,EAAC;AAAG,YAAMC,MAAE,KAAAO,GAAER,IAAE,EAAC,4BAA2B,KAAK,4BAA2B,wBAAuB,KAAK,uBAAsB,CAAC,MAAnH,mBAAsH;AAAK,UAAG,CAAC,KAAK,YAAU,eAAaD,IAAE;AAAC,cAAMA,KAAEmB,GAAElB,IAAE,EAAC,SAAQ,KAAK,SAAQ,cAAaC,GAAC,CAAC;AAAE,wBAAc,KAAK,OAAO,iBAAe,qBAAmBF,GAAE,YAAQ,KAAAC,GAAE,eAAF,mBAAe,GAAG,QAAK,KAAG,WAAO,KAAAA,GAAE,eAAF,mBAAe,GAAG,QAAK,KAAG,WAASD,GAAE,yBAAuB,MAAGA,GAAE,aAAW,MAAK,WAASA,GAAE,gBAAcA,GAAE,cAAY,aAAY,KAAK,WAASA;AAAA,MAAC;AAAC,YAAMG,KAAE,EAAE,EAAC,GAAG,KAAK,SAAS,OAAO,GAAE,cAAaD,GAAC,CAAC,GAAEH,KAAEiB,GAAEf,IAAEC,EAAC;AAAE,WAAK,cAAY,KAAK,WAAW,eAAaC,IAAE,KAAK,WAAW,aAAWJ,MAAG,KAAK,aAAW,IAAIqB,GAAE,EAAC,cAAajB,IAAE,YAAWJ,GAAC,CAAC;AAAE,YAAMe,KAAE,KAAK,WAAW,KAAK;AAAE,UAAGA,GAAE,SAAQ;AAAC,YAAG,WAASd,IAAE;AAAC,gBAAK,EAAC,UAASA,GAAC,IAAE,KAAK,OAAO,YAAWC,KAAE,KAAK;AAAS,cAAG,EAAED,EAAC,EAAE,KAAG,sBAAoBC,GAAE,KAAK,MAAK,uBAAuB,UAAU;AAAA,eAAM;AAAC,kBAAMD,KAAEmB,GAAE,KAAK,OAAO,UAAU;AAAE,iBAAK,UAAUnB,EAAC,MAAI,KAAK,UAAUC,EAAC,KAAG,KAAK,uBAAuB,UAAU;AAAA,UAAC;AAAA,mBAAS,qBAAmBA,GAAE,MAAK;AAAC,kBAAMD,MAAE,UAAK,YAAL,mBAAc,QAAOE,MAAE,KAAAD,GAAE,eAAF,mBAAc;AAAO,aAACA,GAAE,0BAAwBC,MAAGF,MAAGE,OAAIF,MAAG,KAAK,uBAAuB,UAAU;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,MAAM,CAAAH,GAAE,KAAK,sBAAqBiB,GAAE,SAAO,mDAAmD,GAAE,WAASd,MAAG,KAAK,uBAAuB,UAAU;AAAA,IAAC;AAAA,EAAC;AAAE,WAASa,GAAEb,IAAE;AAAC,QAAG,CAACA,GAAE,UAAQ,CAACA,GAAE,WAAW,OAAM,IAAID,GAAE,gBAAe,WAAW;AAAA,EAAC;AAAC,SAAOE,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,uBAAsB,MAAM,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,6BAA4B,MAAM,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,yBAAwB,MAAM,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,oCAAmC,MAAM,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,qBAAoB,MAAM,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,WAAU,MAAM,GAAEG,GAAE,CAAC,EAAE,EAAC,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,EAAC,QAAO,gBAAe,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEH,GAAE,WAAU,aAAY,MAAM,GAAEG,GAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAEH,GAAE,WAAU,cAAa,IAAI,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,iBAAgB,MAAM,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,YAAW,MAAM,GAAEG,GAAE,CAAC,EAAE,EAAC,MAAK,CAACoB,EAAC,GAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEvB,GAAE,WAAU,8BAA6B,IAAI,GAAEG,GAAE,CAAC,EAAE,EAAC,MAAKS,IAAE,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEZ,GAAE,WAAU,0BAAyB,MAAM,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,UAAS,MAAM,GAAEG,GAAE,CAAC,EAAE,EAAC,MAAKW,GAAC,CAAC,CAAC,GAAEd,GAAE,WAAU,kBAAiB,MAAM,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,cAAa,MAAM,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,cAAa,MAAM,GAAEG,GAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,GAAE,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAEH,GAAE,WAAU,oBAAmB,MAAM,GAAEG,GAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAEH,GAAE,WAAU,YAAW,IAAI,GAAEG,GAAE,CAAC,EAAEG,EAAC,CAAC,GAAEN,GAAE,WAAU,OAAM,IAAI,GAAEG,GAAE,CAAC,EAAE,EAAC,OAAMK,GAAC,CAAC,CAAC,GAAER,GAAE,WAAU,YAAW,IAAI,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,cAAa,MAAM,GAAEA,KAAEG,GAAE,CAAC,EAAE,8BAA8B,CAAC,GAAEH,EAAC,GAAEA;AAAC;;;ACA1kZ,SAASwB,GAAEC,IAAE;AAAC,QAAMD,KAAEC,GAAE,QAAOC,KAAED,GAAE,SAAQE,KAAEH,GAAE,KAAM,CAAAC,OAAG,UAAQA,GAAE,KAAK,YAAY,CAAE,IAAE,aAAW,OAAMG,KAAE,CAAC,EAAC,MAAKD,IAAE,MAAK,oBAAmB,OAAM,MAAK,CAAC,EAAE,OAAOH,GAAE,IAAK,CAAAC,QAAI,EAAC,MAAKA,GAAE,MAAK,MAAK,kBAAgBA,GAAE,UAAS,OAAMA,GAAE,KAAI,EAAG,CAAC,GAAEI,KAAED,GAAE,IAAK,CAAAH,OAAGA,GAAE,IAAK,GAAEK,KAAE,CAAC;AAAE,MAAIC,KAAE,GAAEC,KAAE;AAAE,SAAON,GAAE,QAAS,CAAAD,OAAG;AAAC,UAAMD,KAAE,CAAC;AAAE,SAAIA,GAAEG,EAAC,IAAEI,MAAIC,KAAE,GAAEA,KAAEH,GAAE,QAAOG,KAAI,CAAAR,GAAEK,GAAEG,EAAC,CAAC,IAAEP,GAAEO,KAAE,CAAC;AAAE,IAAAF,GAAE,KAAK,EAAC,YAAWN,GAAC,CAAC;AAAA,EAAC,CAAE,GAAE,EAAC,kBAAiB,IAAG,QAAOI,IAAE,UAASE,GAAC;AAAC;AAAC,IAAMJ,KAAN,MAAO;AAAA,EAAC,WAAW,oBAAmB;AAAC,WAAM,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,OAAO,MAAMA,IAAE;AAAC,UAAMC,KAAE,IAAI,SAASD,EAAC,GAAEE,KAAE,IAAED,GAAE,SAAS,CAAC;AAAE,QAAG,MAAIC,GAAE,QAAM,EAAC,QAAO,EAAC,SAAQA,GAAC,GAAE,WAAU,KAAI;AAAE,UAAMC,KAAEF,GAAE,UAAU,GAAE,IAAE,GAAEG,KAAEH,GAAE,UAAU,GAAE,IAAE,GAAEI,KAAEJ,GAAE,UAAU,IAAG,IAAE,GAAEK,KAAE,EAAC,SAAQJ,IAAE,aAAYC,IAAE,iBAAgBC,IAAE,iBAAgBC,GAAC;AAAE,QAAIE,KAAE;AAAG,UAAMC,KAAE,CAAC,GAAEC,KAAE,CAAC;AAAE,QAAIC;AAAE,QAAG,MAAIR,IAAE;AAAC,aAAK,OAAKD,GAAE,SAASM,EAAC,IAAG,CAAAG,KAAE,OAAO,aAAaT,GAAE,SAASM,KAAE,EAAE,CAAC,EAAE,KAAK,GAAEC,GAAE,KAAK,EAAC,MAAKR,GAAE,IAAI,WAAWA,IAAEO,IAAE,EAAE,CAAC,GAAE,MAAKG,IAAE,UAAS,CAAC,UAAS,QAAO,UAAS,WAAU,UAAS,SAAS,EAAE,CAAC,KAAI,KAAI,KAAI,KAAI,KAAI,GAAG,EAAE,QAAQA,EAAC,CAAC,GAAE,QAAOT,GAAE,SAASM,KAAE,EAAE,EAAC,CAAC,GAAEA,MAAG;AAAG,UAAGA,MAAG,GAAEC,GAAE,SAAO,EAAE,QAAKC,GAAE,SAAON,MAAGH,GAAE,aAAWO,KAAEF,MAAG;AAAC,cAAMP,KAAE,CAAC;AAAE,eAAKG,GAAE,SAASM,EAAC,KAAGA,MAAG,GAAEC,GAAE,QAAS,CAAAP,QAAG;AAAC,cAAG,QAAMA,IAAE,KAAK,CAAAH,GAAE,KAAKE,GAAE,IAAI,WAAWA,IAAEO,IAAEN,IAAE,MAAM,CAAC,EAAE,KAAK,CAAC;AAAA,mBAAU,QAAMA,IAAE,KAAK,CAAAH,GAAE,KAAK,SAAS,OAAO,aAAa,MAAM,MAAK,IAAI,WAAWE,IAAEO,IAAEN,IAAE,MAAM,CAAC,EAAE,KAAK,GAAE,EAAE,CAAC;AAAA,mBAAU,QAAMA,IAAE,KAAK,CAAAH,GAAE,KAAK,WAAW,OAAO,aAAa,MAAM,MAAK,IAAI,WAAWE,IAAEO,IAAEN,IAAE,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;AAAA,mBAAU,QAAMA,IAAE,MAAK;AAAC,kBAAMF,KAAE,OAAO,aAAa,MAAM,MAAK,IAAI,WAAWC,IAAEO,IAAEN,IAAE,MAAM,CAAC,EAAE,KAAK;AAAE,YAAAH,GAAE,KAAK,IAAI,KAAK,SAASC,GAAE,UAAU,GAAE,CAAC,GAAE,EAAE,GAAE,SAASA,GAAE,UAAU,GAAE,CAAC,GAAE,EAAE,IAAE,GAAE,SAASA,GAAE,UAAU,GAAE,CAAC,GAAE,EAAE,CAAC,CAAC;AAAA,UAAC;AAAC,UAAAQ,MAAGN,IAAE;AAAA,QAAM,CAAE,GAAEQ,GAAE,KAAKX,EAAC,KAAGS,MAAGF;AAAA,MAAC;AAAA,IAAC;AAAC,WAAM,EAAC,QAAOC,IAAE,QAAOE,IAAE,SAAQC,IAAE,WAAUX,GAAE,EAAC,QAAOU,IAAE,SAAQC,GAAC,CAAC,EAAC;AAAA,EAAC;AAAC;;;ACAj2B,IAAME,KAAE,oBAAI;AAAIA,GAAE,IAAI,SAAQ,2BAA2B,GAAEA,GAAE,IAAI,SAAQ,sBAAsB,GAAEA,GAAE,IAAI,SAAQ,sBAAsB,GAAEA,GAAE,IAAI,WAAU,qBAAqB,GAAEA,GAAE,IAAI,WAAU,qBAAqB,GAAEA,GAAE,IAAI,QAAO,qBAAqB;AAAE,IAAMC,KAAE;AAAE,IAAI,IAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,cAAY,MAAK,KAAK,gBAAc;AAAA,EAAK;AAAA,EAAC,MAAM,KAAKC,IAAE;AAAC,UAAM,KAAK,KAAK;AAAE,UAAK,EAAC,MAAKC,GAAC,IAAE,MAAM,KAAK,QAAQ,KAAK,MAAI,cAAa,EAAC,QAAOD,MAAA,gBAAAA,GAAG,OAAM,CAAC;AAAE,QAAG,CAAC,KAAK,gBAAgBC,EAAC,EAAE,OAAM,IAAIC,GAAE,oBAAmB,mCAAmC;AAAE,SAAK,cAAY,KAAK,IAAI,MAAM,KAAK,IAAI,YAAY,GAAG,IAAE,CAAC;AAAE,UAAK,EAAC,aAAYC,IAAE,YAAWC,GAAC,IAAE,KAAK,aAAaH,EAAC;AAAE,QAAG,eAAaG,GAAE,UAAS;AAAC,YAAMJ,KAAE,MAAM,KAAK,2BAA2B;AAAE,MAAAI,GAAE,iBAAeJ;AAAA,IAAC;AAAC,SAAK,KAAK,eAAcG,EAAC,GAAE,KAAK,KAAK,cAAaC,EAAC,GAAE,KAAK,SAAS,aAAW,KAAK,SAAS,cAAY;AAAA,EAAC;AAAA,EAAC,MAAM,aAAaJ,IAAEK,IAAEJ,IAAEE,KAAE,CAAC,GAAE;AAAC,UAAK,EAAC,eAAcC,GAAC,IAAE,KAAK,WAAW,aAAY,EAAC,wBAAuBF,GAAC,IAAEC,IAAEG,KAAE,EAAE,CAACF,MAAG,CAACF,KAAGK,KAAED,KAAE,IAAE,KAAK,WAAW,YAAY,sBAAoBN;AAAE,QAAGO,KAAE,EAAE,QAAO;AAAK,UAAMC,KAAE,KAAK,oBAAoBD,IAAEF,IAAEJ,IAAEE,GAAE,4BAA2BD,EAAC,GAAEO,KAAE,KAAK,0BAA0BJ,IAAEJ,IAAEK,EAAC,GAAEI,KAAE,MAAM,KAAK,QAAQF,IAAE,EAAC,OAAM,EAAC,MAAK,GAAE,IAAG,KAAK,YAAY,aAAW,EAAC,GAAE,cAAa,gBAAe,QAAOL,GAAE,OAAM,CAAC;AAAE,QAAG,CAACO,GAAE,QAAO;AAAK,UAAMC,MAAE,IAAI,WAAWD,GAAE,IAAI,GAAEE,KAAE,KAAK,0BAA0BD,KAAEF,EAAC;AAAE,QAAG,MAAIG,GAAE,WAAW,QAAO;AAAK,UAAMC,KAAE,MAAM,KAAK,QAAQL,IAAE,EAAC,OAAM,EAAC,MAAKI,GAAE,UAAS,IAAGA,GAAE,WAASA,GAAE,WAAU,GAAE,cAAa,gBAAe,QAAOT,GAAE,OAAM,CAAC;AAAE,QAAG,CAACU,GAAE,QAAO;AAAK,UAAK,CAACC,IAAEC,EAAC,IAAE,KAAK,aAAaT,EAAC;AAAE,WAAO,KAAK,iBAAiBO,GAAE,MAAK,EAAC,OAAMC,IAAE,QAAOC,IAAE,QAAO,MAAK,WAAU,MAAK,mBAAkBT,GAAC,CAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBN,IAAE;AAAC,UAAMK,KAAE,CAAC,UAAS,UAAS,gBAAe,YAAW,cAAa,eAAc,aAAY,aAAY,cAAa,cAAa,UAAS,YAAY;AAAE,WAAOL,MAAG,iBAAeA,GAAE,QAAM,CAACK,GAAE,KAAM,CAAAA,OAAG,CAACL,GAAEK,EAAC,CAAE;AAAA,EAAC;AAAA,EAAC,aAAaL,IAAE;AAJnxF;AAIoxF,UAAMK,KAAE,CAAC,MAAK,MAAK,MAAK,MAAK,MAAK,OAAM,OAAM,OAAM,OAAM,OAAM,KAAK,EAAEL,GAAE,SAAS,GAAE,EAAC,WAAUC,IAAE,YAAWE,IAAE,UAASC,IAAE,YAAWF,IAAE,aAAYQ,IAAE,mBAAkBC,KAAE,qBAAoBC,GAAC,IAAEZ,IAAEa,KAAEb,GAAE,cAAYA,GAAE,WAAW,IAAK,CAAAA,QAAI,EAAC,KAAIA,GAAE,KAAI,KAAIA,GAAE,KAAI,KAAIA,GAAE,MAAK,QAAOA,GAAE,mBAAkB,QAAOA,GAAE,QAAO,MAAKA,GAAE,KAAI,EAAG,GAAEc,KAAEd,GAAE,OAAO,kBAAiBH,MAAE,KAAAG,GAAE,iBAAF,mBAAgB,kBAAiBgB,KAAE,IAAI,GAAEF,MAAA,gBAAAA,GAAG,UAAMA,MAAA,gBAAAA,GAAG,OAAIA,KAAEjB,EAAC;AAAE,QAAIoB,KAAE,IAAIA,GAAE,EAAC,MAAKjB,GAAE,OAAO,MAAK,MAAKA,GAAE,OAAO,MAAK,MAAKA,GAAE,OAAO,MAAK,MAAKA,GAAE,OAAO,MAAK,kBAAiBgB,GAAC,CAAC;AAAE,UAAME,KAAE,IAAID,GAAE,EAAC,GAAEjB,GAAE,YAAW,GAAEA,GAAE,YAAW,kBAAiBgB,GAAC,CAAC,GAAEG,KAAE,KAAK,OAAOF,GAAE,OAAKA,GAAE,QAAMC,GAAE,CAAC,GAAEE,KAAE,KAAK,OAAOH,GAAE,OAAKA,GAAE,QAAMC,GAAE,CAAC,GAAEG,KAAE,KAAK,gBAAgBrB,GAAE,YAAY,GAAEsB,KAAED,KAAEJ,KAAE;AAAK,IAAAI,OAAIJ,KAAEI,GAAE,iBAAiBJ,EAAC,GAAEC,GAAE,KAAGD,GAAE,OAAKA,GAAE,QAAME,IAAED,GAAE,KAAGD,GAAE,OAAKA,GAAE,QAAMG;AAAG,UAAMG,KAAEvB,GAAE,cAAY,CAAC,GAAEwB,KAAExB,GAAE,OAAO,YAAY,EAAE,QAAQ,UAAS,EAAE,GAAEyB,KAAE,IAAIR,GAAEjB,GAAE,OAAO,GAAEA,GAAE,OAAO,GAAEgB,EAAC;AAAE,QAAIU,IAAEC,IAAE,GAAE;AAAE,QAAGvB,MAAGA,GAAE,OAAO,MAAIsB,KAAE,CAAC,GAAEC,KAAE,GAAEA,KAAEvB,GAAE,OAAO,QAAOuB,KAAI,KAAEvB,GAAE,OAAOuB,EAAC,GAAE,IAAEvB,GAAE,SAAOA,GAAE,OAAOuB,EAAC,IAAEA,IAAED,GAAE,KAAK,CAAC,GAAE,MAAI,GAAE,KAAG,OAAK,IAAG,KAAG,MAAI,IAAG,MAAI,EAAE,CAAC;AAAE,UAAME,KAAE5B,GAAE,UAAS6B,KAAE,CAAC;AAAE,SAAIF,KAAE,GAAEA,KAAEC,GAAE,OAAO,QAAOD,KAAI,CAAAE,GAAE,KAAK,IAAI,EAAE,EAAC,OAAMD,GAAE,OAAOD,EAAC,GAAE,YAAWC,GAAE,YAAYD,EAAC,GAAE,OAAM,KAAG,SAAMC,GAAE,YAAYD,EAAC,EAAC,CAAC,CAAC;AAAE,UAAM,IAAE,IAAI,EAAE,EAAC,KAAI,IAAG,MAAKE,IAAE,QAAOL,IAAE,QAAOC,IAAE,MAAK,CAACvB,IAAEQ,EAAC,GAAE,kBAAiBM,GAAC,CAAC,GAAEc,KAAE,EAAC,YAAWhC,IAAE,YAAWE,GAAE,YAAW,YAAWA,GAAE,aAAWA,GAAE,aAAWF,KAAE,GAAE,GAAEiC,KAAE,CAAC,EAAC,QAAO,KAAK,KAAKZ,KAAEjB,EAAC,IAAE,GAAE,QAAO,KAAK,KAAKkB,KAAEV,EAAC,IAAE,GAAE,QAAO,GAAE,QAAO,EAAC,CAAC;AAAE,QAAIsB,KAAE;AAAE,QAAGpB,KAAE,EAAE,MAAIe,KAAE,GAAEA,KAAEf,IAAEe,KAAI,CAAAI,GAAE,KAAK,EAAC,QAAO,KAAK,KAAKZ,KAAEa,KAAE9B,EAAC,IAAE,GAAE,QAAO,KAAK,KAAKkB,KAAEY,KAAEtB,EAAC,IAAE,GAAE,QAAO,GAAE,QAAO,EAAC,CAAC,GAAEsB,MAAG;AAAE,UAAMC,KAAEjC,GAAE;AAAO,QAAI,IAAE;AAAK,QAAGiC,MAAGV,GAAE,MAAK;AAAC,YAAMvB,KAAEuB,GAAE;AAAK,UAAE,EAAC,YAAWvB,GAAE,YAAW,UAAS,CAACA,GAAE,WAAUA,GAAE,SAAS,EAAC;AAAA,IAAC;AAAC,WAAM,EAAC,aAAY8B,IAAE,YAAW,IAAIhB,GAAE,EAAC,OAAMK,IAAE,QAAOC,IAAE,WAAUf,IAAE,WAAUJ,IAAE,QAAOgB,IAAE,cAAaK,IAAE,WAAUD,IAAE,kBAAiBL,IAAE,WAAUE,IAAE,eAAcK,IAAE,YAAWV,IAAE,YAAWV,IAAE,sBAAqB8B,IAAE,UAASP,IAAE,aAAY,IAAInB,GAAE,EAAC,YAAWL,IAAE,aAAYQ,IAAE,mBAAkBR,IAAE,oBAAmBQ,IAAE,QAAOe,IAAE,UAAS,GAAE,eAAc,GAAE,mBAAkBd,KAAE,qBAAoBC,IAAE,eAAcmB,GAAC,CAAC,EAAC,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgB/B,IAAE;AAJlzJ;AAImzJ,QAAG,CAACS,GAAET,EAAC,EAAE,OAAM,IAAIE,GAAE,oBAAmB,uDAAuD;AAAE,UAAMD,KAAEG,GAAEJ,EAAC;AAAE,QAAG,eAAaC,GAAE,KAAK,QAAO;AAAK,QAAG,iBAAeA,GAAE,QAAM,GAAC,KAAAA,GAAE,wBAAF,mBAAuB,WAAQ,GAAC,KAAAA,GAAE,wBAAF,mBAAuB,QAAO,OAAM,IAAIC,GAAE,oBAAmB,iHAAiH;AAAE,WAAOD;AAAA,EAAC;AAAA,EAAC,MAAM,2BAA2BD,IAAE;AAAC,UAAMK,KAAE,KAAK,QAAQ,KAAK,MAAI,kBAAiB,EAAC,QAAOL,GAAC,CAAC,EAAE,KAAM,CAAAA,OAAGA,GAAE,IAAK,EAAE,MAAO,MAAI,IAAK,GAAEC,KAAE,KAAK,QAAQ,KAAK,MAAI,iBAAgB,EAAC,cAAa,gBAAe,QAAOD,GAAC,CAAC,EAAE,KAAM,CAAAA,OAAGA,GAAE,IAAK,EAAE,MAAO,MAAI,IAAK,GAAEG,KAAE,MAAM,QAAQ,IAAI,CAACE,IAAEJ,EAAC,CAAC;AAAE,QAAIG;AAAE,QAAGD,GAAE,CAAC,GAAE;AAAC,UAAIH,KAAEG,GAAE,CAAC,EAAE;AAAO,YAAME,KAAEF,GAAE,CAAC,EAAE;AAAO,UAAGH,MAAGK,IAAE;AAAC,QAAAL,KAAEA,GAAE,IAAK,CAAAA,QAAI,EAAC,MAAK,UAAQA,GAAE,OAAK,qBAAmBH,GAAE,IAAIG,GAAE,IAAI,GAAE,MAAKA,GAAE,MAAK,OAAMA,GAAE,SAAOA,GAAE,KAAI,EAAG;AAAE,cAAMC,KAAEI,GAAE,IAAK,CAAAL,QAAI,EAAC,YAAWA,GAAC,EAAG;AAAE,QAAAA,MAAGK,OAAID,KAAE,EAAC,QAAOJ,IAAE,UAASC,GAAC;AAAA,MAAE;AAAA,IAAC;AAAC,QAAG,CAACG,MAAGD,GAAE,CAAC,GAAE;AAAC,MAAAC,KAAEH,GAAE,MAAME,GAAE,CAAC,CAAC,EAAE;AAAA,IAAS;AAAC,WAAO,EAAE,SAASC,EAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBJ,IAAEK,IAAEF,IAAEC,IAAEF,IAAE;AAAC,UAAMI,KAAE,KAAK,gBAAgB,CAAC,CAACJ,EAAC,GAAEK,KAAE,KAAK,MAAMF,KAAEC,EAAC,IAAEA,IAAEE,KAAE,KAAK,MAAML,KAAEG,EAAC,IAAEA,IAAEG,KAAE,MAAI,KAAK,cAAcF,EAAC,IAAE,MAAI,KAAK,cAAcC,EAAC;AAAE,QAAIE,KAAE;AAAI,IAAAA,MAAGV,MAAG,KAAGA,GAAE,SAAS,IAAE,MAAIA,GAAE,SAAS;AAAE,UAAK,EAAC,sBAAqBW,IAAC,IAAE,KAAK,YAAWC,KAAER,MAAA,gBAAAA,GAAI;AAAG,QAAG,EAAEO,GAAC,KAAG,CAACC,GAAE,QAAM,GAAG,KAAK,GAAG,eAAeF,EAAC,IAAID,EAAC;AAAU,QAAII,KAAE;AAAO,QAAG,CAACX,IAAE;AAAC,MAAAW,KAAEF,IAAE,UAAU,KAAM,CAAAX,OAAGA,GAAE,SAAOY,GAAE,YAAa,EAAE,WAAW,CAAC,EAAE,OAAO,QAAQA,GAAE,OAAO,CAAC,CAAC,EAAE,SAAS,EAAE;AAAE,YAAMZ,KAAE,IAAEa,GAAE;AAAO,eAAQR,KAAE,GAAEA,KAAEL,IAAEK,KAAI,CAAAQ,KAAE,MAAIA;AAAE,MAAAA,KAAE,MAAIA;AAAA,IAAC;AAAC,UAAMC,KAAE,KAAK,uBAAuBZ,MAAGU,GAAE,YAAY;AAAE,WAAM,GAAG,KAAK,GAAG,eAAeE,EAAC,IAAID,EAAC,IAAIH,EAAC,IAAID,EAAC;AAAA,EAAS;AAAA,EAAC,gBAAgBT,KAAE,OAAG;AAAC,UAAK,EAAC,eAAcK,GAAC,IAAE,KAAK,WAAW;AAAY,WAAOL,MAAG,EAAEK,EAAC,IAAEA,GAAE,cAAY,IAAE,KAAK,YAAY;AAAA,EAAU;AAAA,EAAC,aAAaL,KAAE,OAAG;AAAC,UAAK,EAAC,aAAYK,GAAC,IAAE,KAAK,YAAW,EAAC,eAAcJ,GAAC,IAAEI;AAAE,WAAOL,MAAG,EAAEC,EAAC,IAAEA,GAAE,WAASI,GAAE,SAAS;AAAA,EAAI;AAAA,EAAC,uBAAuBL,IAAE;AAAC,WAAM,QAAMA,KAAEA,GAAE,KAAK,KAAG,OAAKA,GAAE,QAAQ,cAAa,GAAG,EAAE,QAAQ,OAAM,IAAI;AAAA,EAAC;AAAA,EAAC,0BAA0BA,IAAEK,IAAEJ,KAAE,OAAG;AAAC,UAAME,KAAE,KAAK,gBAAgBF,EAAC,GAAEG,KAAED,MAAGH,KAAEG,MAAGE,KAAEF;AAAE,QAAGC,KAAE,EAAE,OAAM,IAAI,MAAM,2BAA2B;AAAE,WAAO,KAAGA,KAAE,KAAK,YAAY,aAAW;AAAA,EAAE;AAAA,EAAC,0BAA0BJ,IAAEK,IAAE;AAAC,UAAMJ,KAAED,GAAE,SAASK,IAAEA,KAAE,CAAC;AAAE,QAAIF,IAAEC,KAAE;AAAE,SAAID,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAC,OAAI,MAAIH,GAAEE,EAAC,MAAI,IAAEA;AAAE,UAAMD,KAAE,gBAAaE;AAAE,SAAIA,KAAE,GAAED,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAC,OAAI,MAAIH,GAAEE,EAAC,MAAI,KAAGA,KAAE;AAAG,WAAM,EAAC,UAASD,IAAE,YAAW,gBAAaE,GAAC;AAAA,EAAC;AAAA,EAAC,cAAcJ,IAAE;AAAC,QAAIK,KAAEL,GAAE,SAAS,EAAE;AAAE,QAAG,MAAIK,GAAE,QAAO;AAAC,UAAIL,KAAE,IAAEK,GAAE;AAAO,aAAKL,OAAK,IAAG,CAAAK,KAAE,MAAIA;AAAA,IAAC;AAAC,WAAOA;AAAA,EAAC;AAAC;AAAEL,GAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAE,EAAE,WAAU,eAAc,MAAM,GAAEA,GAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,IAAEA,GAAE,CAAC,EAAE,gDAAgD,CAAC,GAAE,CAAC;AAAE,IAAMiB,KAAE;;;ACA7mN,IAAIiB,KAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,gBAAc,UAAS,KAAK,OAAK;AAAA,EAAI;AAAA,EAAC,MAAM,KAAKC,IAAE;AAAC,UAAM,KAAK,KAAK;AAAE,UAAMC,KAAE,KAAK,MAAK,EAAC,YAAWC,IAAE,YAAWC,IAAE,YAAWC,IAAE,MAAKC,IAAE,eAAcC,IAAE,cAAaC,IAAE,WAAUC,GAAC,IAAE,KAAK,MAAK,EAAC,OAAMV,IAAE,QAAOW,IAAE,WAAUC,GAAC,IAAER,IAAES,KAAEV,GAAE,UAAQ,IAAIW,GAAE,EAAC,MAAK,MAAI,MAAK,KAAG,MAAKd,KAAE,KAAG,MAAKW,KAAE,KAAG,kBAAiB,IAAI,EAAE,EAAC,MAAK,KAAI,CAAC,EAAC,CAAC,GAAEI,KAAEZ,GAAE,4BAA0B,CAACA,GAAE,QAAOa,KAAE,EAAC,GAAEH,GAAE,QAAMb,IAAE,GAAEa,GAAE,SAAOF,GAAC,GAAEM,KAAE,IAAIJ,GAAE,EAAC,OAAMb,IAAE,QAAOW,IAAE,WAAUC,IAAE,QAAOC,IAAE,cAAaJ,IAAE,WAAUC,IAAE,WAAUM,IAAE,kBAAiBH,GAAE,kBAAiB,WAAUT,GAAE,OAAO,QAAO,eAAcI,MAAG,CAAC,GAAE,YAAWH,IAAE,0BAAyBU,IAAE,YAAWT,GAAC,CAAC;AAAE,SAAK,+BAA+BW,IAAE,KAAI,GAAG,GAAE,KAAK,KAAK,cAAaA,EAAC,GAAE,KAAK,eAAe,GAAE,MAAM,KAAK,qBAAqBb,IAAE,EAAC,OAAM,KAAI,QAAO,IAAG,GAAEF,EAAC,GAAE,KAAK,cAAYK,IAAE,KAAK,MAAI,eAAaA;AAAA,EAAC;AAAA,EAAC,aAAaL,IAAEC,IAAEC,IAAEC,KAAE,CAAC,GAAE;AAAC,UAAMC,KAAE,KAAK,iBAAiB,IAAI,GAAGJ,EAAC,IAAIC,EAAC,IAAIC,EAAC,EAAE;AAAE,WAAO,QAAQ,QAAQE,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,qBAAqBJ,IAAEI,IAAEC,IAAE;AAJ5uD;AAI6uD,UAAMW,KAAE,KAAK,WAAW,YAAY,qBAAoBV,KAAE,KAAK,mBAAiB,KAAK,iBAAiB,MAAM,EAAC,YAAWN,IAAE,UAASI,IAAE,qBAAoBY,GAAC,GAAEX,EAAC,IAAE,QAAQ,QAAQY,GAAEjB,IAAEI,IAAEY,EAAC,CAAC,GAAEE,KAAE,EAAE,KAAK,WAAW,UAAU,GAAEC,MAAE,EAAE,KAAK,WAAW,UAAU,GAAErB,KAAEoB,KAAE,QAAQ,QAAQ,EAAC,YAAW,MAAK,YAAW,KAAI,CAAC,IAAE,KAAK,mBAAiB,KAAK,iBAAiB,6BAA6B,EAAC,YAAWlB,GAAC,GAAEK,EAAC,IAAE,QAAQ,QAAQa,GAAElB,EAAC,CAAC,GAAES,KAAE,MAAM,EAAE,CAACH,IAAER,EAAC,CAAC;AAAE,QAAG,CAACW,GAAE,CAAC,EAAE,SAAOA,GAAE,CAAC,EAAE,MAAM,OAAM,IAAIP,GAAE,wBAAuB,kCAAkC;AAAE,SAAK,mBAAiBO,GAAE,CAAC,EAAE,OAAMS,OAAI,KAAK,WAAW,cAAW,KAAAT,GAAE,CAAC,EAAE,UAAL,mBAAY,aAAYU,QAAI,KAAK,WAAW,cAAW,KAAAV,GAAE,CAAC,EAAE,UAAL,mBAAY;AAAA,EAAW;AAAC;AAAER,GAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEH,GAAE,WAAU,iBAAgB,MAAM,GAAEG,GAAE,CAAC,EAAE,CAAC,GAAEH,GAAE,WAAU,QAAO,MAAM,GAAEA,KAAEG,GAAE,CAAC,EAAE,mDAAmD,CAAC,GAAEH,EAAC;AAAE,IAAMW,KAAEX;;;ACAtkF,SAASsB,GAAEC,IAAEC,IAAE;AAAC,MAAG,CAACD,MAAG,CAACC,GAAE,QAAM,CAAC;AAAE,MAAIC,KAAED;AAAE,EAAAA,GAAE,SAAS,GAAG,KAAGC,KAAED,GAAE,MAAM,GAAEA,GAAE,QAAQ,GAAG,CAAC,GAAEA,KAAEA,GAAE,MAAMA,GAAE,QAAQ,GAAG,IAAE,CAAC,KAAGA,KAAE;AAAG,QAAME,KAAE,CAAC;AAAE,MAAGF,IAAE;AAAC,UAAMG,KAAEL,GAAEC,IAAEE,EAAC;AAAE,aAAQF,KAAE,GAAEA,KAAEI,GAAE,QAAOJ,MAAI;AAAC,MAAAD,GAAEK,GAAEJ,EAAC,GAAEC,EAAC,EAAE,QAAS,CAAAF,OAAGI,GAAE,KAAKJ,EAAC,CAAE;AAAA,IAAC;AAAC,WAAOI;AAAA,EAAC;AAAC,QAAMC,KAAEJ,GAAE,uBAAuB,KAAIE,EAAC;AAAE,MAAG,CAACE,MAAG,MAAIA,GAAE,OAAO,QAAM,CAAC;AAAE,WAAQL,KAAE,GAAEA,KAAEK,GAAE,QAAOL,KAAI,CAAAI,GAAE,KAAKC,GAAEL,EAAC,KAAGK,GAAE,KAAKL,EAAC,CAAC;AAAE,SAAOI;AAAC;AAAC,SAASH,GAAEC,IAAEC,IAAE;AAAC,MAAG,CAACD,MAAG,CAACC,GAAE,QAAO;AAAK,MAAIC,KAAED;AAAE,EAAAA,GAAE,SAAS,GAAG,KAAGC,KAAED,GAAE,MAAM,GAAEA,GAAE,QAAQ,GAAG,CAAC,GAAEA,KAAEA,GAAE,MAAMA,GAAE,QAAQ,GAAG,IAAE,CAAC,KAAGA,KAAE;AAAG,QAAME,KAAEL,GAAEE,IAAEE,EAAC;AAAE,SAAOC,GAAE,SAAO,IAAEF,KAAEF,GAAEI,GAAE,CAAC,GAAEF,EAAC,IAAEE,GAAE,CAAC,IAAE;AAAI;AAAC,SAASH,GAAEF,IAAEE,KAAE,MAAK;AAAC,QAAMC,KAAED,KAAED,GAAED,IAAEE,EAAC,IAAEF;AAAE,MAAII;AAAE,SAAOD,MAAGC,KAAED,GAAE,eAAaA,GAAE,WAAUC,KAAEA,GAAE,KAAK,IAAE,QAAM;AAAI;AAAC,SAASD,GAAEF,IAAEC,IAAE;AAAC,QAAMC,KAAEH,GAAEC,IAAEC,EAAC,GAAEE,KAAE,CAAC;AAAE,MAAIC;AAAE,WAAQL,KAAE,GAAEA,KAAEG,GAAE,QAAOH,KAAI,CAAAK,KAAEF,GAAEH,EAAC,EAAE,eAAaG,GAAEH,EAAC,EAAE,WAAUK,OAAIA,KAAEA,GAAE,KAAK,GAAE,OAAKA,MAAGD,GAAE,KAAKC,EAAC;AAAG,SAAOD;AAAC;AAAwE,SAASE,GAAEC,IAAEC,IAAE;AAAC,SAAOC,GAAEF,IAAEC,EAAC,EAAE,IAAK,CAAAD,QAAG,OAAOA,GAAC,CAAE;AAAC;AAAC,SAASG,GAAEH,IAAEC,IAAE;AAAC,QAAMC,KAAEE,GAAEJ,IAAEC,EAAC;AAAE,SAAO,OAAOC,EAAC;AAAC;AAAC,SAASG,GAAEL,IAAEC,IAAE;AAJ58B;AAI68B,QAAMG,MAAE,KAAAJ,MAAA,gBAAAA,GAAG,aAAH,mBAAa,eAAcE,KAAED,GAAE,YAAY;AAAE,SAAOG,GAAE,MAAMA,GAAE,YAAY,GAAG,IAAE,CAAC,MAAIF;AAAC;;;ACAzsB,SAASI,GAAEC,IAAEC,IAAE;AAAC,MAAG,CAACD,MAAG,CAACC,GAAE,QAAO;AAAK,QAAMC,KAAE,CAAC;AAAE,WAAQC,KAAE,GAAEA,KAAEH,GAAE,QAAOG,KAAI,CAAAD,GAAE,KAAKF,GAAEG,EAAC,CAAC,GAAED,GAAE,KAAKD,GAAEE,EAAC,CAAC;AAAE,SAAOD;AAAC;AAAC,SAASE,GAAEJ,IAAE;AAAC,QAAMC,KAAED,GAAEA,IAAE,cAAc,GAAEG,KAAEE,GAAEC,GAAEL,IAAE,uBAAuB,KAAGA,GAAEA,IAAE,sBAAsB,CAAC;AAAE,MAAG,6BAA2BA,GAAE,aAAa,UAAU,EAAE,QAAM,EAAC,kBAAiBE,IAAE,WAAU,KAAI;AAAE,QAAMG,KAAEA,GAAEL,IAAE,iBAAiB,KAAG,GAAEG,KAAEA,GAAEH,IAAE,eAAe,GAAEM,MAAEH,GAAEH,IAAE,eAAe,GAAEO,KAAEJ,GAAEH,IAAE,sBAAsB,GAAEQ,KAAEL,GAAEH,IAAE,sBAAsB,GAAES,KAAEX,GAAEK,IAAEG,GAAC,GAAEI,KAAEZ,GAAES,IAAEC,EAAC;AAAE,SAAM,EAAC,kBAAiBN,IAAE,WAAUO,MAAGC,MAAGD,GAAE,UAAQC,GAAE,SAAO,IAAIN,GAAE,EAAC,kBAAiBF,IAAE,iBAAgBG,IAAE,qBAAoBI,IAAE,qBAAoBC,GAAC,CAAC,IAAE,KAAI;AAAC;AAAC,SAASJ,GAAEP,IAAE;AAJp+B;AAIq+B,QAAMC,KAAEK,GAAEN,IAAE,aAAa,GAAEY,KAAEZ,GAAEA,IAAE,qBAAqB,GAAEa,KAAEP,GAAEM,IAAE,SAAS,GAAEN,KAAEA,GAAEM,IAAE,SAAS,GAAEb,KAAEO,GAAEM,IAAE,aAAa,GAAER,MAAE,KAAAH,GAAEW,IAAE,YAAY,MAAhB,mBAAmB,MAAM,KAAK,IAAK,CAAAZ,OAAG,OAAOA,EAAC;AAAI,MAAIO,KAAEF,IAAEG,IAAEC;AAAE,EAAAP,GAAEF,IAAE,cAAc,EAAE,QAAS,CAAAA,OAAG;AAAC,UAAMC,KAAE,OAAOD,GAAE,eAAaA,GAAE,SAAS;AAAE,YAAOA,GAAE,aAAa,KAAK,EAAE,YAAY,GAAE;AAAA,MAAC,KAAI;AAAqB,QAAAO,MAAEN;AAAE;AAAA,MAAM,KAAI;AAAqB,QAAAI,KAAEJ;AAAE;AAAA,MAAM,KAAI;AAAkB,QAAAO,KAAEP;AAAE;AAAA,MAAM,KAAI;AAAoB,QAAAQ,KAAER;AAAA,IAAC;AAAA,EAAC,CAAE;AAAE,QAAMS,KAAEJ,GAAEN,IAAE,0BAA0B;AAAE,SAAM,EAAC,aAAYC,IAAE,YAAUG,MAAA,gBAAAA,GAAG,WAAQ,QAAMS,MAAG,QAAMP,KAAE,EAAC,KAAIO,IAAE,KAAIP,IAAE,MAAKP,MAAGK,GAAE,QAAO,QAAOA,GAAC,IAAE,MAAK,iBAAgBM,IAAE,YAAW,QAAMH,OAAG,QAAMF,KAAE,EAAC,KAAIE,KAAE,KAAIF,IAAE,KAAIG,IAAE,QAAOC,GAAC,IAAE,KAAI;AAAC;AAAC,SAASJ,GAAEL,IAAE;AAAC,MAAG,CAACA,GAAE,QAAO;AAAK,MAAIC,KAAE,OAAOD,EAAC;AAAE,MAAG,CAAC,MAAMC,EAAC,KAAG,MAAIA,GAAE,QAAO,IAAI,EAAE,EAAC,MAAKA,GAAC,CAAC;AAAE,OAAID,KAAE,OAAOA,EAAC,GAAG,WAAW,UAAU,GAAE;AAAC,QAAG,CAACA,GAAE,SAAS,QAAQ,KAAG,CAACA,GAAE,SAAS,QAAQ,KAAG,CAACA,GAAE,WAAW,QAAQ,EAAE,QAAO;AAAK,UAAME,KAAEF,GAAE,QAAQ,QAAQ,GAAEG,KAAEH,GAAE,QAAQ,QAAQ,GAAEc,KAAEX,KAAE,KAAGA,KAAEH,GAAE,QAAQ,QAAQ;AAAE,QAAG,OAAKc,GAAE,QAAO;AAAK,UAAMC,KAAEf,GAAE,MAAMc,IAAEd,GAAE,YAAY,KAAIE,EAAC,IAAE,CAAC,EAAE,KAAK,GAAEU,KAAEZ,GAAE,MAAME,IAAEF,GAAE,YAAY,GAAG,CAAC,EAAE,KAAK;AAAE,IAAAC,KAAEO,GAAEO,EAAC;AAAE,UAAMF,KAAE,IAAI,EAAEZ,KAAE,EAAC,MAAKA,GAAC,IAAE,EAAC,KAAIc,GAAC,CAAC,GAAEhB,KAAES,GAAEI,EAAC;AAAE,WAAOb,OAAIc,GAAE,UAAQd,KAAGc;AAAA,EAAC;AAAC,SAAOb,GAAE,WAAW,QAAQ,KAAGA,GAAE,WAAW,QAAQ,KAAGC,KAAEO,GAAER,EAAC,GAAE,IAAI,EAAE,MAAIC,KAAE,EAAC,MAAKA,GAAC,IAAE,EAAC,KAAID,GAAC,CAAC,KAAG;AAAI;AAAC,SAASQ,GAAER,IAAE;AAJlsE;AAImsE,QAAMC,KAAED,GAAE,QAAQ,OAAM,GAAG,EAAE,QAAQ,OAAM,EAAE,EAAE,MAAM,GAAG,EAAE,IAAK,CAAAA,OAAGA,GAAE,KAAK,CAAE,EAAE,OAAQ,CAAAA,OAAG,OAAKA,EAAE,GAAEE,KAAED,GAAEA,GAAE,SAAO,CAAC,EAAE,MAAM,GAAG,GAAEE,MAAE,KAAAD,GAAE,CAAC,MAAH,mBAAM;AAAc,OAAI,WAASC,MAAG,WAASA,OAAIH,GAAE,SAAS,KAAK,GAAE;AAAC,UAAMA,KAAE,OAAOE,GAAE,CAAC,CAAC;AAAE,QAAG,CAAC,MAAMF,EAAC,KAAG,MAAIA,GAAE,QAAOA;AAAA,EAAC;AAAC,SAAO;AAAC;AAAC,SAASS,GAAEK,IAAE;AAJ78E;AAI88E,MAAG,mBAAe,KAAAA,MAAA,gBAAAA,GAAG,gBAAgB,YAAnB,mBAA4B,eAAc,QAAM,CAAC;AAAE,QAAMC,KAAE,EAAC,kBAAiB,MAAK,WAAU,MAAK,UAAS,CAAC,GAAE,aAAY,CAAC,GAAE,YAAW,MAAK,YAAW,KAAI;AAAE,EAAAD,GAAE,gBAAgB,WAAW,QAAS,CAAAd,OAAG;AAAC,QAAG,MAAIA,GAAE;AAAS,UAAGY,GAAEZ,IAAE,KAAK,GAAE;AAAC,YAAG,CAACe,GAAE,kBAAiB;AAAC,gBAAMd,KAAEA,GAAED,EAAC;AAAE,UAAAe,GAAE,mBAAiBV,GAAEJ,EAAC;AAAA,QAAC;AAAA,MAAC,WAASW,GAAEZ,IAAE,UAAU,EAAE,KAAG,eAAaA,GAAE,aAAa,QAAQ,GAAE;AAAC,cAAK,EAAC,kBAAiBC,IAAE,WAAUC,GAAC,IAAEE,GAAEJ,EAAC;AAAE,QAAAe,GAAE,YAAUb,IAAEa,GAAE,qBAAmBA,GAAE,mBAAiBd;AAAA,MAAE,OAAK;AAAC,QAAAC,GAAEF,IAAE,KAAK,EAAE,QAAS,CAAAA,OAAGe,GAAE,SAASf,GAAE,aAAa,KAAK,CAAC,IAAEC,GAAED,EAAC,CAAE;AAAA,MAAC;AAAA,eAASY,GAAEZ,IAAE,eAAe,GAAE;AAAC,cAAMC,KAAEM,GAAEP,EAAC;AAAE,gBAAMC,GAAE,mBAAiB,QAAMc,GAAE,YAAYd,GAAE,eAAe,IAAEc,GAAE,YAAYd,GAAE,eAAe,IAAEA,KAAEc,GAAE,YAAY,KAAKd,EAAC;AAAA,MAAC;AAAA;AAAA,EAAC,CAAE;AAAE,QAAMW,KAAEG,GAAE;AAAY,MAAGH,GAAE,QAAO;AAAC,UAAMX,KAAE,CAAC,CAACW,GAAE,CAAC,EAAE;AAAW,IAAAG,GAAE,aAAWd,KAAEW,GAAE,IAAK,CAAAZ,OAAGA,GAAE,UAAW,EAAE,OAAO,CAAC,IAAE;AAAK,UAAME,KAAE,CAAC,CAACU,GAAE,CAAC,EAAE;AAAU,IAAAG,GAAE,aAAWb,KAAEU,GAAE,IAAK,CAAAZ,OAAGA,GAAE,SAAU,EAAE,OAAO,CAAC,IAAE;AAAA,EAAI;AAAC,SAAOe;AAAC;;;ACAr7E,IAAIC,KAAE,cAAcC,GAAC;AAAA,EAAC,MAAM,KAAKC,IAAE;AAAC,UAAM,KAAK,KAAK;AAAE,UAAMC,KAAE,MAAM,KAAK,WAAWD,EAAC;AAAE,QAAG,EAAC,kBAAiBE,IAAE,YAAWC,IAAE,YAAWC,IAAE,WAAUC,GAAC,IAAE,MAAM,KAAK,oBAAoBL,EAAC;AAAE,UAAMM,KAAE,CAACJ;AAAE,IAAAI,OAAIJ,KAAE,IAAI,EAAE,EAAC,MAAK,KAAI,CAAC,KAAGE,MAAA,gBAAAA,GAAG,WAAQ,QAAMD,OAAIA,KAAEI,GAAEH,EAAC;AAAG,UAAK,EAAC,OAAMI,IAAE,QAAOC,GAAC,IAAER;AAAE,QAAIS,KAAE,IAAIC,GAAE,EAAC,MAAK,MAAI,MAAK,MAAGF,IAAE,MAAKD,KAAE,KAAG,MAAK,KAAG,kBAAiBN,GAAC,CAAC;AAAE,UAAMU,KAAEP,KAAEA,GAAE,iBAAiBK,EAAC,IAAEA;AAAE,QAAIZ,KAAE;AAAG,QAAGO,IAAE;AAAC,YAAML,KAAEK,GAAE;AAAoB,MAAAP,KAAEE,MAAG,MAAIA,GAAE,CAAC,KAAG,MAAIA,GAAE,CAAC,GAAEF,OAAIO,KAAE,MAAKK,KAAEE;AAAA,IAAE;AAAC,UAAMD,KAAE,IAAIC,GAAE,EAAC,MAAK,EAAC,QAAOA,IAAE,cAAaF,IAAE,WAAUL,IAAE,YAAWJ,IAAE,YAAWE,IAAE,YAAWC,IAAE,eAAc,EAAC,UAAS,YAAW,GAAE,0BAAyBE,GAAC,EAAC,CAAC;AAAE,UAAMK,GAAE,KAAK,GAAEA,GAAE,OAAK,MAAK,KAAK,KAAK,cAAaA,GAAE,UAAU,GAAE,KAAK,kBAAgBA;AAAA,EAAC;AAAA,EAAC,aAAaX,IAAEC,IAAEC,IAAEC,KAAE,CAAC,GAAE;AAAC,WAAO,KAAK,gBAAgB,aAAaH,IAAEC,IAAEC,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,WAAWH,IAAE;AAAC,UAAK,EAAC,MAAKE,GAAC,IAAE,MAAM,KAAK,QAAQ,KAAK,KAAI,EAAC,cAAa,gBAAe,QAAOF,MAAA,gBAAAA,GAAG,OAAM,CAAC,GAAEG,KAAEU,GAAEX,EAAC,EAAE,YAAY;AAAE,QAAG,UAAQC,MAAG,UAAQA,MAAG,UAAQA,MAAG,UAAQA,GAAE,OAAM,IAAID,GAAE,yBAAwB,oCAAoC;AAAE,SAAK,KAAK,iBAAgBC,EAAC;AAAE,UAAMC,KAAED,GAAE,YAAY,GAAEE,KAAE,UAAQD,MAAG,UAAQA,MAAG,CAAC,IAAI,KAAK,GAAEE,KAAE,MAAM,KAAK,iBAAiBJ,IAAE,EAAC,QAAOE,IAAE,WAAUC,IAAE,eAAc,KAAE,CAAC;AAAE,QAAG,QAAMC,GAAE,OAAM,IAAIJ,GAAE,yBAAwB,4BAA4B;AAAE,WAAOI;AAAA,EAAC;AAAA,EAAC,MAAM,oBAAoBN,IAAE;AAJptE;AAIqtE,UAAMC,KAAE,EAAED,MAAA,gBAAAA,GAAG,MAAM,GAAEK,KAAE,KAAK,SAAS,kBAAgB,CAAC,GAAEC,KAAED,GAAE,SAAS,SAAS,IAAE,OAAK,KAAK,QAAQ,KAAK,MAAI,YAAW,EAAC,cAAa,OAAM,QAAOJ,GAAC,CAAC,GAAEO,KAAE,KAAK,eAAcM,KAAE,UAAQN,KAAE,QAAM,UAAQA,KAAE,QAAM,UAAQA,KAAE,QAAM,MAAKE,KAAEI,MAAGT,GAAE,SAASS,EAAC,IAAE,OAAK,KAAK,QAAQ,KAAK,IAAI,MAAM,GAAE,KAAK,IAAI,YAAY,GAAG,CAAC,IAAE,MAAIA,IAAE,EAAC,cAAa,QAAO,QAAOb,GAAC,CAAC,GAAEc,MAAE,MAAM,EAAE,CAACT,IAAEI,EAAC,CAAC;AAAE,QAAGT,MAAA,gBAAAA,GAAG,QAAQ,OAAMG,GAAE;AAAE,UAAMY,KAAEN,IAAE,KAAAK,IAAE,CAAC,EAAE,UAAL,mBAAY,IAAI;AAAE,QAAG,CAACC,GAAE,WAAU;AAAC,YAAMhB,KAAEe,IAAE,CAAC,EAAE,QAAMA,IAAE,CAAC,EAAE,MAAM,KAAK,MAAM,IAAI,EAAE,MAAM,GAAE,CAAC,EAAE,IAAK,CAAAf,OAAG,OAAOA,EAAC,CAAE,IAAE;AAAK,MAAAgB,GAAE,YAAU,OAAIhB,MAAA,gBAAAA,GAAG,UAAO,IAAIc,GAAE,EAAC,qBAAoB,CAACd,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,CAACA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,CAACA,GAAE,CAAC,CAAC,EAAC,CAAC,IAAE;AAAA,IAAI;AAAC,WAAOgB;AAAA,EAAC;AAAC;AAAEf,GAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEH,GAAE,WAAU,iBAAgB,MAAM,GAAEA,KAAEG,GAAE,CAAC,EAAE,mDAAmD,CAAC,GAAEH,EAAC;AAAE,IAAMa,KAAEb;;;ACA33D,IAAImB,KAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,eAAa,GAAE,KAAK,gBAAc,MAAK,KAAK,UAAQ,MAAK,KAAK,gBAAc,oBAAmB,KAAK,WAAS;AAAA,EAAI;AAAA,EAAC,MAAM,KAAKC,IAAE;AAJjxC;AAIkxC,UAAM,KAAK,KAAK;AAAE,UAAMC,KAAED,MAAGA,GAAE,QAAOE,KAAE,KAAK,aAAW,EAAC,MAAK,KAAK,WAAU,IAAE,MAAM,KAAK,QAAQ,KAAK,KAAI,EAAC,OAAM,EAAC,GAAE,OAAM,GAAE,QAAOD,GAAC,CAAC;AAAE,IAAAC,GAAE,QAAM,KAAK,MAAI,KAAK,IAAI,QAAQ,WAAU,QAAQ;AAAG,UAAMC,KAAED,GAAE;AAAK,QAAG,KAAK,aAAWC,IAAE,CAACA,GAAE,OAAM,IAAIC,GAAE,0BAAyB,6DAA6D;AAAE,QAAG,CAACD,GAAE,SAAS,OAAM,IAAIC,GAAE,0BAAyB,mDAAmD;AAAE,SAAK,uBAAuB;AAAE,UAAMC,KAAE,CAAC,OAAM,QAAO,OAAM,QAAO,SAAQ,SAAQ,OAAO;AAAE,SAAK,WAASF,GAAE,WAAU,QAAM,KAAK,aAAWE,GAAE,SAASF,GAAE,SAAS,OAAO,YAAY,CAAC,IAAE,KAAK,WAAS,QAAM,WAASA,GAAE,SAAS,OAAO,YAAY,IAAE,KAAK,WAAS,cAAY,KAAK,WAAS,WAAU,KAAK,gBAAY,KAAAA,GAAE,SAAF,mBAAQ,MAAMA,GAAE,KAAK,QAAQ,GAAG,IAAE,OAAI;AAAG,UAAMG,MAAE,MAAM,KAAK,iBAAiB,EAAC,QAAOL,GAAC,CAAC;AAAE,QAAG,EAAEK,GAAC,EAAE,OAAM,IAAIF,GAAE,4BAA2B,iCAAiC;AAAE,UAAMG,KAAE,UAAQ,KAAK,WAASF,GAAEF,GAAE,UAASA,EAAC,IAAE,EAAE,SAASA,GAAE,QAAQ;AAAE,MAAEI,EAAC;AAAE,UAAK,CAACC,IAAEC,EAAC,IAAE,KAAK,kBAAkBH,KAAEC,EAAC,GAAE,EAAC,QAAOG,IAAE,WAAUC,GAAC,IAAEL,KAAEM,KAAE,MAAGN,IAAE,QAAMK,GAAE,GAAEE,KAAE,KAAK,IAAIF,GAAE,GAAEA,GAAE,CAAC,GAAE,EAAC,MAAKb,GAAC,IAAES;AAAE,KAAC,UAAQ,KAAK,YAAU,MAAIJ,GAAE,YAAU,KAAK,IAAIQ,GAAE,IAAEA,GAAE,CAAC,IAAEC,MAAG,CAACd,GAAE,KAAM,CAAAE,OAAG,KAAK,IAAIA,GAAE,aAAWa,EAAC,IAAED,EAAE,OAAKD,GAAE,IAAEA,GAAE,IAAEH,GAAE,YAAWF,IAAE,QAAM,KAAK,MAAMI,GAAE,OAAKA,GAAE,QAAMC,GAAE,IAAE,GAAE,GAAEL,IAAE,SAAO,KAAK,MAAMI,GAAE,OAAKA,GAAE,QAAMC,GAAE,IAAE,GAAE;AAAG,UAAMG,KAAEN,GAAE,QAAMC,GAAE,OAAM,CAACM,IAAEC,EAAC,IAAET,GAAE,MAAKU,KAAE,CAAC,GAAE,IAAE,CAAC;AAAE,IAAAnB,GAAE,QAAS,CAACE,IAAEkB,OAAI;AAAC,MAAAlB,GAAE,SAAOS,GAAE,SAAOT,GAAE,SAAOQ,GAAE,SAAOS,GAAE,KAAK,EAAC,GAAEjB,GAAE,YAAW,GAAEA,GAAE,WAAU,CAAC,GAAEkB,KAAEpB,GAAE,SAAO,KAAG,EAAE,KAAK,KAAK,MAAM,KAAGE,GAAE,aAAWF,GAAEoB,KAAE,CAAC,EAAE,UAAU,IAAE,EAAE;AAAA,IAAC,CAAE,GAAED,GAAE,KAAM,CAACjB,IAAEkB,OAAIlB,GAAE,IAAEkB,GAAE,CAAE;AAAE,UAAMC,KAAE,KAAK,qBAAqBT,IAAEK,IAAEC,IAAET,GAAE,QAAOU,IAAEH,EAAC,GAAEM,KAAEH,GAAE,SAAO,IAAEA,GAAE,MAAM,CAAC,IAAE;AAAK,QAAII;AAAE,IAAAlB,GAAE,kBAAgBkB,KAAE,EAAC,UAAS,CAAClB,GAAE,cAAc,MAAKA,GAAE,cAAc,IAAI,GAAE,cAAW,KAAAG,IAAE,kBAAF,mBAAiB,KAAK,eAAY,EAAC;AAAG,UAAM,IAAE,EAAE,UAAQ,KAAG,EAAE,UAAQ,KAAG,EAAE,MAAM,GAAE,EAAE,SAAO,CAAC,EAAE,MAAO,CAAAN,OAAGA,OAAI,EAAE,CAAC,CAAE,IAAE,EAAE,CAAC,KAAG,IAAE,KAAK,MAAM,MAAIS,GAAE,aAAWD,GAAE,gBAAc,KAAGM,GAAE,IAAE;AAAG,QAAGR,IAAE,cAAY,IAAID,GAAE,EAAC,YAAWE,GAAE,KAAK,CAAC,GAAE,aAAYA,GAAE,KAAK,CAAC,GAAE,mBAAkBA,GAAE,KAAK,CAAC,GAAE,oBAAmBA,GAAE,KAAK,CAAC,GAAE,oBAAmBa,IAAE,sBAAqB,GAAE,aAAYb,GAAE,QAAO,QAAOA,GAAE,QAAO,mBAAkB,GAAE,qBAAoBO,IAAE,UAASP,IAAE,eAAcc,IAAE,eAAcF,GAAC,CAAC,GAAE,KAAK,aAAab,GAAC,GAAE,KAAK,KAAK,cAAaA,GAAC,GAAEH,GAAE,aAAa,YAAY,EAAE,SAAS,SAAS,GAAE;AAAC,YAAMH,KAAE,EAAC,UAASM,IAAE,YAAY,UAAS,WAAU,EAAE,KAAK,GAAG,GAAE,KAAI,KAAK,KAAI,aAAY,CAAC,GAAE,MAAK,OAAM;AAAE,WAAK,gBAAc,IAAI,EAAE,EAAC,OAAMN,GAAC,CAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,aAAaA,IAAEkB,IAAEI,IAAElB,KAAE,CAAC,GAAE;AAAC,UAAK,EAAC,aAAYmB,IAAE,QAAOpB,GAAC,IAAE,KAAK,YAAW,EAAC,eAAcE,GAAC,IAAEkB,IAAEjB,MAAE,EAAED,EAAC,KAAG,CAAC,CAACD,GAAE;AAAuB,QAAG,KAAK,WAAS,CAACE,OAAG,QAAMF,GAAE,QAAQ,QAAO;AAAK,UAAMoB,KAAElB,MAAE,IAAEiB,GAAE,sBAAoBvB,KAAE,KAAK,cAAayB,KAAE,GAAG,KAAK,GAAG,SAASD,EAAC,IAAIN,EAAC,IAAII,EAAC,IAAGI,KAAE,KAAK,UAAQpB,MAAE,EAAC,UAASF,GAAE,uBAAsB,IAAE,EAAC,SAAQA,GAAE,WAAS,EAAC,IAAE,MAAK,EAAC,MAAKuB,GAAC,IAAE,MAAM,KAAK,QAAQF,IAAE,EAAC,OAAMC,IAAE,cAAa,gBAAe,QAAOtB,GAAE,OAAM,CAAC;AAAE,QAAG,CAACuB,GAAE,QAAO;AAAK,UAAMpB,KAAED,MAAED,GAAE,WAASkB,GAAE,SAAS,MAAKd,KAAE,MAAM,KAAK,iBAAiBkB,IAAE,EAAC,OAAMpB,GAAE,CAAC,GAAE,QAAOA,GAAE,CAAC,GAAE,QAAO,MAAK,WAAU,MAAK,SAAQ,gBAAc,KAAK,UAAS,mBAAkBD,KAAE,aAAY,EAAE,KAAK,WAAW,WAAW,EAAC,CAAC;AAAE,QAAG,QAAMG,GAAE,QAAO;AAAK,UAAMC,KAAEa,GAAE,cAAcvB,EAAC;AAAE,QAAG,UAAQuB,GAAE,eAAaD,KAAEZ,GAAE,UAAQY,KAAEZ,GAAE,UAAQQ,KAAER,GAAE,UAAQQ,KAAER,GAAE,OAAO,QAAOD;AAAE,UAAK,EAAC,QAAOE,IAAE,YAAWC,IAAE,aAAYC,GAAC,IAAEU,IAAE,EAAC,GAAEzB,IAAE,GAAEgB,GAAC,IAAE,KAAK,oBAAoBd,EAAC,GAAEe,KAAE,KAAK,OAAOZ,GAAE,OAAKQ,GAAE,KAAGb,EAAC,IAAEc,IAAEI,KAAE,KAAK,OAAOb,GAAE,OAAKQ,GAAE,KAAGb,EAAC,IAAEc,MAAGA,IAAEK,KAAE,KAAK,OAAON,GAAE,IAAER,GAAE,QAAMW,EAAC,IAAED,IAAE,IAAE,KAAK,OAAOF,GAAE,IAAER,GAAE,QAAMW,EAAC,IAAED,MAAGA,IAAEM,KAAEG,OAAIZ,GAAE,SAAOK,KAAE,GAAEK,KAAEF,OAAIR,GAAE,SAAOO,KAAE,GAAEI,KAAEC,OAAIZ,GAAE,SAAOM,KAAEJ,IAAE,IAAEM,OAAIR,GAAE,SAAO,IAAEG;AAAE,WAAOA,GAAEJ,IAAE,EAAC,GAAEU,IAAE,GAAEC,GAAC,GAAE,EAAC,OAAMC,KAAEF,IAAE,QAAO,IAAEC,GAAC,CAAC,GAAEX;AAAA,EAAC;AAAA,EAAC,cAAcT,IAAE;AAAC,QAAG,CAAC,KAAK,WAAS,EAAEA,EAAC,KAAG,MAAIA,GAAE,OAAO,QAAO;AAAK,UAAMkB,KAAElB;AAAE,aAAQsB,KAAE,GAAEA,KAAE,KAAK,QAAQ,QAAOA,MAAI;AAAC,YAAMtB,KAAE,KAAK,QAAQsB,EAAC,EAAE;AAA2B,UAAGtB,GAAE,WAASkB,GAAE,UAAQ,CAAClB,GAAE,KAAM,CAAAA,OAAG;AAAC,cAAMsB,KAAEJ,GAAE,KAAM,CAAAA,OAAGlB,GAAE,iBAAekB,GAAE,gBAAcA,GAAE,kBAAgBlB,GAAE,aAAc;AAAE,YAAG,CAACsB,GAAE,QAAM;AAAG,gBAAO,MAAM,QAAQtB,GAAE,OAAO,CAAC,CAAC,IAAE,GAAGA,GAAE,OAAO,CAAC,EAAE,CAAC,CAAC,IAAIA,GAAE,OAAO,CAAC,EAAE,CAAC,CAAC,KAAGA,GAAE,OAAO,CAAC,QAAM,MAAM,QAAQsB,GAAE,OAAO,CAAC,CAAC,IAAE,GAAGA,GAAE,OAAO,CAAC,EAAE,CAAC,CAAC,IAAIA,GAAE,OAAO,CAAC,EAAE,CAAC,CAAC,KAAGA,GAAE,OAAO,CAAC;AAAA,MAAE,CAAE,EAAE,QAAOA;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,MAAM,kCAAkCtB,IAAEkB,IAAE;AAAC,UAAMI,KAAE,KAAK,QAAQ,KAAK,MAAI,eAAc,EAAC,OAAM,EAAC,UAAStB,IAAE,GAAE,OAAM,GAAE,QAAOkB,GAAC,CAAC,EAAE,KAAM,CAAAlB,OAAC;AAJt4K;AAIw4K,mBAAAA,GAAE,SAAF,mBAAQ;AAAA,KAAW,GAAEI,KAAE,KAAK,QAAQ,KAAK,MAAI,eAAc,EAAC,OAAM,EAAC,UAASJ,IAAE,GAAE,OAAM,GAAE,QAAOkB,GAAC,CAAC,EAAE,KAAM,CAAAlB,OAAC;AAJl/K;AAIo/K,mBAAAA,GAAE,SAAF,mBAAQ;AAAA,KAAW,GAAEC,KAAE,MAAM,QAAQ,IAAI,CAACqB,IAAElB,EAAC,CAAC;AAAE,WAAOH,GAAE,CAAC,KAAGA,GAAE,CAAC,EAAE,QAAS,CAAAD,OAAG;AAAC,MAAAA,GAAE,MAAIA,GAAE,MAAKA,GAAE,SAAOA,GAAE;AAAA,IAAiB,CAAE,GAAE,EAAC,YAAWC,GAAE,CAAC,KAAG,MAAK,YAAWA,GAAE,CAAC,KAAG,KAAI;AAAA,EAAC;AAAA,EAAC,MAAM,mCAAmCD,IAAEkB,KAAE,CAAC,GAAE;AAAC,QAAG,CAAC,KAAK,cAAc,QAAO;AAAE,QAAII,KAAE,KAAK,sBAAsBtB,IAAE,GAAE,EAAEkB,GAAE,mBAAmB,CAAC;AAAE,QAAG,SAAOI,GAAE,QAAO;AAAK,QAAIlB,KAAE;AAAE,UAAK,EAAC,qBAAoBH,GAAC,IAAE,KAAK,WAAW;AAAY,QAAIsB,KAAEtB,KAAEG,KAAE,KAAK;AAAa,UAAMD,KAAEmB,GAAE;AAAY,WAAKC,MAAG,KAAG;AAAC,UAAG;AAAC,YAAG,gBAAc,MAAM,KAAK,cAAc,kBAAkBA,IAAED,GAAE,KAAIA,GAAE,KAAIJ,EAAC,EAAE;AAAA,MAAK,QAAM;AAAA,MAAC;AAAC,UAAGK,MAAInB,MAAIkB,KAAE,KAAK,sBAAsBnB,IAAEC,IAAE,EAAEc,GAAE,mBAAmB,CAAC,GAAE,SAAOI,GAAE,QAAO;AAAA,IAAI;AAAC,WAAM,OAAKC,MAAG,QAAMD,KAAE,OAAKlB;AAAA,EAAC;AAAA,EAAC,MAAM,iBAAiBJ,IAAE;AAAC,UAAMkB,KAAE,KAAK;AAAW,QAAG,UAAQ,KAAK,UAAS;AAAC,YAAMlB,KAAEkB,GAAE,cAAYA,GAAE,QAAOI,KAAE,KAAK,MAAMtB,GAAE,OAAKA,GAAE,QAAMkB,GAAE,aAAW,GAAE,GAAEd,KAAE,KAAK,MAAMJ,GAAE,OAAKA,GAAE,QAAMkB,GAAE,aAAW,GAAE,GAAEjB,KAAE,EAAE,SAASiB,GAAE,oBAAkBlB,GAAE,gBAAgB,GAAEE,KAAE,IAAIY,GAAE,EAAC,GAAEI,GAAE,YAAW,GAAEA,GAAE,YAAW,kBAAiBjB,GAAC,CAAC;AAAE,aAAO,IAAIyB,GAAE,EAAC,OAAMJ,IAAE,QAAOlB,IAAE,WAAU,GAAE,QAAOU,GAAE,SAASd,EAAC,GAAE,kBAAiBC,IAAE,WAAUC,IAAE,WAAU,MAAK,YAAW,MAAK,eAAc,EAAC,UAAS,YAAW,EAAC,CAAC;AAAA,IAAC;AAAC,UAAK,EAAC,QAAOoB,GAAC,IAAEtB,IAAEI,KAAEoB,GAAE,KAAK,KAAI,KAAK,YAAW,EAAC,QAAOF,IAAE,OAAM,KAAK,SAAS,sBAAqB,CAAC,GAAErB,KAAEiB,GAAE,qBAAmB,KAAK,QAAQ,GAAG,KAAK,GAAG,WAAU,EAAC,OAAM,EAAC,GAAE,OAAM,GAAE,QAAOI,GAAC,CAAC,EAAE,KAAM,CAAAtB,OAAGA,GAAE,QAAMA,GAAE,KAAK,MAAO,EAAE,MAAO,MAAI,IAAK,IAAE,MAAKE,KAAE,MAAM,QAAQ,IAAI,CAACE,IAAEH,EAAC,CAAC;AAAE,WAAO,KAAK,UAAQC,GAAE,CAAC,GAAEA,GAAE,CAAC;AAAA,EAAC;AAAA,EAAC,yBAAwB;AAAC,UAAK,EAAC,YAAWF,GAAC,IAAE;AAAK,IAAAA,GAAE,YAAUA,GAAE,WAAS,MAAIA,GAAE,WAAS,IAAGA,GAAE,YAAUA,GAAE,WAAS,MAAIA,GAAE,WAAS;AAAA,EAAE;AAAA,EAAC,aAAaA,IAAE;AAAC,UAAK,EAAC,QAAOkB,IAAE,kBAAiBI,GAAC,IAAEtB;AAAE,IAAAkB,GAAE,OAAK,MAAIA,GAAE,OAAK,QAAKI,MAAA,gBAAAA,GAAG,SAAMA,GAAE,iBAAetB,GAAE,eAAaA,GAAE,QAAOA,GAAE,YAAU,IAAIM,MAAEN,GAAE,SAAOA,GAAE,UAAU,iBAAiBkB,EAAC;AAAA,EAAE;AAAA,EAAC,kBAAkBlB,IAAEkB,IAAE;AAAC,UAAK,EAAC,WAAUI,GAAC,IAAEtB,IAAEI,KAAE,MAAGJ,GAAE,QAAMsB,GAAE,GAAE,EAAC,MAAKrB,GAAC,IAAEiB,IAAEhB,KAAEgB,GAAE,MAAM,KAAK,IAAI,MAAM,MAAKjB,GAAE,IAAK,CAAAD,OAAGA,GAAE,KAAM,CAAC,CAAC,GAAEuB,KAAEL,GAAE,MAAM,KAAK,IAAI,MAAM,MAAKjB,GAAE,IAAK,CAAAD,OAAGA,GAAE,KAAM,CAAC,CAAC,GAAE,EAAC,UAASG,GAAC,IAAE;AAAK,QAAG,UAAQA,GAAE,QAAO,KAAK,eAAaF,GAAE,CAAC,EAAE,OAAM,CAACC,IAAEqB,EAAC;AAAE,QAAG,aAAWpB,IAAE;AAAC,aAAM,CAACF,GAAE,KAAM,CAAAD,OAAGA,GAAE,eAAasB,GAAE,CAAE,KAAGpB,IAAEqB,EAAC;AAAA,IAAC;AAAC,UAAK,EAAC,UAASlB,IAAE,UAASC,IAAC,IAAE,KAAK;AAAW,QAAIkB,KAAEtB;AAAE,IAAAI,MAAE,MAAIkB,KAAEvB,GAAE,KAAM,CAAAD,OAAG,KAAK,IAAIA,GAAE,QAAMM,GAAC,IAAEF,EAAE,GAAEoB,OAAIA,KAAEvB,GAAE,OAAQ,CAAAD,OAAGA,GAAE,QAAMM,GAAE,EAAE,KAAM,CAACN,IAAEkB,OAAIlB,GAAE,QAAMkB,GAAE,QAAM,IAAE,EAAG,EAAE,CAAC,KAAGhB;AAAI,QAAIuB,KAAEF;AAAE,WAAOlB,KAAE,MAAIoB,KAAExB,GAAE,KAAM,CAAAD,OAAG,KAAK,IAAIA,GAAE,QAAMK,EAAC,IAAED,EAAE,KAAGmB,IAAE,KAAK,eAAaE,GAAE,QAAMF,GAAE,QAAO,CAACC,IAAEC,EAAC;AAAA,EAAC;AAAC;AAAEzB,GAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEF,GAAE,WAAU,iBAAgB,MAAM,GAAEE,GAAE,CAAC,EAAE,CAAC,GAAEF,GAAE,WAAU,YAAW,MAAM,GAAEA,KAAEE,GAAE,CAAC,EAAE,sDAAsD,CAAC,GAAEF,EAAC;AAAE,IAAMgB,KAAEhB;;;ACA3/N,IAAM8B,KAAE,oBAAI;AAAIA,GAAE,IAAI,QAAO,IAAI,GAAEA,GAAE,IAAI,SAAQ,IAAI,GAAEA,GAAE,IAAI,SAAQ,KAAK,GAAEA,GAAE,IAAI,UAAS,KAAK,GAAEA,GAAE,IAAI,SAAQ,KAAK,GAAEA,GAAE,IAAI,UAAS,KAAK,GAAEA,GAAE,IAAI,WAAU,KAAK,GAAEA,GAAE,IAAI,WAAU,KAAK,GAAEA,GAAE,IAAI,YAAW,KAAK;AAAE,IAAMC,KAAE,oBAAI;AAAIA,GAAE,IAAI,QAAO,EAAC,eAAc,QAAO,cAAa,MAAG,eAAc,MAAK,CAAC,GAAEA,GAAE,IAAI,QAAO,EAAC,eAAc,QAAO,cAAa,OAAG,eAAc,OAAM,CAAC,GAAEA,GAAE,IAAI,WAAU,EAAC,eAAc,QAAO,cAAa,MAAG,eAAc,UAAS,CAAC,GAAEA,GAAE,IAAI,QAAO,EAAC,eAAc,QAAO,cAAa,MAAG,eAAc,MAAK,CAAC;AAAE,IAAI,IAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAO,MAAK,KAAK,gBAAc,MAAK,KAAK,gBAAc;AAAA,EAAK;AAAA,EAAC,MAAM,KAAKC,IAAE;AAJhoD;AAIioD,UAAM,KAAK,KAAK,GAAE,KAAK,cAAY,KAAK,IAAI,MAAM,KAAK,IAAI,YAAY,GAAG,IAAE,CAAC;AAAE,UAAMC,KAAED,KAAE,EAAEA,GAAE,MAAM,IAAE,MAAKE,KAAE,MAAM,KAAK,QAAQ,KAAK,KAAI,EAAC,cAAa,OAAM,QAAOD,GAAC,CAAC,GAAE,EAAC,YAAWE,IAAE,OAAMC,GAAC,IAAE,KAAK,aAAaF,GAAE,IAAI;AAAE,QAAG,SAAK,UAAK,SAAS,mBAAd,mBAA8B,QAAQ,aAAW;AAAC,YAAMD,KAAE,MAAM,KAAK,oBAAoBD,EAAC;AAAE,cAAMC,OAAIE,GAAE,aAAWF,GAAE,cAAYE,GAAE,YAAWA,GAAE,aAAWF,GAAE,YAAWA,GAAE,cAAY,EAAEE,GAAE,UAAU,MAAIA,GAAE,aAAWE,GAAEJ,GAAE,UAAU;AAAA,IAAG;AAAC,SAAK,KAAK,cAAaE,EAAC,GAAE,KAAK,SAAOC;AAAE,UAAME,KAAE,MAAM,KAAK,QAAQF,GAAE,OAAM,EAAC,cAAa,gBAAe,QAAOH,GAAC,CAAC;AAAE,SAAK,gBAAc,KAAK,YAAYK,GAAE,IAAI;AAAE,UAAK,EAAC,YAAWC,IAAE,aAAYC,GAAC,IAAE,KAAK,WAAW,aAAYC,MAAE,KAAK,WAAW,YAAY,sBAAqB,EAAC,OAAMC,IAAE,QAAOC,GAAC,IAAE,KAAK,YAAWC,KAAE,CAAC,GAAEC,KAAE,KAAK,qBAAqB;AAAE,QAAIR,KAAE,GAAES,KAAE;AAAG,WAAKT,KAAE,KAAK,cAAc,UAAQ;AAAC,MAAAS;AAAI,YAAMd,KAAE,KAAK,KAAKU,KAAEH,KAAEE,OAAGK,EAAC,IAAE,GAAEb,KAAE,KAAK,KAAKU,KAAEH,KAAEC,OAAGK,EAAC,IAAE;AAAE,MAAAT,OAAIL,KAAE,MAAIC,KAAE,KAAGY,KAAE,GAAED,GAAE,KAAK,EAAC,QAAOX,IAAE,QAAOD,IAAE,QAAO,GAAE,QAAO,EAAC,CAAC;AAAA,IAAC;AAAC,SAAK,WAAW,YAAY,gBAAcY,IAAEE,KAAE,MAAI,KAAK,WAAW,YAAY,oBAAkB,GAAE,KAAK,WAAW,YAAY,sBAAoBA,KAAG,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,MAAM,aAAad,IAAEC,IAAEc,IAAEC,KAAE,CAAC,GAAE;AAAC,UAAK,EAAC,YAAWb,IAAE,aAAYC,IAAE,eAAcG,GAAC,IAAE,KAAK,WAAW,aAAYC,KAAED,GAAEP,EAAC;AAAE,QAAG,CAACQ,MAAGA,GAAE,SAAOP,MAAGO,GAAE,SAAOO,MAAGP,GAAE,SAAOP,MAAGO,GAAE,SAAOO,GAAE,QAAO;AAAK,UAAK,EAAC,WAAUN,KAAE,WAAUC,GAAC,IAAE,KAAK,YAAW,EAAC,QAAOC,IAAE,iBAAgBC,IAAE,kBAAiBC,GAAC,IAAE,KAAK,iBAAiBb,IAAEC,IAAEc,EAAC;AAAE,QAAG,CAACJ,MAAG,MAAIA,GAAE,OAAO,QAAO;AAAK,QAAG,MAAIA,GAAE,CAAC,EAAE,QAAM,MAAIA,GAAE,CAAC,EAAE,IAAG;AAAC,YAAMX,KAAE,IAAI,WAAWG,KAAEC,EAAC;AAAE,aAAO,IAAIM,GAAE,EAAC,OAAMP,IAAE,QAAOC,IAAE,QAAO,MAAK,MAAKJ,IAAE,iBAAgB,EAAC,CAAC;AAAA,IAAC;AAAC,UAAK,EAAC,SAAQK,GAAC,IAAE,KAAK,UAASY,KAAE,KAAK,qBAAqB,GAAEH,KAAE,CAAC;AAAE,QAAII,KAAE;AAAE,SAAIA,KAAE,GAAEA,KAAED,IAAEC,KAAI,EAAC,CAACb,MAAGA,GAAE,QAAQa,EAAC,IAAE,OAAKJ,GAAE,KAAK,KAAK,QAAQ,KAAK,OAAO,MAAK,EAAC,OAAM,EAAC,MAAKH,GAAEO,EAAC,EAAE,MAAK,IAAGP,GAAEO,EAAC,EAAE,GAAE,GAAE,cAAa,gBAAe,QAAOF,GAAE,OAAM,CAAC,CAAC;AAAE,UAAMG,KAAE,MAAM,QAAQ,IAAIL,EAAC,GAAEjB,KAAEsB,GAAE,IAAK,CAAAnB,OAAGA,GAAE,KAAK,UAAW,EAAE,OAAQ,CAACA,IAAEC,OAAID,KAAEC,EAAE,GAAEmB,KAAE,IAAI,WAAWvB,EAAC;AAAE,QAAIwB,KAAE;AAAE,SAAIH,KAAE,GAAEA,KAAED,IAAEC,KAAI,CAAAE,GAAE,IAAI,IAAI,WAAWD,GAAED,EAAC,EAAE,IAAI,GAAEG,EAAC,GAAEA,MAAGF,GAAED,EAAC,EAAE,KAAK;AAAW,UAAMI,KAAExB,GAAE,IAAI,KAAK,WAAW,YAAY,WAAW,EAAE,eAAcyB,KAAE,MAAM,KAAK,iBAAiBH,GAAE,QAAO,EAAC,OAAMjB,IAAE,QAAOC,IAAE,QAAOkB,IAAE,SAAOjB,MAAA,gBAAAA,GAAG,WAAQI,KAAE,WAAUC,GAAC,CAAC;AAAE,QAAG,QAAMa,GAAE,QAAO;AAAK,QAAG,EAAE,KAAK,WAAW,WAAW,KAAG,WAASD,MAAG,CAACC,GAAE,MAAK;AAAC,YAAMvB,KAAE,KAAK,WAAW,YAAY,CAAC;AAAE,UAAG,QAAMA,IAAE;AAAC,cAAMC,KAAEsB,GAAE,QAAMA,GAAE,QAAOR,KAAE,IAAI,WAAWd,EAAC;AAAE,YAAG,KAAK,IAAID,EAAC,IAAE,KAAK,MAAIkB,KAAE,GAAEA,KAAEjB,IAAEiB,KAAI,MAAK,KAAKK,GAAE,OAAO,CAAC,EAAEL,EAAC,IAAElB,MAAGA,EAAC,IAAE,SAAOe,GAAEG,EAAC,IAAE;AAAA,YAAQ,MAAIA,KAAE,GAAEA,KAAEjB,IAAEiB,KAAI,CAAAK,GAAE,OAAO,CAAC,EAAEL,EAAC,MAAIlB,OAAIe,GAAEG,EAAC,IAAE;AAAG,QAAAK,GAAE,OAAKR;AAAA,MAAC;AAAA,IAAC;AAAC,QAAIS,KAAE,GAAEC,KAAE;AAAE,QAAGb,OAAIT,MAAGU,OAAIT,IAAE;AAAC,UAAIJ,KAAEuB,GAAE;AAAK,UAAGvB,GAAE,MAAIkB,KAAE,GAAEA,KAAEd,IAAEc,KAAI,KAAGO,KAAEP,KAAEf,IAAEe,KAAEL,GAAE,MAAIW,KAAEZ,IAAEY,KAAErB,IAAEqB,KAAI,CAAAxB,GAAEyB,KAAED,EAAC,IAAE;AAAA,UAAO,MAAIA,KAAE,GAAEA,KAAErB,IAAEqB,KAAI,CAAAxB,GAAEyB,KAAED,EAAC,IAAE;AAAA,UAAO,MAAIxB,KAAE,IAAI,WAAWG,KAAEC,EAAC,GAAEmB,GAAE,OAAKvB,IAAEkB,KAAE,GAAEA,KAAEL,IAAEK,KAAI,MAAIO,KAAEP,KAAEf,IAAEqB,KAAE,GAAEA,KAAEZ,IAAEY,KAAI,CAAAxB,GAAEyB,KAAED,EAAC,IAAE;AAAA,IAAC;AAAC,WAAOD;AAAA,EAAC;AAAA,EAAC,YAAYvB,IAAE;AAAC,QAAGA,GAAE,aAAW,KAAG,EAAE,OAAM,IAAI,MAAM,8CAA8C;AAAE,QAAIC,IAAEc,IAAEC,IAAEd,IAAEC,IAAEC;AAAE,QAAGW,IAAE;AAAC,WAAIA,KAAE,IAAI,WAAWf,EAAC,GAAEE,KAAE,IAAI,YAAYF,GAAE,UAAU,GAAEgB,KAAE,IAAI,WAAWd,EAAC,GAAEC,KAAE,GAAEA,KAAEH,GAAE,aAAW,GAAEG,KAAI,MAAIC,KAAE,GAAEA,KAAE,GAAEA,KAAI,CAAAY,GAAE,IAAEb,KAAEC,EAAC,IAAEW,GAAE,IAAEZ,KAAE,IAAEC,EAAC;AAAE,MAAAH,KAAE,IAAI,YAAYC,EAAC;AAAA,IAAC,MAAM,CAAAD,KAAE,IAAI,YAAYD,EAAC;AAAE,WAAOC;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,WAAOH,GAAE,IAAI,KAAK,WAAW,YAAY,WAAW,EAAE,eAAa,IAAE,KAAK,WAAW;AAAA,EAAS;AAAA,EAAC,iBAAiBE,IAAEC,IAAEc,IAAE;AAAC,UAAK,EAAC,YAAWC,IAAE,aAAYd,IAAE,sBAAqBC,GAAC,IAAE,KAAK,WAAW,aAAY,EAAC,OAAMC,IAAE,QAAOE,GAAC,IAAE,KAAK,YAAWC,KAAE,KAAK,qBAAqB;AAAE,QAAIC,IAAEC,KAAEC,IAAEC,KAAE,GAAEC,KAAE;AAAE,SAAIF,KAAE,GAAEA,KAAEV,IAAEU,KAAI,CAAAE,KAAET,MAAGO,IAAEF,KAAE,KAAK,KAAKJ,KAAEY,KAAEJ,EAAC,GAAEH,MAAE,KAAK,KAAKH,KAAEJ,KAAEU,EAAC,GAAED,MAAGH,KAAEC;AAAE,IAAAG,KAAET,MAAGH,IAAEQ,KAAE,KAAK,KAAKJ,KAAEY,KAAEJ,EAAC,GAAEH,MAAE,KAAK,KAAKH,KAAEJ,KAAEU,EAAC,GAAED,MAAGV,KAAEO,KAAEO,IAAEJ,MAAG,IAAEJ;AAAE,UAAMM,KAAE,KAAK,cAAc,SAASF,IAAEA,KAAE,IAAEJ,EAAC;AAAE,QAAIF,KAAE,GAAEY,KAAE;AAAE,UAAMH,KAAE,CAAC;AAAE,aAAQI,KAAE,GAAEA,KAAEX,IAAEW,KAAI,CAAAb,KAAEQ,GAAE,IAAEK,KAAE,CAAC,IAAE,KAAG,KAAGL,GAAE,IAAEK,KAAE,CAAC,GAAED,KAAEZ,KAAEQ,GAAE,IAAEK,KAAE,CAAC,IAAE,KAAG,KAAGL,GAAE,IAAEK,KAAE,CAAC,GAAEJ,GAAE,KAAK,EAAC,MAAKT,IAAE,IAAGY,GAAC,CAAC;AAAE,WAAM,EAAC,QAAOH,IAAE,iBAAgBC,KAAEP,KAAE,IAAEQ,KAAE,KAAK,KAAKZ,KAAEQ,EAAC,IAAEI,MAAGR,KAAE,IAAG,kBAAiBP,KAAEQ,MAAE,IAAEP,KAAE,KAAK,KAAKI,KAAEM,EAAC,IAAEV,MAAGO,MAAE,GAAE;AAAA,EAAC;AAAA,EAAC,aAAaT,IAAE;AAAC,UAAMe,KAAEd,GAAED,IAAE,iBAAiB;AAAE,QAAG,CAACe,GAAE,OAAM,IAAIC,GAAE,YAAW,wBAAwB;AAAE,UAAMA,KAAEf,GAAEc,IAAE,MAAM,GAAEb,KAAE,SAASc,GAAE,aAAa,GAAG,GAAE,EAAE,GAAEb,KAAE,SAASa,GAAE,aAAa,GAAG,GAAE,EAAE,GAAEZ,KAAE,SAASY,GAAE,aAAa,GAAG,GAAE,EAAE,GAAEV,MAAGN,GAAEe,IAAE,aAAa,KAAG,QAAQ,YAAY;AAAE,QAAG,CAACjB,GAAE,IAAIQ,EAAC,EAAE,OAAM,IAAIU,GAAE,YAAW,4CAA0CV,EAAC;AAAE,UAAMG,MAAET,GAAEe,IAAE,UAAU,KAAG,SAAQJ,KAAEd,GAAE,IAAIY,GAAC;AAAE,QAAG,QAAME,GAAE,OAAM,IAAIK,GAAE,YAAW,2CAAyCP,GAAC;AAAE,UAAMJ,KAAEJ,GAAEc,IAAE,UAAU,GAAEE,KAAE,SAASZ,GAAE,aAAa,GAAG,GAAE,EAAE,GAAEe,KAAE,SAASf,GAAE,aAAa,GAAG,GAAE,EAAE,GAAEgB,KAAEpB,GAAEc,IAAE,YAAY;AAAE,QAAIO,IAAEC;AAAE,IAAAF,OAAIE,KAAEF,GAAE,aAAa,QAAQ,GAAE,QAAME,OAAID,KAAEC,GAAE,KAAK,EAAE,MAAM,GAAG,EAAE,IAAK,CAAAvB,OAAG,WAAWA,EAAC,CAAE;AAAI,QAAGC,GAAED,IAAE,uBAAuB,EAAE,OAAM,IAAIgB,GAAE,YAAW,6DAA6D;AAAE,UAAMQ,KAAEvB,GAAED,IAAE,kBAAkB,GAAEyB,KAAExB,GAAEuB,IAAE,aAAa;AAAE,QAAIE,IAAE,IAAE;AAAG,QAAG,QAAMD,IAAE;AAAC,YAAMzB,KAAE,WAAWyB,GAAE,aAAa,MAAM,CAAC,GAAExB,KAAE,WAAWwB,GAAE,aAAa,MAAM,CAAC,GAAEV,KAAE,WAAWU,GAAE,aAAa,MAAM,CAAC,GAAET,KAAE,WAAWS,GAAE,aAAa,MAAM,CAAC,GAAEvB,KAAEF,GAAEwB,IAAE,YAAY,KAAG;AAAG,UAAIrB,KAAE,EAAE;AAAM,UAAG,iBAAeD,GAAE,KAAGA,GAAE,YAAY,EAAE,WAAW,OAAO,GAAE;AAAC,cAAMF,KAAE,OAAOE,GAAE,MAAM,CAAC,CAAC;AAAE,cAAMF,EAAC,KAAG,MAAIA,OAAIG,KAAE,IAAI,EAAE,EAAC,MAAKH,GAAC,CAAC;AAAA,MAAE,MAAM,CAAAG,KAAEO,GAAER,EAAC,KAAG,EAAE;AAAA,UAAW,KAAE,MAAGC,KAAE,IAAI,EAAE,EAAC,MAAK,KAAI,CAAC;AAAE,MAAAuB,KAAE,IAAIP,GAAEnB,IAAEC,IAAEc,IAAEC,EAAC,GAAEU,GAAE,mBAAiBvB;AAAA,IAAC,MAAM,KAAE,MAAGuB,KAAE,IAAIP,GAAE,EAAC,MAAK,MAAI,MAAK,MAAGhB,IAAE,MAAKD,KAAE,KAAG,MAAK,KAAG,kBAAiB,IAAI,EAAE,EAAC,MAAK,KAAI,CAAC,EAAC,CAAC;AAAE,UAAMyB,KAAE1B,GAAED,IAAE,gBAAgB,GAAE4B,KAAE,SAASD,MAAGA,GAAE,aAAa,OAAO,KAAG,KAAI,EAAE,GAAEE,KAAEH,GAAE,kBAAiBI,KAAE,IAAIxB,GAAE,EAAC,QAAO,IAAIa,GAAE,EAAC,GAAEO,GAAE,MAAK,GAAEA,GAAE,MAAK,kBAAiBG,GAAC,CAAC,GAAE,YAAWZ,IAAE,aAAYG,IAAE,mBAAkBH,IAAE,oBAAmBG,IAAE,aAAYd,IAAE,sBAAqBsB,GAAC,CAAC,GAAEG,KAAE,IAAIZ,GAAE,EAAC,GAAEO,GAAE,QAAMxB,IAAE,GAAEwB,GAAE,SAAOvB,IAAE,kBAAiB0B,GAAC,CAAC,GAAEG,KAAE,IAAInB,GAAE,EAAC,OAAMX,IAAE,QAAOC,IAAE,QAAOuB,IAAE,0BAAyB,GAAE,kBAAiBG,IAAE,WAAUzB,IAAE,WAAUO,IAAE,WAAUoB,IAAE,aAAYT,IAAE,aAAYQ,GAAC,CAAC,GAAE,IAAE9B,GAAEA,IAAE,UAAU,GAAEiC,KAAEjC,GAAEA,IAAE,WAAW;AAAE,WAAM,EAAC,YAAWgC,IAAE,OAAM,EAAC,KAAI,KAAK,KAAI,OAAMC,MAAG,KAAK,IAAI,QAAQ,QAAO,MAAM,GAAE,MAAK,KAAG,KAAK,IAAI,QAAQ,QAAOnC,GAAE,IAAIQ,EAAC,EAAE,aAAa,EAAC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,oBAAoBN,IAAE;AAAC,QAAG;AAAC,YAAK,EAAC,MAAKC,GAAC,IAAE,MAAM,KAAK,QAAQ,KAAK,MAAI,YAAW,EAAC,cAAa,OAAM,QAAOD,MAAA,gBAAAA,GAAG,OAAM,CAAC;AAAE,aAAOW,GAAEV,EAAC;AAAA,IAAC,QAAM;AAAC,aAAO;AAAA,IAAI;AAAA,EAAC;AAAC;AAAEA,GAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,UAAS,MAAM,GAAEA,GAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAEA,GAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAE,EAAE,WAAU,iBAAgB,MAAM,GAAE,IAAEA,GAAE,CAAC,EAAE,wCAAwC,CAAC,GAAE,CAAC;AAAE,IAAMoB,KAAE;;;ACA1+M,IAAMa,KAAE,CAACC,IAAEC,OAAE;AAJprC;AAIsrC,eAAAD,GAAE,IAAIC,EAAC,MAAP,mBAAU;AAAA;AAAzB,IAAgCC,KAAE,CAACF,IAAEC,OAAE;AAJ9sC;AAIgtC,qBAAAD,GAAE,IAAIC,EAAC,MAAP,mBAAU,WAAV,mBAAmB;AAAA;AAAG,IAAIE,KAAE,cAAcC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,SAAO,MAAK,KAAK,cAAY,MAAK,KAAK,cAAY,SAAQ,KAAK,gBAAc;AAAA,EAAM;AAAA,EAAC,MAAM,KAAKJ,IAAE;AAAC,UAAM,KAAK,KAAK;AAAE,UAAMK,KAAEL,KAAE,EAAEA,GAAE,MAAM,IAAE,MAAK,EAAC,MAAKM,GAAC,IAAE,MAAM,KAAK,QAAQ,KAAK,KAAI,EAAC,OAAM,EAAC,MAAK,GAAE,IAAG,KAAK,YAAW,GAAE,cAAa,gBAAe,QAAOD,GAAC,CAAC;AAAE,QAAG,CAACC,GAAE,OAAM,IAAID,GAAE,mBAAkB,wBAAsB,KAAK,GAAG;AAAE,SAAK,cAAY,KAAK,IAAI,MAAM,KAAK,IAAI,YAAY,GAAG,IAAE,GAAE,KAAK,IAAI,YAAY,GAAG,CAAC;AAAE,UAAK,EAAC,cAAaE,IAAE,aAAYC,IAAE,WAAUC,GAAC,IAAEC,GAAEJ,EAAC,GAAEK,KAAE,CAAC;AAAE,UAAM,KAAK,UAAUA,IAAEL,IAAEC,IAAEC,IAAE,GAAEC,KAAE,IAAE,GAAEJ,EAAC;AAAE,UAAK,EAAC,WAAUO,IAAE,YAAWC,IAAC,IAAE,KAAK,WAAWF,EAAC,GAAEG,KAAE,EAAEH,EAAC,GAAEI,KAAEC,GAAEL,EAAC;AAAE,QAAG,KAAK,cAAY,EAAC,cAAaJ,IAAE,WAAUE,IAAE,MAAKE,IAAE,aAAYG,IAAE,UAASC,IAAE,GAAGH,GAAC,GAAE,KAAK,KAAK,cAAaC,GAAC,GAAE,CAACD,GAAE,YAAY,OAAM,IAAIP,GAAE,mBAAkB,iCAA+BO,GAAE,OAAO;AAAE,QAAG,CAACA,GAAE,UAAU,OAAM,IAAIP,GAAE,mBAAkB,wEAAwE;AAAE,UAAK,EAAC,gBAAeY,KAAE,CAAC,EAAC,IAAE,KAAK;AAAS,QAAG,CAACA,GAAE,SAAS,SAAS,GAAE;AAAC,YAAMhB,KAAE,MAAM,KAAK,wBAAwBD,EAAC;AAAE,cAAMC,MAAG,KAAK,gBAAgBA,IAAEY,GAAC;AAAA,IAAC;AAAC,IAAAI,GAAE,SAAS,SAAS,KAAG,MAAIJ,IAAE,aAAW,SAAOA,IAAE,cAAYA,IAAE,iBAAe,MAAM,KAAK,qBAAqBb,EAAC,GAAE,EAAEa,IAAE,cAAc,MAAIA,IAAE,cAAc,WAAS,cAAa,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,MAAM,aAAab,IAAEC,IAAEiB,IAAEb,KAAE,CAAC,GAAE;AAJthF;AAIuhF,QAAG,GAAC,UAAK,gBAAL,mBAAkB,gBAAa,KAAK,eAAeL,IAAEC,IAAEiB,EAAC,EAAE,QAAO;AAAK,UAAMZ,KAAE,MAAM,KAAK,kBAAkBN,IAAEC,IAAEiB,IAAE,OAAGb,EAAC;AAAE,QAAG,EAAEC,EAAC,KAAG,KAAK,YAAY,aAAY;AAAC,YAAMC,KAAE,MAAM,KAAK,kBAAkBP,IAAEC,IAAEiB,IAAE,MAAGb,EAAC;AAAE,QAAEE,EAAC,KAAGA,GAAE,OAAO,CAAC,aAAY,eAAaD,GAAE,OAAKC,GAAE,OAAO,CAAC;AAAA,IAAE;AAAC,WAAOD;AAAA,EAAC;AAAA,EAAC,WAAWN,IAAE;AAJ7zF;AAI8zF,UAAMC,KAAEkB,GAAEnB,EAAC,GAAE,EAAC,OAAMkB,IAAE,QAAOE,IAAE,WAAUf,IAAE,YAAWC,IAAE,QAAOC,IAAE,WAAUI,IAAE,aAAYC,IAAE,mBAAkBE,IAAE,qBAAoBO,IAAE,mBAAkBC,IAAE,oBAAmBC,IAAE,cAAaN,IAAE,QAAOO,IAAE,UAASC,GAAC,IAAExB,IAAEyB,OAAE,KAAAzB,GAAE,OAAO,qBAAT,mBAA2B,UAAK,KAAAA,GAAE,OAAO,qBAAT,mBAA2B;AAAK,QAAI0B,KAAEN,GAAEK,EAAC,GAAEE,KAAE,CAAC,CAAC3B,GAAE;AAAmB,YAAM0B,OAAIC,KAAE,MAAGD,KAAE,IAAI,EAAE,EAAC,MAAK,KAAI,CAAC;AAAG,UAAMxB,KAAE,IAAIwB,GAAE,EAAC,GAAG1B,GAAE,QAAO,kBAAiB0B,GAAC,CAAC,GAAEjB,KAAE,IAAIiB,GAAExB,KAAE,EAAC,GAAEA,GAAE,MAAK,GAAEA,GAAE,MAAK,kBAAiBwB,GAAC,IAAE,EAAC,GAAE,GAAE,GAAE,EAAC,CAAC,GAAE,IAAE,IAAIpB,GAAE,EAAC,YAAWF,IAAE,aAAYC,IAAE,mBAAkBgB,IAAE,oBAAmBC,IAAE,aAAYX,IAAE,QAAOF,IAAE,mBAAkBI,IAAE,qBAAoBO,IAAE,eAAcJ,GAAC,CAAC,GAAEY,KAAE,IAAIF,GAAE,EAAC,IAAGxB,GAAE,OAAKA,GAAE,QAAMe,IAAE,IAAGf,GAAE,OAAKA,GAAE,QAAMiB,IAAE,kBAAiBO,GAAC,CAAC,GAAEG,KAAEL,KAAE,EAAC,gBAAeA,GAAE,gBAAe,UAASA,GAAE,SAAQ,IAAE,CAAC;AAAE,QAAIM,KAAE;AAAK,UAAMC,KAAE9B,GAAEF,GAAE,CAAC,GAAE,2BAA2B,GAAEiC,KAAElC,GAAEC,GAAE,CAAC,GAAE,UAAU;AAAE,QAAGgC,MAAG,MAAGC,MAAA,gBAAAA,GAAG,UAAO,KAAGA,GAAE,SAAO,KAAG,GAAE;AAAC,MAAAF,KAAE,CAAC;AAAE,YAAM/B,KAAEiC,GAAE,SAAO;AAAE,eAAQhC,KAAE,GAAEA,KAAED,IAAEC,KAAI,CAAA8B,GAAE,KAAK,CAAC9B,IAAEgC,GAAEhC,EAAC,MAAI,GAAEgC,GAAEhC,KAAED,EAAC,MAAI,GAAEiC,GAAEhC,KAAE,IAAED,EAAC,MAAI,CAAC,CAAC;AAAA,IAAC;AAAC,UAAMkC,KAAE,IAAItB,GAAE,EAAC,OAAMM,IAAE,QAAOE,IAAE,WAAUb,IAAE,WAAUI,IAAE,WAAUkB,IAAE,aAAY,GAAE,kBAAiBF,IAAE,0BAAyBC,IAAE,eAAcE,IAAE,QAAO3B,IAAE,UAAS4B,IAAE,YAAWN,KAAEA,GAAE,aAAW,KAAI,CAAC;AAAE,YAAOD,MAAA,gBAAAA,GAAG,YAASU,GAAE,eAAa,IAAIP,GAAE,EAAC,MAAK,MAAI,MAAK,MAAGP,IAAE,MAAKF,KAAE,KAAG,MAAK,KAAG,kBAAiBS,GAAC,CAAC,GAAEO,GAAE,YAAU,IAAIb,GAAE,EAAC,iBAAgB,GAAE,qBAAoB,CAACG,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,GAAEA,GAAE,CAAC,IAAEA,GAAE,CAAC,IAAE,GAAEA,GAAE,CAAC,GAAEA,GAAE,CAAC,GAAE,CAACA,GAAE,CAAC,GAAE,CAACA,GAAE,CAAC,CAAC,EAAC,CAAC,GAAEU,GAAE,SAAOA,GAAE,UAAU,iBAAiBA,GAAE,YAAY,GAAEA,GAAE,YAAU,IAAIP,GAAE,EAAC,IAAGxB,GAAE,OAAKA,GAAE,QAAMe,IAAE,IAAGf,GAAE,OAAKA,GAAE,QAAMiB,IAAE,kBAAiBO,GAAC,CAAC,GAAE,EAAE,OAAO,IAAE,MAAI,EAAE,OAAO,IAAE,MAAI,EAAC,WAAU1B,IAAE,YAAWiC,GAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBlC,IAAEC,IAAE;AAAC,QAAGA,GAAE,aAAWD,GAAE,cAAYC,GAAE,YAAWA,GAAE,aAAWD,GAAE,YAAWA,GAAE,cAAY,EAAEC,GAAE,UAAU,MAAIA,GAAE,aAAWgB,GAAEjB,GAAE,UAAU,IAAGA,GAAE,aAAW,EAAEC,GAAE,SAAS,GAAE;AAAC,MAAAA,GAAE,YAAUD,GAAE,WAAUC,GAAE,eAAaA,GAAE;AAAO,YAAMiB,KAAEjB,GAAE,UAAU,iBAAiBA,GAAE,YAAY;AAAE,MAAAA,GAAE,YAAU,IAAI0B,GAAE,EAAC,IAAGT,GAAE,OAAKA,GAAE,QAAMjB,GAAE,OAAM,IAAGiB,GAAE,OAAKA,GAAE,QAAMjB,GAAE,QAAO,kBAAiBA,GAAE,iBAAgB,CAAC,GAAEA,GAAE,SAAOiB;AAAA,IAAC;AAAC,IAAAjB,GAAE,4BAA0BD,GAAE,qBAAmBC,GAAE,mBAAiBD,GAAE;AAAA,EAAiB;AAAA,EAAC,MAAM,UAAUA,IAAEC,IAAEiB,IAAEE,IAAEf,IAAEC,KAAE,GAAEC,IAAE;AAAC,QAAG,CAACa,GAAE,QAAO;AAAK,QAAGA,MAAGnB,GAAE,cAAYmB,KAAE,GAAE;AAAC,MAAAnB,MAAG,MAAM,KAAK,QAAQ,KAAK,KAAI,EAAC,OAAM,EAAC,MAAKmB,KAAEf,IAAE,IAAGe,KAAEf,KAAE,KAAK,YAAW,GAAE,cAAa,gBAAe,QAAOE,GAAC,CAAC,GAAG,MAAKF,KAAEe,KAAEf,IAAEe,KAAE;AAAA,IAAC;AAAC,UAAMZ,KAAE,MAAM,KAAK,SAASP,IAAEiB,IAAEE,IAAEf,IAAEE,GAAE,WAAUD,IAAEC,EAAC;AAAE,QAAGP,GAAE,KAAKQ,GAAE,GAAG,GAAE,CAACA,GAAE,QAAQ,QAAO;AAAK,UAAM,KAAK,UAAUR,IAAEC,IAAEiB,IAAEV,GAAE,UAAQH,IAAEA,IAAEC,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,SAASP,IAAEC,IAAEiB,IAAEb,IAAEC,KAAEC,GAAE,WAAUA,KAAE,GAAEC,IAAE;AAJrrK;AAIsrK,QAAG,CAACR,GAAE,QAAO;AAAK,UAAMS,KAAE,EAAET,IAAEC,IAAEiB,IAAEb,IAAEC,IAAEC,EAAC;AAAE,QAAGE,GAAE,SAAQ;AAAC,YAAMS,KAAE,CAAC;AAAE,WAAG,KAAAT,GAAE,QAAF,mBAAO,QAAS,CAAAT,OAAG;AAAC,QAAAA,GAAE,UAAQkB,GAAE,KAAKlB,EAAC;AAAA,MAAC,IAAIkB,GAAE,SAAO,GAAE;AAAC,cAAMZ,KAAEY,GAAE,IAAK,CAAAlB,OAAGA,GAAE,iBAAkB,EAAE,OAAO,CAAC,GAAEO,MAAE,KAAK,IAAI,MAAM,MAAKD,GAAE,IAAK,CAAAN,OAAGA,GAAE,CAAC,CAAE,CAAC;AAAE,YAAG,KAAK,IAAI,MAAM,MAAKM,GAAE,IAAK,CAAAN,OAAGA,GAAE,CAAC,IAAEA,GAAE,CAAC,CAAE,CAAC,IAAEO,OAAG,KAAK,aAAY;AAAC,gBAAK,EAAC,MAAKa,GAAC,IAAE,MAAM,KAAK,QAAQ,KAAK,KAAI,EAAC,OAAM,EAAC,MAAKb,KAAE,IAAGA,MAAE,KAAK,YAAW,GAAE,cAAa,gBAAe,QAAOC,GAAC,CAAC;AAAE,UAAAR,KAAEoB,IAAEf,KAAEE,KAAEW,GAAE,QAAS,CAAAA,OAAGc,GAAEhC,IAAEC,IAAEiB,IAAEb,EAAC,CAAE;AAAA,QAAC;AAAA,MAAC;AAAC,WAAG,KAAAI,GAAE,QAAF,mBAAO,IAAI,oBAAmB;AAAC,cAAMS,KAAET,GAAE,IAAI,IAAI,iBAAiB,GAAEW,KAAEF,MAAA,gBAAAA,GAAG;AAAO,YAAGE,MAAGA,GAAE,SAAO,GAAE;AAAC,gBAAMd,KAAEc,GAAE,CAAC,IAAE,MAAIA,GAAE,CAAC,IAAE,MAAIA,GAAE,CAAC,GAAEb,MAAE,MAAM,KAAK,SAASP,IAAEC,IAAEiB,GAAE,cAAY,IAAEb,IAAEA,IAAEE,GAAE,UAAS,GAAEC,EAAC;AAAE,UAAAU,GAAE,OAAKX,IAAE,KAAIW,GAAE,QAAMA,GAAE,KAAK,IAAI,kBAAiB,EAAC,IAAG,GAAE,MAAK,GAAE,YAAW,GAAE,aAAY,MAAK,QAAO,CAACZ,EAAC,EAAC,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC,aAAOG;AAAA,IAAC;AAAC,QAAGA,GAAE,sBAAoBA,GAAE,uBAAqBT,GAAE,YAAW;AAAC,YAAMkB,KAAE,MAAM,KAAK,QAAQ,KAAK,KAAI,EAAC,OAAM,EAAC,MAAKb,IAAE,IAAGA,KAAEI,GAAE,qBAAmB,EAAC,GAAE,cAAa,gBAAe,QAAOD,GAAC,CAAC;AAAE,cAAOR,KAAEkB,GAAE,MAAM,aAAWT,GAAE,qBAAmB,OAAK,KAAK,SAAST,IAAEC,IAAE,GAAEI,IAAEE,GAAE,WAAU,GAAEC,EAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,MAAM,kBAAkBR,IAAEC,IAAEiB,IAAEE,IAAEf,KAAE,CAAC,GAAE;AAAC,UAAMC,KAAE,KAAK,iBAAiBN,IAAEC,IAAEiB,IAAEE,EAAC;AAAE,QAAG,CAACd,GAAE,QAAO;AAAK,UAAK,EAAC,QAAOC,IAAE,iBAAgBC,IAAE,kBAAiBC,IAAE,KAAIE,GAAC,IAAEL,IAAEM,KAAEL,GAAE,IAAK,CAAAP,OAAG,KAAK,QAAQ,KAAK,KAAI,EAAC,OAAMA,IAAE,cAAa,gBAAe,QAAOK,GAAE,OAAM,CAAC,CAAE,GAAEQ,MAAE,MAAM,QAAQ,IAAID,EAAC,GAAEE,KAAED,IAAE,IAAK,CAAAb,OAAGA,GAAE,KAAK,UAAW,EAAE,OAAQ,CAACA,IAAEC,OAAID,KAAEC,EAAE,GAAEoB,KAAE,MAAIR,IAAE,SAAOA,IAAE,CAAC,EAAE,OAAK,IAAI,YAAYC,EAAC,GAAEQ,KAAE,CAAC,CAAC,GAAEC,KAAE,CAAC,CAAC;AAAE,QAAGV,IAAE,SAAO,GAAE;AAAC,YAAMb,KAAE,IAAI,WAAWqB,EAAC;AAAE,eAAQpB,KAAE,GAAEiB,KAAE,GAAEjB,KAAEY,IAAE,QAAOZ,MAAI;AAAC,cAAMmB,KAAEP,IAAEZ,EAAC,EAAE;AAAK,QAAAD,GAAE,IAAI,IAAI,WAAWoB,EAAC,GAAEF,EAAC,GAAEI,GAAErB,EAAC,IAAEiB,IAAEA,MAAGE,GAAE,YAAWG,GAAEtB,EAAC,IAAEmB,GAAE;AAAA,MAAU;AAAA,IAAC;AAAC,UAAK,EAAC,YAAWL,IAAE,aAAYE,GAAC,IAAE,KAAK,oBAAoBjB,EAAC,GAAEwB,KAAE,MAAM,KAAK,iBAAiBH,IAAE,EAAC,QAAO,QAAO,eAAc,EAAC,YAAW,KAAK,aAAY,KAAIV,IAAE,SAAQW,IAAE,OAAMC,GAAC,GAAE,OAAMR,IAAE,QAAOE,IAAE,QAAO,MAAK,WAAU,KAAI,CAAC;AAAE,QAAG,QAAMO,GAAE,QAAO;AAAK,QAAIC,IAAEC,IAAEC;AAAE,QAAGnB,OAAIO,MAAGN,OAAIQ,IAAE;AAAC,UAAIjB,KAAEwB,GAAE;AAAK,UAAGxB,GAAE,MAAIyB,KAAE,GAAEA,KAAER,IAAEQ,KAAI,KAAGE,KAAEF,KAAEV,IAAEU,KAAEhB,GAAE,MAAIiB,KAAElB,IAAEkB,KAAEX,IAAEW,KAAI,CAAA1B,GAAE2B,KAAED,EAAC,IAAE;AAAA,UAAO,MAAIA,KAAE,GAAEA,KAAEX,IAAEW,KAAI,CAAA1B,GAAE2B,KAAED,EAAC,IAAE;AAAA,UAAO,MAAI1B,KAAE,IAAI,WAAWe,KAAEE,EAAC,GAAEO,GAAE,OAAKxB,IAAEyB,KAAE,GAAEA,KAAEhB,IAAEgB,KAAI,MAAIE,KAAEF,KAAEV,IAAEW,KAAE,GAAEA,KAAElB,IAAEkB,KAAI,CAAA1B,GAAE2B,KAAED,EAAC,IAAE;AAAA,IAAC;AAAC,WAAOF;AAAA,EAAC;AAAA,EAAC,iBAAiBxB,IAAEC,IAAEiB,IAAEE,KAAE,OAAG;AAAC,UAAK,EAAC,mBAAkBf,IAAE,eAAcC,GAAC,IAAE,KAAK,WAAW,aAAYC,KAAE,MAAIP,KAAE,IAAEA,MAAGK,KAAE,IAAG,EAAC,aAAYG,GAAC,IAAE;AAAK,QAAG,CAACA,GAAE,QAAO;AAAK,UAAMC,KAAEW,KAAEZ,GAAE,SAASD,EAAC,IAAE,MAAIA,KAAEC,MAAA,gBAAAA,GAAG,KAAK,KAAGA,MAAA,gBAAAA,GAAG,YAAYD,KAAE;AAAG,QAAG,CAACE,GAAE,QAAO;AAAK,UAAME,KAAER,GAAEM,IAAED,EAAC,GAAEI,KAAEb,GAAEU,IAAE,aAAa;AAAE,QAAG,WAASG,GAAE,QAAO;AAAK,UAAMC,MAAEd,GAAEU,IAAE,gBAAgB,GAAE,EAAC,QAAOK,IAAE,QAAOO,IAAE,QAAOC,IAAE,QAAOC,GAAC,IAAEjB,GAAEC,EAAC;AAAE,QAAGN,KAAEqB,MAAGJ,KAAEK,MAAGtB,KAAEa,MAAGI,KAAEG,GAAE,QAAO;AAAK,UAAMN,KAAEb,GAAEO,IAAE,YAAY,GAAEQ,KAAEf,GAAEO,IAAE,aAAa,GAAEe,KAAEtB,GAAEO,IAAE,WAAW,GAAEiB,KAAExB,GAAEO,IAAE,YAAY,GAAEkB,KAAEhB,KAAE,KAAK,WAAW,YAAU,GAAEwB,KAAER,KAAE1B,MAAGsB,KAAE,KAAGL,IAAEU,KAAE,CAAC,EAAC,MAAKhB,GAAEuB,EAAC,GAAE,IAAGvB,GAAEuB,KAAER,KAAE,CAAC,IAAEd,IAAEsB,KAAER,KAAE,CAAC,IAAE,EAAC,CAAC;AAAE,QAAGhB,IAAE;AAAC,UAAIX,KAAE;AAAG,eAAQC,KAAE,GAAEA,KAAE0B,IAAE1B,KAAI,KAAGW,GAAEuB,KAAElC,EAAC,IAAEY,IAAEsB,KAAElC,EAAC,MAAIW,GAAEuB,KAAElC,KAAE,CAAC,GAAE;AAAC,QAAAD,KAAE;AAAG;AAAA,MAAK;AAAC,UAAG,CAACA,GAAE,UAAQC,KAAE,GAAEA,KAAE0B,IAAE1B,KAAI,CAAA2B,GAAE3B,EAAC,IAAE,EAAC,MAAKW,GAAEuB,KAAElC,EAAC,GAAE,IAAGW,GAAEuB,KAAElC,EAAC,IAAEY,IAAEsB,KAAElC,EAAC,IAAE,EAAC;AAAA,IAAC;AAAC,UAAMmC,KAAExB,GAAEuB,EAAC,GAAEE,KAAExB,IAAEsB,EAAC;AAAE,QAAG,QAAMC,MAAG,QAAMC,GAAE,QAAO;AAAK,WAAM,EAAC,QAAOT,IAAE,KAAInB,IAAE,iBAAgBS,OAAIK,MAAGR,KAAES,MAAGA,IAAE,kBAAiBvB,OAAIqB,MAAGL,KAAES,MAAGA,GAAC;AAAA,EAAC;AAAA,EAAC,MAAM,wBAAwB1B,IAAE;AAAC,QAAG;AAAC,YAAK,EAAC,MAAKC,GAAC,IAAE,MAAM,KAAK,QAAQ,KAAK,MAAI,YAAW,EAAC,cAAa,OAAM,QAAOD,MAAA,gBAAAA,GAAG,OAAM,CAAC;AAAE,aAAOc,GAAEb,EAAC;AAAA,IAAC,QAAM;AAAC,aAAO;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,MAAM,qBAAqBD,IAAE;AAAC,QAAG;AAAC,YAAK,EAAC,MAAKC,GAAC,IAAE,MAAM,KAAK,QAAQ,KAAK,MAAI,YAAW,EAAC,cAAa,gBAAe,QAAOD,MAAA,gBAAAA,GAAG,OAAM,CAAC,GAAEkB,KAAEA,GAAE,MAAMjB,EAAC;AAAE,cAAOiB,MAAA,gBAAAA,GAAG,aAAU,EAAE,SAASA,GAAE,SAAS,IAAE;AAAA,IAAI,QAAM;AAAC,aAAO;AAAA,IAAI;AAAA,EAAC;AAAC;AAAElB,GAAE,CAAC,EAAE,CAAC,GAAEG,GAAE,WAAU,UAAS,MAAM,GAAEH,GAAE,CAAC,EAAE,CAAC,GAAEG,GAAE,WAAU,eAAc,MAAM,GAAEH,GAAE,CAAC,EAAE,CAAC,GAAEG,GAAE,WAAU,eAAc,MAAM,GAAEH,GAAE,CAAC,EAAE,EAAC,MAAK,QAAO,MAAK,EAAC,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEG,GAAE,WAAU,iBAAgB,MAAM,GAAEA,KAAEH,GAAE,CAAC,EAAE,+CAA+C,CAAC,GAAEG,EAAC;AAAE,IAAMO,KAAEP;;;ACAv6Q,IAAMmC,KAAE,oBAAI;AAAIA,GAAE,IAAI,OAAM,EAAC,MAAK,uBAAsB,aAAYC,GAAC,CAAC,GAAED,GAAE,IAAI,OAAM,EAAC,MAAK,sBAAqB,aAAYE,GAAC,CAAC,GAAEF,GAAE,IAAI,QAAO,EAAC,MAAK,WAAU,aAAYG,GAAC,CAAC,GAAEH,GAAE,IAAI,oBAAmB,EAAC,MAAK,sBAAqB,aAAYC,GAAC,CAAC,GAAED,GAAE,IAAI,OAAM,EAAC,MAAK,qBAAoB,aAAYC,GAAC,CAAC,GAAED,GAAE,IAAI,OAAM,EAAC,MAAK,qBAAoB,aAAYC,GAAC,CAAC,GAAED,GAAE,IAAI,OAAM,EAAC,MAAK,qBAAoB,aAAYC,GAAC,CAAC,GAAED,GAAE,IAAI,OAAM,EAAC,MAAK,qBAAoB,aAAYC,GAAC,CAAC;AAAE,IAAMG,KAAN,MAAO;AAAA,EAAC,WAAW,mBAAkB;AAAC,UAAMC,KAAE,oBAAI;AAAI,WAAOL,GAAE,QAAS,CAACM,IAAEC,OAAIF,GAAE,IAAIE,EAAC,CAAE,GAAEF;AAAA,EAAC;AAAA,EAAC,aAAa,KAAKC,IAAE;AAAC,UAAK,EAAC,KAAIC,IAAE,UAASC,IAAE,YAAWC,GAAC,IAAEH;AAAE,QAAII,KAAEJ,GAAE;AAAc,YAAMI,MAAGH,GAAE,YAAY,GAAG,MAAIG,KAAEH,GAAE,MAAMA,GAAE,YAAY,GAAG,IAAE,CAAC,EAAE,YAAY,IAAG,UAAQG,MAAG,UAAQA,KAAEA,KAAE,SAAO,UAAQA,MAAG,WAASA,MAAG,WAASA,OAAIA,KAAE,QAAOH,GAAE,YAAY,EAAE,SAAS,cAAc,KAAG,CAACA,GAAE,YAAY,EAAE,SAAS,YAAY,MAAIG,KAAE;AAAoB,UAAMN,KAAE,EAAC,KAAIG,IAAE,YAAWE,IAAE,eAAcC,IAAE,UAASF,MAAG,EAAC,SAAQ,MAAK,UAAS,KAAI,EAAC;AAAE,QAAIG,IAAEC;AAAE,QAAGF,MAAG,KAAK,iBAAiB,IAAIA,EAAC,GAAE;AAAC,UAAG,UAAQA,MAAG,EAACF,MAAA,gBAAAA,GAAG,WAAU,OAAM,IAAIA,GAAE,sBAAqB,uBAAuBD,EAAC,EAAE;AAAE,aAAOI,KAAEX,GAAE,IAAIU,EAAC,EAAE,aAAYE,KAAE,IAAID,GAAEP,EAAC,GAAE,MAAMQ,GAAE,KAAK,EAAC,QAAON,GAAE,OAAM,CAAC,GAAEM;AAAA,IAAC;AAAC,QAAGF,GAAE,OAAM,IAAIF,GAAE,sBAAqB,4BAA0BE,EAAC;AAAE,UAAMG,KAAE,MAAM,KAAKb,GAAE,KAAK,CAAC;AAAE,QAAIE,KAAE;AAAE,UAAMY,KAAE,OAAKJ,KAAEG,GAAEX,IAAG,GAAEQ,OAAI,UAAQA,OAAGF,MAAA,gBAAAA,GAAG,eAAYG,KAAEX,GAAE,IAAIU,EAAC,EAAE,aAAYE,KAAE,IAAID,GAAEP,EAAC,GAAEQ,GAAE,KAAK,EAAC,QAAON,GAAE,OAAM,CAAC,EAAE,KAAM,MAAIM,EAAE,EAAE,MAAO,MAAIE,GAAE,CAAE,KAAG;AAAM,WAAOA,GAAE;AAAA,EAAC;AAAA,EAAC,OAAO,SAAST,IAAEC,IAAEC,IAAE;AAAC,IAAAP,GAAE,IAAIK,GAAE,YAAY,CAAC,KAAGL,GAAE,IAAIK,GAAE,YAAY,GAAE,EAAC,MAAKC,IAAE,aAAYC,GAAC,CAAC;AAAA,EAAC;AAAC;;;ACA+L,IAAIQ,KAAE,cAAc,EAAEC,GAAEC,GAAE,EAAEC,GAAEC,GAAEC,GAAEC,GAAEA,GAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AAAA,EAAC,eAAeC,IAAE;AAAC,UAAM,GAAGA,EAAC,GAAE,KAAK,kBAAgB,MAAK,KAAK,UAAQ,MAAK,KAAK,gBAAc,MAAK,KAAK,gBAAc,MAAG,KAAK,cAAY,MAAK,KAAK,WAAS,QAAO,KAAK,aAAW,MAAK,KAAK,UAAQ,MAAK,KAAK,OAAK,gBAAe,KAAK,uBAAqB,gCAA+B,KAAK,eAAa,MAAG,KAAK,gBAAc,MAAK,KAAK,SAAO;AAAA,EAAI;AAAA,EAAC,kBAAkBA,IAAEC,IAAE;AAAC,WAAM,YAAU,OAAOD,KAAE,EAAC,KAAIA,IAAE,GAAGC,GAAC,IAAED;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAE;AAAC,UAAMC,KAAE,EAAED,EAAC,IAAEA,GAAE,SAAO;AAAK,WAAO,KAAK,oBAAoB,KAAK,eAAe,EAAC,gBAAe,CAAC,eAAe,EAAC,GAAEA,EAAC,EAAE,MAAM,CAAC,EAAE,KAAM,MAAI,KAAK,YAAYC,EAAC,CAAE,CAAC,GAAE,QAAQ,QAAQ,IAAI;AAAA,EAAC;AAAA,EAAC,IAAI,uBAAsB;AAAC,WAAO,KAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,IAAI,eAAc;AAJzmF;AAI0mF,QAAID,KAAE,CAAC,IAAIE,GAAE,EAAC,MAAK,4BAA2B,OAAM,eAAc,QAAO,MAAK,UAAS,OAAG,QAAO,IAAG,MAAK,SAAQ,CAAC,CAAC;AAAE,UAAK,EAAC,YAAWD,GAAC,IAAE,MAAKP,KAAEO,MAAA,gBAAAA,GAAG,gBAAeE,KAAE,EAAET,EAAC,IAAEA,GAAE,SAAO,MAAKU,KAAE;AAAU,QAAGD,IAAE;AAAC,YAAMF,KAAEE,GAAE,OAAQ,CAAAH,OAAG,UAAQA,GAAE,QAAM,YAAUA,GAAE,KAAK,YAAY,CAAE,EAAE,IAAK,CAAAA,OAAG;AAAC,cAAMC,KAAED,GAAE,MAAM;AAAE,eAAOC,GAAE,OAAKG,KAAEJ,GAAE,MAAKC;AAAA,MAAC,CAAE;AAAE,MAAAD,KAAEA,GAAE,OAAOC,EAAC;AAAA,IAAC;AAAC,UAAML,KAAEK,MAAA,gBAAAA,GAAG,UAASH,KAAEG,MAAA,gBAAAA,GAAG;AAAqB,SAAI,oBAAkBL,MAAG,gBAAcA,OAAI,EAAEE,EAAC,GAAE;AAAC,YAAMG,MAAE,KAAAH,GAAE,UAAU,CAAC,EAAE,SAAf,mBAAqB,QAAOJ,KAAE,eAAaO,KAAE,KAAKA,EAAC,MAAI;AAAI,MAAAD,GAAE,KAAK,IAAIE,GAAE,EAAC,MAAK,oBAAmB,OAAMR,IAAE,QAAO,MAAK,UAAS,OAAG,MAAK,SAAQ,CAAC,CAAC,GAAEM,GAAE,KAAK,IAAIE,GAAE,EAAC,MAAK,oBAAmB,OAAM,iBAAgB,QAAO,MAAK,UAAS,OAAG,MAAK,SAAQ,CAAC,CAAC;AAAA,IAAC;AAAC,WAAOF;AAAA,EAAC;AAAA,EAAC,IAAI,SAASA,IAAE;AAAC,SAAK,KAAK,YAAWA,EAAC,GAAE,KAAK,eAAe;AAAA,EAAC;AAAA,EAAC,aAAaA,IAAEC,IAAEE,IAAE;AAAC,UAAMC,KAAEH,MAAGA,GAAE,mBAAiBA,GAAE,gBAAgB,eAAaA,GAAE,gBAAgB,YAAY,UAASL,KAAED,GAAES,IAAED,EAAC,KAAG;AAAO,QAAG,QAAMP,GAAE,QAAOA;AAAA,EAAC;AAAA,EAAC,oBAAoBI,IAAE;AAAC,WAAOD,GAAE,EAAC,QAAO,KAAK,cAAa,OAAM,KAAK,MAAK,GAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,mBAAmBA,IAAEC,IAAE;AAAC,QAAG,EAAED,KAAE,EAAEK,IAAEL,EAAC,GAAG,QAAO,KAAK,gBAAgB,CAAC,EAAE;AAAW,QAAG;AAAC,YAAMN,KAAE,EAAC,QAAO,KAAK,gBAAgB,CAAC,EAAC;AAAE,WAAK,gBAAgB,SAAO,KAAG,KAAK,gBAAgB,QAAS,CAAAM,OAAGN,GAAEM,GAAE,GAAG,IAAEA,EAAE;AAAE,YAAMG,KAAEV,GAAEO,GAAE,OAAO,GAAEN,EAAC,GAAEU,KAAE,IAAIT,GAAE,EAAC,gBAAeQ,GAAC,CAAC;AAAE,aAAO,MAAMC,GAAE,KAAKH,EAAC,GAAEG,GAAE;AAAA,IAAU,QAAM;AAAC,aAAO;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,MAAMJ,IAAEC,IAAE;AAAC,UAAK,EAAC,QAAOP,GAAC,IAAE;AAAK,QAAG,KAAK,SAAO,uBAAqBA,GAAE,kBAAgB,aAAWA,GAAE,YAAU,UAAQA,GAAE,YAAU,KAAK,OAAK,0BAA0B,KAAK,KAAK,GAAG,EAAE,QAAO,MAAM,MAAMM,IAAEC,EAAC;AAAE,QAAGA,MAAGA,GAAE,UAAS;AAAC,YAAMD,KAAE,GAAGC,GAAE,MAAM,IAAIA,GAAE,sBAAoB,oBAAoB;AAAG,MAAAA,GAAE,SAAS,KAAK,IAAIE,GAAE,qBAAoB,WAAW,KAAK,KAAK,KAAK,KAAK,EAAE,cAAc,KAAK,aAAa,0CAA0CH,EAAC,KAAI,EAAC,OAAM,KAAI,CAAC,CAAC;AAAA,IAAC;AAAC,WAAO;AAAA,EAAI;AAAA,EAAC,MAAM,YAAYA,IAAE;AAAC,QAAIC,KAAE;AAAG,QAAG,KAAK,OAAO,MAAK,OAAO,cAAY,MAAM,KAAK,OAAO,KAAK,GAAE,eAAa,KAAK,OAAO,iBAAeA,KAAE,MAAG,KAAK,kBAAgB,KAAK,OAAO,eAAe,WAAS,KAAK,kBAAgB,CAAC,KAAK,MAAM,GAAE,KAAK,MAAI,KAAK,OAAO;AAAA,SAAQ;AAAC,YAAK,EAAC,gBAAeA,GAAC,IAAE,MAAKP,KAAE,CAAC,KAAK,GAAG;AAAE,MAAAO,MAAGK,GAAEL,GAAE,OAAO,GAAEP,EAAC;AAAE,YAAMU,KAAE,MAAM,QAAQ,IAAIV,GAAE,IAAK,CAAAO,OAAGM,GAAE,KAAK,EAAC,KAAIN,IAAE,YAAW,KAAK,YAAW,UAAS,EAAC,UAAS,WAAU,GAAG,KAAK,UAAS,uBAAsB,KAAK,iBAAgB,GAAE,QAAOD,GAAC,CAAC,CAAE,CAAC,GAAEO,KAAEH,GAAE,UAAW,CAAAJ,OAAG,QAAMA,EAAE;AAAE,UAAGO,KAAE,GAAG,OAAM,IAAIJ,GAAE,2BAA0B,uBAAuBT,GAAEa,EAAC,CAAC,EAAE;AAAE,UAAG,KAAK,kBAAgBH,IAAEH,IAAE;AAAC,cAAMD,KAAE,EAAC,QAAO,KAAK,gBAAgB,CAAC,EAAC;AAAE,aAAK,gBAAgB,SAAO,KAAG,KAAK,gBAAgB,QAAS,CAAAC,OAAGD,GAAEC,GAAE,GAAG,IAAEA,EAAE;AAAE,cAAMP,KAAED,GAAEQ,GAAE,4BAA0BA,GAAE,OAAO,GAAED,EAAC,GAAEO,MAAE,IAAIZ,GAAE,EAAC,gBAAeD,GAAC,CAAC;AAAE,YAAG;AAAC,gBAAMa,IAAE,KAAK,GAAE,KAAK,SAAOA;AAAA,QAAC,SAAOJ,IAAE;AAAC,gBAAMH,KAAE,EAAE,UAAU,KAAK,aAAa;AAAE,UAAAG,cAAaA,MAAGH,GAAE,MAAM,2BAA0BG,GAAE,OAAO,GAAEH,GAAE,KAAK,2BAA0B,sDAAsD,GAAE,KAAK,KAAK,kBAAiB,IAAI,GAAE,KAAK,SAAOI,GAAE,CAAC;AAAA,QAAC;AAAA,MAAC,MAAM,MAAK,SAAOA,GAAE,CAAC;AAAA,IAAC;AAAC,UAAMV,KAAE,KAAK,OAAO;AAAW,QAAG,CAACA,GAAE,OAAM,IAAIS,GAAE,2BAA0B,8BAA4B,KAAK,GAAG;AAAE,QAAG,KAAK,KAAK,cAAaF,KAAEP,KAAE,KAAK,gBAAgB,CAAC,EAAE,UAAU,GAAE,KAAK,KAAK,oBAAmBA,GAAE,gBAAgB,GAAE,KAAK,aAAW,KAAK,cAAY,KAAK,OAAO,YAAW,QAAM,KAAK,YAAW;AAAC,YAAMM,KAAE,UAAQ,KAAK,OAAO,YAAU,QAAM,KAAK,WAAW,UAAQ,QAAM,KAAK,WAAW,SAAO,KAAK,aAAW,EAAC,GAAG,KAAK,YAAW,UAAS,GAAE,UAAS,EAAC;AAAE,WAAK,KAAKA,IAAE,EAAC,QAAO,UAAS,CAAC;AAAA,IAAC;AAAC,SAAK,UAAQ,KAAK,QAAM,KAAK,OAAO,cAAa,UAAQ,KAAK,OAAO,aAAW,KAAK,eAAa,QAAI,KAAK,uBAAuB,GAAE,KAAK,WAAWQ,GAAG,MAAI,KAAK,kBAAmB,CAAAR,OAAG;AAAC,WAAK,WAAS,KAAK,OAAO,SAAS,wBAAsBA;AAAA,IAAE,CAAE,CAAC;AAAA,EAAC;AAAC;AAAEA,GAAE,CAAC,EAAE,CAAC,GAAEP,GAAE,WAAU,mBAAkB,MAAM,GAAEO,GAAE,CAAC,EAAE,EAAC,MAAK,CAAC,CAAC,GAAE,MAAK,EAAC,OAAM,EAAC,iBAAgB;AAJzxM;AAI0xM,SAAM,EAAC,SAAQ,CAAC,KAAK,UAAQ,aAAW,KAAK,OAAO,YAAU,cAAU,UAAK,YAAL,mBAAc,KAAK,MAAI;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEP,GAAE,WAAU,WAAU,MAAM,GAAEO,GAAE,CAAC,EAAE,EAAC,MAAK,EAAC,OAAM,EAAC,iBAAgB;AAAC,SAAM,EAAC,SAAQ,CAAC,KAAK,UAAQ,aAAW,KAAK,OAAO,YAAU,eAAa,KAAK,cAAa;AAAC,EAAC,EAAC,EAAC,CAAC,GAAEJ,GAAEA,EAAC,CAAC,GAAEH,GAAE,WAAU,iBAAgB,MAAM,GAAEO,GAAE,CAAC,EAAEL,EAAC,CAAC,GAAEF,GAAE,WAAU,iBAAgB,MAAM,GAAEO,GAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,MAAK,EAAC,MAAK,OAAG,OAAM,EAAC,SAAQ,MAAG,gBAAe,OAAK,EAAC,SAAQ,MAAE,GAAE,EAAC,EAAC,CAAC,CAAC,GAAEP,GAAE,WAAU,eAAc,MAAM,GAAEO,GAAE,CAAC,EAAE,EAAC,MAAK,CAAC,QAAO,MAAM,EAAC,CAAC,CAAC,GAAEP,GAAE,WAAU,YAAW,MAAM,GAAEO,GAAE,CAAC,EAAE,EAAC,MAAK,EAAC,MAAK,MAAG,OAAM,KAAE,EAAC,CAAC,CAAC,GAAEP,GAAE,WAAU,aAAY,MAAM,GAAEO,GAAE,CAAC,EAAE,CAAC,GAAEP,GAAE,WAAU,cAAa,MAAM,GAAEO,GAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,SAAQ,EAAC,SAAQ,EAAC,MAAK,EAAC,QAAO,iBAAgB,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEP,GAAE,WAAU,WAAU,MAAM,GAAEO,GAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,EAAC,MAAK,MAAE,EAAC,CAAC,CAAC,GAAEP,GAAE,WAAU,QAAO,MAAM,GAAEO,GAAE,CAAC,EAAE,EAAC,MAAK,CAAC,8BAA8B,EAAC,CAAC,CAAC,GAAEP,GAAE,WAAU,wBAAuB,MAAM,GAAEO,GAAE,CAAC,EAAE,EAAC,MAAK,SAAQ,OAAM,MAAG,MAAK,EAAC,MAAK,EAAC,QAAO,gBAAe,QAAO,CAACA,IAAEC,OAAI,CAACA,GAAE,aAAY,GAAE,OAAM,EAAC,QAAO,gBAAe,iBAAgB;AAAC,SAAM,EAAC,SAAQ,CAAC,KAAK,UAAQ,aAAW,KAAK,OAAO,SAAQ;AAAC,GAAE,OAAOD,IAAEC,IAAEP,IAAE;AAAC,EAAAO,GAAEP,EAAC,IAAE,CAACM;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEP,GAAE,WAAU,gBAAe,MAAM,GAAEO,GAAE,CAAC,EAAE,EAAC,MAAK,GAAE,MAAK,EAAC,MAAK,EAAC,QAAO,YAAW,GAAE,OAAM,EAAC,QAAO,aAAY,iBAAgB;AAAC,SAAM,EAAC,SAAQ,CAAC,KAAK,UAAQ,aAAW,KAAK,OAAO,SAAQ;AAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEP,GAAE,WAAU,iBAAgB,MAAM,GAAEO,GAAE,CAAC,EAAE,EAAC,UAAS,KAAE,CAAC,CAAC,GAAEP,GAAE,WAAU,wBAAuB,IAAI,GAAEO,GAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,CAACE,EAAC,EAAC,CAAC,CAAC,GAAET,GAAE,WAAU,UAAS,MAAM,GAAEO,GAAE,CAAC,EAAE,EAAC,UAAS,MAAG,MAAK,CAACE,EAAC,EAAC,CAAC,CAAC,GAAET,GAAE,WAAU,gBAAe,IAAI,GAAEO,GAAE,CAAC,EAAE,EAAC,OAAMQ,IAAE,MAAK,EAAC,MAAK,wCAAuC,OAAM,EAAC,iBAAgB;AAJ/3P;AAIg4P,QAAMR,KAAE,uBAAmB,UAAK,aAAL,mBAAe,SAAM,WAAS,KAAK,SAAS,eAAa,CAAC,KAAK,SAAS;AAAS,SAAM,EAAC,SAAQ,CAAC,KAAK,UAAQ,aAAW,KAAK,OAAO,YAAU,CAACA,GAAC;AAAC,EAAC,GAAE,SAAQ,EAAC,aAAY,EAAC,OAAMS,IAAE,MAAK,wCAAuC,OAAM,EAAC,gBAAe,CAAAT,QAAI,EAAC,SAAQA,MAAG,mBAAiBA,GAAE,QAAM,WAASA,GAAE,KAAI,GAAE,EAAC,EAAC,EAAC,EAAC,CAAC,CAAC,GAAEP,GAAE,WAAU,YAAW,IAAI,GAAEO,GAAE,CAAC,EAAE,UAAU,CAAC,GAAEP,GAAE,WAAU,gBAAe,IAAI,GAAEA,KAAEO,GAAE,CAAC,EAAE,8BAA8B,CAAC,GAAEP,EAAC;AAAE,IAAMiB,KAAEjB;", "names": ["$", "e", "T", "t", "U", "s", "i", "o", "r", "n", "w", "l", "c", "m", "f", "u", "p", "d", "x", "j", "y", "g", "a", "h", "I", "k", "S", "B", "_", "C", "b", "R", "v", "E", "D", "z", "F", "O", "L", "V", "Q", "n", "Q", "r", "e", "s", "a", "o", "t", "i", "c", "p", "l", "m", "u", "O", "z", "s", "t", "e", "r", "i", "f", "a", "l", "u", "m", "d", "c", "h", "w", "C", "n", "o", "S", "v", "L", "j", "T", "p", "t", "e", "r", "n", "i", "s", "a", "o", "l", "p", "g", "u", "d", "x", "S", "Q", "e", "r", "s", "o", "i", "t", "a", "n", "l", "f", "m", "c", "p", "d", "u", "h", "I", "w", "_", "v", "b", "z", "T", "k", "j", "C", "R", "F", "D", "L", "O", "B", "$", "N", "h", "Q", "t", "e", "s", "r", "i", "o", "m", "n", "l", "f", "d", "u", "w", "y", "x", "g", "a", "R", "p", "c", "n", "e", "t", "l", "r", "u", "u", "n", "e", "l", "o", "t", "i", "f", "e", "t", "n", "r", "u", "m", "o", "c", "d", "p", "S", "C", "i", "l", "s", "a", "d", "Q", "t", "e", "s", "r", "a", "o", "i", "g", "n", "l", "p", "w", "f", "j", "m", "c", "u", "I", "Q", "e", "r", "a", "o", "s", "n", "c", "p", "y", "d", "x", "g", "v", "S", "w", "j", "T", "b", "t", "_", "R", "z", "i", "l", "m", "h", "u", "f", "I", "b", "Q", "t", "e", "o", "i", "a", "g", "n", "l", "f", "c", "m", "p", "h", "u", "y", "r", "s", "d", "x", "w", "A", "F", "_", "R", "S", "j", "k", "T", "C", "U", "B", "E", "L", "O", "E", "e", "t", "k", "D", "Q", "s", "a", "n", "o", "f", "v", "l", "u", "c", "p", "y", "U", "g", "r", "R", "i", "m", "h", "d", "T", "x", "I", "w", "b", "O", "j", "B", "L", "A", "z", "_", "F", "S", "c", "w", "F", "v", "n", "t", "e", "r", "s", "o", "a", "l", "i", "u", "m", "C", "t", "c", "o", "z", "a", "p", "e", "r", "y", "s", "i", "w", "h", "n", "l", "d", "E"]}