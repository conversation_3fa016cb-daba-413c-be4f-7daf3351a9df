{"version": 3, "sources": ["../../@arcgis/core/chunks/it_IT.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{g as e}from\"./_commonjsHelpers.js\";import{c as r}from\"./_commonjs-dynamic-modules.js\";function a(e,r){for(var a=0;a<r.length;a++){const i=r[a];if(\"string\"!=typeof i&&!Array.isArray(i))for(const r in i)if(\"default\"!==r&&!(r in e)){const a=Object.getOwnPropertyDescriptor(i,r);a&&Object.defineProperty(e,r,a.get?a:{enumerable:!0,get:()=>i[r]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}))}var i,o,t={},s={get exports(){return t},set exports(e){t=e}};i=s,void 0!==(o=function(e,r){Object.defineProperty(r,\"__esModule\",{value:!0}),r.default={_decimalSeparator:\",\",_thousandSeparator:\".\",_percentPrefix:null,_percentSuffix:\"%\",_big_number_suffix_3:\"k\",_big_number_suffix_6:\"M\",_big_number_suffix_9:\"G\",_big_number_suffix_12:\"T\",_big_number_suffix_15:\"P\",_big_number_suffix_18:\"E\",_big_number_suffix_21:\"Z\",_big_number_suffix_24:\"Y\",_small_number_suffix_3:\"m\",_small_number_suffix_6:\"μ\",_small_number_suffix_9:\"n\",_small_number_suffix_12:\"p\",_small_number_suffix_15:\"f\",_small_number_suffix_18:\"a\",_small_number_suffix_21:\"z\",_small_number_suffix_24:\"y\",_byte_suffix_B:\"B\",_byte_suffix_KB:\"KB\",_byte_suffix_MB:\"MB\",_byte_suffix_GB:\"GB\",_byte_suffix_TB:\"TB\",_byte_suffix_PB:\"PB\",_date_millisecond:\"mm:ss SSS\",_date_second:\"HH:mm:ss\",_date_minute:\"HH:mm\",_date_hour:\"HH:mm\",_date_day:\"dd MMM\",_date_week:\"ww\",_date_month:\"MMM\",_date_year:\"yyyy\",_duration_millisecond:\"SSS\",_duration_millisecond_second:\"ss.SSS\",_duration_millisecond_minute:\"mm:ss SSS\",_duration_millisecond_hour:\"HH:mm:ss SSS\",_duration_millisecond_day:\"d'g' mm:ss SSS\",_duration_millisecond_week:\"d'g' mm:ss SSS\",_duration_millisecond_month:\"M'm' dd'g' mm:ss SSS\",_duration_millisecond_year:\"y'a' MM'm' dd'g' mm:ss SSS\",_duration_second:\"ss\",_duration_second_minute:\"mm:ss\",_duration_second_hour:\"hh:mm:ss\",_duration_second_day:\"d'g' hh:mm:ss\",_duration_second_week:\"d'g' hh:mm:ss\",_duration_second_month:\"M'm' dd'g' hh:mm:ss\",_duration_second_year:\"y'a' MM'm' dd'g' hh:mm:ss\",_duration_minute:\"mm\",_duration_minute_hour:\"hh:mm\",_duration_minute_day:\"d'g' hh:mm\",_duration_minute_week:\"d'g' hh:mm\",_duration_minute_month:\"M'm' dd'g' hh:mm\",_duration_minute_year:\"y'a' MM'm' dd'g' hh:mm\",_duration_hour:\"hh'o'\",_duration_hour_day:\"d'g' hh'o'\",_duration_hour_week:\"d'g' hh'o'\",_duration_hour_month:\"M'm' dd'g' hh'o'\",_duration_hour_year:\"y'a' MM'm' dd'g' hh'o'\",_duration_day:\"d'g'\",_duration_day_week:\"d'g'\",_duration_day_month:\"M'm' dd'g'\",_duration_day_year:\"y'a' MM'm' dd'g'\",_duration_week:\"w's'\",_duration_week_month:\"w's'\",_duration_week_year:\"w's'\",_duration_month:\"M'm'\",_duration_month_year:\"y'a' MM'm'\",_duration_year:\"y'a'\",_era_ad:\"A.C.\",_era_bc:\"D.C.\",A:\"\",P:\"\",AM:\"\",PM:\"\",\"A.M.\":\"\",\"P.M.\":\"\",January:\"Gennaio\",February:\"Febbraio\",March:\"Marzo\",April:\"Aprile\",May:\"Maggio\",June:\"Giugno\",July:\"Luglio\",August:\"Agosto\",September:\"Settembre\",October:\"Ottobre\",November:\"Novembre\",December:\"Dicembre\",Jan:\"Gen\",Feb:\"Feb\",Mar:\"Mar\",Apr:\"Apr\",\"May(short)\":\"Mag\",Jun:\"Giu\",Jul:\"Lug\",Aug:\"Ago\",Sep:\"Set\",Oct:\"Ott\",Nov:\"Nov\",Dec:\"Dic\",Sunday:\"Domenica\",Monday:\"Lunedì\",Tuesday:\"Martedì\",Wednesday:\"Mercoledì\",Thursday:\"Giovedì\",Friday:\"Venerdì\",Saturday:\"Sabato\",Sun:\"Dom\",Mon:\"Lun\",Tue:\"Mar\",Wed:\"Mer\",Thu:\"Gio\",Fri:\"Ven\",Sat:\"Sab\",_dateOrd:function(e){return e+\"°\"},\"Zoom Out\":\"Riduci zoom\",Play:\"Avvia\",Stop:\"Ferma\",Legend:\"Legenda\",\"Click, tap or press ENTER to toggle\":\"Clicca, tappa o premi ENTER per attivare\",Loading:\"Caricamento\",Home:\"Home\",Chart:\"Grafico\",\"Serial chart\":\"Grafico combinato\",\"X/Y chart\":\"Grafico X/Y\",\"Pie chart\":\"Grafico a torta\",\"Gauge chart\":\"Diagramma di livello\",\"Radar chart\":\"Grafico radar\",\"Sankey diagram\":\"Diagramma di Sankey\",\"Flow diagram\":\"Diagramma di flusso\",\"Chord diagram\":\"Diagramma a corda\",\"TreeMap chart\":\"Mappa ad albero\",\"Sliced chart\":\"Grafico a fette\",Series:\"Serie\",\"Candlestick Series\":\"Serie a candele\",\"OHLC Series\":\"Serie OHLC\",\"Column Series\":\"Serie a colonne\",\"Line Series\":\"Serie a linee\",\"Pie Slice Series\":\"Serie a fetta di torta\",\"Funnel Series\":\"Serie ad imbuto\",\"Pyramid Series\":\"Serie a piramide\",\"X/Y Series\":\"Serie X/Y\",Map:\"Mappa\",\"Press ENTER to zoom in\":\"Premi ENTER per ingrandire\",\"Press ENTER to zoom out\":\"Premi ENTER per ridurre\",\"Use arrow keys to zoom in and out\":\"Usa le frecce per ingrandire e ridurre\",\"Use plus and minus keys on your keyboard to zoom in and out\":\"Utilizza i tasti più e meno sulla tastiera per ingrandire e ridurre\",Export:\"Esporta\",Image:\"Immagine\",Data:\"Dati\",Print:\"Stampa\",\"Click, tap or press ENTER to open\":\"Clicca, tappa o premi ENTER per aprire\",\"Click, tap or press ENTER to print.\":\"Clicca, tappa o premi ENTER per stampare.\",\"Click, tap or press ENTER to export as %1.\":\"Clicca, tappa o premi ENTER per esportare come %1.\",'To save the image, right-click this link and choose \"Save picture as...\"':'Per salvare l\\'immagine, fai clic con il pulsante destro del mouse su questo link e seleziona \"Salva immagine come ...\"','To save the image, right-click thumbnail on the left and choose \"Save picture as...\"':'Per salvare l\\'immagine, fare clic con il tasto destro del mouse sulla miniatura e selezionare \"Salva immagine come ...\"',\"(Press ESC to close this message)\":\"(Premere ESC per chiudere questo messaggio)\",\"Image Export Complete\":\"Esportazione immagine completata\",\"Export operation took longer than expected. Something might have gone wrong.\":\"L'operazione di esportazione ha richiesto più tempo del previsto. Potrebbe esserci qualche problema.\",\"Saved from\":\"Salvato da\",PNG:\"\",JPG:\"\",GIF:\"\",SVG:\"\",PDF:\"\",JSON:\"\",CSV:\"\",XLSX:\"\",\"Use TAB to select grip buttons or left and right arrows to change selection\":\"Utilizzare TAB per selezionare i punti di ancoraggio o i tasti freccia sinistra e destra per modificare la selezione\",\"Use left and right arrows to move selection\":\"Utilizzare le frecce sinistra e destra per spostare la selezione\",\"Use left and right arrows to move left selection\":\"Utilizzare frecce destra e sinistra per spostare la selezione sinistra\",\"Use left and right arrows to move right selection\":\"Utilizzare frecce destra e sinistra per spostare la selezione destra\",\"Use TAB select grip buttons or up and down arrows to change selection\":\"Utilizzare TAB per selezionare i punti di ancoraggio o premere le frecce su e giù per modificare la selezione\",\"Use up and down arrows to move selection\":\"Utilizzare le frecce su e giù per spostare la selezione\",\"Use up and down arrows to move lower selection\":\"Utilizzare le frecce su e giù per spostare la selezione inferiore\",\"Use up and down arrows to move upper selection\":\"Utilizzare le frecce su e giù per spostare la selezione superiore\",\"From %1 to %2\":\"Da %1 a %2\",\"From %1\":\"Da %1\",\"To %1\":\"a %1\",\"No parser available for file: %1\":\"Nessun parser disponibile per il file: %1\",\"Error parsing file: %1\":\"Errore durante l'analisi del file: %1\",\"Unable to load file: %1\":\"Impossibile caricare il file: %1\",\"Invalid date\":\"Data non valida\"}}(r,t))&&(i.exports=o);const n=a({__proto__:null,default:e(t)},[t]);export{n as i};\n"], "mappings": ";;;;;;;;;AAI6F,SAAS,EAAE,GAAEA,IAAE;AAAC,WAAQC,KAAE,GAAEA,KAAED,GAAE,QAAOC,MAAI;AAAC,UAAMC,KAAEF,GAAEC,EAAC;AAAE,QAAG,YAAU,OAAOC,MAAG,CAAC,MAAM,QAAQA,EAAC;AAAE,iBAAUF,MAAKE,GAAE,KAAG,cAAYF,MAAG,EAAEA,MAAK,IAAG;AAAC,cAAMC,KAAE,OAAO,yBAAyBC,IAAEF,EAAC;AAAE,QAAAC,MAAG,OAAO,eAAe,GAAED,IAAEC,GAAE,MAAIA,KAAE,EAAC,YAAW,MAAG,KAAI,MAAIC,GAAEF,EAAC,EAAC,CAAC;AAAA,MAAC;AAAA;AAAA,EAAC;AAAC,SAAO,OAAO,OAAO,OAAO,eAAe,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;AAAC;AAAC,IAAI;AAAJ,IAAMG;AAAN,IAAQ,IAAE,CAAC;AAAX,IAAa,IAAE,EAAC,IAAI,UAAS;AAAC,SAAO;AAAC,GAAE,IAAI,QAAQ,GAAE;AAAC,MAAE;AAAC,EAAC;AAAE,IAAE,GAAE,YAAUA,KAAE,SAAS,GAAEH,IAAE;AAAC,SAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAEA,GAAE,UAAQ,EAAC,mBAAkB,KAAI,oBAAmB,KAAI,gBAAe,MAAK,gBAAe,KAAI,sBAAqB,KAAI,sBAAqB,KAAI,sBAAqB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,uBAAsB,KAAI,wBAAuB,KAAI,wBAAuB,KAAI,wBAAuB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,yBAAwB,KAAI,gBAAe,KAAI,iBAAgB,MAAK,iBAAgB,MAAK,iBAAgB,MAAK,iBAAgB,MAAK,iBAAgB,MAAK,mBAAkB,aAAY,cAAa,YAAW,cAAa,SAAQ,YAAW,SAAQ,WAAU,UAAS,YAAW,MAAK,aAAY,OAAM,YAAW,QAAO,uBAAsB,OAAM,8BAA6B,UAAS,8BAA6B,aAAY,4BAA2B,gBAAe,2BAA0B,kBAAiB,4BAA2B,kBAAiB,6BAA4B,wBAAuB,4BAA2B,8BAA6B,kBAAiB,MAAK,yBAAwB,SAAQ,uBAAsB,YAAW,sBAAqB,iBAAgB,uBAAsB,iBAAgB,wBAAuB,uBAAsB,uBAAsB,6BAA4B,kBAAiB,MAAK,uBAAsB,SAAQ,sBAAqB,cAAa,uBAAsB,cAAa,wBAAuB,oBAAmB,uBAAsB,0BAAyB,gBAAe,SAAQ,oBAAmB,cAAa,qBAAoB,cAAa,sBAAqB,oBAAmB,qBAAoB,0BAAyB,eAAc,QAAO,oBAAmB,QAAO,qBAAoB,cAAa,oBAAmB,oBAAmB,gBAAe,QAAO,sBAAqB,QAAO,qBAAoB,QAAO,iBAAgB,QAAO,sBAAqB,cAAa,gBAAe,QAAO,SAAQ,QAAO,SAAQ,QAAO,GAAE,IAAG,GAAE,IAAG,IAAG,IAAG,IAAG,IAAG,QAAO,IAAG,QAAO,IAAG,SAAQ,WAAU,UAAS,YAAW,OAAM,SAAQ,OAAM,UAAS,KAAI,UAAS,MAAK,UAAS,MAAK,UAAS,QAAO,UAAS,WAAU,aAAY,SAAQ,WAAU,UAAS,YAAW,UAAS,YAAW,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,cAAa,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,QAAO,YAAW,QAAO,UAAS,SAAQ,WAAU,WAAU,aAAY,UAAS,WAAU,QAAO,WAAU,UAAS,UAAS,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,UAAS,SAASI,IAAE;AAAC,WAAOA,KAAE;AAAA,EAAG,GAAE,YAAW,eAAc,MAAK,SAAQ,MAAK,SAAQ,QAAO,WAAU,uCAAsC,4CAA2C,SAAQ,eAAc,MAAK,QAAO,OAAM,WAAU,gBAAe,qBAAoB,aAAY,eAAc,aAAY,mBAAkB,eAAc,wBAAuB,eAAc,iBAAgB,kBAAiB,uBAAsB,gBAAe,uBAAsB,iBAAgB,qBAAoB,iBAAgB,mBAAkB,gBAAe,mBAAkB,QAAO,SAAQ,sBAAqB,mBAAkB,eAAc,cAAa,iBAAgB,mBAAkB,eAAc,iBAAgB,oBAAmB,0BAAyB,iBAAgB,mBAAkB,kBAAiB,oBAAmB,cAAa,aAAY,KAAI,SAAQ,0BAAyB,8BAA6B,2BAA0B,2BAA0B,qCAAoC,0CAAyC,+DAA8D,uEAAsE,QAAO,WAAU,OAAM,YAAW,MAAK,QAAO,OAAM,UAAS,qCAAoC,0CAAyC,uCAAsC,6CAA4C,8CAA6C,sDAAqD,4EAA2E,0HAA0H,wFAAuF,2HAA2H,qCAAoC,+CAA8C,yBAAwB,oCAAmC,gFAA+E,wGAAuG,cAAa,cAAa,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,KAAI,IAAG,MAAK,IAAG,KAAI,IAAG,MAAK,IAAG,+EAA8E,wHAAuH,+CAA8C,oEAAmE,oDAAmD,0EAAyE,qDAAoD,wEAAuE,yEAAwE,iHAAgH,4CAA2C,2DAA0D,kDAAiD,qEAAoE,kDAAiD,qEAAoE,iBAAgB,cAAa,WAAU,SAAQ,SAAQ,QAAO,oCAAmC,6CAA4C,0BAAyB,yCAAwC,2BAA0B,oCAAmC,gBAAe,kBAAiB;AAAC,EAAE,GAAE,CAAC,OAAK,EAAE,UAAQD;AAAG,IAAM,IAAE,EAAE,EAAC,WAAU,MAAK,SAAQ,EAAE,CAAC,EAAC,GAAE,CAAC,CAAC,CAAC;", "names": ["r", "a", "i", "o", "e"]}