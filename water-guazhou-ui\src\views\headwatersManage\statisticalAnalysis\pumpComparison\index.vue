<!-- 比泵分析 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <SLCard
      class="card"
      :title="state.activeName==='list'?'比泵分析':'比泵分析图表'"
    >
      <template #query>
        <el-radio-group
          v-model="state.activeName"
        >
          <el-radio-button label="echarts">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="clarity:bar-chart-line"
            />
          </el-radio-button>
          <el-radio-button label="list">
            <Icon
              style="margin-right: 1px;font-size: 16px"
              icon="material-symbols:table"
            />
          </el-radio-button>
        </el-radio-group>
      </template>

      <!-- 列表模式 -->
      <div v-show="state.activeName === 'list'" class="content-container">
        <CardTable
          id="print"
          ref="refTable"
          class="card-table"
          :config="cardTableConfig"
        />
      </div>

      <!-- 图表模式 -->
      <div v-show="state.activeName === 'echarts'" class="content-container">
        <div class="chart-container">
          <VChart
            ref="refChart"
            :theme="useAppStore().isDark?'dark':'light'"
            :option="state.chartOption"
            class="comparison-chart"
          ></VChart>
        </div>
      </div>
    </SLCard>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, nextTick, onMounted, onUnmounted, watch } from 'vue'
import dayjs from 'dayjs'
import { ElMessageBox } from 'element-plus'
import { Icon } from '@iconify/vue'
import { getWaterOutletAndInletReport } from '@/api/headwatersManage/statisticalAnalysis'
import {
  objectLookup
} from '@/utils/GlobalHelper'
import useStation from '@/hooks/station/useStation'
import { printJSON } from '@/utils/printUtils'

import { useAppStore } from '@/store'
import { SLMessage } from '@/utils/Message'

const { getStationTree } = useStation()
const today = dayjs().date()
const cardSearch = ref<any>()
const refTable = ref()
const refChart = ref<any>()

// 状态管理
const state = reactive<{
  activeName: string;
  chartOption: any;
}>({
  activeName: 'list',
  chartOption: null
})

// 水源站点树
const TreeData = reactive<any>({
  data: [],
  checkedKeys: [],
  checkedNodes: [],
  currentProject: {}
})


// 搜索栏初始化配置
const cardSearchConfig = reactive<any>({
  defaultParams: {
    date: [
      dayjs()
        .date(today - 3)
        .format('YYYY-MM-DD'),
      dayjs().date(today).format('YYYY-MM-DD')
    ]
  },
  filters: [
    {
      type: 'select-tree',
      field: 'treeData',
      defaultExpandAll: true,
      multiple: true,
      options: computed(() => TreeData.data) as any,
      label: '水源地选择',
      onChange: (key: any) => {
        TreeData.checkedKeys = key || []
        TreeData.checkedNodes = []
        for (const i in key) {
          const val = objectLookup(TreeData.data, 'children', 'id', i)
          TreeData.checkedNodes.push(val)
        }
        refreshData()
      }
    },
    { type: 'daterange', label: '选择时间', field: 'date' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          icon: 'iconfont icon-chaxun'
        },
        {
          perm: true,
          text: '导出全部',
          click: () => {
            if (cardTableConfig.dataList.length > 0) {
              printJSON(cardTableConfig.dataList)
            } else {
              SLMessage.warning('暂无数据可导出')
            }
          },
          icon: 'iconfont icon-daochu'
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<any>({
  loading: false,
  dataList: [],
  columns: [
    { prop: 'name', label: '水源地名称', minWidth: 200, align: 'center' as const },
    { prop: 'outletTotalFlow', label: '出水量', unit: '(m³)', minWidth: 150, align: 'center' as const },
    { prop: 'inletTotalFlow', label: '进水量', unit: '(m³)', minWidth: 150, align: 'center' as const },
    { prop: 'differenceTotalFlow', label: '进出水差值', unit: '(m³)', minWidth: 160, align: 'center' as const },
    { prop: 'differenceRate', label: '差值率', unit: '(%)', minWidth: 120, align: 'center' as const },
    { prop: 'efficiency', label: '泵效率', unit: '(%)', minWidth: 120, align: 'center' as const },
    { prop: 'performanceRank', label: '效率排名', minWidth: 120, align: 'center' as const }
  ],
  operations: [
    {
      text: '详情',
      perm: true,
      click: (row: any) => {
        ElMessageBox.alert(
          `水源地：${row.name}<br/>
           进水量：${row.inletTotalFlow} m³<br/>
           出水量：${row.outletTotalFlow} m³<br/>
           差值：${row.differenceTotalFlow} m³<br/>
           差值率：${row.differenceRate}%<br/>
           泵效率：${row.efficiency}%<br/>
           效率排名：第${row.performanceRank}名`,
          '水源地详细信息',
          {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定'
          }
        )
      }
    }
  ],
  showSummary: true,
  summaryMethod: (param: any) => {
    const { columns, data } = param
    const sums: string[] = []
    columns.forEach((column: any, index: number) => {
      if (index === 0) {
        sums[index] = '合计/平均'
        return
      }
      if (column.property === 'name') {
        sums[index] = '合计/平均'
        return
      }
      if (column.property === 'performanceRank') {
        sums[index] = '-'
        return
      }
      const values = data.map((item: any) => Number(item[column.property]))
      if (!values.every((value: any) => isNaN(value))) {
        if (column.property === 'differenceRate' || column.property === 'efficiency') {
          // 百分比字段计算平均值
          const avg = values.reduce((prev: any, curr: any) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0) / values.length
          sums[index] = avg.toFixed(2) + '%'
        } else {
          // 其他字段计算总和
          const sum = values.reduce((prev: any, curr: any) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          sums[index] = sum.toFixed(2)
        }
      } else {
        sums[index] = 'N/A'
      }
    })
    return sums
  },
  operationWidth: '100px',
  indexVisible: true,
  pagination: {
    hide: true
  }
})

// 刷新列表数据
const refreshData = () => {
  if (!TreeData.checkedKeys || TreeData.checkedKeys.length === 0) {
    SLMessage.warning('请至少选择一个水源地进行对比分析')
    return
  }

  if (TreeData.checkedKeys.length < 2) {
    SLMessage.warning('建议选择至少2个水源地进行对比分析')
  }

  cardTableConfig.loading = true
  const queryParams = cardSearch.value?.queryParams as any || {}
  const stationIdList = TreeData.checkedKeys as any[]
  const params: any = {
    stationIdList: stationIdList.join(','),
    start: dayjs(queryParams.date[0]).valueOf(),
    end: dayjs(queryParams.date[1]).valueOf()
  }

  getWaterOutletAndInletReport(params).then(res => {
    const data = res.data.data || []
    
    // 处理数据，添加计算字段
    const processedData = data.map((item: any) => {
      const outletFlow = parseFloat(item.outletTotalFlow) || 0
      const inletFlow = parseFloat(item.inletTotalFlow) || 0
      const difference = inletFlow - outletFlow
      const differenceRate = inletFlow > 0 ? ((difference / inletFlow) * 100) : 0
      const efficiency = inletFlow > 0 ? ((outletFlow / inletFlow) * 100) : 0
      
      return {
        ...item,
        outletTotalFlow: outletFlow.toFixed(2),
        inletTotalFlow: inletFlow.toFixed(2),
        differenceTotalFlow: difference.toFixed(2),
        differenceRate: differenceRate.toFixed(2),
        efficiency: efficiency.toFixed(2),
        efficiencyValue: efficiency // 用于排序
      }
    })
    
    // 按效率排序并添加排名
    const sortedData = processedData.sort((a: any, b: any) => b.efficiencyValue - a.efficiencyValue)
    sortedData.forEach((item, index) => {
      item.performanceRank = index + 1
    })
    
    cardTableConfig.dataList = sortedData
    cardTableConfig.loading = false
    
    // 如果是图表模式，生成图表
    if (state.activeName === 'echarts') {
      nextTick(() => {
        setTimeout(() => {
          generateComparisonChart(sortedData)
        }, 200)
      })
    }
  }).catch((error) => {
    console.error('获取数据失败:', error)
    cardTableConfig.loading = false
    SLMessage.error('获取数据失败，请稍后重试')
  })
}

// 生成对比图表
const generateComparisonChart = (data: any[]) => {
  if (!data || data.length === 0) {
    return
  }

  const stationNames = data.map(item => item.name)
  const outletFlowData = data.map(item => parseFloat(item.outletTotalFlow))
  const inletFlowData = data.map(item => parseFloat(item.inletTotalFlow))
  const efficiencyData = data.map(item => parseFloat(item.efficiency))

  const chartConfig = {
    title: {
      text: '水源地比泵分析对比图',
      subtext: `共对比 ${data.length} 个水源地`,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      },
      subtextStyle: {
        fontSize: 12,
        color: '#666'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function(params: any) {
        let result = `<strong>${params[0].axisValue}</strong><br/>`
        params.forEach((param: any) => {
          let unit = ''
          if (param.seriesName.includes('水量')) {
            unit = ' m³'
          } else if (param.seriesName.includes('效率')) {
            unit = '%'
          }
          result += `${param.marker} ${param.seriesName}: ${param.value}${unit}<br/>`
        })
        return result
      }
    },
    legend: {
      top: 50,
      data: ['进水量', '出水量', '泵效率'],
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '15%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: stationNames,
      name: '水源地',
      nameTextStyle: {
        fontSize: 12
      },
      axisLabel: {
        fontSize: 11,
        rotate: 30,
        interval: 0
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '水量(m³)',
        position: 'left',
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          fontSize: 11,
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '效率(%)',
        position: 'right',
        nameTextStyle: {
          fontSize: 12
        },
        axisLabel: {
          fontSize: 11,
          formatter: '{value}%'
        },
        max: 100,
        min: 0
      }
    ],
    series: [
      {
        name: '进水量',
        type: 'bar',
        yAxisIndex: 0,
        data: inletFlowData,
        itemStyle: {
          color: '#5470C6'
        },
        barWidth: '25%',
        label: {
          show: true,
          position: 'top',
          fontSize: 10
        }
      },
      {
        name: '出水量',
        type: 'bar',
        yAxisIndex: 0,
        data: outletFlowData,
        itemStyle: {
          color: '#91CC75'
        },
        barWidth: '25%',
        label: {
          show: true,
          position: 'top',
          fontSize: 10
        }
      },
      {
        name: '泵效率',
        type: 'line',
        yAxisIndex: 1,
        data: efficiencyData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 3,
          color: '#FAC858'
        },
        itemStyle: {
          color: '#FAC858'
        },
        label: {
          show: true,
          position: 'top',
          fontSize: 10,
          formatter: '{c}%'
        }
      }
    ]
  }

  state.chartOption = chartConfig

  // 延迟调整图表大小
  nextTick(() => {
    setTimeout(() => {
      refChart.value?.resize()
    }, 200)
  })
}

// 监听模式切换
watch(() => state.activeName, (newMode) => {
  if (newMode === 'echarts' && cardTableConfig.dataList.length > 0) {
    nextTick(() => {
      setTimeout(() => {
        generateComparisonChart(cardTableConfig.dataList)
      }, 200)
    })
  }
})

// 获取所有最深层节点
const getAllDeepestNodes = (nodes: any[]): any[] => {
  let result: any[] = []

  if (!nodes || !Array.isArray(nodes)) {
    return result
  }

  const traverse = (nodeList: any[]) => {
    if (!nodeList || !Array.isArray(nodeList)) {
      return
    }

    nodeList.forEach(node => {
      if (!node) return

      if (node.children && Array.isArray(node.children) && node.children.length > 0) {
        traverse(node.children)
      } else {
        result.push(node)
      }
    })
  }

  traverse(nodes)
  return result
}

onMounted(async () => {
  try {
    const treeData = await getStationTree('水源地')
    TreeData.data = treeData

    // 默认选择前两个水源地进行对比
    const allStations = getAllDeepestNodes(TreeData.data)
    const defaultStations = allStations.slice(0, 2) // 取前两个
    const defaultStationIds = defaultStations.map(station => station.id)

    if (defaultStationIds.length > 0) {
      cardSearchConfig.defaultParams = {
        ...cardSearchConfig.defaultParams,
        treeData: defaultStationIds
      }

      // 延迟执行，确保组件完全初始化
      nextTick(() => {
        cardSearch.value?.resetForm()
        TreeData.checkedKeys = defaultStationIds
        TreeData.checkedNodes = defaultStations

        setTimeout(() => {
          refreshData()
        }, 100)
      })
    }

    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      setTimeout(() => {
        refChart.value?.resize()
      }, 100)
    })
  } catch (error) {
    console.error('初始化失败:', error)
    SLMessage.error('页面初始化失败，请刷新重试')
  }
})

onUnmounted(() => {
  // 清理事件监听
  window.removeEventListener('resize', () => {
    refChart.value?.resize()
  })
})
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card {
  height: calc(100% - 80px);
}

.content-container {
  width: 100%;
  height: calc(100vh - 254px);
  min-height: 500px;
}

.chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.comparison-chart {
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.card-table {
  height: 100%;
}

// 深色模式适配
:deep(.el-table) {
  background-color: transparent;
}

:deep(.el-table__header-wrapper) {
  background-color: transparent;
}

:deep(.el-table__body-wrapper) {
  background-color: transparent;
}

// 表格样式优化
:deep(.el-table .cell) {
  padding: 8px 12px;
}

:deep(.el-table th) {
  background-color: var(--el-table-header-bg-color);
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid var(--el-table-border-color);
}

// 汇总行样式
:deep(.el-table__footer-wrapper .el-table__footer .cell) {
  font-weight: 600;
  color: var(--el-color-primary);
}

// 排名列特殊样式
:deep(.el-table .cell) {
  &:has([data-prop="performanceRank"]) {
    font-weight: 600;

    &:nth-child(1) {
      color: #FFD700; // 第一名金色
    }

    &:nth-child(2) {
      color: #C0C0C0; // 第二名银色
    }

    &:nth-child(3) {
      color: #CD7F32; // 第三名铜色
    }
  }
}

// 效率高的行高亮
:deep(.el-table__row) {
  &[data-efficiency="high"] {
    background-color: rgba(103, 194, 58, 0.1);
  }

  &[data-efficiency="medium"] {
    background-color: rgba(250, 200, 88, 0.1);
  }

  &[data-efficiency="low"] {
    background-color: rgba(245, 108, 108, 0.1);
  }
}
</style>
