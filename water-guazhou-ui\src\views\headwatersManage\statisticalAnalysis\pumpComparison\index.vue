<!-- 比泵分析 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      class="card-table"
      :config="cardTableConfig"
    />
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { getWaterSupplyAndEnergyData } from '@/api/headwatersManage/statisticalAnalysis'
import { ICardSearchIns } from '@/components/type'
import {
  getFormatTreeNodeDeepestChild,
  objectLookup
} from '@/utils/GlobalHelper'
import useStation from '@/hooks/station/useStation'

const { getStationTree } = useStation()
const today = dayjs().date()
const cardSearch = ref<ICardSearchIns>()

// 水源站点树
const TreeData = reactive<SLTreeConfig>({
  data: [],
  checkedKeys: [],
  checkedNodes: []
})

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    date: [
      dayjs()
        .date(today - 3)
        .format('YYYY-MM-DD'),
      dayjs().date(today).format('YYYY-MM-DD')
    ]
  },
  filters: [
    { type: 'daterange', label: '选择时间', field: 'date' },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          icon: 'iconfont icon-chaxun'
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [
    { prop: 'name', label: '站点名称', minWidth: 120, align: 'center' as const },
    { prop: 'time', label: '数据年份', minWidth: 100, align: 'center' as const },
    { prop: 'unitConsumption', label: '出水电耗（kWh/吨）', minWidth: 150, align: 'center' as const },
    { prop: 'totalFlow', label: '机水量（千吨）', minWidth: 130, align: 'center' as const },
    { prop: 'energy', label: '用电量（千kWh）', minWidth: 140, align: 'center' as const },
    { prop: 'lastTimeEnergy', label: '电耗（千kWh）', minWidth: 130, align: 'center' as const },
    { prop: 'lastTimeTotalFlow', label: '吨水耗电量（千吨）', minWidth: 160, align: 'center' as const },
    { prop: 'differenceValue', label: '供水设备（元）', minWidth: 130, align: 'center' as const },
    { prop: 'changeRate', label: '吨水成本（元）', minWidth: 130, align: 'center' as const },
    { prop: 'suggestion', label: '建议', minWidth: 200, align: 'left' as const }
  ],
  operations: [],
  showSummary: false,
  operationWidth: '150px',
  indexVisible: true,
  pagination: {
    hide: true
  }
})

// 生成建议函数
const generateSuggestion = (unitConsumption: number): string => {
  if (unitConsumption <= 0.3) {
    return '通过对比，自来水厂合格率较高'
  } else if (unitConsumption <= 0.5) {
    return '通过对比，自来水厂合格率一般，建议优化设备运行'
  } else if (unitConsumption <= 0.8) {
    return '通过对比，自来水厂合格率偏低，建议检查设备效率'
  } else {
    return '通过对比，自来水厂合格率较低，建议进行设备维护或更换'
  }
}

// 刷新列表数据
const refreshData = () => {
  cardTableConfig.loading = true
  const queryParams = cardSearch.value?.queryParams as any || {}
  const params: any = {
    start: dayjs(queryParams.date[0]).valueOf(),
    end: dayjs(queryParams.date[1]).valueOf(),
    queryType: 'day' // 默认按天查询
  }

  getWaterSupplyAndEnergyData(params).then((res: any) => {
    const data = res.data.data || []

    // 处理数据，添加计算字段和建议
    const processedData = data.map((item: any) => {
      const unitConsumption = parseFloat(item.unitConsumption) || 0
      const totalFlow = parseFloat(item.totalFlow) || 0
      const energy = parseFloat(item.energy) || 0
      const lastTimeEnergy = parseFloat(item.lastTimeEnergy) || 0
      const lastTimeTotalFlow = parseFloat(item.lastTimeTotalFlow) || 0
      const differenceValue = parseFloat(item.differenceValue) || 0
      const changeRate = parseFloat(item.changeRate) || 0

      // 生成建议
      const suggestion = generateSuggestion(unitConsumption)

      return {
        ...item,
        name: item.name || '未知站点',
        time: item.time || new Date().getFullYear().toString(),
        unitConsumption: unitConsumption.toFixed(2), // 出水电耗（kWh/吨）
        totalFlow: (totalFlow / 1000).toFixed(2), // 机水量（千吨）
        energy: (energy / 1000).toFixed(2), // 用电量（千kWh）
        lastTimeEnergy: (lastTimeEnergy / 1000).toFixed(2), // 上期电耗（千kWh）
        lastTimeTotalFlow: (lastTimeTotalFlow / 1000).toFixed(2), // 上期供水量（千吨）
        differenceValue: differenceValue.toFixed(2), // 吨水电耗差值
        changeRate: changeRate.toFixed(2) + '%', // 变化率
        suggestion: suggestion // 建议
      }
    })

    cardTableConfig.dataList = processedData
    cardTableConfig.loading = false
  }).catch((error: any) => {
    console.error('获取数据失败:', error)
    cardTableConfig.loading = false
  })
}

onMounted(() => {
  refreshData()
})

</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-table {
  height: calc(100% - 80px);
}
</style>