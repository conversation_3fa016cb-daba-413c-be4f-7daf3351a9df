<!-- 比泵分析 -->
<template>
  <div class="wrapper">
    <CardSearch
      ref="cardSearch"
      :config="cardSearchConfig"
    />
    <CardTable
      ref="refTable"
      class="card-table"
      :config="cardTableConfig"
    />
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs'
import { Download, Printer, Search } from '@element-plus/icons-vue'
import { getWaterSupplyAndEnergyData } from '@/api/headwatersManage/statisticalAnalysis'
import { printJSON } from '@/utils/printUtils'

const cardSearch = ref()
const refTable = ref()

// 搜索栏初始化配置
const cardSearchConfig = reactive<ISearch>({
  defaultParams: {
    year: [
      dayjs().format('YYYY'),
      dayjs().format('YYYY')
    ]
  },
  filters: [
    {
      type: 'yearrange',
      label: '选择年份',
      field: 'year',
      clearable: false
    },
    {
      type: 'btn-group',
      btns: [
        {
          perm: true,
          text: '查询',
          click: () => refreshData(),
          svgIcon: shallowRef(Search)
        },
        {
          text: '导出',
          perm: true,
          type: 'warning',
          svgIcon: shallowRef(Download),
          click: () => _exportReport()
        },
        {
          perm: true,
          text: '打印',
          type: 'success',
          svgIcon: shallowRef(Printer),
          click: () => handlePrint()
        }
      ]
    }
  ]
})

// 初始化列表配置数据
const cardTableConfig = reactive<ITable>({
  loading: false,
  dataList: [],
  columns: [
    { prop: 'name', label: '站点名称', minWidth: 120, align: 'center' as const },
    { prop: 'time', label: '数据年份', minWidth: 100, align: 'center' as const },
    { prop: 'unitConsumption', label: '吨水电耗（kWh/吨）', minWidth: 150, align: 'center' as const },
    { prop: 'totalFlow', label: '取水量（千吨）', minWidth: 130, align: 'center' as const },
    { prop: 'energy', label: '用电量（千kWh）', minWidth: 140, align: 'center' as const }
  ],
  operations: [],
  showSummary: false,
  operationWidth: '150px',
  indexVisible: true,
  pagination: {
    hide: true
  }
})

// 生成建议函数 - 只为最高效率的泵机生成建议
const generateSuggestions = (dataList: any[]): any[] => {
  if (!dataList || dataList.length === 0) {
    return dataList
  }

  // 找到效率最高的泵机（吨水电耗最低）
  let bestEfficiencyIndex = 0
  let bestUnitConsumption = parseFloat(dataList[0].rawUnitConsumption) || Infinity

  dataList.forEach((item, index) => {
    const unitConsumption = parseFloat(item.rawUnitConsumption) || Infinity
    if (unitConsumption < bestUnitConsumption && unitConsumption > 0) {
      bestUnitConsumption = unitConsumption
      bestEfficiencyIndex = index
    }
  })

  // 为所有数据添加建议字段，只有最高效率的有建议内容
  return dataList.map((item, index) => {
    let suggestion = ''
    if (index === bestEfficiencyIndex) {
      suggestion = '通过对比，该泵机效率最高，建议作为运行优化参考标准'
    }

    return {
      ...item,
      suggestion
    }
  })
}

// 导出报告
const _exportReport = () => {
  refTable.value?.exportTable()
}

// 打印报表
const handlePrint = () => {
  const title = '比泵分析报告'
  printJSON({ title, data: cardTableConfig.dataList, titleList: cardTableConfig.columns })
}

// 刷新列表数据
const refreshData = () => {
  cardTableConfig.loading = true
  const queryParams = cardSearch.value?.queryParams as any || {}
  const yearRange = queryParams.year || []

  const params: any = {
    start: dayjs(yearRange[0]).startOf('year').valueOf(),
    end: dayjs(yearRange[1]).endOf('year').valueOf(),
    queryType: 'year' // 按年查询
  }

  getWaterSupplyAndEnergyData(params).then((res: any) => {
    const data = res.data.data || []

    // 处理数据，添加计算字段
    const processedData = data.map((item: any) => {
      const unitConsumption = parseFloat(item.unitConsumption) || 0
      const totalFlow = parseFloat(item.totalFlow) || 0
      const energy = parseFloat(item.energy) || 0
      const lastTimeEnergy = parseFloat(item.lastTimeEnergy) || 0
      const lastTimeTotalFlow = parseFloat(item.lastTimeTotalFlow) || 0
      const differenceValue = parseFloat(item.differenceValue) || 0
      const changeRate = parseFloat(item.changeRate) || 0

      return {
        ...item,
        name: item.name || '未知站点',
        time: item.time || yearRange[0] || new Date().getFullYear().toString(),
        unitConsumption: unitConsumption.toFixed(2), // 出水电耗（kWh/吨）
        totalFlow: (totalFlow / 1000).toFixed(2), // 机水量（千吨）
        energy: (energy / 1000).toFixed(2), // 用电量（千kWh）
        lastTimeEnergy: (lastTimeEnergy / 1000).toFixed(2), // 上期电耗（千kWh）
        lastTimeTotalFlow: (lastTimeTotalFlow / 1000).toFixed(2), // 上期供水量（千吨）
        differenceValue: differenceValue.toFixed(2), // 吨水电耗差值
        changeRate: changeRate.toFixed(2) + '%', // 变化率
        rawUnitConsumption: unitConsumption // 保留原始值用于比较
      }
    })

    // 生成建议（只为最高效率的泵机）
    const dataWithSuggestions = generateSuggestions(processedData)

    cardTableConfig.dataList = dataWithSuggestions
    cardTableConfig.loading = false
  }).catch((error: any) => {
    console.error('获取数据失败:', error)
    cardTableConfig.loading = false
  })
}

onMounted(() => {
  refreshData()
})

</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-table {
  height: calc(100% - 80px);
}
</style>