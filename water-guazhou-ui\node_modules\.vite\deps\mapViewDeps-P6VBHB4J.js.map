{"version": 3, "sources": ["../../@arcgis/core/views/2d/engine/vectorTiles/shaders/Programs.js", "../../@arcgis/core/views/2d/engine/vectorTiles/shaders/VTLMaterialManager.js", "../../@arcgis/core/views/2d/engine/webgl/BitBlitRenderer.js", "../../@arcgis/core/views/2d/engine/webgl/MaterialManager.js", "../../@arcgis/core/views/2d/engine/webgl/TextureUploadManager.js", "../../@arcgis/core/views/2d/engine/webgl/WorldExtentClipRenderer.js", "../../@arcgis/core/views/2d/engine/webgl/effects/AnimationEffect.js", "../../@arcgis/core/views/2d/engine/webgl/effects/FeatureEffect.js", "../../@arcgis/core/views/2d/engine/webgl/effects/highlight/HighlightRenderer.js", "../../@arcgis/core/views/2d/engine/webgl/effects/highlight/HighlightSurfaces.js", "../../@arcgis/core/views/2d/engine/webgl/effects/HighlightEffect.js", "../../@arcgis/core/views/2d/engine/webgl/effects/HittestEffect.js", "../../@arcgis/core/views/2d/engine/webgl/effects/HittestEffectVTL.js", "../../@arcgis/core/views/2d/engine/webgl/effects/post-processing/Bloom.js", "../../@arcgis/core/views/2d/engine/webgl/effects/post-processing/Blur.js", "../../@arcgis/core/views/2d/engine/webgl/effects/post-processing/Colorize.js", "../../@arcgis/core/views/2d/engine/webgl/effects/post-processing/DropShadow.js", "../../@arcgis/core/views/2d/engine/webgl/effects/post-processing/Opacity.js", "../../@arcgis/core/views/2d/engine/webgl/effects/post-processing/EffectManager.js", "../../@arcgis/core/views/2d/engine/webgl/painter/RenderPass.js", "../../@arcgis/core/views/2d/engine/webgl/Painter.js", "../../@arcgis/core/views/2d/engine/Stage.js", "../../@arcgis/core/views/magnifier/resources.js", "../../@arcgis/core/views/2d/magnifier/MagnifierView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{resolveIncludes as e}from\"./sources/resolver.js\";import{glslifyDefineMap as r}from\"../../../../webgl/programUtils.js\";const t=e=>r({ID:e.id,PATTERN:e.pattern}),a={shaders:r=>({vertexShader:t(r)+e(\"background/background.vert\"),fragmentShader:t(r)+e(\"background/background.frag\")})},d=e=>r({ID:e.id}),i={shaders:r=>({vertexShader:d(r)+e(\"circle/circle.vert\"),fragmentShader:d(r)+e(\"circle/circle.frag\")})},n=e=>r({ID:e.id,PATTERN:e.pattern}),l={shaders:r=>({vertexShader:n(r)+e(\"fill/fill.vert\"),fragmentShader:n(r)+e(\"fill/fill.frag\")})},s=e=>r({ID:e.id}),f={shaders:r=>({vertexShader:s(r)+e(\"outline/outline.vert\"),fragmentShader:s(r)+e(\"outline/outline.frag\")})},h=e=>r({ID:e.id,SDF:e.sdf}),o={shaders:r=>({vertexShader:h(r)+e(\"icon/icon.vert\"),fragmentShader:h(r)+e(\"icon/icon.frag\")})},g=e=>r({ID:e.id,PATTERN:e.pattern,SDF:e.sdf}),c={shaders:r=>({vertexShader:g(r)+e(\"line/line.vert\"),fragmentShader:g(r)+e(\"line/line.frag\")})},S=e=>r({ID:e.id}),v={shaders:r=>({vertexShader:S(r)+e(\"text/text.vert\"),fragmentShader:S(r)+e(\"text/text.frag\")})};export{a as background,i as circle,l as fill,o as icon,c as line,f as outline,v as text};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{ShaderProgramType as e}from\"../enums.js\";import{text as r,outline as t,line as a,icon as s,fill as n,circle as c,background as i}from\"./Programs.js\";class o{constructor(){this._programByKey=new Map}dispose(){this._programByKey.forEach((e=>e.dispose())),this._programByKey.clear()}getMaterialProgram(e,r,t){const a=r.key<<3|this._getMaterialOptionsValue(r.type,t);if(this._programByKey.has(a))return this._programByKey.get(a);const s=this._getProgramTemplate(r.type),{shaders:n}=s,{vertexShader:c,fragmentShader:i}=n(t),o=r.getShaderHeader(),u=r.getShaderMain(),p=c.replace(\"#pragma header\",o).replace(\"#pragma main\",u),g=e.programCache.acquire(p,i,r.getAttributeLocations());return this._programByKey.set(a,g),g}_getMaterialOptionsValue(r,t){switch(r){case e.BACKGROUND:{const e=t;return(e.pattern?1:0)<<1|(e.id?1:0)}case e.FILL:{const e=t;return(e.pattern?1:0)<<1|(e.id?1:0)}case e.OUTLINE:return t.id?1:0;case e.LINE:{const e=t;return(e.sdf?1:0)<<2|(e.pattern?1:0)<<1|(e.id?1:0)}case e.ICON:{const e=t;return(e.sdf?1:0)<<1|(e.id?1:0)}case e.CIRCLE:return t.id?1:0;case e.TEXT:return t.id?1:0;default:return 0}}_getProgramTemplate(o){switch(o){case e.BACKGROUND:return i;case e.CIRCLE:return c;case e.FILL:return n;case e.ICON:return s;case e.LINE:return a;case e.OUTLINE:return t;case e.TEXT:return r;default:return null}}}export{o as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{disposeMaybe as r}from\"../../../../core/maybe.js\";import{PosTex2b as t}from\"./DefaultVertexAttributeLayouts.js\";import{bitBlit as e}from\"./shaders/BitBlitPrograms.js\";import{BufferObject as i}from\"../../../webgl/BufferObject.js\";import{BlendFactor as s,PrimitiveType as o,Usage as a}from\"../../../webgl/enums.js\";import{createProgram as m}from\"../../../webgl/ProgramTemplate.js\";import{VertexArrayObject as n}from\"../../../webgl/VertexArrayObject.js\";class _{constructor(){this._initialized=!1}dispose(){this._program=r(this._program),this._vertexArrayObject=r(this._vertexArrayObject)}render(r,t,e,i){r&&(this._initialized||this._initialize(r),r.setBlendFunctionSeparate(s.ONE,s.ONE_MINUS_SRC_ALPHA,s.ONE,s.ONE_MINUS_SRC_ALPHA),r.bindVAO(this._vertexArrayObject),r.useProgram(this._program),t.setSamplingMode(e),r.bindTexture(t,0),this._program.setUniform1i(\"u_tex\",0),this._program.setUniform1f(\"u_opacity\",i),r.drawArrays(o.TRIANGLE_STRIP,0,4),r.bindTexture(null,0),r.bindVAO())}_initialize(r){if(this._initialized)return!0;const s=m(r,e);if(!s)return!1;const o=new Int8Array(16);o[0]=-1,o[1]=-1,o[2]=0,o[3]=0,o[4]=1,o[5]=-1,o[6]=1,o[7]=0,o[8]=-1,o[9]=1,o[10]=0,o[11]=1,o[12]=1,o[13]=1,o[14]=1,o[15]=1;const _=e.attributes,p=new n(r,_,t,{geometry:i.createVertex(r,a.STATIC_DRAW,o)});return this._program=s,this._vertexArrayObject=p,this._initialized=!0,!0}}export{_ as BitBlitRenderer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as r}from\"../../../../core/maybe.js\";import{WGLDrawPhase as e}from\"./enums.js\";import{createProgramTemplate as t}from\"./shaders/MaterialPrograms.js\";const a=r=>r===e.HITTEST||r===e.LABEL_ALPHA,s=r=>(a(r)?1:0)|(r===e.HIGHLIGHT?2:0),o=({rendererInfo:e,drawPhase:t},a,o)=>`${a.getVariationHash()}-${s(t)}-${e.getVariationHash()}-${r(o)&&o.join(\".\")}`,i=(t,s,o,i={})=>{if(i={...i,...s.getVariation(),...t.rendererInfo.getVariation(),highlight:t.drawPhase===e.HIGHLIGHT,id:a(t.drawPhase)},r(o))for(const r of o)i[r]=!0;return i};class h{constructor(r){this._rctx=r,this._programByKey=new Map}dispose(){this._programByKey.forEach((r=>r.dispose())),this._programByKey.clear()}getProgram(r,e=[]){const a=r.vsPath+\".\"+r.fsPath+JSON.stringify(e);if(this._programByKey.has(a))return this._programByKey.get(a);const s={...e.map((r=>\"string\"==typeof r?{name:r,value:!0}:r)).reduce(((r,e)=>({...r,[e.name]:e.value})),{})},{vsPath:o,fsPath:i,attributes:h}=r,n=t(o,i,h,s),g=this._rctx.programCache.acquire(n.shaders.vertexShader,n.shaders.fragmentShader,n.attributes);if(!g)throw new Error(\"Unable to get program for key: ${key}\");return this._programByKey.set(a,g),g}getMaterialProgram(r,e,a,s,h){const n=o(r,e,h);if(this._programByKey.has(n))return this._programByKey.get(n);const g=i(r,e,h,{ignoresSamplerPrecision:r.context.driverTest.ignoresSamplerPrecision.result}),m=t(a,a,s,g),y=this._rctx.programCache.acquire(m.shaders.vertexShader,m.shaders.fragmentShader,m.attributes);if(!y)throw new Error(\"Unable to get program for key: ${key}\");return this._programByKey.set(n,y),y}}export{h as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e}from\"../../../../core/maybe.js\";import{createResolver as t,throwIfAborted as s,onAbortOrThrow as o}from\"../../../../core/promiseUtils.js\";import{TEXTURE_UPLOAD_MANAGER_BUDGET as r,TEXTURE_UPLOAD_MANAGER_CHUNK_SIZE as u}from\"./definitions.js\";import{ContextType as n}from\"../../../webgl/context-util.js\";class i{constructor(e,t){this._queue=[],this._context=e,this._refreshable=t}destroy(){this._queue=[]}enqueueTextureUpdate(e,r){const i=t(),h=e,a=u,c=Math.ceil(h.height/a);if(s(r),this._context.type===n.WEBGL1)this._queue.push({type:\"no-chunk\",request:e,resolver:i,options:r});else for(let t=0;t<c;t++){const s=t*a,o=t===c-1,u=o?h.height-a*t:a;this._queue.push({type:\"chunk\",request:e,resolver:i,chunk:t,chunkOffset:s,destHeight:u,chunkIsLast:o,options:r})}return o(r,(e=>i.reject(e))),i.promise}upload(){let t=0;for(;this._queue.length;){const s=performance.now(),o=this._queue.shift();if(o){if(e(o.options.signal)&&o.options.signal.aborted)continue;switch(o.type){case\"chunk\":this._uploadChunk(o);break;case\"no-chunk\":this._uploadNoChunk(o)}const u=performance.now()-s;if(t+=u,t+u>=r)break}}this._queue.length&&this._refreshable.requestRender()}_uploadChunk(t){const{request:s,resolver:o,chunkOffset:r,chunkIsLast:u,destHeight:n}=t,{data:i,texture:h,width:a}=s;e(i)&&(h.updateData(0,0,r,a,n,i,r),u&&o.resolve())}_uploadNoChunk(e){const{request:t,resolver:s}=e,{data:o,texture:r}=t;r.setData(o),s.resolve()}}export{i as TextureUploadManager};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{dispose<PERSON>aybe as t}from\"../../../../core/maybe.js\";import{t as s}from\"../../../../chunks/common.js\";import{j as i,d as r,r as e,h as o}from\"../../../../chunks/mat3.js\";import{c as a}from\"../../../../chunks/mat3f32.js\";import{f as n}from\"../../../../chunks/vec2f64.js\";import{s as h}from\"../../../../chunks/vec3.js\";import{c as m}from\"../../../../chunks/vec3f64.js\";import c from\"./VertexStream.js\";import{stencil as d}from\"./shaders/StencilPrograms.js\";import{StencilOperation as p,CompareFunction as _}from\"../../../webgl/enums.js\";import{createProgram as l}from\"../../../webgl/ProgramTemplate.js\";const u=n(-.5,-.5);class f{constructor(){this._centerNdc=m(),this._pxToNdc=m(),this._worldDimensionsPx=m(),this._mat3=a(),this._initialized=!1}dispose(){this._program=t(this._program),this._quad=t(this._quad)}render(t,s){const{context:i}=t;return!!this._updateGeometry(t,s)&&(this._initialized||this._initialize(i),i.setDepthWriteEnabled(!1),i.setDepthTestEnabled(!1),i.setColorMask(!1,!1,!1,!1),i.setBlendingEnabled(!1),i.setStencilOp(p.KEEP,p.KEEP,p.REPLACE),i.setStencilFunction(_.ALWAYS,1,255),i.setStencilTestEnabled(!0),i.useProgram(this._program),this._program.setUniformMatrix3fv(\"u_worldExtent\",this._mat3),this._quad.draw(),this._quad.unbind(),!0)}_initialize(t){if(this._initialized)return;const s=l(t,d);s&&(this._program=s,this._quad=new c(t,[0,0,1,0,0,1,1,1]),this._initialized=!0)}_updateGeometry(t,a){const{state:n,pixelRatio:m}=t,{size:c,rotation:d}=n,p=Math.round(c[0]*m),_=Math.round(c[1]*m);if(!n.spatialReference.isWrappable)return!1;const l=s(d),f=Math.abs(Math.cos(l)),b=Math.abs(Math.sin(l)),g=Math.round(p*f+_*b),j=Math.round(n.worldScreenWidth);if(g<=j)return!1;const x=p*b+_*f,E=j*m,M=(a.left-a.right)*m/p,w=(a.bottom-a.top)*m/_;h(this._worldDimensionsPx,E,x,1),h(this._pxToNdc,2/p,-2/_,1),h(this._centerNdc,M,w,1);const P=this._mat3;return i(P,this._centerNdc),r(P,P,this._pxToNdc),0!==d&&e(P,P,l),r(P,P,this._worldDimensionsPx),o(P,P,u),!0}}export{f as WorldExtentClipRenderer};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isNone as e}from\"../../../../../core/maybe.js\";import{ATTRIBUTE_DATA_ANIMATION as t,TEXTURE_BINDING_ATTRIBUTE_DATA_0 as r,TEXTURE_BINDING_ATTRIBUTE_DATA_1 as i}from\"../definitions.js\";import s from\"../VertexStream.js\";import{Effect as a}from\"./Effect.js\";class o extends a{constructor(){super(...arguments),this.defines=[],this._desc={vsPath:\"fx/integrate\",fsPath:\"fx/integrate\",attributes:new Map([[\"a_position\",0]])}}dispose(){this._quad&&this._quad.dispose()}bind(){}unbind(){}draw(r,i){if(!i?.size)return;const{context:a,renderingOptions:o}=r;this._quad||(this._quad=new s(a,[0,0,1,0,0,1,1,1]));const n=a.getBoundFramebufferObject(),{x:u,y:m,width:d,height:f}=a.getViewport();i.bindTextures(a);const c=i.getBlock(t);if(e(c))return;const _=c.getFBO(a),l=c.getFBO(a,1);a.setViewport(0,0,i.size,i.size),this._computeDelta(r,l,o.labelsAnimationTime),this._updateAnimationState(r,l,_),a.bindFramebuffer(n),a.setViewport(u,m,d,f)}_computeDelta(e,t,s){const{context:a,painter:o,displayLevel:n}=e,u=o.materialManager.getProgram(this._desc,[\"delta\"]);a.bindFramebuffer(t),a.setClearColor(0,0,0,0),a.clear(a.gl.COLOR_BUFFER_BIT),a.useProgram(u),u.setUniform1i(\"u_maskTexture\",r),u.setUniform1i(\"u_sourceTexture\",i),u.setUniform1f(\"u_timeDelta\",e.deltaTime),u.setUniform1f(\"u_animationTime\",s),u.setUniform1f(\"u_zoomLevel\",Math.round(10*n)),this._quad.draw()}_updateAnimationState(e,t,r){const{context:i,painter:s}=e,a=s.materialManager.getProgram(this._desc,[\"update\"]);i.bindTexture(t.colorTexture,1),i.useProgram(a),a.setUniform1i(\"u_sourceTexture\",1),i.bindFramebuffer(r),i.setClearColor(0,0,0,0),i.clear(i.gl.COLOR_BUFFER_BIT),this._quad.draw()}}export{o as AnimationEffect};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{Effect as e}from\"./Effect.js\";import{TextureSamplingMode as t}from\"../../../../webgl/enums.js\";class r extends e{constructor(e){super(),this.name=this.constructor.name,this.defines=[e]}dispose(){}bind({context:e,painter:t}){this._prev=e.getBoundFramebufferObject();const{width:r,height:s}=e.getViewport(),o=t.getFbos(r,s).effect0;e.bindFramebuffer(o),e.setColorMask(!0,!0,!0,!0),e.setClearColor(0,0,0,0),e.clear(e.gl.COLOR_BUFFER_BIT)}unbind(){}draw(e,r){const{context:s,painter:o}=e,n=o.getPostProcessingEffects(r),c=s.getBoundFramebufferObject();for(const{postProcessingEffect:t,effect:f}of n)t.draw(e,c,f);s.bindFramebuffer(this._prev),s.setStencilTestEnabled(!1),o.blitTexture(s,c.colorTexture,t.NEAREST),s.setStencilTestEnabled(!0)}}export{r as FeatureEffect};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{TEXTURE_BINDING_HIGHLIGHT_0 as r,TEXTURE_BINDING_HIGHLIGHT_1 as e}from\"../../definitions.js\";import{ALPHA_TO_RGBA_CHANNEL_SELECTOR_MATRIX as s,RGBA_TO_RGBA_CHANNEL_SELECTOR_MATRIX as i,SIGMA as t}from\"./parameters.js\";import{highlight as o,blur as u}from\"../../shaders/HighlightPrograms.js\";import{BufferObject as h}from\"../../../../../webgl/BufferObject.js\";import{PrimitiveType as a,BlendFactor as m,Usage as n,DataType as _}from\"../../../../../webgl/enums.js\";import{createProgram as g}from\"../../../../../webgl/ProgramTemplate.js\";import{VertexArrayObject as d}from\"../../../../../webgl/VertexArrayObject.js\";import{VertexElementDescriptor as l}from\"../../../../../webgl/VertexElementDescriptor.js\";class c{constructor(){this._width=void 0,this._height=void 0,this._resources=null}dispose(){this._resources&&(this._resources.quadGeometry.dispose(),this._resources.quadVAO.dispose(),this._resources.highlightProgram.dispose(),this._resources.blurProgram.dispose(),this._resources=null)}preBlur(e,i){e.bindTexture(i,r),e.useProgram(this._resources.blurProgram),this._resources.blurProgram.setUniform4fv(\"u_direction\",[1,0,1/this._width,0]),this._resources.blurProgram.setUniformMatrix4fv(\"u_channelSelector\",s),e.bindVAO(this._resources.quadVAO),e.drawArrays(a.TRIANGLE_STRIP,0,4),e.bindVAO()}finalBlur(e,s){e.bindTexture(s,r),e.useProgram(this._resources.blurProgram),this._resources.blurProgram.setUniform4fv(\"u_direction\",[0,1,0,1/this._height]),this._resources.blurProgram.setUniformMatrix4fv(\"u_channelSelector\",i),e.bindVAO(this._resources.quadVAO),e.drawArrays(a.TRIANGLE_STRIP,0,4),e.bindVAO()}renderHighlight(e,s,i){e.bindTexture(s,r),e.useProgram(this._resources.highlightProgram),i.applyHighlightOptions(e,this._resources.highlightProgram),e.bindVAO(this._resources.quadVAO),e.setBlendingEnabled(!0),e.setBlendFunction(m.ONE,m.ONE_MINUS_SRC_ALPHA),e.drawArrays(a.TRIANGLE_STRIP,0,4),e.bindVAO()}_initialize(s,i,a){this._width=i,this._height=a;const m=h.createVertex(s,n.STATIC_DRAW,new Int8Array([-1,-1,0,0,1,-1,1,0,-1,1,0,1,1,1,1,1]).buffer),c=new d(s,new Map([[\"a_position\",0],[\"a_texcoord\",1]]),{geometry:[new l(\"a_position\",2,_.BYTE,0,4),new l(\"a_texcoord\",2,_.UNSIGNED_BYTE,2,4)]},{geometry:m}),f=g(s,o),b=g(s,u);s.useProgram(f),f.setUniform1i(\"u_texture\",r),f.setUniform1i(\"u_shade\",e),f.setUniform1f(\"u_sigma\",t),s.useProgram(b),b.setUniform1i(\"u_texture\",r),b.setUniform1f(\"u_sigma\",t),this._resources={quadGeometry:m,quadVAO:c,highlightProgram:f,blurProgram:b}}setup(r,e,s){this._resources?(this._width=e,this._height=s):this._initialize(r,e,s)}}export{c as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{nullifyNonNullableForDispose as e}from\"../../../../../../core/maybe.js\";import{TextureType as s,PixelFormat as r,PixelType as t,TextureWrapMode as i,TextureSamplingMode as h,TargetType as o,DepthStencilTargetType as u}from\"../../../../../webgl/enums.js\";import{FramebufferObject as d}from\"../../../../../webgl/FramebufferObject.js\";import{Texture as a}from\"../../../../../webgl/Texture.js\";function l(e,l,_){const c=new a(e,{target:s.TEXTURE_2D,pixelFormat:r.RGBA,dataType:t.UNSIGNED_BYTE,wrapMode:i.CLAMP_TO_EDGE,width:l,height:_,samplingMode:h.LINEAR});return[c,new d(e,{colorTarget:o.TEXTURE,depthStencilTarget:u.STENCIL_RENDER_BUFFER},c)]}class _{constructor(){this._width=void 0,this._height=void 0,this._resources=null}dispose(){this._resources&&(this._resources.sharedBlur1Tex.dispose(),this._resources.sharedBlur1Fbo.dispose(),this._resources.sharedBlur2Tex.dispose(),this._resources.sharedBlur2Fbo.dispose(),this._resources=e(this._resources))}_initialize(e,s,r){this._width=s,this._height=r;const[t,i]=l(e,s,r),[h,o]=l(e,s,r);this._resources={sharedBlur1Tex:t,sharedBlur1Fbo:i,sharedBlur2Tex:h,sharedBlur2Fbo:o}}setup(e,s,r){!this._resources||this._width===s&&this._height===r||this.dispose(),this._resources||this._initialize(e,s,r)}get sharedBlur1Tex(){return this._resources.sharedBlur1Tex}get sharedBlur1Fbo(){return this._resources.sharedBlur1Fbo}get sharedBlur2Tex(){return this._resources.sharedBlur2Tex}get sharedBlur2Fbo(){return this._resources.sharedBlur2Fbo}}export{_ as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport\"../../../../../core/has.js\";import{BitBlitRenderer as e}from\"../BitBlitRenderer.js\";import{Effect as t}from\"./Effect.js\";import s from\"./highlight/HighlightRenderer.js\";import r from\"./highlight/HighlightSurfaces.js\";import{TextureSamplingMode as i}from\"../../../../webgl/enums.js\";const h=4,d=4/h;class l extends t{constructor(){super(...arguments),this.defines=[\"highlight\"],this._hlRenderer=new s,this._width=void 0,this._height=void 0,this._boundFBO=null,this._hlSurfaces=new r,this._adjustedWidth=void 0,this._adjustedHeight=void 0,this._blitRenderer=new e}dispose(){this._hlSurfaces?.dispose(),this._hlRenderer?.dispose(),this._boundFBO=null}bind(e){const{context:t,painter:s}=e,{width:r,height:i}=t.getViewport(),h=s.getFbos(r,i).effect0;this.setup(e,r,i),t.bindFramebuffer(h),t.setColorMask(!0,!0,!0,!0),t.setClearColor(0,0,0,0),t.clear(t.gl.COLOR_BUFFER_BIT)}unbind(){}setup({context:e},t,s){this._width=t,this._height=s;const r=t%h,i=s%h;t+=r<h/2?-r:h-r,s+=i<h/2?-i:h-i,this._adjustedWidth=t,this._adjustedHeight=s,this._boundFBO=e.getBoundFramebufferObject();const l=Math.round(t*d),o=Math.round(s*d);this._hlRenderer.setup(e,l,o),this._hlSurfaces.setup(e,l,o)}draw(e){const{context:t,highlightGradient:s}=e;if(!s)return;const r=t.getBoundFramebufferObject();t.setViewport(0,0,this._adjustedWidth*d,this._adjustedHeight*d),t.bindFramebuffer(this._hlSurfaces.sharedBlur1Fbo),t.setStencilTestEnabled(!1),t.setClearColor(0,0,0,0),t.clear(t.gl.COLOR_BUFFER_BIT),this._blitRenderer.render(t,r.colorTexture,i.NEAREST,1),t.setStencilTestEnabled(!1),t.setBlendingEnabled(!1),t.setColorMask(!1,!1,!1,!0),t.bindFramebuffer(this._hlSurfaces.sharedBlur2Fbo),t.setClearColor(0,0,0,0),t.clear(t.gl.COLOR_BUFFER_BIT),this._hlRenderer.preBlur(t,this._hlSurfaces.sharedBlur1Tex),t.bindFramebuffer(this._hlSurfaces.sharedBlur1Fbo),t.setClearColor(0,0,0,0),t.clear(t.gl.COLOR_BUFFER_BIT),this._hlRenderer.finalBlur(t,this._hlSurfaces.sharedBlur2Tex),t.bindFramebuffer(this._boundFBO),t.setBlendingEnabled(!0),t.setColorMask(!0,!0,!0,!0),t.setViewport(0,0,this._width,this._height),this._hlRenderer.renderHighlight(t,this._hlSurfaces.sharedBlur1Tex,s),this._boundFBO=null}}export{l as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t,isNone as e}from\"../../../../../core/maybe.js\";import{HITTEST_RADIUS as i,ATTRIBUTE_DATA_GPGPU as s}from\"../definitions.js\";import{Effect as r}from\"./Effect.js\";import{PixelFormat as o,PixelType as n}from\"../../../../webgl/enums.js\";class c extends r{constructor(){super(...arguments),this.name=this.constructor.name,this.defines=[\"hittest\"]}dispose(){t(this._fbo)&&this._fbo.dispose()}createOptions({pixelRatio:t},e,s=i){if(!e.length)return null;const r=e.shift(),o=r.x,n=r.y;return this._outstanding=r,{type:\"hittest\",distance:s*t,position:[o,n]}}bind(t){const{context:i,attributeView:r}=t;if(!r.size)return;const o=r.getBlock(s);if(e(o))return;const n=o.getFBO(i);i.setViewport(0,0,r.size,r.size),i.bindFramebuffer(n),i.setColorMask(!0,!0,!0,!0),i.setClearColor(0,0,0,0),i.clear(i.gl.COLOR_BUFFER_BIT|i.gl.DEPTH_BUFFER_BIT)}unbind(t){}draw(t){if(e(this._outstanding))return;const i=this._outstanding;this._outstanding=null,this._resolve(t,i.resolvers)}async _resolve(t,i){const{context:r,attributeView:c}=t,a=c.getBlock(s);if(e(a))return void i.forEach((t=>t.resolve([])));const d=a.getFBO(r),h=new Uint8Array(d.width*d.height*4);try{await d.readPixelsAsync(0,0,d.width,d.height,o.RGBA,n.UNSIGNED_BYTE,h)}catch(u){return void i.forEach((t=>t.resolve([])))}const l=[];for(let e=0;e<h.length;e+=4){const t=h[e],i=h[e+3];t&&l.push({id:e/4,directHits:i})}l.sort(((t,e)=>e.directHits===t.directHits?e.id-t.id:e.directHits-t.directHits));const f=l.map((t=>t.id));i.forEach((t=>t.resolve(f)))}}export{c as HittestEffect};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as t}from\"../../../../../core/maybe.js\";import{HITTEST_RADIUS as e}from\"../definitions.js\";import{Effect as s}from\"./Effect.js\";import{PixelFormat as o,PixelType as r}from\"../../../../webgl/enums.js\";class i extends s{constructor(){super(...arguments),this.name=this.constructor.name,this.defines=[\"id\"],this._lastSize=0,this._boundFBO=null}dispose(){t(this._fbo)&&this._fbo.dispose()}bind({context:t,painter:e}){const{width:s,height:o}=t.getViewport();this._boundFBO=t.getBoundFramebufferObject();const r=e.getFbos(s,o).effect0;t.bindFramebuffer(r),t.setColorMask(!0,!0,!0,!0),t.setClearColor(0,0,0,0),t.clear(t.gl.COLOR_BUFFER_BIT)}unbind({context:t}){t.bindFramebuffer(this._boundFBO),this._boundFBO=null}draw(t,s,o=2*e){this._resolve(t,s,o)}async _resolve({context:t,state:e,pixelRatio:s},i,n){const f=t.getBoundFramebufferObject(),a=e.size[1]*s,h=Math.round(n*s),u=h/2,b=h/2;this._ensureBuffer(h),i.forEach((async(t,e)=>{const n=new Map,c=Math.floor(e.x*s-h/2),l=Math.floor(a-e.y*s-h/2);await f.readPixelsAsync(c,l,h,h,o.RGBA,r.UNSIGNED_BYTE,this._buf);for(let s=0;s<this._buf32.length;s++){const t=this._buf32[s];if(4294967295!==t&&0!==t){const e=s%h,o=h-Math.floor(s/h),r=(u-e)*(u-e)+(b-o)*(b-o),i=n.has(t)?n.get(t):4294967295;n.set(t,Math.min(r,i))}}const _=Array.from(n).sort(((t,e)=>t[1]-e[1])).map((t=>t[0]));t.resolve(_),i.delete(e)}))}_ensureBuffer(t){this._lastSize!==t&&(this._lastSize=t,this._buf=new Uint8Array(4*t*t),this._buf32=new Uint32Array(this._buf.buffer))}}export{i as HittestEffectVTL};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{disposeMaybe as t}from\"../../../../../../core/maybe.js\";import e from\"../../VertexStream.js\";import{BlendFactor as i,TargetType as s,DepthStencilTargetType as r,TextureType as o,PixelFormat as n,PixelType as a,TextureWrapMode as h,TextureSamplingMode as l}from\"../../../../../webgl/enums.js\";import{FramebufferObject as u}from\"../../../../../webgl/FramebufferObject.js\";const p=5,m=[1,0],_=[0,1],c=[1,.8,.6,.4,.2],d=[1,1,1,1,1,1,1,1,1,1,1,1,1,1,1];class T{constructor(){this._intensityFBO=null,this._compositeFBO=null,this._mipsFBOs=new Array(p),this._nMips=p,this._kernelSizeArray=[3,5,7,9,11],this._size=[0,0],this._programDesc={luminosityHighPass:{vsPath:\"post-processing/pp\",fsPath:\"post-processing/bloom/luminosityHighPass\",attributes:new Map([[\"a_position\",0]])},gaussianBlur:{vsPath:\"post-processing/pp\",fsPath:\"post-processing/bloom/gaussianBlur\",attributes:new Map([[\"a_position\",0]])},composite:{vsPath:\"post-processing/pp\",fsPath:\"post-processing/bloom/composite\",attributes:new Map([[\"a_position\",0]])},blit:{vsPath:\"post-processing/pp\",fsPath:\"post-processing/blit\",attributes:new Map([[\"a_position\",0]])}}}dispose(){if(this._quad=t(this._quad),this._intensityFBO=t(this._intensityFBO),this._compositeFBO=t(this._compositeFBO),this._mipsFBOs){for(let t=0;t<this._nMips;t++)this._mipsFBOs[t]&&(this._mipsFBOs[t].horizontal.dispose(),this._mipsFBOs[t].vertical.dispose());this._mipsFBOs=null}}draw(t,s,r){const{width:o,height:n}=s,{context:a,painter:h}=t,{materialManager:l}=h,u=a.gl,T=this._programDesc,{strength:f,radius:g,threshold:B}=r;this._quad||(this._quad=new e(a,[-1,-1,1,-1,-1,1,1,1])),this._createOrResizeResources(t,o,n),a.setStencilTestEnabled(!1),a.setBlendingEnabled(!0),a.setBlendFunction(i.ONE,i.ONE_MINUS_SRC_ALPHA),a.setStencilWriteMask(0);const O=this._quad;O.bind(),a.bindFramebuffer(this._intensityFBO);const F=l.getProgram(T.luminosityHighPass);a.useProgram(F),a.bindTexture(s.colorTexture,0),F.setUniform1i(\"u_texture\",0),F.setUniform3fv(\"u_defaultColor\",[0,0,0]),F.setUniform1f(\"u_defaultOpacity\",0),F.setUniform1f(\"u_luminosityThreshold\",B),F.setUniform1f(\"u_smoothWidth\",.01);const b=[Math.round(o/2),Math.round(n/2)];a.setViewport(0,0,b[0],b[1]),a.setClearColor(0,0,0,0),a.clear(u.COLOR_BUFFER_BIT),O.draw(),a.setBlendingEnabled(!1);let E=this._intensityFBO.colorTexture;for(let e=0;e<this._nMips;e++){const t=l.getProgram(T.gaussianBlur,[{name:\"radius\",value:this._kernelSizeArray[e]}]);a.useProgram(t),a.bindTexture(E,e+1),t.setUniform1i(\"u_colorTexture\",e+1),t.setUniform2fv(\"u_texSize\",b),t.setUniform2fv(\"u_direction\",m),a.setViewport(0,0,b[0],b[1]);const i=this._mipsFBOs[e];a.bindFramebuffer(i.horizontal),O.draw(),E=i.horizontal.colorTexture,a.bindFramebuffer(i.vertical),a.bindTexture(E,e+1),t.setUniform2fv(\"u_direction\",_),O.draw(),E=i.vertical.colorTexture,b[0]=Math.round(b[0]/2),b[1]=Math.round(b[1]/2)}a.setViewport(0,0,o,n);const x=l.getProgram(T.composite,[{name:\"nummips\",value:p}]);a.bindFramebuffer(this._compositeFBO),a.useProgram(x),x.setUniform1f(\"u_bloomStrength\",f),x.setUniform1f(\"u_bloomRadius\",g),x.setUniform1fv(\"u_bloomFactors\",c),x.setUniform3fv(\"u_bloomTintColors\",d),a.bindTexture(this._mipsFBOs[0].vertical.colorTexture,1),x.setUniform1i(\"u_blurTexture1\",1),a.bindTexture(this._mipsFBOs[1].vertical.colorTexture,2),x.setUniform1i(\"u_blurTexture2\",2),a.bindTexture(this._mipsFBOs[2].vertical.colorTexture,3),x.setUniform1i(\"u_blurTexture3\",3),a.bindTexture(this._mipsFBOs[3].vertical.colorTexture,4),x.setUniform1i(\"u_blurTexture4\",4),a.bindTexture(this._mipsFBOs[4].vertical.colorTexture,5),x.setUniform1i(\"u_blurTexture5\",5),O.draw(),a.bindFramebuffer(s),a.setBlendingEnabled(!0);const w=l.getProgram(T.blit);a.useProgram(w),a.bindTexture(this._compositeFBO.colorTexture,6),w.setUniform1i(\"u_texture\",6),a.setBlendFunction(i.ONE,i.ONE),O.draw(),O.unbind(),a.setBlendFunction(i.ONE,i.ONE_MINUS_SRC_ALPHA),a.setStencilTestEnabled(!0)}_createOrResizeResources(t,e,i){const{context:p}=t;if(this._compositeFBO&&this._size[0]===e&&this._size[1]===i)return;this._size[0]=e,this._size[1]=i;const m=[Math.round(e/2),Math.round(i/2)];this._compositeFBO?this._compositeFBO.resize(e,i):this._compositeFBO=new u(p,{colorTarget:s.TEXTURE,depthStencilTarget:r.NONE,width:e,height:i}),this._intensityFBO?this._intensityFBO.resize(m[0],m[1]):this._intensityFBO=new u(p,{colorTarget:s.TEXTURE,depthStencilTarget:r.NONE,width:m[0],height:m[1]},{target:o.TEXTURE_2D,pixelFormat:n.RGBA,internalFormat:n.RGBA,dataType:a.UNSIGNED_BYTE,wrapMode:h.CLAMP_TO_EDGE,samplingMode:l.LINEAR,flipped:!1,width:m[0],height:m[1]});for(let _=0;_<this._nMips;_++)this._mipsFBOs[_]?(this._mipsFBOs[_].horizontal.resize(m[0],m[1]),this._mipsFBOs[_].vertical.resize(m[0],m[1])):this._mipsFBOs[_]={horizontal:new u(p,{colorTarget:s.TEXTURE,depthStencilTarget:r.NONE,width:m[0],height:m[1]},{target:o.TEXTURE_2D,pixelFormat:n.RGBA,internalFormat:n.RGBA,dataType:a.UNSIGNED_BYTE,wrapMode:h.CLAMP_TO_EDGE,samplingMode:l.LINEAR,flipped:!1,width:m[0],height:m[1]}),vertical:new u(p,{colorTarget:s.TEXTURE,depthStencilTarget:r.NONE,width:m[0],height:m[1]},{target:o.TEXTURE_2D,pixelFormat:n.RGBA,internalFormat:n.RGBA,dataType:a.UNSIGNED_BYTE,wrapMode:h.CLAMP_TO_EDGE,samplingMode:l.LINEAR,flipped:!1,width:m[0],height:m[1]})},m[0]=Math.round(m[0]/2),m[1]=Math.round(m[1]/2)}}export{T as Bloom};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../VertexStream.js\";import{BlendFactor as t,TargetType as r,DepthStencilTargetType as s,TextureType as i,PixelFormat as a,PixelType as n,TextureWrapMode as o,TextureSamplingMode as u}from\"../../../../../webgl/enums.js\";import{FramebufferObject as l}from\"../../../../../webgl/FramebufferObject.js\";const d=[1,0],_=[0,1];class b{constructor(){this._blurFBO=null,this._size=[0,0],this._programDesc={gaussianBlur:{vsPath:\"post-processing/pp\",fsPath:\"post-processing/blur/gaussianBlur\",attributes:new Map([[\"a_position\",0]])},radialBlur:{vsPath:\"post-processing/pp\",fsPath:\"post-processing/blur/radial-blur\",attributes:new Map([[\"a_position\",0]])},blit:{vsPath:\"post-processing/pp\",fsPath:\"post-processing/blit\",attributes:new Map([[\"a_position\",0]])}}}dispose(){this._blurFBO&&(this._blurFBO.dispose(),this._blurFBO=null)}draw(t,r,s){const{context:i}=t,{type:a,radius:n}=s;if(0===n)return;this._createOrResizeResources(t),this._quad||(this._quad=new e(i,[-1,-1,1,-1,-1,1,1,1]));const o=this._quad;o.bind(),\"blur\"===a?this._gaussianBlur(t,r,n):this._radialBlur(t,r),o.unbind()}_gaussianBlur(e,r,s){const{context:i,state:a,painter:n,pixelRatio:o}=e,{size:u}=a,{materialManager:l}=n,b=this._programDesc,p=this._quad,c=[Math.round(o*u[0]),Math.round(o*u[1])],h=this._blurFBO,g=l.getProgram(b.gaussianBlur,[{name:\"radius\",value:Math.ceil(s)}]);i.useProgram(g),i.setBlendingEnabled(!1),i.bindFramebuffer(h),i.bindTexture(r.colorTexture,4),g.setUniform1i(\"u_colorTexture\",4),g.setUniform2fv(\"u_texSize\",c),g.setUniform2fv(\"u_direction\",d),g.setUniform1f(\"u_sigma\",s),p.draw(),i.bindFramebuffer(r),i.setStencilWriteMask(0),i.setStencilTestEnabled(!1),i.setDepthWriteEnabled(!1),i.setDepthTestEnabled(!1),i.bindTexture(h?.colorTexture,5),g.setUniform1i(\"u_colorTexture\",5),g.setUniform2fv(\"u_direction\",_),p.draw(),i.setBlendingEnabled(!0),i.setBlendFunction(t.ONE,t.ONE_MINUS_SRC_ALPHA),i.setStencilTestEnabled(!0)}_radialBlur(e,r){const{context:s,painter:i}=e,{materialManager:a}=i,n=this._programDesc,o=this._quad,u=this._blurFBO;s.bindFramebuffer(u);const l=a.getProgram(n.radialBlur);s.useProgram(l),s.setBlendingEnabled(!1),s.bindTexture(r.colorTexture,4),l.setUniform1i(\"u_colorTexture\",4),o.draw(),s.bindFramebuffer(r),s.setStencilWriteMask(0),s.setStencilTestEnabled(!1),s.setDepthWriteEnabled(!1),s.setDepthTestEnabled(!1),s.setBlendingEnabled(!0);const d=a.getProgram(n.blit);s.useProgram(d),s.bindTexture(u?.colorTexture,5),d.setUniform1i(\"u_texture\",5),s.setBlendFunction(t.ONE,t.ONE_MINUS_SRC_ALPHA),o.draw()}_createOrResizeResources(e){const{context:t,state:d,pixelRatio:_}=e,{size:b}=d,p=Math.round(_*b[0]),c=Math.round(_*b[1]);this._blurFBO&&this._size[0]===p&&this._size[1]===c||(this._size[0]=p,this._size[1]=c,this._blurFBO?this._blurFBO.resize(p,c):this._blurFBO=new l(t,{colorTarget:r.TEXTURE,depthStencilTarget:s.NONE,width:p,height:c},{target:i.TEXTURE_2D,pixelFormat:a.RGBA,internalFormat:a.RGBA,dataType:n.UNSIGNED_BYTE,wrapMode:o.CLAMP_TO_EDGE,samplingMode:u.LINEAR,flipped:!1,width:p,height:c}))}}export{b as Blur};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{disposeMaybe as e}from\"../../../../../../core/maybe.js\";import t from\"../../VertexStream.js\";import{BlendFactor as r,TextureType as s,PixelFormat as i,PixelType as a,TextureWrapMode as o,TextureSamplingMode as n}from\"../../../../../webgl/enums.js\";import{Texture as l}from\"../../../../../webgl/Texture.js\";class _{constructor(){this._layerFBOTexture=null,this._size=[0,0],this._programDesc={vsPath:\"post-processing/pp\",fsPath:\"post-processing/filterEffect\",attributes:new Map([[\"a_position\",0]])}}dispose(){this._layerFBOTexture=e(this._layerFBOTexture)}draw(e,t,s){const{width:i,height:a}=t;this._createOrResizeResources(e,i,a);const{context:o,painter:n}=e,{materialManager:l}=n,_=this._programDesc,c=this._quad,u=s.colorMatrix;c.bind();const h=this._layerFBOTexture;o.bindFramebuffer(t),t.copyToTexture(0,0,i,a,0,0,h),o.setBlendingEnabled(!1),o.setStencilTestEnabled(!1);const p=l.getProgram(_);o.useProgram(p),o.bindTexture(h,2),p.setUniformMatrix4fv(\"u_coefficients\",u),p.setUniform1i(\"u_colorTexture\",2),c.draw(),o.setBlendingEnabled(!0),o.setBlendFunction(r.ONE,r.ONE_MINUS_SRC_ALPHA),o.setStencilTestEnabled(!0),c.unbind()}_createOrResizeResources(e,r,_){const{context:c}=e;this._layerFBOTexture&&this._size[0]===r&&this._size[1]===_||(this._size[0]=r,this._size[1]=_,this._layerFBOTexture?this._layerFBOTexture.resize(r,_):this._layerFBOTexture=new l(c,{target:s.TEXTURE_2D,pixelFormat:i.RGBA,internalFormat:i.RGBA,dataType:a.UNSIGNED_BYTE,wrapMode:o.CLAMP_TO_EDGE,samplingMode:n.LINEAR,flipped:!1,width:r,height:_}),this._quad||(this._quad=new t(c,[-1,-1,1,-1,-1,1,1,1])))}}export{_ as Colorize};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{disposeMaybe as e}from\"../../../../../../core/maybe.js\";import{pt2px as t}from\"../../../../../../core/screenUtils.js\";import r from\"../../VertexStream.js\";import{BlendFactor as i,TargetType as s,DepthStencilTargetType as o,TextureType as a,PixelFormat as l,PixelType as n,TextureWrapMode as u,TextureSamplingMode as h}from\"../../../../../webgl/enums.js\";import{FramebufferObject as p}from\"../../../../../webgl/FramebufferObject.js\";import{Texture as _}from\"../../../../../webgl/Texture.js\";const d=[1,0],c=[0,1];class m{constructor(){this._layerFBOTexture=null,this._horizontalBlurFBO=null,this._verticalBlurFBO=null,this._size=[0,0],this._quad=null,this._programDesc={blur:{vsPath:\"post-processing/pp\",fsPath:\"post-processing/blur/gaussianBlur\",attributes:new Map([[\"a_position\",0]])},composite:{vsPath:\"post-processing/pp\",fsPath:\"post-processing/drop-shadow/composite\",attributes:new Map([[\"a_position\",0]])},blit:{vsPath:\"post-processing/pp\",fsPath:\"post-processing/blit\",attributes:new Map([[\"a_position\",0]])}}}dispose(){this._layerFBOTexture=e(this._layerFBOTexture),this._horizontalBlurFBO=e(this._horizontalBlurFBO),this._verticalBlurFBO=e(this._verticalBlurFBO)}draw(e,s,o){const{context:a,state:l,painter:n}=e,{materialManager:u}=n,h=this._programDesc,p=s.width,_=s.height,m=[Math.round(p),Math.round(_)],{blurRadius:B,offsetX:T,offsetY:f,color:g}=o,E=[t(T),t(f)];this._createOrResizeResources(e,p,_,m);const b=this._horizontalBlurFBO,F=this._verticalBlurFBO;a.setStencilWriteMask(0),a.setStencilTestEnabled(!1),a.setDepthWriteEnabled(!1),a.setDepthTestEnabled(!1);const O=this._layerFBOTexture;s.copyToTexture(0,0,p,_,0,0,O),this._quad||(this._quad=new r(a,[-1,-1,1,-1,-1,1,1,1])),a.setViewport(0,0,m[0],m[1]);const x=this._quad;x.bind(),a.setBlendingEnabled(!1);const w=u.getProgram(h.blur,[{name:\"radius\",value:Math.ceil(B)}]);a.useProgram(w),a.bindFramebuffer(b),a.bindTexture(s.colorTexture,4),w.setUniform1i(\"u_colorTexture\",4),w.setUniform2fv(\"u_texSize\",m),w.setUniform2fv(\"u_direction\",d),w.setUniform1f(\"u_sigma\",B),x.draw(),a.bindFramebuffer(F),a.bindTexture(b?.colorTexture,5),w.setUniform1i(\"u_colorTexture\",5),w.setUniform2fv(\"u_direction\",c),x.draw(),a.bindFramebuffer(s),a.setViewport(0,0,p,_);const M=u.getProgram(h.composite);a.useProgram(M),a.bindTexture(F?.colorTexture,2),M.setUniform1i(\"u_blurTexture\",2),a.bindTexture(O,3),M.setUniform1i(\"u_layerFBOTexture\",3),M.setUniform4fv(\"u_shadowColor\",[g[3]*(g[0]/255),g[3]*(g[1]/255),g[3]*(g[2]/255),g[3]]),M.setUniformMatrix3fv(\"u_displayViewMat3\",l.displayMat3),M.setUniform2fv(\"u_shadowOffset\",E),x.draw(),a.setBlendingEnabled(!0),a.setStencilTestEnabled(!0),a.setBlendFunction(i.ONE,i.ONE_MINUS_SRC_ALPHA),x.unbind()}_createOrResizeResources(e,t,r,i){const{context:d}=e;this._horizontalBlurFBO&&this._size[0]===t&&this._size[1]===r||(this._size[0]=t,this._size[1]=r,this._horizontalBlurFBO?this._horizontalBlurFBO.resize(i[0],i[1]):this._horizontalBlurFBO=new p(d,{colorTarget:s.TEXTURE,depthStencilTarget:o.NONE,width:i[0],height:i[1]},{target:a.TEXTURE_2D,pixelFormat:l.RGBA,internalFormat:l.RGBA,dataType:n.UNSIGNED_BYTE,wrapMode:u.CLAMP_TO_EDGE,samplingMode:h.LINEAR,flipped:!1,width:i[0],height:i[1]}),this._verticalBlurFBO?this._verticalBlurFBO.resize(i[0],i[1]):this._verticalBlurFBO=new p(d,{colorTarget:s.TEXTURE,depthStencilTarget:o.NONE,width:i[0],height:i[1]},{target:a.TEXTURE_2D,pixelFormat:l.RGBA,internalFormat:l.RGBA,dataType:n.UNSIGNED_BYTE,wrapMode:u.CLAMP_TO_EDGE,samplingMode:h.LINEAR,flipped:!1,width:i[0],height:i[1]}),this._layerFBOTexture?this._layerFBOTexture.resize(t,r):this._layerFBOTexture=new _(d,{target:a.TEXTURE_2D,pixelFormat:l.RGBA,internalFormat:l.RGBA,dataType:n.UNSIGNED_BYTE,wrapMode:u.CLAMP_TO_EDGE,samplingMode:h.LINEAR,flipped:!1,width:t,height:r}))}}export{m as DropShadow};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{disposeMaybe as e}from\"../../../../../../core/maybe.js\";import{TextureSamplingMode as t,TextureType as r,PixelFormat as s,PixelType as i,TextureWrapMode as a}from\"../../../../../webgl/enums.js\";import{Texture as l}from\"../../../../../webgl/Texture.js\";class o{constructor(){this._size=[0,0],this._layerFBOTexture=null}dispose(){this._layerFBOTexture=e(this._layerFBOTexture)}draw(e,r,s){const{width:i,height:a}=r;this._createOrResizeResources(e,i,a);const{context:l,painter:o}=e,{amount:T}=s,h=l.gl,n=this._layerFBOTexture;l.bind<PERSON><PERSON><PERSON>er(r),r.copyToTexture(0,0,i,a,0,0,n),l.setBlendingEnabled(!0),l.setStencilTestEnabled(!1),l.setDepthTestEnabled(!1),l.setClearColor(0,0,0,0),l.clear(h.COLOR_BUFFER_BIT),o.blitTexture(l,n,t.NEAREST,T)}_createOrResizeResources(e,o,T){const{context:h}=e;this._layerFBOTexture&&this._size[0]===o&&this._size[1]===T||(this._size[0]=o,this._size[1]=T,this._layerFBOTexture?this._layerFBOTexture.resize(o,T):this._layerFBOTexture=new l(h,{target:r.TEXTURE_2D,pixelFormat:s.RGBA,internalFormat:s.RGBA,dataType:i.UNSIGNED_BYTE,wrapMode:a.CLAMP_TO_EDGE,samplingMode:t.NEAREST,flipped:!1,width:o,height:T}))}}export{o as Opacity};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{<PERSON> as o}from\"./Bloom.js\";import{Blur as e}from\"./Blur.js\";import{Colorize as t}from\"./Colorize.js\";import{DropShadow as s}from\"./DropShadow.js\";import{Opacity as r}from\"./Opacity.js\";function c(o){switch(o){case\"bloom\":case\"blur\":case\"opacity\":case\"drop-shadow\":return o;default:return\"colorize\"}}const f={colorize:()=>new t,blur:()=>new e,bloom:()=>new o,opacity:()=>new r,\"drop-shadow\":()=>new s};class i{constructor(){this._effectMap=new Map}dispose(){this._effectMap.forEach((o=>o.dispose())),this._effectMap.clear()}getPostProcessingEffects(o){if(!o||0===o.length)return[];const e=[];for(const t of o){const o=c(t.type);let s=this._effectMap.get(o);s||(s=f[o](),this._effectMap.set(o,s)),e.push({postProcessingEffect:s,effect:t})}return e}}export{i as EffectManager};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isArrayLike as e}from\"../../../../../core/arrayUtils.js\";import{isNone as t,isSome as r}from\"../../../../../core/maybe.js\";import{WGLDrawPhase as s}from\"../enums.js\";class a{constructor(e,t){this.brushes=e,this.name=t.name,this.drawPhase=t.drawPhase||s.MAP,this._targetFn=t.target,this.effects=t.effects||[],this.enableDefaultDraw=t.enableDefaultDraw??(()=>!0)}render(e){const{context:t,profiler:r}=e,s=this._targetFn(),a=this.drawPhase&e.drawPhase;if(r.recordPassStart(this.name),a){this.enableDefaultDraw()&&this._doRender(e,s),r.recordPassEnd();for(const r of this.effects){if(!r.enable())continue;const a=r.apply,n=r.args&&r.args(),i=t.getViewport(),o=t.getBoundFramebufferObject(),f=e.passOptions;this._bindEffect(e,a,n),this._doRender(e,s,a.defines),this._drawAndUnbindEffect(e,a,i,o,f,n)}}}_doRender(e,s,a){if(t(s))return;const{profiler:n,context:i}=e;for(const t of this.brushes){if(n.recordBrushStart(t.name),r(t.brushEffect)){const r=i.getViewport(),n=i.getBoundFramebufferObject(),o=e.passOptions;this._bindEffect(e,t.brushEffect),this._drawWithBrush(t,e,s,a),this._drawAndUnbindEffect(e,t.brushEffect,r,n,o)}else this._drawWithBrush(t,e,s,a);n.recordBrushEnd()}}_drawWithBrush(t,r,s,a){e(s)?(t.prepareState(r,a),t.drawMany(r,s,a)):s.visible&&(t.prepareState(r,a),t.draw(r,s,a))}_bindEffect(e,t,r){const{profiler:s}=e;s.recordPassStart(this.name+\".\"+t.name),t.bind(e,r);const a=t.createOptions(e,r);e.passOptions=a}_drawAndUnbindEffect(e,t,r,s,a,n){const{profiler:i,context:o}=e;e.passOptions=a,i.recordBrushStart(t.name),t.draw(e,n),t.unbind(e,n),o.bindFramebuffer(s);const{x:f,y:d,width:h,height:c}=r;o.setViewport(f,d,h,c),i.recordBrushEnd(),i.recordPassEnd()}}export{a as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{isSome as e,isNone as t,disposeMaybe as s}from\"../../../../core/maybe.js\";import{brushes as r}from\"../brushes.js\";import i from\"../vectorTiles/shaders/VTLMaterialManager.js\";import{BitBlitRenderer as n}from\"./BitBlitRenderer.js\";import{WGLDrawPhase as o,WGLGeometryType as a,WGLSymbologyType as l}from\"./enums.js\";import h from\"./MaterialManager.js\";import f from\"./TextureManager.js\";import{TextureUploadManager as c}from\"./TextureUploadManager.js\";import{WorldExtentClipRenderer as _}from\"./WorldExtentClipRenderer.js\";import{AnimationEffect as d}from\"./effects/AnimationEffect.js\";import{BlendEffect as u}from\"./effects/BlendEffect.js\";import{FeatureEffect as E}from\"./effects/FeatureEffect.js\";import p from\"./effects/HighlightEffect.js\";import{HittestEffect as b}from\"./effects/HittestEffect.js\";import{HittestEffectVTL as g}from\"./effects/HittestEffectVTL.js\";import{EffectManager as T}from\"./effects/post-processing/EffectManager.js\";import m from\"./painter/RenderPass.js\";import{ContextType as C}from\"../../../webgl/context-util.js\";import{TextureType as M,PixelFormat as F,PixelType as R,TextureSamplingMode as w,TextureWrapMode as B,TargetType as S,DepthStencilTargetType as O,RenderbufferFormat as x,BlendFactor as P,CompareFunction as L}from\"../../../webgl/enums.js\";import{FramebufferObject as N}from\"../../../webgl/FramebufferObject.js\";import{Renderbuffer as D}from\"../../../webgl/Renderbuffer.js\";function I(e,t){switch(e){case a.LINE:return r.line;case a.TEXT:return r.text;case a.LABEL:return r.label;case a.FILL:return t===l.DOT_DENSITY?r.dotDensity:r.fill;case a.MARKER:switch(t){case l.HEATMAP:return r.heatmap;case l.PIE_CHART:return r.pieChart;default:return r.marker}}}class j{constructor(e,t,s){this.context=e,this._blitRenderer=new n,this._worldExtentClipRenderer=new _,this._isClippedToWorldExtent=!1,this._brushCache=new Map,this._lastWidth=null,this._lastHeight=null,this._prevFBO=null,this._vtlMaterialManager=new i,this._blendEffect=new u,this._stencilBuf=null,this._fbos=null,this._fboPool=[],this._renderTarget=null,this.effects={highlight:new p,hittest:new b,hittestVTL:new g,integrate:new d,insideEffect:new E(\"inside\"),outsideEffect:new E(\"outside\")},this.materialManager=new h(e),this.textureManager=new f(t,s,e.type===C.WEBGL2),this.textureUploadManager=new c(e,t),this._effectsManager=new T}get vectorTilesMaterialManager(){return this._vtlMaterialManager}getRenderTarget(){return this._renderTarget}setRenderTarget(e){this._renderTarget=e}getFbos(e,t){if(e!==this._lastWidth||t!==this._lastHeight){if(this._lastWidth=e,this._lastHeight=t,this._fbos){for(const s in this._fbos)this._fbos[s].resize(e,t);return this._fbos}const s={target:M.TEXTURE_2D,pixelFormat:F.RGBA,dataType:R.UNSIGNED_BYTE,samplingMode:w.NEAREST,wrapMode:B.CLAMP_TO_EDGE,width:e,height:t},r={colorTarget:S.TEXTURE,depthStencilTarget:O.DEPTH_STENCIL_RENDER_BUFFER},i=new D(this.context,{width:e,height:t,internalFormat:x.DEPTH_STENCIL});this._stencilBuf=i,this._fbos={output:new N(this.context,r,s,i),blend:new N(this.context,r,s,i),effect0:new N(this.context,r,s,i)}}return this._fbos}acquireFbo(e,t){let s;s=this._fboPool.length>0?this._fboPool.pop():new N(this.context,{colorTarget:S.TEXTURE,depthStencilTarget:O.DEPTH_STENCIL_RENDER_BUFFER},{target:M.TEXTURE_2D,pixelFormat:F.RGBA,dataType:R.UNSIGNED_BYTE,samplingMode:w.NEAREST,wrapMode:B.CLAMP_TO_EDGE,width:e,height:t},this._stencilBuf);const r=s.descriptor;return r.width===e&&r.height===t||s.resize(e,t),s}releaseFbo(e){this._fboPool.push(e)}getSharedStencilBuffer(){return this._stencilBuf}beforeRenderLayers(t,s=null){const{width:r,height:i}=t.getViewport();this._prevFBO=t.getBoundFramebufferObject();const n=this.getFbos(r,i);if(t.bindFramebuffer(n?.output),t.setColorMask(!0,!0,!0,!0),e(s)){const{r:e,g:r,b:i,a:n}=s.color;t.setClearColor(n*e/255,n*r/255,n*i/255,n)}else t.setClearColor(0,0,0,0);t.setDepthWriteEnabled(!0),t.setClearDepth(1),t.clear(t.gl.COLOR_BUFFER_BIT|t.gl.DEPTH_BUFFER_BIT),t.setDepthWriteEnabled(!1)}beforeRenderLayer(e,t,s){const{context:r,blendMode:i,effects:n,requireFBO:o,drawPhase:a}=e;if(o||A(a,i,n,s))r.bindFramebuffer(this._fbos?.blend),r.setColorMask(!0,!0,!0,!0),r.setClearColor(0,0,0,0),r.setDepthWriteEnabled(!0),r.setClearDepth(1),r.clear(r.gl.COLOR_BUFFER_BIT|r.gl.DEPTH_BUFFER_BIT),r.setDepthWriteEnabled(!1);else{const e=this._getOutputFBO();r.bindFramebuffer(e)}r.setDepthWriteEnabled(!1),r.setDepthTestEnabled(!1),r.setStencilTestEnabled(!0),r.setClearStencil(t),r.setStencilWriteMask(255),r.clear(r.gl.STENCIL_BUFFER_BIT)}compositeLayer(s,r){const{context:i,blendMode:n,effects:a,requireFBO:l,drawPhase:h}=s;if(l||A(h,n,a,r)){e(a)&&a.length>0&&h===o.MAP&&this._applyEffects(s,a);const l=this._getOutputFBO();i.bindFramebuffer(l),i.setStencilTestEnabled(!1),i.setStencilWriteMask(0),i.setBlendingEnabled(!0),i.setBlendFunctionSeparate(P.ONE,P.ONE_MINUS_SRC_ALPHA,P.ONE,P.ONE_MINUS_SRC_ALPHA),i.setColorMask(!0,!0,!0,!0);const f=t(n)||h===o.HIGHLIGHT?\"normal\":n,c=this._fbos;c?.blend.colorTexture&&this._blendEffect.draw(s,c.blend.colorTexture,w.NEAREST,f,r)}}renderLayers(e){e.bindFramebuffer(this._prevFBO);const t=this._getOutputFBO();t&&(e.setDepthTestEnabled(!1),e.setStencilWriteMask(0),this._isClippedToWorldExtent?(e.setStencilTestEnabled(!0),e.setStencilFunction(L.EQUAL,1,255)):e.setStencilTestEnabled(!1),this.blitTexture(e,t.colorTexture,w.NEAREST))}prepareDisplay(t,s,r){const{context:i}=t;if(i.bindFramebuffer(this._prevFBO),i.setColorMask(!0,!0,!0,!0),e(s)){const{r:e,g:t,b:r,a:n}=s.color;i.setClearColor(n*e/255,n*t/255,n*r/255,n)}else i.setClearColor(0,0,0,0);i.setStencilWriteMask(255),i.setClearStencil(0),i.clear(i.gl.COLOR_BUFFER_BIT|i.gl.STENCIL_BUFFER_BIT),this._isClippedToWorldExtent=this._worldExtentClipRenderer.render(t,r)}dispose(){if(this.materialManager.dispose(),this.textureManager.dispose(),this.textureUploadManager.destroy(),this._blitRenderer=s(this._blitRenderer),this._worldExtentClipRenderer=s(this._worldExtentClipRenderer),this._brushCache&&(this._brushCache.forEach((e=>e.dispose())),this._brushCache.clear(),this._brushCache=null),this._fbos)for(const e in this._fbos)this._fbos[e]&&this._fbos[e].dispose();for(const e of this._fboPool)e.dispose();if(this._fboPool.length=0,this.effects)for(const e in this.effects)this.effects[e]&&this.effects[e].dispose();this._effectsManager.dispose(),this._vtlMaterialManager=s(this._vtlMaterialManager),this._prevFBO=null}getBrush(e,t){const s=I(e,t);let r=this._brushCache.get(s);return void 0===r&&(r=new s,this._brushCache.set(s,r)),r}renderObject(e,t,s,i){const n=r[s];if(!n)return;let o=this._brushCache.get(n);void 0===o&&(o=new n,this._brushCache.set(n,o)),o.prepareState(e,i),o.draw(e,t,i)}renderObjects(e,t,s,i){const n=r[s];if(!n)return;let o=this._brushCache.get(n);void 0===o&&(o=new n,this._brushCache.set(n,o)),o.drawMany(e,t,i)}registerRenderPass(e){const t=e.brushes.map((e=>(this._brushCache.has(e)||this._brushCache.set(e,new e),this._brushCache.get(e))));return new m(t,e)}blitTexture(e,t,s,r=1){e.setBlendingEnabled(!0),e.setBlendFunctionSeparate(P.ONE,P.ONE_MINUS_SRC_ALPHA,P.ONE,P.ONE_MINUS_SRC_ALPHA),e.setColorMask(!0,!0,!0,!0),this._blitRenderer.render(e,t,s,r)}getPostProcessingEffects(e){return this._effectsManager.getPostProcessingEffects(e)}_getOutputFBO(){return null!=this._renderTarget?this._renderTarget:this._fbos?.output??null}_applyEffects(e,t){const s=this._fbos?.blend;if(!s)return;const{context:r}=e,i=this._effectsManager.getPostProcessingEffects(t);for(const{postProcessingEffect:n,effect:o}of i)r.bindFramebuffer(s),n.draw(e,s,o)}}function A(t,s,r,i){return t!==o.HIGHLIGHT&&(1!==i||e(s)&&\"normal\"!==s||e(r)&&r.length>0)}export{j as default};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../core/Error.js\";import{on as t}from\"../../../core/events.js\";import has from\"../../../core/has.js\";import{unwrap as r,removeMaybe as s}from\"../../../core/maybe.js\";import{addFrameTask as i}from\"../../../core/scheduling.js\";import{c as n}from\"../../../chunks/mat3f32.js\";import a from\"../../../symbols/cim/CIMResourceManager.js\";import{Container as o}from\"./Container.js\";import{BufferPool as h}from\"./webgl/BufferPool.js\";import{WGLDrawPhase as d}from\"./webgl/enums.js\";import l from\"./webgl/Painter.js\";import{Profiler as c}from\"./webgl/Profiler.js\";import{Timeline as u}from\"../support/Timeline.js\";import{resampleHermite as p}from\"../../support/screenshotUtils.js\";import{createContextOrErrorHTML as m}from\"../../webgl/context-util.js\";import{TargetType as f,DepthStencilTargetType as _,PixelFormat as g,PixelType as b}from\"../../webgl/enums.js\";import{FramebufferObject as w}from\"../../webgl/FramebufferObject.js\";import{RenderingContext as R}from\"../../webgl/RenderingContext.js\";import{createEmptyImageData as y}from\"../../../core/imageUtils.js\";const x=2e3;class P extends o{constructor(s,n){super(),this._trash=new Set,this._renderRemainingTime=0,this._lastFrameRenderTime=0,this.renderRequested=!1,this.stage=this,this._stationary=!0;const{canvas:o=document.createElement(\"canvas\"),alpha:d=!0,stencil:p=!0,contextOptions:f={}}=n;this._canvas=o;const _=m(\"2d\",o,{alpha:d,antialias:!1,depth:!0,stencil:p});this.context=new R(r(_)??null,f),this.resourceManager=new a,this.painter=new l(this.context,this,this.resourceManager),has(\"esri-2d-profiler\")&&(this._debugOutput=document.createElement(\"div\"),this._debugOutput.setAttribute(\"style\",\"margin: 24px 64px; position: absolute; color: red;\"),s.appendChild(this._debugOutput));const g=()=>this._highlightGradient;this._renderParameters={drawPhase:0,state:this.state,pixelRatio:window.devicePixelRatio,stationary:!1,globalOpacity:1,blendMode:null,deltaTime:-1,time:0,inFadeTransition:!1,effects:null,context:this.context,painter:this.painter,timeline:n.timeline||new u,renderingOptions:n.renderingOptions,requestRender:()=>this.requestRender(),allowDelayedRender:!1,requireFBO:!1,profiler:new c(this.context,this._debugOutput),dataUploadCounter:0,get highlightGradient(){return g()}},this._taskHandle=i({render:e=>this.renderFrame(e)}),this._taskHandle.pause(),this._lostWebGLContextHandle=t(o,\"webglcontextlost\",(()=>{this.emit(\"webgl-error\",{error:new e(\"webgl-context-lost\")})})),this._bufferPool=new h,o.setAttribute(\"style\",\"width: 100%; height:100%; display:block;\"),s.appendChild(o)}destroy(){this.removeAllChildren(),this._emptyTrash(),this._taskHandle=s(this._taskHandle),this._lostWebGLContextHandle=s(this._lostWebGLContextHandle),this._canvas.parentNode?.removeChild(this._canvas),this._debugOutput?.parentNode?.removeChild(this._debugOutput),this._bufferPool.destroy(),this.resourceManager.destroy(),this.painter.dispose(),this.context.dispose(),this._canvas=null}get background(){return this._background}set background(e){this._background=e,this.requestRender()}get bufferPool(){return this._bufferPool}get renderingOptions(){return this._renderingOptions}set renderingOptions(e){this._renderingOptions=e,this.requestRender()}get state(){return this._state}set state(e){this._state=e,this.requestRender()}get stationary(){return this._stationary}set stationary(e){this._stationary!==e&&(this._stationary=e,this.requestRender())}trashDisplayObject(e){this._trash.add(e),this.requestRender()}untrashDisplayObject(e){return this._trash.delete(e)}requestRender(){this._renderRemainingTime=x,this.renderRequested||(this.renderRequested=!0,this.emit(\"will-render\"),this._taskHandle.resume())}renderFrame(e){const t=this._lastFrameRenderTime?e.time-this._lastFrameRenderTime:0;this._renderRemainingTime-=t,this._renderRemainingTime<=0&&this._taskHandle.pause(),this._lastFrameRenderTime=e.time,this.renderRequested=!1,this._renderParameters.state=this._state,this._renderParameters.stationary=this.stationary,this._renderParameters.pixelRatio=window.devicePixelRatio,this._renderParameters.globalOpacity=1,this._renderParameters.time=e.time,this._renderParameters.deltaTime=e.deltaTime,this._renderParameters.effects=null,this.processRender(this._renderParameters),this._emptyTrash(),this.emit(\"post-render\")}_createTransforms(){return{dvs:n()}}renderChildren(e){for(const t of this.children)t.beforeRender(e);this._renderChildren(this.children,e);for(const t of this.children)t.afterRender(e)}_renderChildren(e,t){const r=this.context;this.painter.textureUploadManager.upload(),r.resetInfo(),t.profiler.recordStart(\"drawLayers\"),t.dataUploadCounter=0,t.drawPhase=d.MAP,this.painter.beforeRenderLayers(r,this.background);for(const s of e)s.processRender(t);this.painter.prepareDisplay(t,this.background,this.state.padding),this.painter.renderLayers(r),t.drawPhase=d.HIGHLIGHT,this.painter.beforeRenderLayers(r);for(const s of e)s.processRender(t);this.painter.renderLayers(r);if(this._isLabelDrawPhaseRequired(e)){t.drawPhase=d.LABEL,this.painter.beforeRenderLayers(r);for(const r of e)r.processRender(t);this.painter.renderLayers(r)}if(has(\"esri-tiles-debug\")){t.drawPhase=d.DEBUG,this.painter.beforeRenderLayers(r);for(const r of e)r.processRender(t);this.painter.renderLayers(r)}t.profiler.recordEnd(\"drawLayers\"),r.logInfo()}doRender(e){const t=this.context,{state:r,pixelRatio:s}=e;this._resizeCanvas(e),t.setViewport(0,0,s*r.size[0],s*r.size[1]),t.setDepthWriteEnabled(!0),t.setStencilWriteMask(255),super.doRender(e)}async takeScreenshot(e){const{framebufferWidth:t,framebufferHeight:r}={framebufferWidth:Math.round(this.state.size[0]*e.resolutionScale),framebufferHeight:Math.round(this.state.size[1]*e.resolutionScale)},s=e.resolutionScale,i=this.context,n=this._state.clone();if(null!=e.rotation){const t=n.viewpoint;n.viewpoint.rotation=e.rotation,n.viewpoint=t}const a={...this._renderParameters,drawPhase:null,globalOpacity:1,stationary:!0,state:n,pixelRatio:s,time:performance.now(),deltaTime:0,blendMode:null,effects:null,inFadeTransition:!1},o=new w(i,{colorTarget:f.TEXTURE,depthStencilTarget:_.DEPTH_STENCIL_RENDER_BUFFER,width:t,height:r}),h=i.getBoundFramebufferObject(),d=i.getViewport();i.bindFramebuffer(o),i.setViewport(0,0,t,r),this._renderChildren(e.children,a);const l=this._readbackScreenshot(o,{...e.cropArea,y:r-(e.cropArea.y+e.cropArea.height)});i.bindFramebuffer(h),i.setViewport(d.x,d.y,d.width,d.height),this.requestRender();const c=await l;let u;return 1===e.outputScale?u=c:(u=new ImageData(Math.round(c.width*e.outputScale),Math.round(c.height*e.outputScale)),p(c,u,!0)),u}async _readbackScreenshot(e,t){const r=y(t.width,t.height,document.createElement(\"canvas\"));return await e.readPixelsAsync(t.x,t.y,t.width,t.height,g.RGBA,b.UNSIGNED_BYTE,new Uint8Array(r.data.buffer)),r}_resizeCanvas(e){const t=this._canvas,r=t.style,{state:{size:s},pixelRatio:i}=e,n=s[0],a=s[1],o=Math.round(n*i),h=Math.round(a*i);t.width===o&&t.height===h||(t.width=o,t.height=h),r.width=n+\"px\",r.height=a+\"px\"}_emptyTrash(){for(;this._trash.size>0;){const e=Array.from(this._trash);this._trash.clear();for(const t of e)t.processDetach()}}_isLabelDrawPhaseRequired(e){let t=!1;for(const r of e){if(!(r instanceof o)){t=t||!1;break}if(r.hasLabels)return!0;t=t||this._isLabelDrawPhaseRequired(r.children)}return t}}export{x as EXTRA_RENDER_TIME,P as Stage};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{throwIfAborted as a}from\"../../core/promiseUtils.js\";import{requestImage as t}from\"../../support/requestImageUtils.js\";async function s(s){const r=import(\"./mask-svg.js\"),i=import(\"./overlay-svg.js\"),o=t((await r).default,{signal:s}),e=t((await i).default,{signal:s}),m={mask:await o,overlay:await e};return a(s),m}export{s as loadMagnifierResources};\n", "/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport e from\"../../../request.js\";import{createTask as r}from\"../../../core/asyncUtils.js\";import t from\"../../../core/Handles.js\";import{clamp as s}from\"../../../core/mathUtils.js\";import{destroyMaybe as i,abortMaybe as a,isNone as o,isSome as n,disposeMaybe as l}from\"../../../core/maybe.js\";import{watch as h,initial as m}from\"../../../core/reactiveUtils.js\";import{isSVG as u}from\"../../../core/urlUtils.js\";import{c}from\"../../../chunks/mat3f32.js\";import{DisplayObject as d}from\"../engine/DisplayObject.js\";import{Pos2us as _}from\"../engine/webgl/DefaultVertexAttributeLayouts.js\";import{WGLDrawPhase as p}from\"../engine/webgl/enums.js\";import{createMagnifierProgram as f,magnifierProgramTemplate as g}from\"../engine/webgl/shaders/MagnifierPrograms.js\";import{loadMagnifierResources as T}from\"../../magnifier/resources.js\";import{BufferObject as x}from\"../../webgl/BufferObject.js\";import{PrimitiveType as b,Usage as R,TextureType as y,PixelFormat as k,PixelType as A,TextureWrapMode as v,TextureSamplingMode as E}from\"../../webgl/enums.js\";import{Texture as j}from\"../../webgl/Texture.js\";import{VertexArrayObject as w}from\"../../webgl/VertexArrayObject.js\";class U extends d{constructor(){super(),this._handles=new t,this._resourcePixelRatio=1,this.visible=!1}destroy(){this._handles=i(this._handles),this._disposeRenderResources(),this._resourcesTask=a(this._resourcesTask)}get background(){return this._background}set background(e){this._background=e,this.requestRender()}get magnifier(){return this._magnifier}set magnifier(e){this._magnifier=e,this._handles.removeAll(),this._handles.add([h((()=>e.version),(()=>{this.visible=e.visible&&n(e.position)&&e.size>0,this.requestRender()}),m),h((()=>[e.maskUrl,e.overlayUrl]),(()=>this._reloadResources())),h((()=>e.size),(()=>{this._disposeRenderResources(),this.requestRender()}))])}_createTransforms(){return{dvs:c()}}doRender(e){const r=e.context;if(!this._resourcesTask)return void this._reloadResources();if(e.drawPhase!==p.MAP||!this._canRender())return;this._updateResources(e);const t=this._magnifier;if(o(t.position))return;const i=e.pixelRatio,a=t.size*i,n=1/t.factor,l=Math.ceil(n*a);this._readbackTexture.resize(l,l);const{size:h}=e.state,m=i*h[0],u=i*h[1],c=.5*l,d=.5*l,_=s(i*t.position.x,c,m-c-1),f=s(u-i*t.position.y,d,u-d-1);r.setBlendingEnabled(!0);const g=_-c,T=f-d,x=this._readbackTexture;r.bindTexture(x,0),r.gl.copyTexImage2D(x.descriptor.target,0,x.descriptor.pixelFormat,g,T,l,l,0);const R=this.background?.color,y=R?[R.a*R.r/255,R.a*R.g/255,R.a*R.b/255,R.a]:[1,1,1,1],k=(_+t.offset.x*i)/m*2-1,A=(f-t.offset.y*i)/u*2-1,v=a/m*2,E=a/u*2,j=this._program;r.bindVAO(this._vertexArrayObject),r.bindTexture(this._overlayTexture,6),r.bindTexture(this._maskTexture,7),r.useProgram(j),j.setUniform4fv(\"u_background\",y),j.setUniform1i(\"u_readbackTexture\",0),j.setUniform1i(\"u_overlayTexture\",6),j.setUniform1i(\"u_maskTexture\",7),j.setUniform4f(\"u_drawPos\",k,A,v,E),j.setUniform1i(\"u_maskEnabled\",t.maskEnabled?1:0),j.setUniform1i(\"u_overlayEnabled\",t.overlayEnabled?1:0),r.setStencilTestEnabled(!1),r.setColorMask(!0,!0,!0,!0),r.drawArrays(b.TRIANGLE_STRIP,0,4),r.bindVAO()}_canRender(){return this.mask&&this.overlay&&null!=this._magnifier}_reloadResources(){this._resourcesTask&&this._resourcesTask.abort();const t=n(this._magnifier)?this._magnifier.maskUrl:null,s=n(this._magnifier)?this._magnifier.overlayUrl:null;this._resourcesTask=r((async r=>{const i=o(t)||o(s)?T(r):null,a=n(t)?e(t,{responseType:\"image\",signal:r}).then((e=>e.data)):i.then((e=>e.mask)),l=n(s)?e(s,{responseType:\"image\",signal:r}).then((e=>e.data)):i.then((e=>e.overlay)),[h,m]=await Promise.all([a,l]);this.mask=h,this.overlay=m,this._disposeRenderResources(),this.requestRender()}))}_disposeRenderResources(){this._readbackTexture=l(this._readbackTexture),this._overlayTexture=l(this._overlayTexture),this._maskTexture=l(this._maskTexture),this._vertexArrayObject=l(this._vertexArrayObject),this._program=l(this._program)}_updateResources(e){if(e.pixelRatio!==this._resourcePixelRatio&&this._disposeRenderResources(),this._readbackTexture)return;const r=e.context;this._resourcePixelRatio=e.pixelRatio;const t=Math.ceil(this._magnifier.size*e.pixelRatio);this._program=f(r);const s=new Uint16Array([0,1,0,0,1,1,1,0]),i=g.attributes;this._vertexArrayObject=new w(r,i,_,{geometry:x.createVertex(r,R.STATIC_DRAW,s)}),this.overlay.width=t,this.overlay.height=t,this._overlayTexture=new j(r,{target:y.TEXTURE_2D,pixelFormat:k.RGBA,internalFormat:k.RGBA,dataType:A.UNSIGNED_BYTE,wrapMode:v.CLAMP_TO_EDGE,samplingMode:E.NEAREST,flipped:!0,preMultiplyAlpha:!u(this.overlay.src)||!e.context.driverTest.svgPremultipliesAlpha.result},this.overlay),this.mask.width=t,this.mask.height=t,this._maskTexture=new j(r,{target:y.TEXTURE_2D,pixelFormat:k.ALPHA,internalFormat:k.ALPHA,dataType:A.UNSIGNED_BYTE,wrapMode:v.CLAMP_TO_EDGE,samplingMode:E.NEAREST,flipped:!0},this.mask);const a=1/this._magnifier.factor;this._readbackTexture=new j(r,{target:y.TEXTURE_2D,pixelFormat:k.RGBA,internalFormat:k.RGBA,dataType:A.UNSIGNED_BYTE,wrapMode:v.CLAMP_TO_EDGE,samplingMode:E.LINEAR,flipped:!1,width:Math.ceil(a*t),height:Math.ceil(a*t)})}}export{U as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI6H,IAAMA,MAAE,CAAAC,OAAGC,GAAE,EAAC,IAAGD,GAAE,IAAG,SAAQA,GAAE,QAAO,CAAC;AAAxC,IAA0CE,KAAE,EAAC,SAAQ,CAAAC,SAAI,EAAC,cAAaJ,IAAEI,GAAC,IAAEF,GAAE,4BAA4B,GAAE,gBAAeF,IAAEI,GAAC,IAAEF,GAAE,4BAA4B,EAAC,GAAE;AAAjK,IAAmKG,KAAE,CAAAJ,OAAGC,GAAE,EAAC,IAAGD,GAAE,GAAE,CAAC;AAAnL,IAAqLK,KAAE,EAAC,SAAQ,CAAAF,SAAI,EAAC,cAAaC,GAAED,GAAC,IAAEF,GAAE,oBAAoB,GAAE,gBAAeG,GAAED,GAAC,IAAEF,GAAE,oBAAoB,EAAC,GAAE;AAA5R,IAA8RA,KAAE,CAAAD,OAAGC,GAAE,EAAC,IAAGD,GAAE,IAAG,SAAQA,GAAE,QAAO,CAAC;AAAhU,IAAkUM,KAAE,EAAC,SAAQ,CAAAH,SAAI,EAAC,cAAaF,GAAEE,GAAC,IAAEF,GAAE,gBAAgB,GAAE,gBAAeA,GAAEE,GAAC,IAAEF,GAAE,gBAAgB,EAAC,GAAE;AAAja,IAAmaM,KAAE,CAAAP,OAAGC,GAAE,EAAC,IAAGD,GAAE,GAAE,CAAC;AAAnb,IAAqbQ,KAAE,EAAC,SAAQ,CAAAL,SAAI,EAAC,cAAaI,GAAEJ,GAAC,IAAEF,GAAE,sBAAsB,GAAE,gBAAeM,GAAEJ,GAAC,IAAEF,GAAE,sBAAsB,EAAC,GAAE;AAAhiB,IAAkiBQ,KAAE,CAAAT,OAAGC,GAAE,EAAC,IAAGD,GAAE,IAAG,KAAIA,GAAE,IAAG,CAAC;AAA5jB,IAA8jBU,KAAE,EAAC,SAAQ,CAAAP,SAAI,EAAC,cAAaM,GAAEN,GAAC,IAAEF,GAAE,gBAAgB,GAAE,gBAAeQ,GAAEN,GAAC,IAAEF,GAAE,gBAAgB,EAAC,GAAE;AAA7pB,IAA+pB,IAAE,CAAAD,OAAGC,GAAE,EAAC,IAAGD,GAAE,IAAG,SAAQA,GAAE,SAAQ,KAAIA,GAAE,IAAG,CAAC;AAA3sB,IAA6sB,IAAE,EAAC,SAAQ,CAAAG,SAAI,EAAC,cAAa,EAAEA,GAAC,IAAEF,GAAE,gBAAgB,GAAE,gBAAe,EAAEE,GAAC,IAAEF,GAAE,gBAAgB,EAAC,GAAE;AAA5yB,IAA8yBU,KAAE,CAAAX,OAAGC,GAAE,EAAC,IAAGD,GAAE,GAAE,CAAC;AAA9zB,IAAg0BY,KAAE,EAAC,SAAQ,CAAAT,SAAI,EAAC,cAAaQ,GAAER,GAAC,IAAEF,GAAE,gBAAgB,GAAE,gBAAeU,GAAER,GAAC,IAAEF,GAAE,gBAAgB,EAAC,GAAE;;;ACAh4B,IAAMY,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,gBAAc,oBAAI;AAAA,EAAG;AAAA,EAAC,UAAS;AAAC,SAAK,cAAc,QAAS,CAAAC,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,cAAc,MAAM;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAEC,KAAEC,KAAE;AAAC,UAAMC,KAAEF,IAAE,OAAK,IAAE,KAAK,yBAAyBA,IAAE,MAAKC,GAAC;AAAE,QAAG,KAAK,cAAc,IAAIC,EAAC,EAAE,QAAO,KAAK,cAAc,IAAIA,EAAC;AAAE,UAAMC,KAAE,KAAK,oBAAoBH,IAAE,IAAI,GAAE,EAAC,SAAQI,GAAC,IAAED,IAAE,EAAC,cAAaE,IAAE,gBAAeC,GAAC,IAAEF,GAAEH,GAAC,GAAEH,MAAEE,IAAE,gBAAgB,GAAEO,KAAEP,IAAE,cAAc,GAAEQ,KAAEH,GAAE,QAAQ,kBAAiBP,GAAC,EAAE,QAAQ,gBAAeS,EAAC,GAAEE,KAAEV,GAAE,aAAa,QAAQS,IAAEF,IAAEN,IAAE,sBAAsB,CAAC;AAAE,WAAO,KAAK,cAAc,IAAIE,IAAEO,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,yBAAyBT,KAAEC,KAAE;AAAC,YAAOD,KAAE;AAAA,MAAC,KAAKU,GAAE,YAAW;AAAC,cAAMX,KAAEE;AAAE,gBAAOF,GAAE,UAAQ,IAAE,MAAI,KAAGA,GAAE,KAAG,IAAE;AAAA,MAAE;AAAA,MAAC,KAAKW,GAAE,MAAK;AAAC,cAAMX,KAAEE;AAAE,gBAAOF,GAAE,UAAQ,IAAE,MAAI,KAAGA,GAAE,KAAG,IAAE;AAAA,MAAE;AAAA,MAAC,KAAKW,GAAE;AAAQ,eAAOT,IAAE,KAAG,IAAE;AAAA,MAAE,KAAKS,GAAE,MAAK;AAAC,cAAMX,KAAEE;AAAE,gBAAOF,GAAE,MAAI,IAAE,MAAI,KAAGA,GAAE,UAAQ,IAAE,MAAI,KAAGA,GAAE,KAAG,IAAE;AAAA,MAAE;AAAA,MAAC,KAAKW,GAAE,MAAK;AAAC,cAAMX,KAAEE;AAAE,gBAAOF,GAAE,MAAI,IAAE,MAAI,KAAGA,GAAE,KAAG,IAAE;AAAA,MAAE;AAAA,MAAC,KAAKW,GAAE;AAAO,eAAOT,IAAE,KAAG,IAAE;AAAA,MAAE,KAAKS,GAAE;AAAK,eAAOT,IAAE,KAAG,IAAE;AAAA,MAAE;AAAQ,eAAO;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,oBAAoBH,KAAE;AAAC,YAAOA,KAAE;AAAA,MAAC,KAAKY,GAAE;AAAW,eAAOR;AAAA,MAAE,KAAKQ,GAAE;AAAO,eAAOJ;AAAA,MAAE,KAAKI,GAAE;AAAK,eAAOC;AAAA,MAAE,KAAKD,GAAE;AAAK,eAAOZ;AAAA,MAAE,KAAKY,GAAE;AAAK,eAAO;AAAA,MAAE,KAAKA,GAAE;AAAQ,eAAOE;AAAA,MAAE,KAAKF,GAAE;AAAK,eAAOG;AAAA,MAAE;AAAQ,eAAO;AAAA,IAAI;AAAA,EAAC;AAAC;;;ACAz2B,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,eAAa;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,SAAK,WAAS,EAAE,KAAK,QAAQ,GAAE,KAAK,qBAAmB,EAAE,KAAK,kBAAkB;AAAA,EAAC;AAAA,EAAC,OAAOC,KAAEC,KAAEC,IAAEC,IAAE;AAAC,IAAAH,QAAI,KAAK,gBAAc,KAAK,YAAYA,GAAC,GAAEA,IAAE,yBAAyB,EAAE,KAAI,EAAE,qBAAoB,EAAE,KAAI,EAAE,mBAAmB,GAAEA,IAAE,QAAQ,KAAK,kBAAkB,GAAEA,IAAE,WAAW,KAAK,QAAQ,GAAEC,IAAE,gBAAgBC,EAAC,GAAEF,IAAE,YAAYC,KAAE,CAAC,GAAE,KAAK,SAAS,aAAa,SAAQ,CAAC,GAAE,KAAK,SAAS,aAAa,aAAYE,EAAC,GAAEH,IAAE,WAAW,EAAE,gBAAe,GAAE,CAAC,GAAEA,IAAE,YAAY,MAAK,CAAC,GAAEA,IAAE,QAAQ;AAAA,EAAE;AAAA,EAAC,YAAYA,KAAE;AAAC,QAAG,KAAK,aAAa,QAAM;AAAG,UAAMI,KAAEF,GAAEF,KAAEE,EAAC;AAAE,QAAG,CAACE,GAAE,QAAM;AAAG,UAAMC,MAAE,IAAI,UAAU,EAAE;AAAE,IAAAA,IAAE,CAAC,IAAE,IAAGA,IAAE,CAAC,IAAE,IAAGA,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,IAAGA,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,GAAEA,IAAE,CAAC,IAAE,IAAGA,IAAE,CAAC,IAAE,GAAEA,IAAE,EAAE,IAAE,GAAEA,IAAE,EAAE,IAAE,GAAEA,IAAE,EAAE,IAAE,GAAEA,IAAE,EAAE,IAAE,GAAEA,IAAE,EAAE,IAAE,GAAEA,IAAE,EAAE,IAAE;AAAE,UAAMN,KAAEG,GAAE,YAAWI,KAAE,IAAIC,GAAEP,KAAED,IAAEE,IAAE,EAAC,UAASO,GAAE,aAAaR,KAAE,EAAE,aAAYK,GAAC,EAAC,CAAC;AAAE,WAAO,KAAK,WAASD,IAAE,KAAK,qBAAmBE,IAAE,KAAK,eAAa,MAAG;AAAA,EAAE;AAAC;;;ACAnrC,IAAMG,KAAE,CAAAC,QAAGA,QAAI,EAAE,WAASA,QAAI,EAAE;AAAhC,IAA4CC,KAAE,CAAAD,SAAID,GAAEC,GAAC,IAAE,IAAE,MAAIA,QAAI,EAAE,YAAU,IAAE;AAA/E,IAAkFE,KAAE,CAAC,EAAC,cAAaC,IAAE,WAAUC,IAAC,GAAEL,IAAEG,QAAI,GAAGH,GAAE,iBAAiB,CAAC,IAAIE,GAAEG,GAAC,CAAC,IAAID,GAAE,iBAAiB,CAAC,IAAI,EAAED,GAAC,KAAGA,IAAE,KAAK,GAAG,CAAC;AAApM,IAAuMG,KAAE,CAACD,KAAEH,IAAEC,KAAEG,KAAE,CAAC,MAAI;AAAC,MAAGA,KAAE,EAAC,GAAGA,IAAE,GAAGJ,GAAE,aAAa,GAAE,GAAGG,IAAE,aAAa,aAAa,GAAE,WAAUA,IAAE,cAAY,EAAE,WAAU,IAAGL,GAAEK,IAAE,SAAS,EAAC,GAAE,EAAEF,GAAC,EAAE,YAAUF,OAAKE,IAAE,CAAAG,GAAEL,GAAC,IAAE;AAAG,SAAOK;AAAC;AAAE,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYN,KAAE;AAAC,SAAK,QAAMA,KAAE,KAAK,gBAAc,oBAAI;AAAA,EAAG;AAAA,EAAC,UAAS;AAAC,SAAK,cAAc,QAAS,CAAAA,QAAGA,IAAE,QAAQ,CAAE,GAAE,KAAK,cAAc,MAAM;AAAA,EAAC;AAAA,EAAC,WAAWA,KAAEG,KAAE,CAAC,GAAE;AAAC,UAAMJ,KAAEC,IAAE,SAAO,MAAIA,IAAE,SAAO,KAAK,UAAUG,EAAC;AAAE,QAAG,KAAK,cAAc,IAAIJ,EAAC,EAAE,QAAO,KAAK,cAAc,IAAIA,EAAC;AAAE,UAAME,KAAE,EAAC,GAAGE,GAAE,IAAK,CAAAH,QAAG,YAAU,OAAOA,MAAE,EAAC,MAAKA,KAAE,OAAM,KAAE,IAAEA,GAAE,EAAE,OAAQ,CAACA,KAAEG,QAAK,EAAC,GAAGH,KAAE,CAACG,GAAE,IAAI,GAAEA,GAAE,MAAK,IAAI,CAAC,CAAC,EAAC,GAAE,EAAC,QAAOD,KAAE,QAAOG,IAAE,YAAWC,GAAC,IAAEN,KAAEO,KAAEL,GAAEA,KAAEG,IAAEC,IAAEL,EAAC,GAAEO,KAAE,KAAK,MAAM,aAAa,QAAQD,GAAE,QAAQ,cAAaA,GAAE,QAAQ,gBAAeA,GAAE,UAAU;AAAE,QAAG,CAACC,GAAE,OAAM,IAAI,MAAM,uCAAuC;AAAE,WAAO,KAAK,cAAc,IAAIT,IAAES,EAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,mBAAmBR,KAAEG,IAAEJ,IAAEE,IAAEK,IAAE;AAAC,UAAMC,KAAEL,GAAEF,KAAEG,IAAEG,EAAC;AAAE,QAAG,KAAK,cAAc,IAAIC,EAAC,EAAE,QAAO,KAAK,cAAc,IAAIA,EAAC;AAAE,UAAMC,KAAEH,GAAEL,KAAEG,IAAEG,IAAE,EAAC,yBAAwBN,IAAE,QAAQ,WAAW,wBAAwB,OAAM,CAAC,GAAES,KAAEP,GAAEH,IAAEA,IAAEE,IAAEO,EAAC,GAAEE,KAAE,KAAK,MAAM,aAAa,QAAQD,GAAE,QAAQ,cAAaA,GAAE,QAAQ,gBAAeA,GAAE,UAAU;AAAE,QAAG,CAACC,GAAE,OAAM,IAAI,MAAM,uCAAuC;AAAE,WAAO,KAAK,cAAc,IAAIH,IAAEG,EAAC,GAAEA;AAAA,EAAC;AAAC;;;ACAzuC,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,KAAE;AAAC,SAAK,SAAO,CAAC,GAAE,KAAK,WAASD,IAAE,KAAK,eAAaC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,SAAO,CAAC;AAAA,EAAC;AAAA,EAAC,qBAAqBD,IAAEE,KAAE;AAAC,UAAMH,KAAE,EAAE,GAAEI,KAAEH,IAAEI,KAAE,IAAEC,KAAE,KAAK,KAAKF,GAAE,SAAOC,EAAC;AAAE,QAAG,EAAEF,GAAC,GAAE,KAAK,SAAS,SAAOA,GAAE,OAAO,MAAK,OAAO,KAAK,EAAC,MAAK,YAAW,SAAQF,IAAE,UAASD,IAAE,SAAQG,IAAC,CAAC;AAAA,QAAO,UAAQD,MAAE,GAAEA,MAAEI,IAAEJ,OAAI;AAAC,YAAMK,KAAEL,MAAEG,IAAEG,MAAEN,QAAII,KAAE,GAAEG,KAAED,MAAEJ,GAAE,SAAOC,KAAEH,MAAEG;AAAE,WAAK,OAAO,KAAK,EAAC,MAAK,SAAQ,SAAQJ,IAAE,UAASD,IAAE,OAAME,KAAE,aAAYK,IAAE,YAAWE,IAAE,aAAYD,KAAE,SAAQL,IAAC,CAAC;AAAA,IAAC;AAAC,WAAO,EAAEA,KAAG,CAAAF,OAAGD,GAAE,OAAOC,EAAC,CAAE,GAAED,GAAE;AAAA,EAAO;AAAA,EAAC,SAAQ;AAAC,QAAIE,MAAE;AAAE,WAAK,KAAK,OAAO,UAAQ;AAAC,YAAMK,KAAE,YAAY,IAAI,GAAEC,MAAE,KAAK,OAAO,MAAM;AAAE,UAAGA,KAAE;AAAC,YAAG,EAAEA,IAAE,QAAQ,MAAM,KAAGA,IAAE,QAAQ,OAAO,QAAQ;AAAS,gBAAOA,IAAE,MAAK;AAAA,UAAC,KAAI;AAAQ,iBAAK,aAAaA,GAAC;AAAE;AAAA,UAAM,KAAI;AAAW,iBAAK,eAAeA,GAAC;AAAA,QAAC;AAAC,cAAMC,KAAE,YAAY,IAAI,IAAEF;AAAE,YAAGL,OAAGO,IAAEP,MAAEO,MAAG,GAAE;AAAA,MAAK;AAAA,IAAC;AAAC,SAAK,OAAO,UAAQ,KAAK,aAAa,cAAc;AAAA,EAAC;AAAA,EAAC,aAAaP,KAAE;AAAC,UAAK,EAAC,SAAQK,IAAE,UAASC,KAAE,aAAYL,KAAE,aAAYM,IAAE,YAAWC,GAAC,IAAER,KAAE,EAAC,MAAKF,IAAE,SAAQI,IAAE,OAAMC,GAAC,IAAEE;AAAE,MAAEP,EAAC,MAAII,GAAE,WAAW,GAAE,GAAED,KAAEE,IAAEK,IAAEV,IAAEG,GAAC,GAAEM,MAAGD,IAAE,QAAQ;AAAA,EAAE;AAAA,EAAC,eAAeP,IAAE;AAAC,UAAK,EAAC,SAAQC,KAAE,UAASK,GAAC,IAAEN,IAAE,EAAC,MAAKO,KAAE,SAAQL,IAAC,IAAED;AAAE,IAAAC,IAAE,QAAQK,GAAC,GAAED,GAAE,QAAQ;AAAA,EAAC;AAAC;;;ACAxzB,IAAMI,KAAEC,GAAE,MAAI,IAAG;AAAE,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,aAAW,EAAE,GAAE,KAAK,WAAS,EAAE,GAAE,KAAK,qBAAmB,EAAE,GAAE,KAAK,QAAMC,GAAE,GAAE,KAAK,eAAa;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,SAAK,WAAS,EAAE,KAAK,QAAQ,GAAE,KAAK,QAAM,EAAE,KAAK,KAAK;AAAA,EAAC;AAAA,EAAC,OAAOC,KAAEC,IAAE;AAAC,UAAK,EAAC,SAAQC,GAAC,IAAEF;AAAE,WAAM,CAAC,CAAC,KAAK,gBAAgBA,KAAEC,EAAC,MAAI,KAAK,gBAAc,KAAK,YAAYC,EAAC,GAAEA,GAAE,qBAAqB,KAAE,GAAEA,GAAE,oBAAoB,KAAE,GAAEA,GAAE,aAAa,OAAG,OAAG,OAAG,KAAE,GAAEA,GAAE,mBAAmB,KAAE,GAAEA,GAAE,aAAa,EAAE,MAAK,EAAE,MAAK,EAAE,OAAO,GAAEA,GAAE,mBAAmB,EAAE,QAAO,GAAE,GAAG,GAAEA,GAAE,sBAAsB,IAAE,GAAEA,GAAE,WAAW,KAAK,QAAQ,GAAE,KAAK,SAAS,oBAAoB,iBAAgB,KAAK,KAAK,GAAE,KAAK,MAAM,KAAK,GAAE,KAAK,MAAM,OAAO,GAAE;AAAA,EAAG;AAAA,EAAC,YAAYF,KAAE;AAAC,QAAG,KAAK,aAAa;AAAO,UAAMC,KAAEF,GAAEC,KAAEH,EAAC;AAAE,IAAAI,OAAI,KAAK,WAASA,IAAE,KAAK,QAAM,IAAIE,GAAEH,KAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAE,KAAK,eAAa;AAAA,EAAG;AAAA,EAAC,gBAAgBA,KAAEI,IAAE;AAAC,UAAK,EAAC,OAAMD,IAAE,YAAWE,GAAC,IAAEL,KAAE,EAAC,MAAKM,IAAE,UAASC,GAAC,IAAEJ,IAAEK,KAAE,KAAK,MAAMF,GAAE,CAAC,IAAED,EAAC,GAAEI,KAAE,KAAK,MAAMH,GAAE,CAAC,IAAED,EAAC;AAAE,QAAG,CAACF,GAAE,iBAAiB,YAAY,QAAM;AAAG,UAAMO,KAAEb,GAAEU,EAAC,GAAET,KAAE,KAAK,IAAI,KAAK,IAAIY,EAAC,CAAC,GAAEC,KAAE,KAAK,IAAI,KAAK,IAAID,EAAC,CAAC,GAAEE,KAAE,KAAK,MAAMJ,KAAEV,KAAEW,KAAEE,EAAC,GAAEE,KAAE,KAAK,MAAMV,GAAE,gBAAgB;AAAE,QAAGS,MAAGC,GAAE,QAAM;AAAG,UAAMC,KAAEN,KAAEG,KAAEF,KAAEX,IAAEiB,KAAEF,KAAER,IAAEW,MAAGZ,GAAE,OAAKA,GAAE,SAAOC,KAAEG,IAAES,MAAGb,GAAE,SAAOA,GAAE,OAAKC,KAAEI;AAAE,MAAE,KAAK,oBAAmBM,IAAED,IAAE,CAAC,GAAE,EAAE,KAAK,UAAS,IAAEN,IAAE,KAAGC,IAAE,CAAC,GAAE,EAAE,KAAK,YAAWO,IAAEC,IAAE,CAAC;AAAE,UAAMC,KAAE,KAAK;AAAM,WAAOR,GAAEQ,IAAE,KAAK,UAAU,GAAEpB,GAAEoB,IAAEA,IAAE,KAAK,QAAQ,GAAE,MAAIX,MAAGY,GAAED,IAAEA,IAAER,EAAC,GAAEZ,GAAEoB,IAAEA,IAAE,KAAK,kBAAkB,GAAEF,GAAEE,IAAEA,IAAEtB,EAAC,GAAE;AAAA,EAAE;AAAC;;;ACAhrD,IAAMwB,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,UAAQ,CAAC,GAAE,KAAK,QAAM,EAAC,QAAO,gBAAe,QAAO,gBAAe,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,SAAO,KAAK,MAAM,QAAQ;AAAA,EAAC;AAAA,EAAC,OAAM;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAA,EAAC;AAAA,EAAC,KAAKC,KAAEC,IAAE;AAAC,QAAG,EAACA,MAAA,gBAAAA,GAAG,MAAK;AAAO,UAAK,EAAC,SAAQC,IAAE,kBAAiBJ,IAAC,IAAEE;AAAE,SAAK,UAAQ,KAAK,QAAM,IAAIG,GAAED,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC;AAAG,UAAMC,KAAED,GAAE,0BAA0B,GAAE,EAAC,GAAEE,IAAE,GAAEC,IAAE,OAAMC,IAAE,QAAOC,GAAC,IAAEL,GAAE,YAAY;AAAE,IAAAD,GAAE,aAAaC,EAAC;AAAE,UAAMM,KAAEP,GAAE,SAAS,CAAC;AAAE,QAAG,EAAEO,EAAC,EAAE;AAAO,UAAMC,KAAED,GAAE,OAAON,EAAC,GAAEQ,KAAEF,GAAE,OAAON,IAAE,CAAC;AAAE,IAAAA,GAAE,YAAY,GAAE,GAAED,GAAE,MAAKA,GAAE,IAAI,GAAE,KAAK,cAAcD,KAAEU,IAAEZ,IAAE,mBAAmB,GAAE,KAAK,sBAAsBE,KAAEU,IAAED,EAAC,GAAEP,GAAE,gBAAgBC,EAAC,GAAED,GAAE,YAAYE,IAAEC,IAAEC,IAAEC,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcI,IAAEZ,KAAEa,IAAE;AAAC,UAAK,EAAC,SAAQV,IAAE,SAAQJ,KAAE,cAAaK,GAAC,IAAEQ,IAAEP,KAAEN,IAAE,gBAAgB,WAAW,KAAK,OAAM,CAAC,OAAO,CAAC;AAAE,IAAAI,GAAE,gBAAgBH,GAAC,GAAEG,GAAE,cAAc,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,GAAG,gBAAgB,GAAEA,GAAE,WAAWE,EAAC,GAAEA,GAAE,aAAa,iBAAgBS,EAAC,GAAET,GAAE,aAAa,mBAAkBU,EAAC,GAAEV,GAAE,aAAa,eAAcO,GAAE,SAAS,GAAEP,GAAE,aAAa,mBAAkBQ,EAAC,GAAER,GAAE,aAAa,eAAc,KAAK,MAAM,KAAGD,EAAC,CAAC,GAAE,KAAK,MAAM,KAAK;AAAA,EAAC;AAAA,EAAC,sBAAsBQ,IAAEZ,KAAEC,KAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,SAAQW,GAAC,IAAED,IAAET,KAAEU,GAAE,gBAAgB,WAAW,KAAK,OAAM,CAAC,QAAQ,CAAC;AAAE,IAAAX,GAAE,YAAYF,IAAE,cAAa,CAAC,GAAEE,GAAE,WAAWC,EAAC,GAAEA,GAAE,aAAa,mBAAkB,CAAC,GAAED,GAAE,gBAAgBD,GAAC,GAAEC,GAAE,cAAc,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,GAAG,gBAAgB,GAAE,KAAK,MAAM,KAAK;AAAA,EAAC;AAAC;;;ACA5gD,IAAMc,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,IAAE;AAAC,UAAM,GAAE,KAAK,OAAK,KAAK,YAAY,MAAK,KAAK,UAAQ,CAACA,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,KAAK,EAAC,SAAQA,IAAE,SAAQD,IAAC,GAAE;AAAC,SAAK,QAAMC,GAAE,0BAA0B;AAAE,UAAK,EAAC,OAAMF,KAAE,QAAOG,GAAC,IAAED,GAAE,YAAY,GAAEE,MAAEH,IAAE,QAAQD,KAAEG,EAAC,EAAE;AAAQ,IAAAD,GAAE,gBAAgBE,GAAC,GAAEF,GAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAEA,GAAE,cAAc,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,GAAG,gBAAgB;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAA,EAAC;AAAA,EAAC,KAAKA,IAAEF,KAAE;AAAC,UAAK,EAAC,SAAQG,IAAE,SAAQC,IAAC,IAAEF,IAAEG,KAAED,IAAE,yBAAyBJ,GAAC,GAAEM,KAAEH,GAAE,0BAA0B;AAAE,eAAS,EAAC,sBAAqBF,KAAE,QAAOM,GAAC,KAAIF,GAAE,CAAAJ,IAAE,KAAKC,IAAEI,IAAEC,EAAC;AAAE,IAAAJ,GAAE,gBAAgB,KAAK,KAAK,GAAEA,GAAE,sBAAsB,KAAE,GAAEC,IAAE,YAAYD,IAAEG,GAAE,cAAa,EAAE,OAAO,GAAEH,GAAE,sBAAsB,IAAE;AAAA,EAAC;AAAC;;;ACAlC,IAAMK,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,SAAO,QAAO,KAAK,UAAQ,QAAO,KAAK,aAAW;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,SAAK,eAAa,KAAK,WAAW,aAAa,QAAQ,GAAE,KAAK,WAAW,QAAQ,QAAQ,GAAE,KAAK,WAAW,iBAAiB,QAAQ,GAAE,KAAK,WAAW,YAAY,QAAQ,GAAE,KAAK,aAAW;AAAA,EAAK;AAAA,EAAC,QAAQC,IAAEC,IAAE;AAAC,IAAAD,GAAE,YAAYC,IAAEC,EAAC,GAAEF,GAAE,WAAW,KAAK,WAAW,WAAW,GAAE,KAAK,WAAW,YAAY,cAAc,eAAc,CAAC,GAAE,GAAE,IAAE,KAAK,QAAO,CAAC,CAAC,GAAE,KAAK,WAAW,YAAY,oBAAoB,qBAAoBG,EAAC,GAAEH,GAAE,QAAQ,KAAK,WAAW,OAAO,GAAEA,GAAE,WAAW,EAAE,gBAAe,GAAE,CAAC,GAAEA,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAEI,IAAE;AAAC,IAAAJ,GAAE,YAAYI,IAAEF,EAAC,GAAEF,GAAE,WAAW,KAAK,WAAW,WAAW,GAAE,KAAK,WAAW,YAAY,cAAc,eAAc,CAAC,GAAE,GAAE,GAAE,IAAE,KAAK,OAAO,CAAC,GAAE,KAAK,WAAW,YAAY,oBAAoB,qBAAoB,CAAC,GAAEA,GAAE,QAAQ,KAAK,WAAW,OAAO,GAAEA,GAAE,WAAW,EAAE,gBAAe,GAAE,CAAC,GAAEA,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAEI,IAAEH,IAAE;AAAC,IAAAD,GAAE,YAAYI,IAAEF,EAAC,GAAEF,GAAE,WAAW,KAAK,WAAW,gBAAgB,GAAEC,GAAE,sBAAsBD,IAAE,KAAK,WAAW,gBAAgB,GAAEA,GAAE,QAAQ,KAAK,WAAW,OAAO,GAAEA,GAAE,mBAAmB,IAAE,GAAEA,GAAE,iBAAiB,EAAE,KAAI,EAAE,mBAAmB,GAAEA,GAAE,WAAW,EAAE,gBAAe,GAAE,CAAC,GAAEA,GAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,YAAYI,IAAEH,IAAEI,IAAE;AAAC,SAAK,SAAOJ,IAAE,KAAK,UAAQI;AAAE,UAAMC,KAAEC,GAAE,aAAaH,IAAE,EAAE,aAAY,IAAI,UAAU,CAAC,IAAG,IAAG,GAAE,GAAE,GAAE,IAAG,GAAE,GAAE,IAAG,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,EAAE,MAAM,GAAEL,KAAE,IAAIS,GAAEJ,IAAE,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,GAAE,CAAC,cAAa,CAAC,CAAC,CAAC,GAAE,EAAC,UAAS,CAAC,IAAID,GAAE,cAAa,GAAE,EAAE,MAAK,GAAE,CAAC,GAAE,IAAIA,GAAE,cAAa,GAAE,EAAE,eAAc,GAAE,CAAC,CAAC,EAAC,GAAE,EAAC,UAASG,GAAC,CAAC,GAAEE,KAAER,GAAEI,IAAED,EAAC,GAAEM,KAAET,GAAEI,IAAEM,EAAC;AAAE,IAAAN,GAAE,WAAWI,EAAC,GAAEA,GAAE,aAAa,aAAYN,EAAC,GAAEM,GAAE,aAAa,WAAU,CAAC,GAAEA,GAAE,aAAa,WAAUG,EAAC,GAAEP,GAAE,WAAWK,EAAC,GAAEA,GAAE,aAAa,aAAYP,EAAC,GAAEO,GAAE,aAAa,WAAUE,EAAC,GAAE,KAAK,aAAW,EAAC,cAAaL,IAAE,SAAQP,IAAE,kBAAiBS,IAAE,aAAYC,GAAC;AAAA,EAAC;AAAA,EAAC,MAAMC,KAAEV,IAAEI,IAAE;AAAC,SAAK,cAAY,KAAK,SAAOJ,IAAE,KAAK,UAAQI,MAAG,KAAK,YAAYM,KAAEV,IAAEI,EAAC;AAAA,EAAC;AAAC;;;ACAjoE,SAASQ,GAAEC,IAAED,IAAEE,IAAE;AAAC,QAAMC,KAAE,IAAIC,GAAEH,IAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,UAAS,EAAE,eAAc,UAASI,GAAE,eAAc,OAAML,IAAE,QAAOE,IAAE,cAAa,EAAE,OAAM,CAAC;AAAE,SAAM,CAACC,IAAE,IAAI,EAAEF,IAAE,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,sBAAqB,GAAEE,EAAC,CAAC;AAAC;AAAC,IAAMD,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,SAAO,QAAO,KAAK,UAAQ,QAAO,KAAK,aAAW;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,SAAK,eAAa,KAAK,WAAW,eAAe,QAAQ,GAAE,KAAK,WAAW,eAAe,QAAQ,GAAE,KAAK,WAAW,eAAe,QAAQ,GAAE,KAAK,WAAW,eAAe,QAAQ,GAAE,KAAK,aAAW,EAAE,KAAK,UAAU;AAAA,EAAE;AAAA,EAAC,YAAYD,IAAEK,IAAEC,KAAE;AAAC,SAAK,SAAOD,IAAE,KAAK,UAAQC;AAAE,UAAK,CAACC,KAAEC,EAAC,IAAET,GAAEC,IAAEK,IAAEC,GAAC,GAAE,CAACG,IAAEC,GAAC,IAAEX,GAAEC,IAAEK,IAAEC,GAAC;AAAE,SAAK,aAAW,EAAC,gBAAeC,KAAE,gBAAeC,IAAE,gBAAeC,IAAE,gBAAeC,IAAC;AAAA,EAAC;AAAA,EAAC,MAAMV,IAAEK,IAAEC,KAAE;AAAC,KAAC,KAAK,cAAY,KAAK,WAASD,MAAG,KAAK,YAAUC,OAAG,KAAK,QAAQ,GAAE,KAAK,cAAY,KAAK,YAAYN,IAAEK,IAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK,WAAW;AAAA,EAAc;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK,WAAW;AAAA,EAAc;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK,WAAW;AAAA,EAAc;AAAA,EAAC,IAAI,iBAAgB;AAAC,WAAO,KAAK,WAAW;AAAA,EAAc;AAAC;;;ACA9qC,IAAMK,KAAE;AAAR,IAAUC,KAAE,IAAED;AAAE,IAAME,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,UAAQ,CAAC,WAAW,GAAE,KAAK,cAAY,IAAIC,MAAE,KAAK,SAAO,QAAO,KAAK,UAAQ,QAAO,KAAK,YAAU,MAAK,KAAK,cAAY,IAAIC,MAAE,KAAK,iBAAe,QAAO,KAAK,kBAAgB,QAAO,KAAK,gBAAc,IAAIA;AAAA,EAAC;AAAA,EAAC,UAAS;AAJlkB;AAImkB,eAAK,gBAAL,mBAAkB,YAAU,UAAK,gBAAL,mBAAkB,WAAU,KAAK,YAAU;AAAA,EAAI;AAAA,EAAC,KAAKC,IAAE;AAAC,UAAK,EAAC,SAAQH,KAAE,SAAQI,GAAC,IAAED,IAAE,EAAC,OAAME,KAAE,QAAOC,GAAC,IAAEN,IAAE,YAAY,GAAEH,KAAEO,GAAE,QAAQC,KAAEC,EAAC,EAAE;AAAQ,SAAK,MAAMH,IAAEE,KAAEC,EAAC,GAAEN,IAAE,gBAAgBH,EAAC,GAAEG,IAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAEA,IAAE,cAAc,GAAE,GAAE,GAAE,CAAC,GAAEA,IAAE,MAAMA,IAAE,GAAG,gBAAgB;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAA,EAAC;AAAA,EAAC,MAAM,EAAC,SAAQG,GAAC,GAAEH,KAAEI,IAAE;AAAC,SAAK,SAAOJ,KAAE,KAAK,UAAQI;AAAE,UAAMC,MAAEL,MAAEH,IAAES,KAAEF,KAAEP;AAAE,IAAAG,OAAGK,MAAER,KAAE,IAAE,CAACQ,MAAER,KAAEQ,KAAED,MAAGE,KAAET,KAAE,IAAE,CAACS,KAAET,KAAES,IAAE,KAAK,iBAAeN,KAAE,KAAK,kBAAgBI,IAAE,KAAK,YAAUD,GAAE,0BAA0B;AAAE,UAAMJ,KAAE,KAAK,MAAMC,MAAEF,EAAC,GAAES,MAAE,KAAK,MAAMH,KAAEN,EAAC;AAAE,SAAK,YAAY,MAAMK,IAAEJ,IAAEQ,GAAC,GAAE,KAAK,YAAY,MAAMJ,IAAEJ,IAAEQ,GAAC;AAAA,EAAC;AAAA,EAAC,KAAKJ,IAAE;AAAC,UAAK,EAAC,SAAQH,KAAE,mBAAkBI,GAAC,IAAED;AAAE,QAAG,CAACC,GAAE;AAAO,UAAMC,MAAEL,IAAE,0BAA0B;AAAE,IAAAA,IAAE,YAAY,GAAE,GAAE,KAAK,iBAAeF,IAAE,KAAK,kBAAgBA,EAAC,GAAEE,IAAE,gBAAgB,KAAK,YAAY,cAAc,GAAEA,IAAE,sBAAsB,KAAE,GAAEA,IAAE,cAAc,GAAE,GAAE,GAAE,CAAC,GAAEA,IAAE,MAAMA,IAAE,GAAG,gBAAgB,GAAE,KAAK,cAAc,OAAOA,KAAEK,IAAE,cAAa,EAAE,SAAQ,CAAC,GAAEL,IAAE,sBAAsB,KAAE,GAAEA,IAAE,mBAAmB,KAAE,GAAEA,IAAE,aAAa,OAAG,OAAG,OAAG,IAAE,GAAEA,IAAE,gBAAgB,KAAK,YAAY,cAAc,GAAEA,IAAE,cAAc,GAAE,GAAE,GAAE,CAAC,GAAEA,IAAE,MAAMA,IAAE,GAAG,gBAAgB,GAAE,KAAK,YAAY,QAAQA,KAAE,KAAK,YAAY,cAAc,GAAEA,IAAE,gBAAgB,KAAK,YAAY,cAAc,GAAEA,IAAE,cAAc,GAAE,GAAE,GAAE,CAAC,GAAEA,IAAE,MAAMA,IAAE,GAAG,gBAAgB,GAAE,KAAK,YAAY,UAAUA,KAAE,KAAK,YAAY,cAAc,GAAEA,IAAE,gBAAgB,KAAK,SAAS,GAAEA,IAAE,mBAAmB,IAAE,GAAEA,IAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAEA,IAAE,YAAY,GAAE,GAAE,KAAK,QAAO,KAAK,OAAO,GAAE,KAAK,YAAY,gBAAgBA,KAAE,KAAK,YAAY,gBAAeI,EAAC,GAAE,KAAK,YAAU;AAAA,EAAI;AAAC;;;ACA93D,IAAMI,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK,KAAK,YAAY,MAAK,KAAK,UAAQ,CAAC,SAAS;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,MAAE,KAAK,IAAI,KAAG,KAAK,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,cAAc,EAAC,YAAWA,IAAC,GAAEC,IAAEC,KAAEC,IAAE;AAAC,QAAG,CAACF,GAAE,OAAO,QAAO;AAAK,UAAMG,MAAEH,GAAE,MAAM,GAAEI,MAAED,IAAE,GAAEE,KAAEF,IAAE;AAAE,WAAO,KAAK,eAAaA,KAAE,EAAC,MAAK,WAAU,UAASF,KAAEF,KAAE,UAAS,CAACK,KAAEC,EAAC,EAAC;AAAA,EAAC;AAAA,EAAC,KAAKN,KAAE;AAAC,UAAK,EAAC,SAAQO,IAAE,eAAcH,IAAC,IAAEJ;AAAE,QAAG,CAACI,IAAE,KAAK;AAAO,UAAMC,MAAED,IAAE,SAASI,EAAC;AAAE,QAAG,EAAEH,GAAC,EAAE;AAAO,UAAMC,KAAED,IAAE,OAAOE,EAAC;AAAE,IAAAA,GAAE,YAAY,GAAE,GAAEH,IAAE,MAAKA,IAAE,IAAI,GAAEG,GAAE,gBAAgBD,EAAC,GAAEC,GAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAEA,GAAE,cAAc,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMA,GAAE,GAAG,mBAAiBA,GAAE,GAAG,gBAAgB;AAAA,EAAC;AAAA,EAAC,OAAOP,KAAE;AAAA,EAAC;AAAA,EAAC,KAAKA,KAAE;AAAC,QAAG,EAAE,KAAK,YAAY,EAAE;AAAO,UAAMO,KAAE,KAAK;AAAa,SAAK,eAAa,MAAK,KAAK,SAASP,KAAEO,GAAE,SAAS;AAAA,EAAC;AAAA,EAAC,MAAM,SAASP,KAAEO,IAAE;AAAC,UAAK,EAAC,SAAQH,KAAE,eAAcL,GAAC,IAAEC,KAAES,KAAEV,GAAE,SAASS,EAAC;AAAE,QAAG,EAAEC,EAAC,EAAE,QAAO,KAAKF,GAAE,QAAS,CAAAP,QAAGA,IAAE,QAAQ,CAAC,CAAC,CAAE;AAAE,UAAMU,KAAED,GAAE,OAAOL,GAAC,GAAEO,KAAE,IAAI,WAAWD,GAAE,QAAMA,GAAE,SAAO,CAAC;AAAE,QAAG;AAAC,YAAMA,GAAE,gBAAgB,GAAE,GAAEA,GAAE,OAAMA,GAAE,QAAO,EAAE,MAAK,EAAE,eAAcC,EAAC;AAAA,IAAC,SAAOC,IAAE;AAAC,aAAO,KAAKL,GAAE,QAAS,CAAAP,QAAGA,IAAE,QAAQ,CAAC,CAAC,CAAE;AAAA,IAAC;AAAC,UAAMa,KAAE,CAAC;AAAE,aAAQZ,KAAE,GAAEA,KAAEU,GAAE,QAAOV,MAAG,GAAE;AAAC,YAAMD,MAAEW,GAAEV,EAAC,GAAEM,MAAEI,GAAEV,KAAE,CAAC;AAAE,MAAAD,OAAGa,GAAE,KAAK,EAAC,IAAGZ,KAAE,GAAE,YAAWM,IAAC,CAAC;AAAA,IAAC;AAAC,IAAAM,GAAE,KAAM,CAACb,KAAEC,OAAIA,GAAE,eAAaD,IAAE,aAAWC,GAAE,KAAGD,IAAE,KAAGC,GAAE,aAAWD,IAAE,UAAW;AAAE,UAAMc,KAAED,GAAE,IAAK,CAAAb,QAAGA,IAAE,EAAG;AAAE,IAAAO,GAAE,QAAS,CAAAP,QAAGA,IAAE,QAAQc,EAAC,CAAE;AAAA,EAAC;AAAC;;;ACA9wC,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,OAAK,KAAK,YAAY,MAAK,KAAK,UAAQ,CAAC,IAAI,GAAE,KAAK,YAAU,GAAE,KAAK,YAAU;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,MAAE,KAAK,IAAI,KAAG,KAAK,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,KAAK,EAAC,SAAQA,KAAE,SAAQC,GAAC,GAAE;AAAC,UAAK,EAAC,OAAMC,IAAE,QAAOC,IAAC,IAAEH,IAAE,YAAY;AAAE,SAAK,YAAUA,IAAE,0BAA0B;AAAE,UAAMI,MAAEH,GAAE,QAAQC,IAAEC,GAAC,EAAE;AAAQ,IAAAH,IAAE,gBAAgBI,GAAC,GAAEJ,IAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAEA,IAAE,cAAc,GAAE,GAAE,GAAE,CAAC,GAAEA,IAAE,MAAMA,IAAE,GAAG,gBAAgB;AAAA,EAAC;AAAA,EAAC,OAAO,EAAC,SAAQA,IAAC,GAAE;AAAC,IAAAA,IAAE,gBAAgB,KAAK,SAAS,GAAE,KAAK,YAAU;AAAA,EAAI;AAAA,EAAC,KAAKA,KAAEE,IAAEC,MAAE,IAAEE,IAAE;AAAC,SAAK,SAASL,KAAEE,IAAEC,GAAC;AAAA,EAAC;AAAA,EAAC,MAAM,SAAS,EAAC,SAAQH,KAAE,OAAMC,IAAE,YAAWC,GAAC,GAAEH,IAAEO,IAAE;AAAC,UAAMC,KAAEP,IAAE,0BAA0B,GAAEQ,KAAEP,GAAE,KAAK,CAAC,IAAEC,IAAEO,KAAE,KAAK,MAAMH,KAAEJ,EAAC,GAAEQ,KAAED,KAAE,GAAEE,KAAEF,KAAE;AAAE,SAAK,cAAcA,EAAC,GAAEV,GAAE,QAAS,OAAMC,KAAEC,OAAI;AAAC,YAAMK,KAAE,oBAAI,OAAIM,KAAE,KAAK,MAAMX,GAAE,IAAEC,KAAEO,KAAE,CAAC,GAAEI,KAAE,KAAK,MAAML,KAAEP,GAAE,IAAEC,KAAEO,KAAE,CAAC;AAAE,YAAMF,GAAE,gBAAgBK,IAAEC,IAAEJ,IAAEA,IAAE,EAAE,MAAK,EAAE,eAAc,KAAK,IAAI;AAAE,eAAQP,KAAE,GAAEA,KAAE,KAAK,OAAO,QAAOA,MAAI;AAAC,cAAMF,MAAE,KAAK,OAAOE,EAAC;AAAE,YAAG,eAAaF,OAAG,MAAIA,KAAE;AAAC,gBAAMC,KAAEC,KAAEO,IAAEN,MAAEM,KAAE,KAAK,MAAMP,KAAEO,EAAC,GAAEL,OAAGM,KAAET,OAAIS,KAAET,OAAIU,KAAER,QAAIQ,KAAER,MAAGJ,MAAEO,GAAE,IAAIN,GAAC,IAAEM,GAAE,IAAIN,GAAC,IAAE;AAAW,UAAAM,GAAE,IAAIN,KAAE,KAAK,IAAII,KAAEL,GAAC,CAAC;AAAA,QAAC;AAAA,MAAC;AAAC,YAAMe,KAAE,MAAM,KAAKR,EAAC,EAAE,KAAM,CAACN,KAAEC,OAAID,IAAE,CAAC,IAAEC,GAAE,CAAC,CAAE,EAAE,IAAK,CAAAD,QAAGA,IAAE,CAAC,CAAE;AAAE,MAAAA,IAAE,QAAQc,EAAC,GAAEf,GAAE,OAAOE,EAAC;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,cAAcD,KAAE;AAAC,SAAK,cAAYA,QAAI,KAAK,YAAUA,KAAE,KAAK,OAAK,IAAI,WAAW,IAAEA,MAAEA,GAAC,GAAE,KAAK,SAAO,IAAI,YAAY,KAAK,KAAK,MAAM;AAAA,EAAE;AAAC;;;ACA/lC,IAAMe,KAAE;AAAR,IAAUC,KAAE,CAAC,GAAE,CAAC;AAAhB,IAAkBC,KAAE,CAAC,GAAE,CAAC;AAAxB,IAA0BC,KAAE,CAAC,GAAE,KAAG,KAAG,KAAG,GAAE;AAA1C,IAA4CC,KAAE,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC;AAAE,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,gBAAc,MAAK,KAAK,gBAAc,MAAK,KAAK,YAAU,IAAI,MAAML,EAAC,GAAE,KAAK,SAAOA,IAAE,KAAK,mBAAiB,CAAC,GAAE,GAAE,GAAE,GAAE,EAAE,GAAE,KAAK,QAAM,CAAC,GAAE,CAAC,GAAE,KAAK,eAAa,EAAC,oBAAmB,EAAC,QAAO,sBAAqB,QAAO,4CAA2C,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC,GAAE,cAAa,EAAC,QAAO,sBAAqB,QAAO,sCAAqC,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC,GAAE,WAAU,EAAC,QAAO,sBAAqB,QAAO,mCAAkC,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC,GAAE,MAAK,EAAC,QAAO,sBAAqB,QAAO,wBAAuB,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,QAAG,KAAK,QAAM,EAAE,KAAK,KAAK,GAAE,KAAK,gBAAc,EAAE,KAAK,aAAa,GAAE,KAAK,gBAAc,EAAE,KAAK,aAAa,GAAE,KAAK,WAAU;AAAC,eAAQM,MAAE,GAAEA,MAAE,KAAK,QAAOA,MAAI,MAAK,UAAUA,GAAC,MAAI,KAAK,UAAUA,GAAC,EAAE,WAAW,QAAQ,GAAE,KAAK,UAAUA,GAAC,EAAE,SAAS,QAAQ;AAAG,WAAK,YAAU;AAAA,IAAI;AAAA,EAAC;AAAA,EAAC,KAAKA,KAAEC,IAAEC,KAAE;AAAC,UAAK,EAAC,OAAMC,KAAE,QAAOC,GAAC,IAAEH,IAAE,EAAC,SAAQI,IAAE,SAAQC,GAAC,IAAEN,KAAE,EAAC,iBAAgBO,GAAC,IAAED,IAAEE,KAAEH,GAAE,IAAGN,KAAE,KAAK,cAAa,EAAC,UAASU,IAAE,QAAOC,IAAE,WAAUC,GAAC,IAAET;AAAE,SAAK,UAAQ,KAAK,QAAM,IAAIE,GAAEC,IAAE,CAAC,IAAG,IAAG,GAAE,IAAG,IAAG,GAAE,GAAE,CAAC,CAAC,IAAG,KAAK,yBAAyBL,KAAEG,KAAEC,EAAC,GAAEC,GAAE,sBAAsB,KAAE,GAAEA,GAAE,mBAAmB,IAAE,GAAEA,GAAE,iBAAiB,EAAE,KAAI,EAAE,mBAAmB,GAAEA,GAAE,oBAAoB,CAAC;AAAE,UAAMO,KAAE,KAAK;AAAM,IAAAA,GAAE,KAAK,GAAEP,GAAE,gBAAgB,KAAK,aAAa;AAAE,UAAMQ,KAAEN,GAAE,WAAWR,GAAE,kBAAkB;AAAE,IAAAM,GAAE,WAAWQ,EAAC,GAAER,GAAE,YAAYJ,GAAE,cAAa,CAAC,GAAEY,GAAE,aAAa,aAAY,CAAC,GAAEA,GAAE,cAAc,kBAAiB,CAAC,GAAE,GAAE,CAAC,CAAC,GAAEA,GAAE,aAAa,oBAAmB,CAAC,GAAEA,GAAE,aAAa,yBAAwBF,EAAC,GAAEE,GAAE,aAAa,iBAAgB,IAAG;AAAE,UAAMC,KAAE,CAAC,KAAK,MAAMX,MAAE,CAAC,GAAE,KAAK,MAAMC,KAAE,CAAC,CAAC;AAAE,IAAAC,GAAE,YAAY,GAAE,GAAES,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAET,GAAE,cAAc,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAMG,GAAE,gBAAgB,GAAEI,GAAE,KAAK,GAAEP,GAAE,mBAAmB,KAAE;AAAE,QAAIU,KAAE,KAAK,cAAc;AAAa,aAAQC,KAAE,GAAEA,KAAE,KAAK,QAAOA,MAAI;AAAC,YAAMhB,MAAEO,GAAE,WAAWR,GAAE,cAAa,CAAC,EAAC,MAAK,UAAS,OAAM,KAAK,iBAAiBiB,EAAC,EAAC,CAAC,CAAC;AAAE,MAAAX,GAAE,WAAWL,GAAC,GAAEK,GAAE,YAAYU,IAAEC,KAAE,CAAC,GAAEhB,IAAE,aAAa,kBAAiBgB,KAAE,CAAC,GAAEhB,IAAE,cAAc,aAAYc,EAAC,GAAEd,IAAE,cAAc,eAAcL,EAAC,GAAEU,GAAE,YAAY,GAAE,GAAES,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAE,YAAMG,KAAE,KAAK,UAAUD,EAAC;AAAE,MAAAX,GAAE,gBAAgBY,GAAE,UAAU,GAAEL,GAAE,KAAK,GAAEG,KAAEE,GAAE,WAAW,cAAaZ,GAAE,gBAAgBY,GAAE,QAAQ,GAAEZ,GAAE,YAAYU,IAAEC,KAAE,CAAC,GAAEhB,IAAE,cAAc,eAAcJ,EAAC,GAAEgB,GAAE,KAAK,GAAEG,KAAEE,GAAE,SAAS,cAAaH,GAAE,CAAC,IAAE,KAAK,MAAMA,GAAE,CAAC,IAAE,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,MAAMA,GAAE,CAAC,IAAE,CAAC;AAAA,IAAC;AAAC,IAAAT,GAAE,YAAY,GAAE,GAAEF,KAAEC,EAAC;AAAE,UAAMc,KAAEX,GAAE,WAAWR,GAAE,WAAU,CAAC,EAAC,MAAK,WAAU,OAAML,GAAC,CAAC,CAAC;AAAE,IAAAW,GAAE,gBAAgB,KAAK,aAAa,GAAEA,GAAE,WAAWa,EAAC,GAAEA,GAAE,aAAa,mBAAkBT,EAAC,GAAES,GAAE,aAAa,iBAAgBR,EAAC,GAAEQ,GAAE,cAAc,kBAAiBrB,EAAC,GAAEqB,GAAE,cAAc,qBAAoBpB,EAAC,GAAEO,GAAE,YAAY,KAAK,UAAU,CAAC,EAAE,SAAS,cAAa,CAAC,GAAEa,GAAE,aAAa,kBAAiB,CAAC,GAAEb,GAAE,YAAY,KAAK,UAAU,CAAC,EAAE,SAAS,cAAa,CAAC,GAAEa,GAAE,aAAa,kBAAiB,CAAC,GAAEb,GAAE,YAAY,KAAK,UAAU,CAAC,EAAE,SAAS,cAAa,CAAC,GAAEa,GAAE,aAAa,kBAAiB,CAAC,GAAEb,GAAE,YAAY,KAAK,UAAU,CAAC,EAAE,SAAS,cAAa,CAAC,GAAEa,GAAE,aAAa,kBAAiB,CAAC,GAAEb,GAAE,YAAY,KAAK,UAAU,CAAC,EAAE,SAAS,cAAa,CAAC,GAAEa,GAAE,aAAa,kBAAiB,CAAC,GAAEN,GAAE,KAAK,GAAEP,GAAE,gBAAgBJ,EAAC,GAAEI,GAAE,mBAAmB,IAAE;AAAE,UAAMc,KAAEZ,GAAE,WAAWR,GAAE,IAAI;AAAE,IAAAM,GAAE,WAAWc,EAAC,GAAEd,GAAE,YAAY,KAAK,cAAc,cAAa,CAAC,GAAEc,GAAE,aAAa,aAAY,CAAC,GAAEd,GAAE,iBAAiB,EAAE,KAAI,EAAE,GAAG,GAAEO,GAAE,KAAK,GAAEA,GAAE,OAAO,GAAEP,GAAE,iBAAiB,EAAE,KAAI,EAAE,mBAAmB,GAAEA,GAAE,sBAAsB,IAAE;AAAA,EAAC;AAAA,EAAC,yBAAyBL,KAAEgB,IAAEC,IAAE;AAAC,UAAK,EAAC,SAAQvB,GAAC,IAAEM;AAAE,QAAG,KAAK,iBAAe,KAAK,MAAM,CAAC,MAAIgB,MAAG,KAAK,MAAM,CAAC,MAAIC,GAAE;AAAO,SAAK,MAAM,CAAC,IAAED,IAAE,KAAK,MAAM,CAAC,IAAEC;AAAE,UAAMtB,KAAE,CAAC,KAAK,MAAMqB,KAAE,CAAC,GAAE,KAAK,MAAMC,KAAE,CAAC,CAAC;AAAE,SAAK,gBAAc,KAAK,cAAc,OAAOD,IAAEC,EAAC,IAAE,KAAK,gBAAc,IAAI,EAAEvB,IAAE,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,MAAK,OAAMsB,IAAE,QAAOC,GAAC,CAAC,GAAE,KAAK,gBAAc,KAAK,cAAc,OAAOtB,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,IAAE,KAAK,gBAAc,IAAI,EAAED,IAAE,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,MAAK,OAAMC,GAAE,CAAC,GAAE,QAAOA,GAAE,CAAC,EAAC,GAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,gBAAe,EAAE,MAAK,UAAS,EAAE,eAAc,UAASyB,GAAE,eAAc,cAAa,EAAE,QAAO,SAAQ,OAAG,OAAMzB,GAAE,CAAC,GAAE,QAAOA,GAAE,CAAC,EAAC,CAAC;AAAE,aAAQC,KAAE,GAAEA,KAAE,KAAK,QAAOA,KAAI,MAAK,UAAUA,EAAC,KAAG,KAAK,UAAUA,EAAC,EAAE,WAAW,OAAOD,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,GAAE,KAAK,UAAUC,EAAC,EAAE,SAAS,OAAOD,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,KAAG,KAAK,UAAUC,EAAC,IAAE,EAAC,YAAW,IAAI,EAAEF,IAAE,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,MAAK,OAAMC,GAAE,CAAC,GAAE,QAAOA,GAAE,CAAC,EAAC,GAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,gBAAe,EAAE,MAAK,UAAS,EAAE,eAAc,UAASyB,GAAE,eAAc,cAAa,EAAE,QAAO,SAAQ,OAAG,OAAMzB,GAAE,CAAC,GAAE,QAAOA,GAAE,CAAC,EAAC,CAAC,GAAE,UAAS,IAAI,EAAED,IAAE,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,MAAK,OAAMC,GAAE,CAAC,GAAE,QAAOA,GAAE,CAAC,EAAC,GAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,gBAAe,EAAE,MAAK,UAAS,EAAE,eAAc,UAASyB,GAAE,eAAc,cAAa,EAAE,QAAO,SAAQ,OAAG,OAAMzB,GAAE,CAAC,GAAE,QAAOA,GAAE,CAAC,EAAC,CAAC,EAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,MAAMA,GAAE,CAAC,IAAE,CAAC,GAAEA,GAAE,CAAC,IAAE,KAAK,MAAMA,GAAE,CAAC,IAAE,CAAC;AAAA,EAAC;AAAC;;;ACAl4J,IAAM0B,KAAE,CAAC,GAAE,CAAC;AAAZ,IAAcC,KAAE,CAAC,GAAE,CAAC;AAAE,IAAM,IAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,WAAS,MAAK,KAAK,QAAM,CAAC,GAAE,CAAC,GAAE,KAAK,eAAa,EAAC,cAAa,EAAC,QAAO,sBAAqB,QAAO,qCAAoC,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC,GAAE,YAAW,EAAC,QAAO,sBAAqB,QAAO,oCAAmC,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC,GAAE,MAAK,EAAC,QAAO,sBAAqB,QAAO,wBAAuB,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,aAAW,KAAK,SAAS,QAAQ,GAAE,KAAK,WAAS;AAAA,EAAK;AAAA,EAAC,KAAKC,KAAEC,KAAEC,IAAE;AAAC,UAAK,EAAC,SAAQC,GAAC,IAAEH,KAAE,EAAC,MAAKI,IAAE,QAAOC,GAAC,IAAEH;AAAE,QAAG,MAAIG,GAAE;AAAO,SAAK,yBAAyBL,GAAC,GAAE,KAAK,UAAQ,KAAK,QAAM,IAAIK,GAAEF,IAAE,CAAC,IAAG,IAAG,GAAE,IAAG,IAAG,GAAE,GAAE,CAAC,CAAC;AAAG,UAAMG,MAAE,KAAK;AAAM,IAAAA,IAAE,KAAK,GAAE,WAASF,KAAE,KAAK,cAAcJ,KAAEC,KAAEI,EAAC,IAAE,KAAK,YAAYL,KAAEC,GAAC,GAAEK,IAAE,OAAO;AAAA,EAAC;AAAA,EAAC,cAAcC,IAAEN,KAAEC,IAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,OAAMC,IAAE,SAAQC,IAAE,YAAWC,IAAC,IAAEC,IAAE,EAAC,MAAKC,GAAC,IAAEJ,IAAE,EAAC,iBAAgBK,GAAC,IAAEJ,IAAEK,KAAE,KAAK,cAAaC,KAAE,KAAK,OAAMC,KAAE,CAAC,KAAK,MAAMN,MAAEE,GAAE,CAAC,CAAC,GAAE,KAAK,MAAMF,MAAEE,GAAE,CAAC,CAAC,CAAC,GAAEK,KAAE,KAAK,UAASC,KAAEL,GAAE,WAAWC,GAAE,cAAa,CAAC,EAAC,MAAK,UAAS,OAAM,KAAK,KAAKR,EAAC,EAAC,CAAC,CAAC;AAAE,IAAAC,GAAE,WAAWW,EAAC,GAAEX,GAAE,mBAAmB,KAAE,GAAEA,GAAE,gBAAgBU,EAAC,GAAEV,GAAE,YAAYF,IAAE,cAAa,CAAC,GAAEa,GAAE,aAAa,kBAAiB,CAAC,GAAEA,GAAE,cAAc,aAAYF,EAAC,GAAEE,GAAE,cAAc,eAAchB,EAAC,GAAEgB,GAAE,aAAa,WAAUZ,EAAC,GAAES,GAAE,KAAK,GAAER,GAAE,gBAAgBF,GAAC,GAAEE,GAAE,oBAAoB,CAAC,GAAEA,GAAE,sBAAsB,KAAE,GAAEA,GAAE,qBAAqB,KAAE,GAAEA,GAAE,oBAAoB,KAAE,GAAEA,GAAE,YAAYU,MAAA,gBAAAA,GAAG,cAAa,CAAC,GAAEC,GAAE,aAAa,kBAAiB,CAAC,GAAEA,GAAE,cAAc,eAAcf,EAAC,GAAEY,GAAE,KAAK,GAAER,GAAE,mBAAmB,IAAE,GAAEA,GAAE,iBAAiB,EAAE,KAAI,EAAE,mBAAmB,GAAEA,GAAE,sBAAsB,IAAE;AAAA,EAAC;AAAA,EAAC,YAAYI,IAAEN,KAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,SAAQC,GAAC,IAAEI,IAAE,EAAC,iBAAgBH,GAAC,IAAED,IAAEE,KAAE,KAAK,cAAaC,MAAE,KAAK,OAAME,KAAE,KAAK;AAAS,IAAAN,GAAE,gBAAgBM,EAAC;AAAE,UAAMC,KAAEL,GAAE,WAAWC,GAAE,UAAU;AAAE,IAAAH,GAAE,WAAWO,EAAC,GAAEP,GAAE,mBAAmB,KAAE,GAAEA,GAAE,YAAYD,IAAE,cAAa,CAAC,GAAEQ,GAAE,aAAa,kBAAiB,CAAC,GAAEH,IAAE,KAAK,GAAEJ,GAAE,gBAAgBD,GAAC,GAAEC,GAAE,oBAAoB,CAAC,GAAEA,GAAE,sBAAsB,KAAE,GAAEA,GAAE,qBAAqB,KAAE,GAAEA,GAAE,oBAAoB,KAAE,GAAEA,GAAE,mBAAmB,IAAE;AAAE,UAAMJ,KAAEM,GAAE,WAAWC,GAAE,IAAI;AAAE,IAAAH,GAAE,WAAWJ,EAAC,GAAEI,GAAE,YAAYM,MAAA,gBAAAA,GAAG,cAAa,CAAC,GAAEV,GAAE,aAAa,aAAY,CAAC,GAAEI,GAAE,iBAAiB,EAAE,KAAI,EAAE,mBAAmB,GAAEI,IAAE,KAAK;AAAA,EAAC;AAAA,EAAC,yBAAyBC,IAAE;AAAC,UAAK,EAAC,SAAQP,KAAE,OAAMF,IAAE,YAAWC,GAAC,IAAEQ,IAAE,EAAC,MAAKG,GAAC,IAAEZ,IAAEa,KAAE,KAAK,MAAMZ,KAAEW,GAAE,CAAC,CAAC,GAAEE,KAAE,KAAK,MAAMb,KAAEW,GAAE,CAAC,CAAC;AAAE,SAAK,YAAU,KAAK,MAAM,CAAC,MAAIC,MAAG,KAAK,MAAM,CAAC,MAAIC,OAAI,KAAK,MAAM,CAAC,IAAED,IAAE,KAAK,MAAM,CAAC,IAAEC,IAAE,KAAK,WAAS,KAAK,SAAS,OAAOD,IAAEC,EAAC,IAAE,KAAK,WAAS,IAAI,EAAEZ,KAAE,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,MAAK,OAAMW,IAAE,QAAOC,GAAC,GAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,gBAAe,EAAE,MAAK,UAAS,EAAE,eAAc,UAASG,GAAE,eAAc,cAAa,EAAE,QAAO,SAAQ,OAAG,OAAMJ,IAAE,QAAOC,GAAC,CAAC;AAAA,EAAE;AAAC;;;ACA3pF,IAAMI,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,mBAAiB,MAAK,KAAK,QAAM,CAAC,GAAE,CAAC,GAAE,KAAK,eAAa,EAAC,QAAO,sBAAqB,QAAO,gCAA+B,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,mBAAiB,EAAE,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,KAAKC,IAAEC,KAAEC,IAAE;AAAC,UAAK,EAAC,OAAMC,IAAE,QAAOC,GAAC,IAAEH;AAAE,SAAK,yBAAyBD,IAAEG,IAAEC,EAAC;AAAE,UAAK,EAAC,SAAQC,KAAE,SAAQC,GAAC,IAAEN,IAAE,EAAC,iBAAgBO,GAAC,IAAED,IAAEP,KAAE,KAAK,cAAaS,KAAE,KAAK,OAAMC,KAAEP,GAAE;AAAY,IAAAM,GAAE,KAAK;AAAE,UAAME,KAAE,KAAK;AAAiB,IAAAL,IAAE,gBAAgBJ,GAAC,GAAEA,IAAE,cAAc,GAAE,GAAEE,IAAEC,IAAE,GAAE,GAAEM,EAAC,GAAEL,IAAE,mBAAmB,KAAE,GAAEA,IAAE,sBAAsB,KAAE;AAAE,UAAMM,KAAEJ,GAAE,WAAWR,EAAC;AAAE,IAAAM,IAAE,WAAWM,EAAC,GAAEN,IAAE,YAAYK,IAAE,CAAC,GAAEC,GAAE,oBAAoB,kBAAiBF,EAAC,GAAEE,GAAE,aAAa,kBAAiB,CAAC,GAAEH,GAAE,KAAK,GAAEH,IAAE,mBAAmB,IAAE,GAAEA,IAAE,iBAAiB,EAAE,KAAI,EAAE,mBAAmB,GAAEA,IAAE,sBAAsB,IAAE,GAAEG,GAAE,OAAO;AAAA,EAAC;AAAA,EAAC,yBAAyBR,IAAEY,KAAEb,IAAE;AAAC,UAAK,EAAC,SAAQS,GAAC,IAAER;AAAE,SAAK,oBAAkB,KAAK,MAAM,CAAC,MAAIY,OAAG,KAAK,MAAM,CAAC,MAAIb,OAAI,KAAK,MAAM,CAAC,IAAEa,KAAE,KAAK,MAAM,CAAC,IAAEb,IAAE,KAAK,mBAAiB,KAAK,iBAAiB,OAAOa,KAAEb,EAAC,IAAE,KAAK,mBAAiB,IAAIc,GAAEL,IAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,gBAAe,EAAE,MAAK,UAAS,EAAE,eAAc,UAASM,GAAE,eAAc,cAAa,EAAE,QAAO,SAAQ,OAAG,OAAMF,KAAE,QAAOb,GAAC,CAAC,GAAE,KAAK,UAAQ,KAAK,QAAM,IAAIO,GAAEE,IAAE,CAAC,IAAG,IAAG,GAAE,IAAG,IAAG,GAAE,GAAE,CAAC,CAAC;AAAA,EAAG;AAAC;;;ACApkC,IAAMO,KAAE,CAAC,GAAE,CAAC;AAAZ,IAAcC,KAAE,CAAC,GAAE,CAAC;AAAE,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,mBAAiB,MAAK,KAAK,qBAAmB,MAAK,KAAK,mBAAiB,MAAK,KAAK,QAAM,CAAC,GAAE,CAAC,GAAE,KAAK,QAAM,MAAK,KAAK,eAAa,EAAC,MAAK,EAAC,QAAO,sBAAqB,QAAO,qCAAoC,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC,GAAE,WAAU,EAAC,QAAO,sBAAqB,QAAO,yCAAwC,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC,GAAE,MAAK,EAAC,QAAO,sBAAqB,QAAO,wBAAuB,YAAW,oBAAI,IAAI,CAAC,CAAC,cAAa,CAAC,CAAC,CAAC,EAAC,EAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,SAAK,mBAAiB,EAAE,KAAK,gBAAgB,GAAE,KAAK,qBAAmB,EAAE,KAAK,kBAAkB,GAAE,KAAK,mBAAiB,EAAE,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,KAAKC,IAAEC,IAAEC,KAAE;AAAC,UAAK,EAAC,SAAQC,IAAE,OAAMC,IAAE,SAAQC,GAAC,IAAEL,IAAE,EAAC,iBAAgBM,GAAC,IAAED,IAAEE,KAAE,KAAK,cAAaC,KAAEP,GAAE,OAAMQ,KAAER,GAAE,QAAOF,KAAE,CAAC,KAAK,MAAMS,EAAC,GAAE,KAAK,MAAMC,EAAC,CAAC,GAAE,EAAC,YAAWC,IAAE,SAAQC,IAAE,SAAQC,IAAE,OAAMC,GAAC,IAAEX,KAAEY,KAAE,CAAC,EAAEH,EAAC,GAAE,EAAEC,EAAC,CAAC;AAAE,SAAK,yBAAyBZ,IAAEQ,IAAEC,IAAEV,EAAC;AAAE,UAAMgB,KAAE,KAAK,oBAAmBC,KAAE,KAAK;AAAiB,IAAAb,GAAE,oBAAoB,CAAC,GAAEA,GAAE,sBAAsB,KAAE,GAAEA,GAAE,qBAAqB,KAAE,GAAEA,GAAE,oBAAoB,KAAE;AAAE,UAAMc,KAAE,KAAK;AAAiB,IAAAhB,GAAE,cAAc,GAAE,GAAEO,IAAEC,IAAE,GAAE,GAAEQ,EAAC,GAAE,KAAK,UAAQ,KAAK,QAAM,IAAIZ,GAAEF,IAAE,CAAC,IAAG,IAAG,GAAE,IAAG,IAAG,GAAE,GAAE,CAAC,CAAC,IAAGA,GAAE,YAAY,GAAE,GAAEJ,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC;AAAE,UAAMmB,KAAE,KAAK;AAAM,IAAAA,GAAE,KAAK,GAAEf,GAAE,mBAAmB,KAAE;AAAE,UAAMgB,KAAEb,GAAE,WAAWC,GAAE,MAAK,CAAC,EAAC,MAAK,UAAS,OAAM,KAAK,KAAKG,EAAC,EAAC,CAAC,CAAC;AAAE,IAAAP,GAAE,WAAWgB,EAAC,GAAEhB,GAAE,gBAAgBY,EAAC,GAAEZ,GAAE,YAAYF,GAAE,cAAa,CAAC,GAAEkB,GAAE,aAAa,kBAAiB,CAAC,GAAEA,GAAE,cAAc,aAAYpB,EAAC,GAAEoB,GAAE,cAAc,eAActB,EAAC,GAAEsB,GAAE,aAAa,WAAUT,EAAC,GAAEQ,GAAE,KAAK,GAAEf,GAAE,gBAAgBa,EAAC,GAAEb,GAAE,YAAYY,MAAA,gBAAAA,GAAG,cAAa,CAAC,GAAEI,GAAE,aAAa,kBAAiB,CAAC,GAAEA,GAAE,cAAc,eAAcrB,EAAC,GAAEoB,GAAE,KAAK,GAAEf,GAAE,gBAAgBF,EAAC,GAAEE,GAAE,YAAY,GAAE,GAAEK,IAAEC,EAAC;AAAE,UAAMW,KAAEd,GAAE,WAAWC,GAAE,SAAS;AAAE,IAAAJ,GAAE,WAAWiB,EAAC,GAAEjB,GAAE,YAAYa,MAAA,gBAAAA,GAAG,cAAa,CAAC,GAAEI,GAAE,aAAa,iBAAgB,CAAC,GAAEjB,GAAE,YAAYc,IAAE,CAAC,GAAEG,GAAE,aAAa,qBAAoB,CAAC,GAAEA,GAAE,cAAc,iBAAgB,CAACP,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAE,MAAKA,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAE,MAAKA,GAAE,CAAC,KAAGA,GAAE,CAAC,IAAE,MAAKA,GAAE,CAAC,CAAC,CAAC,GAAEO,GAAE,oBAAoB,qBAAoBhB,GAAE,WAAW,GAAEgB,GAAE,cAAc,kBAAiBN,EAAC,GAAEI,GAAE,KAAK,GAAEf,GAAE,mBAAmB,IAAE,GAAEA,GAAE,sBAAsB,IAAE,GAAEA,GAAE,iBAAiB,EAAE,KAAI,EAAE,mBAAmB,GAAEe,GAAE,OAAO;AAAA,EAAC;AAAA,EAAC,yBAAyBlB,IAAEqB,KAAEC,KAAEC,IAAE;AAAC,UAAK,EAAC,SAAQ1B,GAAC,IAAEG;AAAE,SAAK,sBAAoB,KAAK,MAAM,CAAC,MAAIqB,OAAG,KAAK,MAAM,CAAC,MAAIC,QAAI,KAAK,MAAM,CAAC,IAAED,KAAE,KAAK,MAAM,CAAC,IAAEC,KAAE,KAAK,qBAAmB,KAAK,mBAAmB,OAAOC,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,IAAE,KAAK,qBAAmB,IAAI,EAAE1B,IAAE,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,MAAK,OAAM0B,GAAE,CAAC,GAAE,QAAOA,GAAE,CAAC,EAAC,GAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,gBAAe,EAAE,MAAK,UAAS,EAAE,eAAc,UAASC,GAAE,eAAc,cAAa,EAAE,QAAO,SAAQ,OAAG,OAAMD,GAAE,CAAC,GAAE,QAAOA,GAAE,CAAC,EAAC,CAAC,GAAE,KAAK,mBAAiB,KAAK,iBAAiB,OAAOA,GAAE,CAAC,GAAEA,GAAE,CAAC,CAAC,IAAE,KAAK,mBAAiB,IAAI,EAAE1B,IAAE,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,MAAK,OAAM0B,GAAE,CAAC,GAAE,QAAOA,GAAE,CAAC,EAAC,GAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,gBAAe,EAAE,MAAK,UAAS,EAAE,eAAc,UAASC,GAAE,eAAc,cAAa,EAAE,QAAO,SAAQ,OAAG,OAAMD,GAAE,CAAC,GAAE,QAAOA,GAAE,CAAC,EAAC,CAAC,GAAE,KAAK,mBAAiB,KAAK,iBAAiB,OAAOF,KAAEC,GAAC,IAAE,KAAK,mBAAiB,IAAIR,GAAEjB,IAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,gBAAe,EAAE,MAAK,UAAS,EAAE,eAAc,UAAS2B,GAAE,eAAc,cAAa,EAAE,QAAO,SAAQ,OAAG,OAAMH,KAAE,QAAOC,IAAC,CAAC;AAAA,EAAE;AAAC;;;ACAj8G,IAAMG,MAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,QAAM,CAAC,GAAE,CAAC,GAAE,KAAK,mBAAiB;AAAA,EAAI;AAAA,EAAC,UAAS;AAAC,SAAK,mBAAiB,EAAE,KAAK,gBAAgB;AAAA,EAAC;AAAA,EAAC,KAAKC,IAAEC,KAAEC,IAAE;AAAC,UAAK,EAAC,OAAMC,IAAE,QAAOC,GAAC,IAAEH;AAAE,SAAK,yBAAyBD,IAAEG,IAAEC,EAAC;AAAE,UAAK,EAAC,SAAQC,IAAE,SAAQN,IAAC,IAAEC,IAAE,EAAC,QAAOM,GAAC,IAAEJ,IAAEK,KAAEF,GAAE,IAAGG,KAAE,KAAK;AAAiB,IAAAH,GAAE,gBAAgBJ,GAAC,GAAEA,IAAE,cAAc,GAAE,GAAEE,IAAEC,IAAE,GAAE,GAAEI,EAAC,GAAEH,GAAE,mBAAmB,IAAE,GAAEA,GAAE,sBAAsB,KAAE,GAAEA,GAAE,oBAAoB,KAAE,GAAEA,GAAE,cAAc,GAAE,GAAE,GAAE,CAAC,GAAEA,GAAE,MAAME,GAAE,gBAAgB,GAAER,IAAE,YAAYM,IAAEG,IAAE,EAAE,SAAQF,EAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBN,IAAED,KAAEO,IAAE;AAAC,UAAK,EAAC,SAAQC,GAAC,IAAEP;AAAE,SAAK,oBAAkB,KAAK,MAAM,CAAC,MAAID,OAAG,KAAK,MAAM,CAAC,MAAIO,OAAI,KAAK,MAAM,CAAC,IAAEP,KAAE,KAAK,MAAM,CAAC,IAAEO,IAAE,KAAK,mBAAiB,KAAK,iBAAiB,OAAOP,KAAEO,EAAC,IAAE,KAAK,mBAAiB,IAAIG,GAAEF,IAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,gBAAe,EAAE,MAAK,UAAS,EAAE,eAAc,UAASG,GAAE,eAAc,cAAa,EAAE,SAAQ,SAAQ,OAAG,OAAMX,KAAE,QAAOO,GAAC,CAAC;AAAA,EAAE;AAAC;;;ACAr7B,SAASK,GAAEC,KAAE;AAAC,UAAOA,KAAE;AAAA,IAAC,KAAI;AAAA,IAAQ,KAAI;AAAA,IAAO,KAAI;AAAA,IAAU,KAAI;AAAc,aAAOA;AAAA,IAAE;AAAQ,aAAM;AAAA,EAAU;AAAC;AAAC,IAAMC,KAAE,EAAC,UAAS,MAAI,IAAIC,MAAE,MAAK,MAAI,IAAI,KAAE,OAAM,MAAI,IAAIC,MAAE,SAAQ,MAAI,IAAIH,OAAE,eAAc,MAAI,IAAII,KAAC;AAAE,IAAMC,KAAN,MAAO;AAAA,EAAC,cAAa;AAAC,SAAK,aAAW,oBAAI;AAAA,EAAG;AAAA,EAAC,UAAS;AAAC,SAAK,WAAW,QAAS,CAAAL,QAAGA,IAAE,QAAQ,CAAE,GAAE,KAAK,WAAW,MAAM;AAAA,EAAC;AAAA,EAAC,yBAAyBA,KAAE;AAAC,QAAG,CAACA,OAAG,MAAIA,IAAE,OAAO,QAAM,CAAC;AAAE,UAAMM,KAAE,CAAC;AAAE,eAAUC,OAAKP,KAAE;AAAC,YAAMA,MAAED,GAAEQ,IAAE,IAAI;AAAE,UAAIC,KAAE,KAAK,WAAW,IAAIR,GAAC;AAAE,MAAAQ,OAAIA,KAAEP,GAAED,GAAC,EAAE,GAAE,KAAK,WAAW,IAAIA,KAAEQ,EAAC,IAAGF,GAAE,KAAK,EAAC,sBAAqBE,IAAE,QAAOD,IAAC,CAAC;AAAA,IAAC;AAAC,WAAOD;AAAA,EAAC;AAAC;;;ACArkB,IAAMG,KAAN,MAAO;AAAA,EAAC,YAAYC,IAAEC,KAAE;AAAC,SAAK,UAAQD,IAAE,KAAK,OAAKC,IAAE,MAAK,KAAK,YAAUA,IAAE,aAAW,EAAE,KAAI,KAAK,YAAUA,IAAE,QAAO,KAAK,UAAQA,IAAE,WAAS,CAAC,GAAE,KAAK,oBAAkBA,IAAE,sBAAoB,MAAI;AAAA,EAAG;AAAA,EAAC,OAAOD,IAAE;AAAC,UAAK,EAAC,SAAQC,KAAE,UAASC,IAAC,IAAEF,IAAEG,KAAE,KAAK,UAAU,GAAEJ,KAAE,KAAK,YAAUC,GAAE;AAAU,QAAGE,IAAE,gBAAgB,KAAK,IAAI,GAAEH,IAAE;AAAC,WAAK,kBAAkB,KAAG,KAAK,UAAUC,IAAEG,EAAC,GAAED,IAAE,cAAc;AAAE,iBAAUA,OAAK,KAAK,SAAQ;AAAC,YAAG,CAACA,IAAE,OAAO,EAAE;AAAS,cAAMH,KAAEG,IAAE,OAAME,KAAEF,IAAE,QAAMA,IAAE,KAAK,GAAEG,KAAEJ,IAAE,YAAY,GAAEK,MAAEL,IAAE,0BAA0B,GAAEM,KAAEP,GAAE;AAAY,aAAK,YAAYA,IAAED,IAAEK,EAAC,GAAE,KAAK,UAAUJ,IAAEG,IAAEJ,GAAE,OAAO,GAAE,KAAK,qBAAqBC,IAAED,IAAEM,IAAEC,KAAEC,IAAEH,EAAC;AAAA,MAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,UAAUJ,IAAEG,IAAEJ,IAAE;AAAC,QAAG,EAAEI,EAAC,EAAE;AAAO,UAAK,EAAC,UAASC,IAAE,SAAQC,GAAC,IAAEL;AAAE,eAAUC,OAAK,KAAK,SAAQ;AAAC,UAAGG,GAAE,iBAAiBH,IAAE,IAAI,GAAE,EAAEA,IAAE,WAAW,GAAE;AAAC,cAAMC,MAAEG,GAAE,YAAY,GAAED,KAAEC,GAAE,0BAA0B,GAAEC,MAAEN,GAAE;AAAY,aAAK,YAAYA,IAAEC,IAAE,WAAW,GAAE,KAAK,eAAeA,KAAED,IAAEG,IAAEJ,EAAC,GAAE,KAAK,qBAAqBC,IAAEC,IAAE,aAAYC,KAAEE,IAAEE,GAAC;AAAA,MAAC,MAAM,MAAK,eAAeL,KAAED,IAAEG,IAAEJ,EAAC;AAAE,MAAAK,GAAE,eAAe;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,eAAeH,KAAEC,KAAEC,IAAEJ,IAAE;AAAC,IAAAA,GAAEI,EAAC,KAAGF,IAAE,aAAaC,KAAEH,EAAC,GAAEE,IAAE,SAASC,KAAEC,IAAEJ,EAAC,KAAGI,GAAE,YAAUF,IAAE,aAAaC,KAAEH,EAAC,GAAEE,IAAE,KAAKC,KAAEC,IAAEJ,EAAC;AAAA,EAAE;AAAA,EAAC,YAAYC,IAAEC,KAAEC,KAAE;AAAC,UAAK,EAAC,UAASC,GAAC,IAAEH;AAAE,IAAAG,GAAE,gBAAgB,KAAK,OAAK,MAAIF,IAAE,IAAI,GAAEA,IAAE,KAAKD,IAAEE,GAAC;AAAE,UAAMH,KAAEE,IAAE,cAAcD,IAAEE,GAAC;AAAE,IAAAF,GAAE,cAAYD;AAAA,EAAC;AAAA,EAAC,qBAAqBC,IAAEC,KAAEC,KAAEC,IAAEJ,IAAEK,IAAE;AAAC,UAAK,EAAC,UAASC,IAAE,SAAQC,IAAC,IAAEN;AAAE,IAAAA,GAAE,cAAYD,IAAEM,GAAE,iBAAiBJ,IAAE,IAAI,GAAEA,IAAE,KAAKD,IAAEI,EAAC,GAAEH,IAAE,OAAOD,IAAEI,EAAC,GAAEE,IAAE,gBAAgBH,EAAC;AAAE,UAAK,EAAC,GAAEI,IAAE,GAAEC,IAAE,OAAMC,IAAE,QAAOC,GAAC,IAAER;AAAE,IAAAI,IAAE,YAAYC,IAAEC,IAAEC,IAAEC,EAAC,GAAEL,GAAE,eAAe,GAAEA,GAAE,cAAc;AAAA,EAAC;AAAC;;;ACArQ,SAASM,GAAEC,IAAEC,KAAE;AAAC,UAAOD,IAAE;AAAA,IAAC,KAAKE,GAAE;AAAK,aAAOC,GAAE;AAAA,IAAK,KAAKD,GAAE;AAAK,aAAOC,GAAE;AAAA,IAAK,KAAKD,GAAE;AAAM,aAAOC,GAAE;AAAA,IAAM,KAAKD,GAAE;AAAK,aAAOD,QAAI,EAAE,cAAYE,GAAE,aAAWA,GAAE;AAAA,IAAK,KAAKD,GAAE;AAAO,cAAOD,KAAE;AAAA,QAAC,KAAK,EAAE;AAAQ,iBAAOE,GAAE;AAAA,QAAQ,KAAK,EAAE;AAAU,iBAAOA,GAAE;AAAA,QAAS;AAAQ,iBAAOA,GAAE;AAAA,MAAM;AAAA,EAAC;AAAC;AAAC,IAAMC,KAAN,MAAO;AAAA,EAAC,YAAYJ,IAAEC,KAAEI,IAAE;AAAC,SAAK,UAAQL,IAAE,KAAK,gBAAc,IAAIM,MAAE,KAAK,2BAAyB,IAAIC,MAAE,KAAK,0BAAwB,OAAG,KAAK,cAAY,oBAAI,OAAI,KAAK,aAAW,MAAK,KAAK,cAAY,MAAK,KAAK,WAAS,MAAK,KAAK,sBAAoB,IAAIC,MAAE,KAAK,eAAa,IAAI,KAAE,KAAK,cAAY,MAAK,KAAK,QAAM,MAAK,KAAK,WAAS,CAAC,GAAE,KAAK,gBAAc,MAAK,KAAK,UAAQ,EAAC,WAAU,IAAIC,MAAE,SAAQ,IAAIC,MAAE,YAAW,IAAIC,MAAE,WAAU,IAAIH,MAAE,cAAa,IAAII,GAAE,QAAQ,GAAE,eAAc,IAAIA,GAAE,SAAS,EAAC,GAAE,KAAK,kBAAgB,IAAIC,GAAEb,EAAC,GAAE,KAAK,iBAAe,IAAIc,GAAEb,KAAEI,IAAEL,GAAE,SAAOY,GAAE,MAAM,GAAE,KAAK,uBAAqB,IAAID,GAAEX,IAAEC,GAAC,GAAE,KAAK,kBAAgB,IAAIU;AAAA,EAAC;AAAA,EAAC,IAAI,6BAA4B;AAAC,WAAO,KAAK;AAAA,EAAmB;AAAA,EAAC,kBAAiB;AAAC,WAAO,KAAK;AAAA,EAAa;AAAA,EAAC,gBAAgBX,IAAE;AAAC,SAAK,gBAAcA;AAAA,EAAC;AAAA,EAAC,QAAQA,IAAEC,KAAE;AAAC,QAAGD,OAAI,KAAK,cAAYC,QAAI,KAAK,aAAY;AAAC,UAAG,KAAK,aAAWD,IAAE,KAAK,cAAYC,KAAE,KAAK,OAAM;AAAC,mBAAUI,MAAK,KAAK,MAAM,MAAK,MAAMA,EAAC,EAAE,OAAOL,IAAEC,GAAC;AAAE,eAAO,KAAK;AAAA,MAAK;AAAC,YAAMI,KAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,UAAS,EAAE,eAAc,cAAa,EAAE,SAAQ,UAASU,GAAE,eAAc,OAAMf,IAAE,QAAOC,IAAC,GAAEW,MAAE,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,4BAA2B,GAAED,KAAE,IAAIN,GAAE,KAAK,SAAQ,EAAC,OAAML,IAAE,QAAOC,KAAE,gBAAe,EAAE,cAAa,CAAC;AAAE,WAAK,cAAYU,IAAE,KAAK,QAAM,EAAC,QAAO,IAAI,EAAE,KAAK,SAAQC,KAAEP,IAAEM,EAAC,GAAE,OAAM,IAAI,EAAE,KAAK,SAAQC,KAAEP,IAAEM,EAAC,GAAE,SAAQ,IAAI,EAAE,KAAK,SAAQC,KAAEP,IAAEM,EAAC,EAAC;AAAA,IAAC;AAAC,WAAO,KAAK;AAAA,EAAK;AAAA,EAAC,WAAWX,IAAEC,KAAE;AAAC,QAAII;AAAE,IAAAA,KAAE,KAAK,SAAS,SAAO,IAAE,KAAK,SAAS,IAAI,IAAE,IAAI,EAAE,KAAK,SAAQ,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,4BAA2B,GAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,UAAS,EAAE,eAAc,cAAa,EAAE,SAAQ,UAASU,GAAE,eAAc,OAAMf,IAAE,QAAOC,IAAC,GAAE,KAAK,WAAW;AAAE,UAAMW,MAAEP,GAAE;AAAW,WAAOO,IAAE,UAAQZ,MAAGY,IAAE,WAASX,OAAGI,GAAE,OAAOL,IAAEC,GAAC,GAAEI;AAAA,EAAC;AAAA,EAAC,WAAWL,IAAE;AAAC,SAAK,SAAS,KAAKA,EAAC;AAAA,EAAC;AAAA,EAAC,yBAAwB;AAAC,WAAO,KAAK;AAAA,EAAW;AAAA,EAAC,mBAAmBC,KAAEI,KAAE,MAAK;AAAC,UAAK,EAAC,OAAMO,KAAE,QAAOD,GAAC,IAAEV,IAAE,YAAY;AAAE,SAAK,WAASA,IAAE,0BAA0B;AAAE,UAAMe,KAAE,KAAK,QAAQJ,KAAED,EAAC;AAAE,QAAGV,IAAE,gBAAgBe,MAAA,gBAAAA,GAAG,MAAM,GAAEf,IAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAE,EAAEI,EAAC,GAAE;AAAC,YAAK,EAAC,GAAEL,IAAE,GAAEY,KAAE,GAAED,KAAE,GAAEK,GAAC,IAAEX,GAAE;AAAM,MAAAJ,IAAE,cAAce,KAAEhB,KAAE,KAAIgB,KAAEJ,MAAE,KAAII,KAAEL,MAAE,KAAIK,EAAC;AAAA,IAAC,MAAM,CAAAf,IAAE,cAAc,GAAE,GAAE,GAAE,CAAC;AAAE,IAAAA,IAAE,qBAAqB,IAAE,GAAEA,IAAE,cAAc,CAAC,GAAEA,IAAE,MAAMA,IAAE,GAAG,mBAAiBA,IAAE,GAAG,gBAAgB,GAAEA,IAAE,qBAAqB,KAAE;AAAA,EAAC;AAAA,EAAC,kBAAkBD,IAAEC,KAAEI,IAAE;AAJz7H;AAI07H,UAAK,EAAC,SAAQO,KAAE,WAAUD,IAAE,SAAQK,IAAE,YAAWR,KAAE,WAAUS,GAAC,IAAEjB;AAAE,QAAGQ,OAAGU,GAAED,IAAEN,IAAEK,IAAEX,EAAC,EAAE,CAAAO,IAAE,iBAAgB,UAAK,UAAL,mBAAY,KAAK,GAAEA,IAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAEA,IAAE,cAAc,GAAE,GAAE,GAAE,CAAC,GAAEA,IAAE,qBAAqB,IAAE,GAAEA,IAAE,cAAc,CAAC,GAAEA,IAAE,MAAMA,IAAE,GAAG,mBAAiBA,IAAE,GAAG,gBAAgB,GAAEA,IAAE,qBAAqB,KAAE;AAAA,SAAM;AAAC,YAAMZ,KAAE,KAAK,cAAc;AAAE,MAAAY,IAAE,gBAAgBZ,EAAC;AAAA,IAAC;AAAC,IAAAY,IAAE,qBAAqB,KAAE,GAAEA,IAAE,oBAAoB,KAAE,GAAEA,IAAE,sBAAsB,IAAE,GAAEA,IAAE,gBAAgBX,GAAC,GAAEW,IAAE,oBAAoB,GAAG,GAAEA,IAAE,MAAMA,IAAE,GAAG,kBAAkB;AAAA,EAAC;AAAA,EAAC,eAAeP,IAAEO,KAAE;AAAC,UAAK,EAAC,SAAQD,IAAE,WAAUK,IAAE,SAAQC,IAAE,YAAWR,IAAE,WAAUI,GAAC,IAAER;AAAE,QAAGI,MAAGS,GAAEL,IAAEG,IAAEC,IAAEL,GAAC,GAAE;AAAC,QAAEK,EAAC,KAAGA,GAAE,SAAO,KAAGJ,OAAI,EAAE,OAAK,KAAK,cAAcR,IAAEY,EAAC;AAAE,YAAMR,KAAE,KAAK,cAAc;AAAE,MAAAE,GAAE,gBAAgBF,EAAC,GAAEE,GAAE,sBAAsB,KAAE,GAAEA,GAAE,oBAAoB,CAAC,GAAEA,GAAE,mBAAmB,IAAE,GAAEA,GAAE,yBAAyB,EAAE,KAAI,EAAE,qBAAoB,EAAE,KAAI,EAAE,mBAAmB,GAAEA,GAAE,aAAa,MAAG,MAAG,MAAG,IAAE;AAAE,YAAMJ,KAAE,EAAES,EAAC,KAAGH,OAAI,EAAE,YAAU,WAASG,IAAEN,KAAE,KAAK;AAAM,OAAAA,MAAA,gBAAAA,GAAG,MAAM,iBAAc,KAAK,aAAa,KAAKL,IAAEK,GAAE,MAAM,cAAa,EAAE,SAAQH,IAAEK,GAAC;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,aAAaZ,IAAE;AAAC,IAAAA,GAAE,gBAAgB,KAAK,QAAQ;AAAE,UAAMC,MAAE,KAAK,cAAc;AAAE,IAAAA,QAAID,GAAE,oBAAoB,KAAE,GAAEA,GAAE,oBAAoB,CAAC,GAAE,KAAK,2BAAyBA,GAAE,sBAAsB,IAAE,GAAEA,GAAE,mBAAmB,EAAE,OAAM,GAAE,GAAG,KAAGA,GAAE,sBAAsB,KAAE,GAAE,KAAK,YAAYA,IAAEC,IAAE,cAAa,EAAE,OAAO;AAAA,EAAE;AAAA,EAAC,eAAeA,KAAEI,IAAEO,KAAE;AAAC,UAAK,EAAC,SAAQD,GAAC,IAAEV;AAAE,QAAGU,GAAE,gBAAgB,KAAK,QAAQ,GAAEA,GAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAE,EAAEN,EAAC,GAAE;AAAC,YAAK,EAAC,GAAEL,IAAE,GAAEC,KAAE,GAAEW,KAAE,GAAEI,GAAC,IAAEX,GAAE;AAAM,MAAAM,GAAE,cAAcK,KAAEhB,KAAE,KAAIgB,KAAEf,MAAE,KAAIe,KAAEJ,MAAE,KAAII,EAAC;AAAA,IAAC,MAAM,CAAAL,GAAE,cAAc,GAAE,GAAE,GAAE,CAAC;AAAE,IAAAA,GAAE,oBAAoB,GAAG,GAAEA,GAAE,gBAAgB,CAAC,GAAEA,GAAE,MAAMA,GAAE,GAAG,mBAAiBA,GAAE,GAAG,kBAAkB,GAAE,KAAK,0BAAwB,KAAK,yBAAyB,OAAOV,KAAEW,GAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAAC,QAAG,KAAK,gBAAgB,QAAQ,GAAE,KAAK,eAAe,QAAQ,GAAE,KAAK,qBAAqB,QAAQ,GAAE,KAAK,gBAAc,EAAE,KAAK,aAAa,GAAE,KAAK,2BAAyB,EAAE,KAAK,wBAAwB,GAAE,KAAK,gBAAc,KAAK,YAAY,QAAS,CAAAZ,OAAGA,GAAE,QAAQ,CAAE,GAAE,KAAK,YAAY,MAAM,GAAE,KAAK,cAAY,OAAM,KAAK,MAAM,YAAUA,MAAK,KAAK,MAAM,MAAK,MAAMA,EAAC,KAAG,KAAK,MAAMA,EAAC,EAAE,QAAQ;AAAE,eAAUA,MAAK,KAAK,SAAS,CAAAA,GAAE,QAAQ;AAAE,QAAG,KAAK,SAAS,SAAO,GAAE,KAAK,QAAQ,YAAUA,MAAK,KAAK,QAAQ,MAAK,QAAQA,EAAC,KAAG,KAAK,QAAQA,EAAC,EAAE,QAAQ;AAAE,SAAK,gBAAgB,QAAQ,GAAE,KAAK,sBAAoB,EAAE,KAAK,mBAAmB,GAAE,KAAK,WAAS;AAAA,EAAI;AAAA,EAAC,SAASA,IAAEC,KAAE;AAAC,UAAMI,KAAEN,GAAEC,IAAEC,GAAC;AAAE,QAAIW,MAAE,KAAK,YAAY,IAAIP,EAAC;AAAE,WAAO,WAASO,QAAIA,MAAE,IAAIP,MAAE,KAAK,YAAY,IAAIA,IAAEO,GAAC,IAAGA;AAAA,EAAC;AAAA,EAAC,aAAaZ,IAAEC,KAAEI,IAAEM,IAAE;AAAC,UAAMK,KAAEb,GAAEE,EAAC;AAAE,QAAG,CAACW,GAAE;AAAO,QAAIR,MAAE,KAAK,YAAY,IAAIQ,EAAC;AAAE,eAASR,QAAIA,MAAE,IAAIQ,MAAE,KAAK,YAAY,IAAIA,IAAER,GAAC,IAAGA,IAAE,aAAaR,IAAEW,EAAC,GAAEH,IAAE,KAAKR,IAAEC,KAAEU,EAAC;AAAA,EAAC;AAAA,EAAC,cAAcX,IAAEC,KAAEI,IAAEM,IAAE;AAAC,UAAMK,KAAEb,GAAEE,EAAC;AAAE,QAAG,CAACW,GAAE;AAAO,QAAIR,MAAE,KAAK,YAAY,IAAIQ,EAAC;AAAE,eAASR,QAAIA,MAAE,IAAIQ,MAAE,KAAK,YAAY,IAAIA,IAAER,GAAC,IAAGA,IAAE,SAASR,IAAEC,KAAEU,EAAC;AAAA,EAAC;AAAA,EAAC,mBAAmBX,IAAE;AAAC,UAAMC,MAAED,GAAE,QAAQ,IAAK,CAAAA,QAAI,KAAK,YAAY,IAAIA,EAAC,KAAG,KAAK,YAAY,IAAIA,IAAE,IAAIA,IAAC,GAAE,KAAK,YAAY,IAAIA,EAAC,EAAG;AAAE,WAAO,IAAIiB,GAAEhB,KAAED,EAAC;AAAA,EAAC;AAAA,EAAC,YAAYA,IAAEC,KAAEI,IAAEO,MAAE,GAAE;AAAC,IAAAZ,GAAE,mBAAmB,IAAE,GAAEA,GAAE,yBAAyB,EAAE,KAAI,EAAE,qBAAoB,EAAE,KAAI,EAAE,mBAAmB,GAAEA,GAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAE,KAAK,cAAc,OAAOA,IAAEC,KAAEI,IAAEO,GAAC;AAAA,EAAC;AAAA,EAAC,yBAAyBZ,IAAE;AAAC,WAAO,KAAK,gBAAgB,yBAAyBA,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAe;AAJvnO;AAIwnO,WAAO,QAAM,KAAK,gBAAc,KAAK,kBAAc,UAAK,UAAL,mBAAY,WAAQ;AAAA,EAAI;AAAA,EAAC,cAAcA,IAAEC,KAAE;AAJttO;AAIutO,UAAMI,MAAE,UAAK,UAAL,mBAAY;AAAM,QAAG,CAACA,GAAE;AAAO,UAAK,EAAC,SAAQO,IAAC,IAAEZ,IAAEW,KAAE,KAAK,gBAAgB,yBAAyBV,GAAC;AAAE,eAAS,EAAC,sBAAqBe,IAAE,QAAOR,IAAC,KAAIG,GAAE,CAAAC,IAAE,gBAAgBP,EAAC,GAAEW,GAAE,KAAKhB,IAAEK,IAAEG,GAAC;AAAA,EAAC;AAAC;AAAC,SAASU,GAAEjB,KAAEI,IAAEO,KAAED,IAAE;AAAC,SAAOV,QAAI,EAAE,cAAY,MAAIU,MAAG,EAAEN,EAAC,KAAG,aAAWA,MAAG,EAAEO,GAAC,KAAGA,IAAE,SAAO;AAAE;;;ACA97M,IAAMO,KAAE;AAAI,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,YAAYC,IAAEC,IAAE;AAAC,UAAM,GAAE,KAAK,SAAO,oBAAI,OAAI,KAAK,uBAAqB,GAAE,KAAK,uBAAqB,GAAE,KAAK,kBAAgB,OAAG,KAAK,QAAM,MAAK,KAAK,cAAY;AAAG,UAAK,EAAC,QAAOC,MAAE,SAAS,cAAc,QAAQ,GAAE,OAAMC,KAAE,MAAG,SAAQC,KAAE,MAAG,gBAAeC,KAAE,CAAC,EAAC,IAAEJ;AAAE,SAAK,UAAQC;AAAE,UAAMI,KAAEJ,GAAE,MAAKA,KAAE,EAAC,OAAMC,IAAE,WAAU,OAAG,OAAM,MAAG,SAAQC,GAAC,CAAC;AAAE,SAAK,UAAQ,IAAIG,GAAE,EAAED,EAAC,KAAG,MAAKD,EAAC,GAAE,KAAK,kBAAgB,IAAIH,MAAE,KAAK,UAAQ,IAAIM,GAAE,KAAK,SAAQ,MAAK,KAAK,eAAe,GAAE,IAAI,kBAAkB,MAAI,KAAK,eAAa,SAAS,cAAc,KAAK,GAAE,KAAK,aAAa,aAAa,SAAQ,oDAAoD,GAAER,GAAE,YAAY,KAAK,YAAY;AAAG,UAAMS,KAAE,MAAI,KAAK;AAAmB,SAAK,oBAAkB,EAAC,WAAU,GAAE,OAAM,KAAK,OAAM,YAAW,OAAO,kBAAiB,YAAW,OAAG,eAAc,GAAE,WAAU,MAAK,WAAU,IAAG,MAAK,GAAE,kBAAiB,OAAG,SAAQ,MAAK,SAAQ,KAAK,SAAQ,SAAQ,KAAK,SAAQ,UAASR,GAAE,YAAU,IAAIS,MAAE,kBAAiBT,GAAE,kBAAiB,eAAc,MAAI,KAAK,cAAc,GAAE,oBAAmB,OAAG,YAAW,OAAG,UAAS,IAAIU,GAAE,KAAK,SAAQ,KAAK,YAAY,GAAE,mBAAkB,GAAE,IAAI,oBAAmB;AAAC,aAAOF,GAAE;AAAA,IAAC,EAAC,GAAE,KAAK,cAAY,EAAE,EAAC,QAAO,CAAAC,OAAG,KAAK,YAAYA,EAAC,EAAC,CAAC,GAAE,KAAK,YAAY,MAAM,GAAE,KAAK,0BAAwBE,GAAEV,KAAE,oBAAoB,MAAI;AAAC,WAAK,KAAK,eAAc,EAAC,OAAM,IAAI,EAAE,oBAAoB,EAAC,CAAC;AAAA,IAAC,CAAE,GAAE,KAAK,cAAY,IAAID,MAAEC,IAAE,aAAa,SAAQ,0CAA0C,GAAEF,GAAE,YAAYE,GAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAJhhF;AAIihF,SAAK,kBAAkB,GAAE,KAAK,YAAY,GAAE,KAAK,cAAY,EAAE,KAAK,WAAW,GAAE,KAAK,0BAAwB,EAAE,KAAK,uBAAuB,IAAE,UAAK,QAAQ,eAAb,mBAAyB,YAAY,KAAK,WAAS,gBAAK,iBAAL,mBAAmB,eAAnB,mBAA+B,YAAY,KAAK,eAAc,KAAK,YAAY,QAAQ,GAAE,KAAK,gBAAgB,QAAQ,GAAE,KAAK,QAAQ,QAAQ,GAAE,KAAK,QAAQ,QAAQ,GAAE,KAAK,UAAQ;AAAA,EAAI;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK;AAAA,EAAW;AAAA,EAAC,IAAI,WAAWQ,IAAE;AAAC,SAAK,cAAYA,IAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK;AAAA,EAAW;AAAA,EAAC,IAAI,mBAAkB;AAAC,WAAO,KAAK;AAAA,EAAiB;AAAA,EAAC,IAAI,iBAAiBA,IAAE;AAAC,SAAK,oBAAkBA,IAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,IAAI,QAAO;AAAC,WAAO,KAAK;AAAA,EAAM;AAAA,EAAC,IAAI,MAAMA,IAAE;AAAC,SAAK,SAAOA,IAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK;AAAA,EAAW;AAAA,EAAC,IAAI,WAAWA,IAAE;AAAC,SAAK,gBAAcA,OAAI,KAAK,cAAYA,IAAE,KAAK,cAAc;AAAA,EAAE;AAAA,EAAC,mBAAmBA,IAAE;AAAC,SAAK,OAAO,IAAIA,EAAC,GAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,qBAAqBA,IAAE;AAAC,WAAO,KAAK,OAAO,OAAOA,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAe;AAAC,SAAK,uBAAqBb,IAAE,KAAK,oBAAkB,KAAK,kBAAgB,MAAG,KAAK,KAAK,aAAa,GAAE,KAAK,YAAY,OAAO;AAAA,EAAE;AAAA,EAAC,YAAYa,IAAE;AAAC,UAAMG,MAAE,KAAK,uBAAqBH,GAAE,OAAK,KAAK,uBAAqB;AAAE,SAAK,wBAAsBG,KAAE,KAAK,wBAAsB,KAAG,KAAK,YAAY,MAAM,GAAE,KAAK,uBAAqBH,GAAE,MAAK,KAAK,kBAAgB,OAAG,KAAK,kBAAkB,QAAM,KAAK,QAAO,KAAK,kBAAkB,aAAW,KAAK,YAAW,KAAK,kBAAkB,aAAW,OAAO,kBAAiB,KAAK,kBAAkB,gBAAc,GAAE,KAAK,kBAAkB,OAAKA,GAAE,MAAK,KAAK,kBAAkB,YAAUA,GAAE,WAAU,KAAK,kBAAkB,UAAQ,MAAK,KAAK,cAAc,KAAK,iBAAiB,GAAE,KAAK,YAAY,GAAE,KAAK,KAAK,aAAa;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAM,EAAC,KAAIA,GAAE,EAAC;AAAA,EAAC;AAAA,EAAC,eAAeA,IAAE;AAAC,eAAUG,OAAK,KAAK,SAAS,CAAAA,IAAE,aAAaH,EAAC;AAAE,SAAK,gBAAgB,KAAK,UAASA,EAAC;AAAE,eAAUG,OAAK,KAAK,SAAS,CAAAA,IAAE,YAAYH,EAAC;AAAA,EAAC;AAAA,EAAC,gBAAgBA,IAAEG,KAAE;AAAC,UAAMD,MAAE,KAAK;AAAQ,SAAK,QAAQ,qBAAqB,OAAO,GAAEA,IAAE,UAAU,GAAEC,IAAE,SAAS,YAAY,YAAY,GAAEA,IAAE,oBAAkB,GAAEA,IAAE,YAAU,EAAE,KAAI,KAAK,QAAQ,mBAAmBD,KAAE,KAAK,UAAU;AAAE,eAAUZ,MAAKU,GAAE,CAAAV,GAAE,cAAca,GAAC;AAAE,SAAK,QAAQ,eAAeA,KAAE,KAAK,YAAW,KAAK,MAAM,OAAO,GAAE,KAAK,QAAQ,aAAaD,GAAC,GAAEC,IAAE,YAAU,EAAE,WAAU,KAAK,QAAQ,mBAAmBD,GAAC;AAAE,eAAUZ,MAAKU,GAAE,CAAAV,GAAE,cAAca,GAAC;AAAE,SAAK,QAAQ,aAAaD,GAAC;AAAE,QAAG,KAAK,0BAA0BF,EAAC,GAAE;AAAC,MAAAG,IAAE,YAAU,EAAE,OAAM,KAAK,QAAQ,mBAAmBD,GAAC;AAAE,iBAAUA,OAAKF,GAAE,CAAAE,IAAE,cAAcC,GAAC;AAAE,WAAK,QAAQ,aAAaD,GAAC;AAAA,IAAC;AAAC,QAAG,IAAI,kBAAkB,GAAE;AAAC,MAAAC,IAAE,YAAU,EAAE,OAAM,KAAK,QAAQ,mBAAmBD,GAAC;AAAE,iBAAUA,OAAKF,GAAE,CAAAE,IAAE,cAAcC,GAAC;AAAE,WAAK,QAAQ,aAAaD,GAAC;AAAA,IAAC;AAAC,IAAAC,IAAE,SAAS,UAAU,YAAY,GAAED,IAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,SAASF,IAAE;AAAC,UAAMG,MAAE,KAAK,SAAQ,EAAC,OAAMD,KAAE,YAAWZ,GAAC,IAAEU;AAAE,SAAK,cAAcA,EAAC,GAAEG,IAAE,YAAY,GAAE,GAAEb,KAAEY,IAAE,KAAK,CAAC,GAAEZ,KAAEY,IAAE,KAAK,CAAC,CAAC,GAAEC,IAAE,qBAAqB,IAAE,GAAEA,IAAE,oBAAoB,GAAG,GAAE,MAAM,SAASH,EAAC;AAAA,EAAC;AAAA,EAAC,MAAM,eAAeA,IAAE;AAAC,UAAK,EAAC,kBAAiBG,KAAE,mBAAkBD,IAAC,IAAE,EAAC,kBAAiB,KAAK,MAAM,KAAK,MAAM,KAAK,CAAC,IAAEF,GAAE,eAAe,GAAE,mBAAkB,KAAK,MAAM,KAAK,MAAM,KAAK,CAAC,IAAEA,GAAE,eAAe,EAAC,GAAEV,KAAEU,GAAE,iBAAgBC,KAAE,KAAK,SAAQV,KAAE,KAAK,OAAO,MAAM;AAAE,QAAG,QAAMS,GAAE,UAAS;AAAC,YAAMG,MAAEZ,GAAE;AAAU,MAAAA,GAAE,UAAU,WAASS,GAAE,UAAST,GAAE,YAAUY;AAAA,IAAC;AAAC,UAAMC,KAAE,EAAC,GAAG,KAAK,mBAAkB,WAAU,MAAK,eAAc,GAAE,YAAW,MAAG,OAAMb,IAAE,YAAWD,IAAE,MAAK,YAAY,IAAI,GAAE,WAAU,GAAE,WAAU,MAAK,SAAQ,MAAK,kBAAiB,MAAE,GAAEE,MAAE,IAAI,EAAES,IAAE,EAAC,aAAY,EAAE,SAAQ,oBAAmB,EAAE,6BAA4B,OAAME,KAAE,QAAOD,IAAC,CAAC,GAAEb,KAAEY,GAAE,0BAA0B,GAAER,KAAEQ,GAAE,YAAY;AAAE,IAAAA,GAAE,gBAAgBT,GAAC,GAAES,GAAE,YAAY,GAAE,GAAEE,KAAED,GAAC,GAAE,KAAK,gBAAgBF,GAAE,UAASI,EAAC;AAAE,UAAMC,KAAE,KAAK,oBAAoBb,KAAE,EAAC,GAAGQ,GAAE,UAAS,GAAEE,OAAGF,GAAE,SAAS,IAAEA,GAAE,SAAS,QAAO,CAAC;AAAE,IAAAC,GAAE,gBAAgBZ,EAAC,GAAEY,GAAE,YAAYR,GAAE,GAAEA,GAAE,GAAEA,GAAE,OAAMA,GAAE,MAAM,GAAE,KAAK,cAAc;AAAE,UAAMa,KAAE,MAAMD;AAAE,QAAIE;AAAE,WAAO,MAAIP,GAAE,cAAYO,KAAED,MAAGC,KAAE,IAAI,UAAU,KAAK,MAAMD,GAAE,QAAMN,GAAE,WAAW,GAAE,KAAK,MAAMM,GAAE,SAAON,GAAE,WAAW,CAAC,GAAEQ,GAAEF,IAAEC,IAAE,IAAE,IAAGA;AAAA,EAAC;AAAA,EAAC,MAAM,oBAAoBP,IAAEG,KAAE;AAAC,UAAMD,MAAEF,GAAEG,IAAE,OAAMA,IAAE,QAAO,SAAS,cAAc,QAAQ,CAAC;AAAE,WAAO,MAAMH,GAAE,gBAAgBG,IAAE,GAAEA,IAAE,GAAEA,IAAE,OAAMA,IAAE,QAAO,EAAE,MAAK,EAAE,eAAc,IAAI,WAAWD,IAAE,KAAK,MAAM,CAAC,GAAEA;AAAA,EAAC;AAAA,EAAC,cAAcF,IAAE;AAAC,UAAMG,MAAE,KAAK,SAAQD,MAAEC,IAAE,OAAM,EAAC,OAAM,EAAC,MAAKb,GAAC,GAAE,YAAWW,GAAC,IAAED,IAAET,KAAED,GAAE,CAAC,GAAEc,KAAEd,GAAE,CAAC,GAAEE,MAAE,KAAK,MAAMD,KAAEU,EAAC,GAAEZ,KAAE,KAAK,MAAMe,KAAEH,EAAC;AAAE,IAAAE,IAAE,UAAQX,OAAGW,IAAE,WAASd,OAAIc,IAAE,QAAMX,KAAEW,IAAE,SAAOd,KAAGa,IAAE,QAAMX,KAAE,MAAKW,IAAE,SAAOE,KAAE;AAAA,EAAI;AAAA,EAAC,cAAa;AAAC,WAAK,KAAK,OAAO,OAAK,KAAG;AAAC,YAAMJ,KAAE,MAAM,KAAK,KAAK,MAAM;AAAE,WAAK,OAAO,MAAM;AAAE,iBAAUG,OAAKH,GAAE,CAAAG,IAAE,cAAc;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,0BAA0BH,IAAE;AAAC,QAAIG,MAAE;AAAG,eAAUD,OAAKF,IAAE;AAAC,UAAG,EAAEE,eAAab,KAAG;AAAC,QAAAc,MAAEA,OAAG;AAAG;AAAA,MAAK;AAAC,UAAGD,IAAE,UAAU,QAAM;AAAG,MAAAC,MAAEA,OAAG,KAAK,0BAA0BD,IAAE,QAAQ;AAAA,IAAC;AAAC,WAAOC;AAAA,EAAC;AAAC;;;ACAhhO,eAAeM,GAAEA,IAAE;AAAC,QAAMC,MAAE,OAAO,wBAAe,GAAEC,KAAE,OAAO,2BAAkB,GAAEC,MAAEC,IAAG,MAAMH,KAAG,SAAQ,EAAC,QAAOD,GAAC,CAAC,GAAEK,KAAED,IAAG,MAAMF,IAAG,SAAQ,EAAC,QAAOF,GAAC,CAAC,GAAEM,KAAE,EAAC,MAAK,MAAMH,KAAE,SAAQ,MAAME,GAAC;AAAE,SAAO,EAAEL,EAAC,GAAEM;AAAC;;;ACA60B,IAAMC,KAAN,cAAgBC,GAAC;AAAA,EAAC,cAAa;AAAC,UAAM,GAAE,KAAK,WAAS,IAAIC,MAAE,KAAK,sBAAoB,GAAE,KAAK,UAAQ;AAAA,EAAE;AAAA,EAAC,UAAS;AAAC,SAAK,WAAS,EAAE,KAAK,QAAQ,GAAE,KAAK,wBAAwB,GAAE,KAAK,iBAAe,EAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,IAAI,aAAY;AAAC,WAAO,KAAK;AAAA,EAAW;AAAA,EAAC,IAAI,WAAWC,IAAE;AAAC,SAAK,cAAYA,IAAE,KAAK,cAAc;AAAA,EAAC;AAAA,EAAC,IAAI,YAAW;AAAC,WAAO,KAAK;AAAA,EAAU;AAAA,EAAC,IAAI,UAAUA,IAAE;AAAC,SAAK,aAAWA,IAAE,KAAK,SAAS,UAAU,GAAE,KAAK,SAAS,IAAI,CAAC,EAAG,MAAIA,GAAE,SAAU,MAAI;AAAC,WAAK,UAAQA,GAAE,WAAS,EAAEA,GAAE,QAAQ,KAAGA,GAAE,OAAK,GAAE,KAAK,cAAc;AAAA,IAAC,GAAGC,EAAC,GAAE,EAAG,MAAI,CAACD,GAAE,SAAQA,GAAE,UAAU,GAAI,MAAI,KAAK,iBAAiB,CAAE,GAAE,EAAG,MAAIA,GAAE,MAAO,MAAI;AAAC,WAAK,wBAAwB,GAAE,KAAK,cAAc;AAAA,IAAC,CAAE,CAAC,CAAC;AAAA,EAAC;AAAA,EAAC,oBAAmB;AAAC,WAAM,EAAC,KAAIA,GAAE,EAAC;AAAA,EAAC;AAAA,EAAC,SAASA,IAAE;AAJj2D;AAIk2D,UAAMF,MAAEE,GAAE;AAAQ,QAAG,CAAC,KAAK,eAAe,QAAO,KAAK,KAAK,iBAAiB;AAAE,QAAGA,GAAE,cAAY,EAAE,OAAK,CAAC,KAAK,WAAW,EAAE;AAAO,SAAK,iBAAiBA,EAAC;AAAE,UAAMD,MAAE,KAAK;AAAW,QAAG,EAAEA,IAAE,QAAQ,EAAE;AAAO,UAAMG,KAAEF,GAAE,YAAWG,KAAEJ,IAAE,OAAKG,IAAEE,KAAE,IAAEL,IAAE,QAAOM,KAAE,KAAK,KAAKD,KAAED,EAAC;AAAE,SAAK,iBAAiB,OAAOE,IAAEA,EAAC;AAAE,UAAK,EAAC,MAAKJ,GAAC,IAAED,GAAE,OAAMM,KAAEJ,KAAED,GAAE,CAAC,GAAEM,KAAEL,KAAED,GAAE,CAAC,GAAEO,KAAE,MAAGH,IAAEI,KAAE,MAAGJ,IAAEK,KAAEP,GAAED,KAAEH,IAAE,SAAS,GAAES,IAAEF,KAAEE,KAAE,CAAC,GAAEG,KAAER,GAAEI,KAAEL,KAAEH,IAAE,SAAS,GAAEU,IAAEF,KAAEE,KAAE,CAAC;AAAE,IAAAX,IAAE,mBAAmB,IAAE;AAAE,UAAMc,KAAEF,KAAEF,IAAEK,KAAEF,KAAEF,IAAEK,KAAE,KAAK;AAAiB,IAAAhB,IAAE,YAAYgB,IAAE,CAAC,GAAEhB,IAAE,GAAG,eAAegB,GAAE,WAAW,QAAO,GAAEA,GAAE,WAAW,aAAYF,IAAEC,IAAER,IAAEA,IAAE,CAAC;AAAE,UAAMU,MAAE,UAAK,eAAL,mBAAiB,OAAMC,KAAED,KAAE,CAACA,GAAE,IAAEA,GAAE,IAAE,KAAIA,GAAE,IAAEA,GAAE,IAAE,KAAIA,GAAE,IAAEA,GAAE,IAAE,KAAIA,GAAE,CAAC,IAAE,CAAC,GAAE,GAAE,GAAE,CAAC,GAAE,KAAGL,KAAEX,IAAE,OAAO,IAAEG,MAAGI,KAAE,IAAE,GAAEW,MAAGN,KAAEZ,IAAE,OAAO,IAAEG,MAAGK,KAAE,IAAE,GAAEW,KAAEf,KAAEG,KAAE,GAAEa,KAAEhB,KAAEI,KAAE,GAAEa,KAAE,KAAK;AAAS,IAAAtB,IAAE,QAAQ,KAAK,kBAAkB,GAAEA,IAAE,YAAY,KAAK,iBAAgB,CAAC,GAAEA,IAAE,YAAY,KAAK,cAAa,CAAC,GAAEA,IAAE,WAAWsB,EAAC,GAAEA,GAAE,cAAc,gBAAeJ,EAAC,GAAEI,GAAE,aAAa,qBAAoB,CAAC,GAAEA,GAAE,aAAa,oBAAmB,CAAC,GAAEA,GAAE,aAAa,iBAAgB,CAAC,GAAEA,GAAE,aAAa,aAAY,GAAEH,IAAEC,IAAEC,EAAC,GAAEC,GAAE,aAAa,iBAAgBrB,IAAE,cAAY,IAAE,CAAC,GAAEqB,GAAE,aAAa,oBAAmBrB,IAAE,iBAAe,IAAE,CAAC,GAAED,IAAE,sBAAsB,KAAE,GAAEA,IAAE,aAAa,MAAG,MAAG,MAAG,IAAE,GAAEA,IAAE,WAAW,EAAE,gBAAe,GAAE,CAAC,GAAEA,IAAE,QAAQ;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,QAAM,KAAK,WAAS,QAAM,KAAK;AAAA,EAAU;AAAA,EAAC,mBAAkB;AAAC,SAAK,kBAAgB,KAAK,eAAe,MAAM;AAAE,UAAMC,MAAE,EAAE,KAAK,UAAU,IAAE,KAAK,WAAW,UAAQ,MAAKsB,KAAE,EAAE,KAAK,UAAU,IAAE,KAAK,WAAW,aAAW;AAAK,SAAK,iBAAe,EAAG,OAAMvB,QAAG;AAAC,YAAMI,KAAE,EAAEH,GAAC,KAAG,EAAEsB,EAAC,IAAEA,GAAEvB,GAAC,IAAE,MAAKK,KAAE,EAAEJ,GAAC,IAAE,EAAEA,KAAE,EAAC,cAAa,SAAQ,QAAOD,IAAC,CAAC,EAAE,KAAM,CAAAE,OAAGA,GAAE,IAAK,IAAEE,GAAE,KAAM,CAAAF,OAAGA,GAAE,IAAK,GAAEK,KAAE,EAAEgB,EAAC,IAAE,EAAEA,IAAE,EAAC,cAAa,SAAQ,QAAOvB,IAAC,CAAC,EAAE,KAAM,CAAAE,OAAGA,GAAE,IAAK,IAAEE,GAAE,KAAM,CAAAF,OAAGA,GAAE,OAAQ,GAAE,CAACC,IAAEK,EAAC,IAAE,MAAM,QAAQ,IAAI,CAACH,IAAEE,EAAC,CAAC;AAAE,WAAK,OAAKJ,IAAE,KAAK,UAAQK,IAAE,KAAK,wBAAwB,GAAE,KAAK,cAAc;AAAA,IAAC,CAAE;AAAA,EAAC;AAAA,EAAC,0BAAyB;AAAC,SAAK,mBAAiB,EAAE,KAAK,gBAAgB,GAAE,KAAK,kBAAgB,EAAE,KAAK,eAAe,GAAE,KAAK,eAAa,EAAE,KAAK,YAAY,GAAE,KAAK,qBAAmB,EAAE,KAAK,kBAAkB,GAAE,KAAK,WAAS,EAAE,KAAK,QAAQ;AAAA,EAAC;AAAA,EAAC,iBAAiBN,IAAE;AAAC,QAAGA,GAAE,eAAa,KAAK,uBAAqB,KAAK,wBAAwB,GAAE,KAAK,iBAAiB;AAAO,UAAMF,MAAEE,GAAE;AAAQ,SAAK,sBAAoBA,GAAE;AAAW,UAAMD,MAAE,KAAK,KAAK,KAAK,WAAW,OAAKC,GAAE,UAAU;AAAE,SAAK,WAASD,GAAED,GAAC;AAAE,UAAMuB,KAAE,IAAI,YAAY,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC,GAAEnB,KAAEC,GAAE;AAAW,SAAK,qBAAmB,IAAIQ,GAAEb,KAAEI,IAAE,GAAE,EAAC,UAASiB,GAAE,aAAarB,KAAE,EAAE,aAAYuB,EAAC,EAAC,CAAC,GAAE,KAAK,QAAQ,QAAMtB,KAAE,KAAK,QAAQ,SAAOA,KAAE,KAAK,kBAAgB,IAAIoB,GAAErB,KAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,gBAAe,EAAE,MAAK,UAAS,EAAE,eAAc,UAASwB,GAAE,eAAc,cAAa,EAAE,SAAQ,SAAQ,MAAG,kBAAiB,CAAC,GAAE,KAAK,QAAQ,GAAG,KAAG,CAACtB,GAAE,QAAQ,WAAW,sBAAsB,OAAM,GAAE,KAAK,OAAO,GAAE,KAAK,KAAK,QAAMD,KAAE,KAAK,KAAK,SAAOA,KAAE,KAAK,eAAa,IAAIoB,GAAErB,KAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,OAAM,gBAAe,EAAE,OAAM,UAAS,EAAE,eAAc,UAASwB,GAAE,eAAc,cAAa,EAAE,SAAQ,SAAQ,KAAE,GAAE,KAAK,IAAI;AAAE,UAAMnB,KAAE,IAAE,KAAK,WAAW;AAAO,SAAK,mBAAiB,IAAIgB,GAAErB,KAAE,EAAC,QAAO,EAAE,YAAW,aAAY,EAAE,MAAK,gBAAe,EAAE,MAAK,UAAS,EAAE,eAAc,UAASwB,GAAE,eAAc,cAAa,EAAE,QAAO,SAAQ,OAAG,OAAM,KAAK,KAAKnB,KAAEJ,GAAC,GAAE,QAAO,KAAK,KAAKI,KAAEJ,GAAC,EAAC,CAAC;AAAA,EAAC;AAAC;", "names": ["t", "e", "n", "a", "r", "d", "i", "l", "s", "f", "h", "o", "S", "v", "o", "e", "r", "t", "a", "s", "n", "c", "i", "u", "p", "g", "L", "l", "f", "v", "_", "r", "t", "e", "i", "s", "o", "p", "f", "E", "a", "r", "s", "o", "e", "t", "i", "h", "n", "g", "m", "y", "i", "e", "t", "r", "h", "a", "c", "s", "o", "u", "n", "u", "r", "f", "e", "t", "s", "i", "n", "a", "m", "c", "d", "p", "_", "l", "b", "g", "j", "x", "E", "M", "w", "P", "h", "o", "t", "r", "i", "a", "n", "u", "m", "d", "f", "c", "_", "l", "e", "s", "B", "C", "r", "t", "e", "s", "o", "n", "c", "f", "c", "e", "i", "I", "t", "s", "a", "m", "E", "f", "b", "r", "o", "l", "e", "_", "c", "E", "D", "s", "r", "t", "i", "h", "o", "h", "d", "l", "t", "c", "_", "e", "s", "r", "i", "o", "c", "t", "e", "s", "Y", "r", "o", "n", "i", "O", "a", "d", "h", "u", "l", "f", "i", "t", "e", "s", "o", "r", "Y", "n", "f", "a", "h", "u", "b", "c", "l", "_", "p", "m", "_", "c", "d", "T", "t", "s", "r", "o", "n", "a", "h", "l", "u", "f", "g", "B", "O", "F", "b", "E", "e", "i", "x", "w", "D", "d", "_", "t", "r", "s", "i", "a", "n", "o", "e", "u", "l", "b", "p", "c", "h", "g", "D", "_", "e", "t", "s", "i", "a", "o", "n", "l", "c", "u", "h", "p", "r", "E", "D", "d", "c", "m", "e", "s", "o", "a", "l", "n", "u", "h", "p", "_", "B", "T", "f", "g", "E", "b", "F", "O", "x", "w", "M", "t", "r", "i", "D", "o", "e", "r", "s", "i", "a", "l", "T", "h", "n", "E", "D", "c", "o", "f", "_", "T", "m", "i", "e", "t", "s", "a", "e", "t", "r", "s", "n", "i", "o", "f", "d", "h", "c", "I", "e", "t", "E", "w", "j", "s", "_", "f", "o", "l", "c", "i", "r", "h", "J", "D", "n", "a", "A", "x", "P", "h", "s", "n", "o", "d", "p", "f", "_", "I", "j", "g", "e", "i", "r", "t", "a", "l", "c", "u", "m", "s", "r", "i", "o", "t", "e", "m", "U", "r", "t", "e", "h", "i", "a", "n", "l", "m", "u", "c", "d", "_", "f", "g", "T", "x", "R", "y", "A", "v", "E", "j", "s", "D"]}