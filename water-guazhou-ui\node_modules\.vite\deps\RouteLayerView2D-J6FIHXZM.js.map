{"version": 3, "sources": ["../../@arcgis/core/views/2d/layers/RouteLayerView2D.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{_ as t}from\"../../../chunks/tslib.es6.js\";import e from\"../../../core/Collection.js\";import r from\"../../../core/CollectionFlattener.js\";import{isSome as i,isNone as s}from\"../../../core/maybe.js\";import{initial as o,watch as a}from\"../../../core/reactiveUtils.js\";import{property as h}from\"../../../core/accessorSupport/decorators/property.js\";import\"../../../core/accessorSupport/ensureType.js\";import\"../../../core/arrayUtils.js\";import{subclass as n}from\"../../../core/accessorSupport/decorators/subclass.js\";import p from\"../../../rest/support/DirectionLine.js\";import c from\"../../../rest/support/DirectionPoint.js\";import l from\"../../../rest/support/PointBarrier.js\";import g from\"../../../rest/support/PolygonBarrier.js\";import u from\"../../../rest/support/PolylineBarrier.js\";import d from\"../../../rest/support/RouteInfo.js\";import _ from\"../../../rest/support/Stop.js\";import{LayerView2DMixin as m}from\"./LayerView2D.js\";import w from\"./graphics/GraphicContainer.js\";import f from\"./graphics/GraphicsView2D.js\";import y from\"../../layers/LayerView.js\";const k=Object.freeze({remove(){},pause(){},resume(){}}),v=[\"route-info\",\"direction-line\",\"direction-point\",\"polygon-barrier\",\"polyline-barrier\",\"point-barrier\",\"stop\"],M={graphic:null,property:null,oldValue:null,newValue:null};function V(t){return t instanceof p||t instanceof c||t instanceof l||t instanceof g||t instanceof u||t instanceof d||t instanceof _}function j(t){return e.isCollection(t)&&t.length&&V(t.getItemAt(0))}function G(t){return Array.isArray(t)&&t.length>0&&V(t[0])}let I=class extends(m(y)){constructor(){super(...arguments),this._graphics=new e,this._highlightIds=new Map,this._networkFeatureMap=new Map,this._networkGraphicMap=new Map}get _routeItems(){return new r({getCollections:()=>i(this.layer)&&!this.destroyed?[i(this.layer.routeInfo)?new e([this.layer.routeInfo]):null,this.layer.directionLines,this.layer.directionPoints,this.layer.polygonBarriers,this.layer.polylineBarriers,this.layer.pointBarriers,this.layer.stops]:[]})}initialize(){this.updatingHandles.addOnCollectionChange((()=>this._routeItems),(t=>this._routeItemsChanged(t)),o)}destroy(){this._networkFeatureMap.clear(),this._networkGraphicMap.clear(),this._graphics.removeAll(),this._get(\"_routeItems\")?.destroy()}attach(){this._createGraphicsView()}detach(){this._destroyGraphicsView()}async fetchPopupFeatures(t){return this._graphicsView.hitTest(t).filter((t=>!!t.popupTemplate))}highlight(t){let e;e=V(t)?[this._getNetworkFeatureUid(t)]:G(t)?t.map((t=>this._getNetworkFeatureUid(t))):j(t)?t.map((t=>this._getNetworkFeatureUid(t))).toArray():[t.uid];const r=e.filter(i);return r.length?(this._addHighlight(r),{remove:()=>this._removeHighlight(r)}):k}async hitTest(t,e){if(this.suspended)return null;const r=this._graphicsView.hitTest(t).filter(i).map((t=>this._networkGraphicMap.get(t)));if(!r.length)return null;const{layer:s}=this;return r.reverse().map((e=>({type:\"route\",layer:s,mapPoint:t,networkFeature:e})))}isUpdating(){return this._graphicsView.updating}moveStart(){}moveEnd(){}update(t){this._graphicsView.processUpdate(t)}viewChange(){this._graphicsView.viewChange()}_addHighlight(t){for(const e of t)if(this._highlightIds.has(e)){const t=this._highlightIds.get(e);this._highlightIds.set(e,t+1)}else this._highlightIds.set(e,1);this._updateHighlight()}_createGraphic(t){const e=t.toGraphic();return e.layer=this.layer,e.sourceLayer=this.layer,e}_createGraphicsView(){const t=this.view,e=()=>this.requestUpdate(),r=new w(t.featuresTilingScheme);this._graphicsView=new f({container:r,graphics:this._graphics,requestUpdateCallback:e,view:t}),this.container.addChild(r),this._updateHighlight()}_destroyGraphicsView(){this.container.removeChild(this._graphicsView.container),this._graphicsView.destroy()}_getDrawOrder(t){const e=this._networkGraphicMap.get(t);return v.indexOf(e.type)}_getNetworkFeatureUid(t){return this._networkFeatureMap.has(t)?this._networkFeatureMap.get(t).uid:null}_removeHighlight(t){for(const e of t)if(this._highlightIds.has(e)){const t=this._highlightIds.get(e)-1;0===t?this._highlightIds.delete(e):this._highlightIds.set(e,t)}this._updateHighlight()}_routeItemsChanged(t){if(t.removed.length){this._graphics.removeMany(t.removed.map((t=>{const e=this._networkFeatureMap.get(t);return this._networkFeatureMap.delete(t),this._networkGraphicMap.delete(e),e})));for(const e of t.removed)this.removeHandles(e)}if(t.added.length){this._graphics.addMany(t.added.map((t=>{const e=this._createGraphic(t);return s(e.symbol)?null:(this._networkFeatureMap.set(t,e),this._networkGraphicMap.set(e,t),e)})).filter(i));for(const e of t.added)this.addHandles([a((()=>e.geometry),((t,r)=>{this._updateGraphic(e,\"geometry\",t,r)})),a((()=>e.symbol),((t,r)=>{this._updateGraphic(e,\"symbol\",t,r)}))],e);this._graphics.sort(((t,e)=>this._getDrawOrder(t)-this._getDrawOrder(e)))}}_updateGraphic(t,e,r,i){if(!this._networkFeatureMap.has(t)){const e=this._createGraphic(t);return this._networkFeatureMap.set(t,e),this._networkGraphicMap.set(e,t),void this._graphics.add(e)}const s=this._networkFeatureMap.get(t);s[e]=r,M.graphic=s,M.property=e,M.oldValue=i,M.newValue=r,this._graphicsView.graphicUpdateHandler(M)}_updateHighlight(){const t=Array.from(this._highlightIds.keys());this._graphicsView.setHighlight(t)}};t([h()],I.prototype,\"_graphics\",void 0),t([h()],I.prototype,\"_routeItems\",null),I=t([n(\"esri.views.2d.layers.RouteLayerView2D\")],I);const F=I;export{F as default};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAI+iC,IAAM,IAAE,OAAO,OAAO,EAAC,SAAQ;AAAC,GAAE,QAAO;AAAC,GAAE,SAAQ;AAAC,EAAC,CAAC;AAAvD,IAAyD,IAAE,CAAC,cAAa,kBAAiB,mBAAkB,mBAAkB,oBAAmB,iBAAgB,MAAM;AAAvK,IAAyK,IAAE,EAAC,SAAQ,MAAK,UAAS,MAAK,UAAS,MAAK,UAAS,KAAI;AAAE,SAAS,EAAEA,IAAE;AAAC,SAAOA,cAAa,KAAGA,cAAaC,MAAGD,cAAa,KAAGA,cAAa,KAAGA,cAAa,KAAGA,cAAaC,MAAGD,cAAa;AAAC;AAAC,SAASE,GAAEF,IAAE;AAAC,SAAO,EAAE,aAAaA,EAAC,KAAGA,GAAE,UAAQ,EAAEA,GAAE,UAAU,CAAC,CAAC;AAAC;AAAC,SAAS,EAAEA,IAAE;AAAC,SAAO,MAAM,QAAQA,EAAC,KAAGA,GAAE,SAAO,KAAG,EAAEA,GAAE,CAAC,CAAC;AAAC;AAAC,IAAI,IAAE,cAAcG,GAAE,CAAC,EAAE;AAAA,EAAC,cAAa;AAAC,UAAM,GAAG,SAAS,GAAE,KAAK,YAAU,IAAI,KAAE,KAAK,gBAAc,oBAAI,OAAI,KAAK,qBAAmB,oBAAI,OAAI,KAAK,qBAAmB,oBAAI;AAAA,EAAG;AAAA,EAAC,IAAI,cAAa;AAAC,WAAO,IAAIC,GAAE,EAAC,gBAAe,MAAI,EAAE,KAAK,KAAK,KAAG,CAAC,KAAK,YAAU,CAAC,EAAE,KAAK,MAAM,SAAS,IAAE,IAAI,EAAE,CAAC,KAAK,MAAM,SAAS,CAAC,IAAE,MAAK,KAAK,MAAM,gBAAe,KAAK,MAAM,iBAAgB,KAAK,MAAM,iBAAgB,KAAK,MAAM,kBAAiB,KAAK,MAAM,eAAc,KAAK,MAAM,KAAK,IAAE,CAAC,EAAC,CAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,gBAAgB,sBAAuB,MAAI,KAAK,aAAc,CAAAJ,OAAG,KAAK,mBAAmBA,EAAC,GAAG,CAAC;AAAA,EAAC;AAAA,EAAC,UAAS;AAJvmE;AAIwmE,SAAK,mBAAmB,MAAM,GAAE,KAAK,mBAAmB,MAAM,GAAE,KAAK,UAAU,UAAU,IAAE,UAAK,KAAK,aAAa,MAAvB,mBAA0B;AAAA,EAAS;AAAA,EAAC,SAAQ;AAAC,SAAK,oBAAoB;AAAA,EAAC;AAAA,EAAC,SAAQ;AAAC,SAAK,qBAAqB;AAAA,EAAC;AAAA,EAAC,MAAM,mBAAmBA,IAAE;AAAC,WAAO,KAAK,cAAc,QAAQA,EAAC,EAAE,OAAQ,CAAAA,OAAG,CAAC,CAACA,GAAE,aAAc;AAAA,EAAC;AAAA,EAAC,UAAUA,IAAE;AAAC,QAAIK;AAAE,IAAAA,KAAE,EAAEL,EAAC,IAAE,CAAC,KAAK,sBAAsBA,EAAC,CAAC,IAAE,EAAEA,EAAC,IAAEA,GAAE,IAAK,CAAAA,OAAG,KAAK,sBAAsBA,EAAC,CAAE,IAAEE,GAAEF,EAAC,IAAEA,GAAE,IAAK,CAAAA,OAAG,KAAK,sBAAsBA,EAAC,CAAE,EAAE,QAAQ,IAAE,CAACA,GAAE,GAAG;AAAE,UAAMM,KAAED,GAAE,OAAO,CAAC;AAAE,WAAOC,GAAE,UAAQ,KAAK,cAAcA,EAAC,GAAE,EAAC,QAAO,MAAI,KAAK,iBAAiBA,EAAC,EAAC,KAAG;AAAA,EAAC;AAAA,EAAC,MAAM,QAAQN,IAAEK,IAAE;AAAC,QAAG,KAAK,UAAU,QAAO;AAAK,UAAMC,KAAE,KAAK,cAAc,QAAQN,EAAC,EAAE,OAAO,CAAC,EAAE,IAAK,CAAAA,OAAG,KAAK,mBAAmB,IAAIA,EAAC,CAAE;AAAE,QAAG,CAACM,GAAE,OAAO,QAAO;AAAK,UAAK,EAAC,OAAM,EAAC,IAAE;AAAK,WAAOA,GAAE,QAAQ,EAAE,IAAK,CAAAD,QAAI,EAAC,MAAK,SAAQ,OAAM,GAAE,UAASL,IAAE,gBAAeK,GAAC,EAAG;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,WAAO,KAAK,cAAc;AAAA,EAAQ;AAAA,EAAC,YAAW;AAAA,EAAC;AAAA,EAAC,UAAS;AAAA,EAAC;AAAA,EAAC,OAAOL,IAAE;AAAC,SAAK,cAAc,cAAcA,EAAC;AAAA,EAAC;AAAA,EAAC,aAAY;AAAC,SAAK,cAAc,WAAW;AAAA,EAAC;AAAA,EAAC,cAAcA,IAAE;AAAC,eAAUK,MAAKL,GAAE,KAAG,KAAK,cAAc,IAAIK,EAAC,GAAE;AAAC,YAAML,KAAE,KAAK,cAAc,IAAIK,EAAC;AAAE,WAAK,cAAc,IAAIA,IAAEL,KAAE,CAAC;AAAA,IAAC,MAAM,MAAK,cAAc,IAAIK,IAAE,CAAC;AAAE,SAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,eAAeL,IAAE;AAAC,UAAMK,KAAEL,GAAE,UAAU;AAAE,WAAOK,GAAE,QAAM,KAAK,OAAMA,GAAE,cAAY,KAAK,OAAMA;AAAA,EAAC;AAAA,EAAC,sBAAqB;AAAC,UAAML,KAAE,KAAK,MAAKK,KAAE,MAAI,KAAK,cAAc,GAAEC,KAAE,IAAI,EAAEN,GAAE,oBAAoB;AAAE,SAAK,gBAAc,IAAI,GAAE,EAAC,WAAUM,IAAE,UAAS,KAAK,WAAU,uBAAsBD,IAAE,MAAKL,GAAC,CAAC,GAAE,KAAK,UAAU,SAASM,EAAC,GAAE,KAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,uBAAsB;AAAC,SAAK,UAAU,YAAY,KAAK,cAAc,SAAS,GAAE,KAAK,cAAc,QAAQ;AAAA,EAAC;AAAA,EAAC,cAAcN,IAAE;AAAC,UAAMK,KAAE,KAAK,mBAAmB,IAAIL,EAAC;AAAE,WAAO,EAAE,QAAQK,GAAE,IAAI;AAAA,EAAC;AAAA,EAAC,sBAAsBL,IAAE;AAAC,WAAO,KAAK,mBAAmB,IAAIA,EAAC,IAAE,KAAK,mBAAmB,IAAIA,EAAC,EAAE,MAAI;AAAA,EAAI;AAAA,EAAC,iBAAiBA,IAAE;AAAC,eAAUK,MAAKL,GAAE,KAAG,KAAK,cAAc,IAAIK,EAAC,GAAE;AAAC,YAAML,KAAE,KAAK,cAAc,IAAIK,EAAC,IAAE;AAAE,YAAIL,KAAE,KAAK,cAAc,OAAOK,EAAC,IAAE,KAAK,cAAc,IAAIA,IAAEL,EAAC;AAAA,IAAC;AAAC,SAAK,iBAAiB;AAAA,EAAC;AAAA,EAAC,mBAAmBA,IAAE;AAAC,QAAGA,GAAE,QAAQ,QAAO;AAAC,WAAK,UAAU,WAAWA,GAAE,QAAQ,IAAK,CAAAA,OAAG;AAAC,cAAMK,KAAE,KAAK,mBAAmB,IAAIL,EAAC;AAAE,eAAO,KAAK,mBAAmB,OAAOA,EAAC,GAAE,KAAK,mBAAmB,OAAOK,EAAC,GAAEA;AAAA,MAAC,CAAE,CAAC;AAAE,iBAAUA,MAAKL,GAAE,QAAQ,MAAK,cAAcK,EAAC;AAAA,IAAC;AAAC,QAAGL,GAAE,MAAM,QAAO;AAAC,WAAK,UAAU,QAAQA,GAAE,MAAM,IAAK,CAAAA,OAAG;AAAC,cAAMK,KAAE,KAAK,eAAeL,EAAC;AAAE,eAAO,EAAEK,GAAE,MAAM,IAAE,QAAM,KAAK,mBAAmB,IAAIL,IAAEK,EAAC,GAAE,KAAK,mBAAmB,IAAIA,IAAEL,EAAC,GAAEK;AAAA,MAAE,CAAE,EAAE,OAAO,CAAC,CAAC;AAAE,iBAAUA,MAAKL,GAAE,MAAM,MAAK,WAAW,CAAC,EAAG,MAAIK,GAAE,UAAW,CAACL,IAAEM,OAAI;AAAC,aAAK,eAAeD,IAAE,YAAWL,IAAEM,EAAC;AAAA,MAAC,CAAE,GAAE,EAAG,MAAID,GAAE,QAAS,CAACL,IAAEM,OAAI;AAAC,aAAK,eAAeD,IAAE,UAASL,IAAEM,EAAC;AAAA,MAAC,CAAE,CAAC,GAAED,EAAC;AAAE,WAAK,UAAU,KAAM,CAACL,IAAEK,OAAI,KAAK,cAAcL,EAAC,IAAE,KAAK,cAAcK,EAAC,CAAE;AAAA,IAAC;AAAA,EAAC;AAAA,EAAC,eAAeL,IAAEK,IAAEC,IAAEC,IAAE;AAAC,QAAG,CAAC,KAAK,mBAAmB,IAAIP,EAAC,GAAE;AAAC,YAAMK,KAAE,KAAK,eAAeL,EAAC;AAAE,aAAO,KAAK,mBAAmB,IAAIA,IAAEK,EAAC,GAAE,KAAK,mBAAmB,IAAIA,IAAEL,EAAC,GAAE,KAAK,KAAK,UAAU,IAAIK,EAAC;AAAA,IAAC;AAAC,UAAM,IAAE,KAAK,mBAAmB,IAAIL,EAAC;AAAE,MAAEK,EAAC,IAAEC,IAAE,EAAE,UAAQ,GAAE,EAAE,WAASD,IAAE,EAAE,WAASE,IAAE,EAAE,WAASD,IAAE,KAAK,cAAc,qBAAqB,CAAC;AAAA,EAAC;AAAA,EAAC,mBAAkB;AAAC,UAAMN,KAAE,MAAM,KAAK,KAAK,cAAc,KAAK,CAAC;AAAE,SAAK,cAAc,aAAaA,EAAC;AAAA,EAAC;AAAC;AAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,aAAY,MAAM,GAAE,EAAE,CAAC,EAAE,CAAC,GAAE,EAAE,WAAU,eAAc,IAAI,GAAE,IAAE,EAAE,CAAC,EAAE,uCAAuC,CAAC,GAAE,CAAC;AAAE,IAAM,IAAE;", "names": ["t", "h", "j", "f", "l", "e", "r", "i"]}