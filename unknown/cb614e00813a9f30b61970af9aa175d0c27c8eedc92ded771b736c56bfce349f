#
# Copyright © 2016-2019 The Thingsboard Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

server:
  # Server bind address
  address: "${HTTP_BIND_ADDRESS:0.0.0.0}"
  # Server bind port
  port: "${HTTP_BIND_PORT:8765}"
  # Server SSL configuration
  ssl:
    # Enable/disable SSL support
    enabled: "${SSL_ENABLED:false}"
    # Path to the key store that holds the SSL certificate
    key-store: "${SSL_KEY_STORE:classpath:keystore/keystore.p12}"
    # Password used to access the key store
    key-store-password: "${SSL_KEY_STORE_PASSWORD:thingsboard}"
    # Type of the key store
    key-store-type: "${SSL_KEY_STORE_TYPE:PKCS12}"
    # Alias that identifies the key in the key store
    key-alias: "${SSL_KEY_ALIAS:tomcat}"
  log_controller_error_stack_trace: "${HTTP_LOG_CONTROLLER_ERROR_STACK_TRACE:true}"
  ws:
    send_timeout: "${TB_SERVER_WS_SEND_TIMEOUT:5000}"
    limits:
      # Limit the amount of sessions and subscriptions available on each server. Put values to zero to disable particular limitation
      max_sessions_per_tenant: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_SESSIONS_PER_TENANT:0}"
      max_sessions_per_customer: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_SESSIONS_PER_CUSTOMER:0}"
      max_sessions_per_regular_user: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_SESSIONS_PER_REGULAR_USER:0}"
      max_sessions_per_public_user: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_SESSIONS_PER_PUBLIC_USER:0}"
      max_queue_per_ws_session: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_QUEUE_PER_WS_SESSION:500}"
      max_subscriptions_per_tenant: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_SUBSCRIPTIONS_PER_TENANT:0}"
      max_subscriptions_per_customer: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_SUBSCRIPTIONS_PER_CUSTOMER:0}"
      max_subscriptions_per_regular_user: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_SUBSCRIPTIONS_PER_REGULAR_USER:0}"
      max_subscriptions_per_public_user: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_SUBSCRIPTIONS_PER_PUBLIC_USER:0}"
      max_updates_per_session: "${TB_SERVER_WS_TENANT_RATE_LIMITS_MAX_UPDATES_PER_SESSION:300:1,3000:60}"
  rest:
    limits:
      tenant:
        enabled: "${TB_SERVER_REST_LIMITS_TENANT_ENABLED:false}"
        configuration: "${TB_SERVER_REST_LIMITS_TENANT_CONFIGURATION:100:1,2000:60}"
      customer:
        enabled: "${TB_SERVER_REST_LIMITS_CUSTOMER_ENABLED:false}"
        configuration: "${TB_SERVER_REST_LIMITS_CUSTOMER_CONFIGURATION:50:1,1000:60}"

# Zookeeper connection parameters. Used for service discovery.
zk:
  # Enable/disable zookeeper discovery service.
  enabled: "${ZOOKEEPER_ENABLED:false}"
  # Zookeeper connect string
  url: "${ZOOKEEPER_URL:**************:2181}"
  # Zookeeper retry interval in milliseconds
  retry_interval_ms: "${ZOOKEEPER_RETRY_INTERVAL_MS:3000}"
  # Zookeeper connection timeout in milliseconds
  connection_timeout_ms: "${ZOOKEEPER_CONNECTION_TIMEOUT_MS:3000}"
  # Zookeeper session timeout in milliseconds
  session_timeout_ms: "${ZOOKEEPER_SESSION_TIMEOUT_MS:3000}"
  # Name of the directory in zookeeper 'filesystem'
  zk_dir: "${ZOOKEEPER_NODES_DIR:/thingsboard}"

# RPC connection parameters. Used only in cluster mode only.
rpc:
  bind_host: "${RPC_HOST:localhost}"
  bind_port: "${RPC_PORT:29001}"

# Clustering properties related to consistent-hashing. See architecture docs for more details.
cluster:
  # Unique id for this node (autogenerated if empty)
  node_id: "${CLUSTER_NODE_ID:}"
  # Name of hash function used for consistent hash ring.
  hash_function_name: "${CLUSTER_HASH_FUNCTION_NAME:murmur3_128}"
  # Amount of virtual nodes in consistent hash ring.
  vitrual_nodes_size: "${CLUSTER_VIRTUAL_NODES_SIZE:16}"
  # Queue partition id for current node
  partition_id: "${QUEUE_PARTITION_ID:0}"

# Plugins configuration parameters
plugins:
  # Comma separated package list used during classpath scanning for plugins
  scan_packages: "${PLUGINS_SCAN_PACKAGES:org.thingsboard.server.extensions,org.thingsboard.rule.engine}"

# Security parameters
security:
  # JWT Token parameters
  jwt:
    # token过期时间
    tokenExpirationTime: "${JWT_TOKEN_EXPIRATION_TIME:604800}" # Number of seconds (15 mins)
    refreshTokenExpTime: "${JWT_REFRESH_TOKEN_EXPIRATION_TIME:3600}" # Seconds (1 hour)
    tokenIssuer: "${JWT_TOKEN_ISSUER:thingsboard.io}"
    tokenSigningKey: "${JWT_TOKEN_SIGNING_KEY:thingsboardDefaultSigningKey}"
  # Enable/disable access to Tenant Administrators JWT token by System Administrator or Customer Users JWT token by Tenant Administrator
  user_token_access_enabled: "${SECURITY_USER_TOKEN_ACCESS_ENABLED:true}"
  # Enable/disable case-sensitive username login
  user_login_case_sensitive: "${SECURITY_USER_LOGIN_CASE_SENSITIVE:true}"

# Dashboard parameters
dashboard:
  # Maximum allowed datapoints fetched by widgets
  max_datapoints_limit: "${DASHBOARD_MAX_DATAPOINTS_LIMIT:60000}"

database:
  ts_max_intervals: "${DATABASE_TS_MAX_INTERVALS:700}" # Max number of DB queries generated by single API call to fetch telemetry records
  entities:
    type: "${DATABASE_ENTITIES_TYPE:sql}" # cassandra OR sql
  ts:
    type: "${DATABASE_TS_TYPE:sql}" # cassandra OR sql (for hybrid mode, only this value should be cassandra)

# SQL configuration parameters
sql:
  # Specify executor service type used to perform timeseries insert tasks: SINGLE FIXED CACHED
  ts_inserts_executor_type: "${SQL_TS_INSERTS_EXECUTOR_TYPE:cached}"
  # Specify thread pool size for FIXED executor service type
  ts_inserts_fixed_thread_pool_size: "${SQL_TS_INSERTS_FIXED_THREAD_POOL_SIZE:64}"

# Actor system parameters
actors:
  cluster:
    grpc_callback_thread_pool_size: "${ACTORS_CLUSTER_GRPC_CALLBACK_THREAD_POOL_SIZE:12}"
  tenant:
    create_components_on_init: "${ACTORS_TENANT_CREATE_COMPONENTS_ON_INIT:true}"
  session:
    max_concurrent_sessions_per_device: "${ACTORS_MAX_CONCURRENT_SESSION_PER_DEVICE:1}"
    sync:
      # Default timeout for processing request using synchronous session (HTTP, CoAP) in milliseconds
      timeout: "${ACTORS_SESSION_SYNC_TIMEOUT:10000}"
  rule:
    # Specify thread pool size for database request callbacks executor service
    db_callback_thread_pool_size: "${ACTORS_RULE_DB_CALLBACK_THREAD_POOL_SIZE:1}"
    # Specify thread pool size for javascript executor service
    js_thread_pool_size: "${ACTORS_RULE_JS_THREAD_POOL_SIZE:10}"
    # Specify thread pool size for mail sender executor service
    mail_thread_pool_size: "${ACTORS_RULE_MAIL_THREAD_POOL_SIZE:10}"
    # Whether to allow usage of system mail service for rules
    allow_system_mail_service: "${ACTORS_RULE_ALLOW_SYSTEM_MAIL_SERVICE:true}"
    # Specify thread pool size for external call service
    external_call_thread_pool_size: "${ACTORS_RULE_EXTERNAL_CALL_THREAD_POOL_SIZE:10}"
    chain:
      # Errors for particular actor are persisted once per specified amount of milliseconds
      error_persist_frequency: "${ACTORS_RULE_CHAIN_ERROR_FREQUENCY:3000}"
    node:
      # Errors for particular actor are persisted once per specified amount of milliseconds
      error_persist_frequency: "${ACTORS_RULE_NODE_ERROR_FREQUENCY:3000}"
    transaction:
      # Size of queues which store messages for transaction rule nodes
      queue_size: "${ACTORS_RULE_TRANSACTION_QUEUE_SIZE:200}"
      # Time in milliseconds for transaction to complete
      duration: "${ACTORS_RULE_TRANSACTION_DURATION:15000}"
  statistics:
    # Enable/disable actor statistics
    enabled: "${ACTORS_STATISTICS_ENABLED:true}"
    persist_frequency: "${ACTORS_STATISTICS_PERSIST_FREQUENCY:3600000}"
  queue:
    # Enable/disable persistence of un-processed messages to the queue
    enabled: "${ACTORS_QUEUE_ENABLED:true}"
    # Maximum allowed timeout for persistence into the queue
    timeout: "${ACTORS_QUEUE_PERSISTENCE_TIMEOUT:30000}"
  client_side_rpc:
    timeout: "${CLIENT_SIDE_RPC_TIMEOUT:60000}"

cache:
  # caffeine or redis
  type: "${CACHE_TYPE:redis}"

caffeine:
  specs:
    relations:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    deviceCredentials:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    devices:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    sessions:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    assets:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    entityViews:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    attribute:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    alarm:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    virtual:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    alarmJson:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    lastData:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    project:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    tenantMenus:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    waterPrice:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    stationAttrs:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    stations:
      timeToLiveInMinutes: 1440
      maxSize: 100000
    alarmRules:
      timeToLiveInMinutes: 1440
      maxSize: 100000
redis:
  # standalone or cluster
  connection:
    type: standalone
    host: "${REDIS_HOST:***********}"
    port: "${REDIS_PORT:6379}"
    db: "${REDIS_DB:10}"
    password: "${REDIS_PASSWORD:zdscRedis2022}"

# Check new version updates parameters
updates:
  # Enable/disable updates checking.
  enabled: "${UPDATES_ENABLED:false}"

# spring CORS configuration
spring.mvc.cors:
  mappings:
    # Intercept path
    "/api/**":
      #Comma-separated list of origins to allow. '*' allows all origins. When not set,CORS support is disabled.
      allowed-origins: "*"
      #Comma-separated list of methods to allow. '*' allows all methods.
      allowed-methods: "POST,GET,OPTIONS,DELETE,PATCH"
      #Comma-separated list of headers to allow in a request. '*' allows all headers.
      allowed-headers: "*"
      #How long, in seconds, the response from a pre-flight request can be cached by clients.
      max-age: "1800"
      #Set whether credentials are supported. When not set, credentials are not supported.
      allow-credentials: "true"
    "/api/v1/**":
      allowed-origins: "*"
      allowed-methods: "*"
      allowed-headers: "*"
      max-age: "1800"
      allow-credentials: "true"


# spring serve gzip compressed static resources
spring.resources.chain:
  gzipped: "true"
  strategy:
    content:
      enabled: "true"

      # HSQLDB DAO Configuration
      #  spring:
      ##    data:
      ##      jpa:
      ##        repositories:
      ##          enabled: "true"
      ##    jpa:
      ##      hibernate:
      ##        ddl-auto: "validate"
      ##      database-platform: "${SPRING_JPA_DATABASE_PLATFORM:org.hibernate.dialect.HSQLDialect}"
      ##    datasource:
      ##      driverClassName: "${SPRING_DRIVER_CLASS_NAME:org.hsqldb.jdbc.JDBCDriver}"
      ##      url: "${SPRING_DATASOURCE_URL:jdbc:hsqldb:file:${SQL_DATA_FOLDER:/tmp}/thingsboardDb;sql.enforce_size=false;hsqldb.log_size=5}"
      ##      username: "${SPRING_DATASOURCE_USERNAME:sa}"
      ##      password: "${SPRING_DATASOURCE_PASSWORD:}"

  #PostgreSQL DAO Configuration
spring:
  jackson:
    parser:
      ALLOW_COMMENTS: true

  jpa:
    properties:
      hibernate:
        query:
          plan_cache_max_size: 64
          plan_parameter_metadata_max_size: 32
  #    show-sql: true
  data:
    sql:
      repositories:
        enabled: "true"
  sql:
    hibernate:
      ddl-auto: "validate"
    database-platform: "${SPRING_JPA_DATABASE_PLATFORM:org.hibernate.dialect.PostgreSQLDialect}"
  datasource:
    driverClassName: "${SPRING_DRIVER_CLASS_NAME:org.postgresql.Driver}"
    url: "${SPRING_DATASOURCE_URL:****************************************************************,revenue}"
    username: postgres
    password: Admin123!@#
  application:
    name: base-service
  redis:
    host: ***********
    port: 6379
    database: 10
    password: "${REDIS_PASSWORD:zdscRedis2022}"
  rabbitmq:
    shortmessage:
      enabled: true
      host: ***********
      port: 5672
      username: mq
      password: mq4123@
      virtual-host: /
    #    host: localhost
    #    username: guest
    #    password: guest
    #    host: *************
    #    username: ipc
    #    password: Indark116
    virtual-host: /
    listener:
      concurrency: 20
    exchangeSyncUser: exchangeSyncUser
    queueAddUser: queueAddUser
    queueUpdateUser: queueUpdateUser
    queueDeleteUser: queueDeleteUser
    routingKeyAddUser: add_user
    routingKeyUpdateUser: update_user
    routingKeyDeleteUser: delete_user
      # 消息手动应答
  #      acknowledge-mode: manual
  # 邮箱配置
  mail:
    host: smtp.qq.com
    username: <EMAIL>
    password: wfylfovthfoihcej

#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/*.xml,classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: org.thingsboard.server.dao.model.sql
  global-config:
    #数据库相关配置
    db-config:
      #主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: UUID
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    default-statement-timeout: 90000
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

mail:
  # 接收人，多个以逗号分隔
  receiver: <EMAIL>,<EMAIL>,<EMAIL>

# Audit log parameters
audit_log:
  # Enable/disable audit log functionality.
  enabled: "${AUDIT_LOG_ENABLED:true}"
  # Specify partitioning size for audit log by tenant id storage. Example MINUTES, HOURS, DAYS, MONTHS
  by_tenant_partitioning: "${AUDIT_LOG_BY_TENANT_PARTITIONING:MONTHS}"
  # Number of days as history period if startTime and endTime are not specified
  default_query_period: "${AUDIT_LOG_DEFAULT_QUERY_PERIOD:30}"
  # Logging levels per each entity type.
  # Allowed values: OFF (disable), W (log write operations), RW (log read and write operations)
  logging_level:
    mask:
      "device": "${AUDIT_LOG_MASK_DEVICE:W}"
      "asset": "${AUDIT_LOG_MASK_ASSET:W}"
      "dashboard": "${AUDIT_LOG_MASK_DASHBOARD:W}"
      "customer": "${AUDIT_LOG_MASK_CUSTOMER:W}"
      "user": "${AUDIT_LOG_MASK_USER:W}"
      "rule_chain": "${AUDIT_LOG_MASK_RULE_CHAIN:W}"
      "alarm": "${AUDIT_LOG_MASK_ALARM:W}"
      "entity_view": "${AUDIT_LOG_MASK_ENTITY_VIEW:W}"
  sink:
    # Type of external sink. possible options: none, elasticsearch
    type: "${AUDIT_LOG_SINK_TYPE:none}"
    # Name of the index where audit logs stored
    # Index name could contain next placeholders (not mandatory):
    # @{TENANT} - substituted by tenant ID
    # @{DATE} - substituted by current date in format provided in audit_log.sink.date_format
    index_pattern: "${AUDIT_LOG_SINK_INDEX_PATTERN:@{TENANT}_AUDIT_LOG_@{DATE}}"
    # Date format. Details of the pattern could be found here:
    # https://docs.oracle.com/javase/8/docs/api/java/time/format/DateTimeFormatter.html
    date_format: "${AUDIT_LOG_SINK_DATE_FORMAT:YYYY.MM.DD}"
    scheme_name: "${AUDIT_LOG_SINK_SCHEME_NAME:http}" # http or https
    host: "${AUDIT_LOG_SINK_HOST:**************}"
    port: "${AUDIT_LOG_SINK_PORT:9200}"
    user_name: "${AUDIT_LOG_SINK_USER_NAME:}"
    password: "${AUDIT_LOG_SINK_PASSWORD:}"

state:
  defaultInactivityTimeoutInSec: "${DEFAULT_INACTIVITY_TIMEOUT:10}"
  defaultStateCheckIntervalInSec: "${DEFAULT_STATE_CHECK_INTERVAL:10}"

kafka:
  enabled: true
  bootstrap.servers: "${TB_KAFKA_SERVERS:**************:9092}"
  acks: "${TB_KAFKA_ACKS:all}"
  retries: "${TB_KAFKA_RETRIES:1}"
  batch.size: "${TB_KAFKA_BATCH_SIZE:16384}"
  linger.ms: "${TB_KAFKA_LINGER_MS:1}"
  buffer.memory: "${TB_BUFFER_MEMORY:33554432}"
  transport_api:
    requests_topic: "${TB_TRANSPORT_API_REQUEST_TOPIC:tb.transport.api.requests}"
    responses_topic: "${TB_TRANSPORT_API_RESPONSE_TOPIC:tb.transport.api.responses}"
    max_pending_requests: "${TB_TRANSPORT_MAX_PENDING_REQUESTS:10000}"
    max_requests_timeout: "${TB_TRANSPORT_MAX_REQUEST_TIMEOUT:10000}"
    request_poll_interval: "${TB_TRANSPORT_REQUEST_POLL_INTERVAL_MS:25}"
    request_auto_commit_interval: "${TB_TRANSPORT_REQUEST_AUTO_COMMIT_INTERVAL_MS:100}"
  rule_engine:
    topic: "${TB_RULE_ENGINE_TOPIC:tb.rule-engine}"
    poll_interval: "${TB_RULE_ENGINE_POLL_INTERVAL_MS:25}"
    auto_commit_interval: "${TB_RULE_ENGINE_AUTO_COMMIT_INTERVAL_MS:100}"
  notifications:
    topic: "${TB_TRANSPORT_NOTIFICATIONS_TOPIC:tb.transport.notifications}"

js:
  evaluator: "${JS_EVALUATOR:local}" # local/remote
  # Built-in JVM JavaScript environment properties
  local:
    # Use Sandboxed (secured) JVM JavaScript environment
    use_js_sandbox: "${USE_LOCAL_JS_SANDBOX:false}"
    # Specify thread pool size for JavaScript sandbox resource monitor
    monitor_thread_pool_size: "${LOCAL_JS_SANDBOX_MONITOR_THREAD_POOL_SIZE:20}"
    # Maximum CPU time in milliseconds allowed for script execution
    max_cpu_time: "${LOCAL_JS_SANDBOX_MAX_CPU_TIME:200}"
    # Maximum allowed JavaScript execution errors before JavaScript will be blacklisted
    max_errors: "${LOCAL_JS_SANDBOX_MAX_ERRORS:3}"
  # Remote JavaScript environment properties
  remote:
    # JS Eval request topic
    request_topic: "${REMOTE_JS_EVAL_REQUEST_TOPIC:js.eval.requests}"
    # JS Eval responses topic prefix that is combined with node id
    response_topic_prefix: "${REMOTE_JS_EVAL_RESPONSE_TOPIC:js.eval.responses}"
    # JS Eval max pending requests
    max_pending_requests: "${REMOTE_JS_MAX_PENDING_REQUESTS:10000}"
    # JS Eval max request timeout
    max_requests_timeout: "${REMOTE_JS_MAX_REQUEST_TIMEOUT:10000}"
    # JS response poll interval
    response_poll_interval: "${REMOTE_JS_RESPONSE_POLL_INTERVAL_MS:25}"
    # JS response auto commit interval
    response_auto_commit_interval: "${REMOTE_JS_RESPONSE_AUTO_COMMIT_INTERVAL_MS:100}"
    # Maximum allowed JavaScript execution errors before JavaScript will be blacklisted
    max_errors: "${REMOTE_JS_SANDBOX_MAX_ERRORS:3}"

transport:
  type: "${TRANSPORT_TYPE:local}" # local or remote
  remote:
    transport_api:
      requests_topic: "${TB_TRANSPORT_API_REQUEST_TOPIC:tb.transport.api.requests}"
      max_pending_requests: "${TB_TRANSPORT_MAX_PENDING_REQUESTS:10000}"
      request_timeout: "${TB_TRANSPORT_MAX_REQUEST_TIMEOUT:10000}"
      request_poll_interval: "${TB_TRANSPORT_RESPONSE_POLL_INTERVAL_MS:25}"
      request_auto_commit_interval: "${TB_TRANSPORT_RESPONSE_AUTO_COMMIT_INTERVAL_MS:1000}"
    rule_engine:
      topic: "${TB_RULE_ENGINE_TOPIC:tb.rule-engine}"
      poll_interval: "${TB_RULE_ENGINE_POLL_INTERVAL_MS:25}"
      auto_commit_interval: "${TB_RULE_ENGINE_AUTO_COMMIT_INTERVAL_MS:100}"
      poll_records_pack_size: "${TB_RULE_ENGINE_MAX_POLL_RECORDS:1000}"
      max_poll_records_per_second: "${TB_RULE_ENGINE_MAX_POLL_RECORDS_PER_SECOND:10000}"
      max_poll_records_per_minute: "${TB_RULE_ENGINE_MAX_POLL_RECORDS_PER_MINUTE:120000}"
    notifications:
      topic: "${TB_TRANSPORT_NOTIFICATIONS_TOPIC:tb.transport.notifications}"
  sessions:
    inactivity_timeout: "${TB_TRANSPORT_SESSIONS_INACTIVITY_TIMEOUT:300000}"
    report_timeout: "${TB_TRANSPORT_SESSIONS_REPORT_TIMEOUT:30000}"
  rate_limits:
    enabled: "${TB_TRANSPORT_RATE_LIMITS_ENABLED:false}"
    tenant: "${TB_TRANSPORT_RATE_LIMITS_TENANT:1000:1,20000:60}"
    device: "${TB_TRANSPORT_RATE_LIMITS_DEVICE:10:1,300:60}"
  json:
    # Cast String data types to Numeric if possible when processing Telemetry/Attributes JSON
    type_cast_enabled: "${JSON_TYPE_CAST_ENABLED:true}"
    # Maximum allowed string value length when processing Telemetry/Attributes JSON (0 value disables string value length check)
    max_string_value_length: "${JSON_MAX_STRING_VALUE_LENGTH:0}"
  # Local HTTP transport parameters
  http:
    enabled: "${HTTP_ENABLED:true}"
    request_timeout: "${HTTP_REQUEST_TIMEOUT:60000}"
  # Local MQTT transport parameters
  mqtt:
    # Enable/disable mqtt transport protocol.
    enabled: "${MQTT_ENABLED:true}"
    bind_address: "${MQTT_BIND_ADDRESS:0.0.0.0}"
    bind_port: "${MQTT_BIND_PORT:17777}"
    timeout: "${MQTT_TIMEOUT:900000}"
    netty:
      leak_detector_level: "${NETTY_LEAK_DETECTOR_LVL:DISABLED}"
      boss_group_thread_count: "${NETTY_BOSS_GROUP_THREADS:1}"
      worker_group_thread_count: "${NETTY_WORKER_GROUP_THREADS:36}"
      max_payload_size: "${NETTY_MAX_PAYLOAD_SIZE:99999999}"
    # MQTT SSL configuration
    ssl:
      # Enable/disable SSL support
      enabled: "${MQTT_SSL_ENABLED:false}"
      # SSL protocol: See http://docs.oracle.com/javase/8/docs/technotes/guides/security/StandardNames.html#SSLContext
      protocol: "${MQTT_SSL_PROTOCOL:TLSv1.2}"
      # Path to the key store that holds the SSL certificate
      key_store: "${MQTT_SSL_KEY_STORE:mqttserver.jks}"
      # Password used to access the key store
      key_store_password: "${MQTT_SSL_KEY_STORE_PASSWORD:server_ks_password}"
      # Password used to access the key
      key_password: "${MQTT_SSL_KEY_PASSWORD:server_key_password}"
      # Type of the key store
      key_store_type: "${MQTT_SSL_KEY_STORE_TYPE:JKS}"
  # Local CoAP transport parameters
  coap:
    # Enable/disable coap transport protocol.
    enabled: "${COAP_ENABLED:false}"
    bind_address: "${COAP_BIND_ADDRESS:0.0.0.0}"
    bind_port: "${COAP_BIND_PORT:5683}"
    timeout: "${COAP_TIMEOUT:10000}"

eureka:
  client:
    serviceUrl:
      #      defaultZone: http://*************:8081/eureka/
      defaultZone: http://127.0.0.1:8081/eureka/



tsdb:
  HITSDB_IP: "http://localhost"
  HITSDB_PORT: "4242"

timezone:
  default: "GMT+8:00"
install:
  #  data_dir: C:\Users\<USER>\Documents\istar-iot\thingsboard-master\application\src\main\data\
  data_dir: /usr/share/thingsboard/data
  #  image_dir: C:\Users\<USER>\nginx\data\
  image_dir: /usr/local/thingsboard/image/
  server_ip: http://*************/
  update_status: false
jasypt:
  encryptor:
    password: istar
hostname:
  ui: https://ems.istarscloud.com
  api: https://api.istarscloud.com
  image: https://image.istarscloud.com/
  # 主机地址
  myhost: http://***********:8081
## 默认logo
defaultLogo: https://image.istarscloud.com/istars.png
database-backup:
  #  opentsdb-backup-script-path: C:/backup.bat
  opentsdb-backup-script-path: /root/backup-script/backup.sh
  #  postgresql-backup-script-path: C:/backup.bat
  postgresql-backup-script-path: /root/backup-script/postgresql-backup.sh
  influxdb-backup-script-path:
istar-ipc:
  rabbitmq:
    # 待执行节点消息队列
    queue_execute_script: queue_execute_script
    # 执行结果消息队列
    queue_receive_result: queue_receive_result
  # 有效数据最后的时效, 单位：毫秒
  data_time_range: 900000
  ip: "*"
#license_path: /opt/istarcloud.license
license_path: C:/istarcloud.license
img:
  url: http://localhost:8766/api/file/getImgByName
#  url: http://**************:8766/api/file/getImgByName
login:
  yingshou: http://localhost:8081/water/login

#海康摄像头
camera:
  host: ***********:443
  appKey: 27918248
  appSecret: o5vmi9wmmjTgkNxFu8iN

#大华摄像头
dahua:
  host: ***********:443:4077
  username: TEST
  password: Admin123
  # 客户端id
  clientId: CompanyName
  # 客户端秘钥
  clientSecret: 42bec152-8f04-476a-9aec-e7d616ff3cb3

#萤石云摄像头
yingshi:
#  appKey: 22e11707b8364984a5a7922a31074834
#  appSecret: 33704e3578ca84046f7be09592ae3933
  # 锦州
  appKey: 293b416d4503497eaf343dfcff615e83
  appSecret: a7ed3319c467a5440cc7da99af129c85

wvp:
  url: http://************:18080

#oracle连接
oracle:
  url: ******************************************
  user: gis_bs
  password: gis_bs
water:
  #  templateid: ff8080817d321053017d4a6ff1601eb4
  templateid: ff8080817e04ffe6017f448678247dd5
  tenantid: 1ec0d23e1fc08508d23f9a3288503fb
  projectid: ff8080817e04ffe6017f448403bf7dd2
  gatewayid: 1eca5cfaf98e910b80957c47eac9bc3
  aep:
    model: LXSYHDZWRQ
    loginUrl: https://device.api.ct10649.com:8743/iocm/app/sec/v1.1.0/login
    registerUrl: https://device.api.ct10649.com:8743/iocm/app/reg/v1.1.0/deviceCredentials?appId=
    appId: 393d3c4eb23740999eba431212d06a3b
    secret: ffb5c162c53446acb3fa488a3863dfe0
    enable: false
control:
  dtu-modbus:
    url: http://localhost:8103/rpc/send_rpc_command_v2
  meter-valve:
    open: http://localhost:19001/api/meter/openValve
    close: http://localhost:19001/api/meter/closeValve

# 阿里云短信配置
alibaba:
  cloud:
    sms:
      accessKeyId: LTAI4GDQ7jLXwaevuMY26Tan
      accessKeySecret: ******************************
      signName: 黑桃树
      templateCode: SMS_190282749

# 微信配置
wechat:
  appId: wxbf475d8989c91bb0
  appSecret: 85fb599fe8be8ef6f4959e4887d4afd8
  mchID: 1615748947
  key: vGftduwfzHjmx7Fv9bfdjS3kPwJsySGE
  accessTokenUrl: https://api.weixin.qq.com/cgi-bin/token
  weChatUrl: https://zyswechat.siloon.com/api
  notifyUrl: ${wechat.weChatUrl}/api/wechat/notify
  userInfoUrl: https://api.weixin.qq.com/cgi-bin/user/info?access_token={0}&openid={1}
  sendMsgUrl: https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={0}
  cxf:
    wsdl: http://zb4nmrmf.xiaomy.net/services/INoticeService?wsdl

# 文件注册中心配置
file-registry:
  # 文件上传后的保存地址
  path: D:/tmp/files
  # 文件服务器的根地址
  base-url: http://************:5500/

#influxdb配置
influx:
  ip: http://***********
  port: 8086
  token: 9tzuY-qNMmlh9EonBDArT6_ks1bN4-vu6GtMnzuvp7tVN1jNJdPbFCZropqyOsQqAOU090qg98SvztgV4Tzu4w==
  org: istar
app-push:
  app-id: OMMZ09jdv6A9lWco4SoPH5
  app-key: oW4Tb5sp8e70LbLCfRlRU5
  app-secret: 4uVbbem1gY7TFfOTJo5x39
  master-secret: 8aLt9xTr38A7kyG54ENvc4
msg:
  version: shuiwu
device-command:
  url: http://localhost:8769/api/data/v1/sendCommand
  cancel-url: http://localhost:8769/api/data/v1/cancelCommand

minio:
  url: http://***********:9090
  accessKey: minio
  secretKey: WjS3Q4jxp6Ls
  bucketName: istar
