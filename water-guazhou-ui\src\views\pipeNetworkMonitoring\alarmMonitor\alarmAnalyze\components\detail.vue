<!-- eslint-disable vue/no-mutating-props -->
<template>
  <div class="cards">
    <el-card shadow="never" class="cards_card">
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="title">
            <span>报警内容</span>
            <div class="title_item">
              {{ props.config.alarmInfo || '-' }}
            </div>
          </div>
        </el-col>
        <el-col v-for="(item, o) in state" :key="o" :span="6">
          <div class="title">
            <span>{{ item.label }}：</span>
            <div class="title_item">
              {{ item.format(props.config[item.value] || '') }}
            </div>
          </div>
        </el-col>
      </el-row>
      <!-- <div class="title">
        <div
          v-for="(item, o) in state"
          :key="o"
          style="
            width: 25%;
            display: flex;
            justify-content: flex-start;
            align-items: center;
          "
        >
          <span>{{ item.label }}：</span>
          <div class="title_item">
            {{ item.format(props.config[item.value] || '') }}
          </div>
        </div>
      </div> -->
    </el-card>

    <!-- <el-card
      shadow="never"
      class="cards_card"
    >
      <div class="card_title">
        <span>报警信息</span>
      </div>
      <div style="color:#d6a683 ;">
        {{ props.config.alarmInfo||'-' }}
      </div>
    </el-card> -->
    <!-- <el-card shadow="never" class="cards_card">
      <div class="card_title">
        <span>处理建议</span>
      </div>
      <div>{{ props.config.processMethod || '-' }}</div>
    </el-card> -->
    <el-card shadow="never" class="cards_card">
      <div class="card_title">
        <span>报警历史</span>
      </div>
      <div>
        <Search
          ref="refHistorySearch"
          :config="searchConfig"
          style="margin-left: -20px"
        ></Search>
        <div
          class="echarts"
          style="height: 350px; overflow-y: auto; padding: 5px 0px"
        >
          <el-timeline style="max-width: 600px">
            <el-timeline-item
              v-for="(alarm, index) in alarmList"
              :key="index"
              placement="top"
              :timestamp="alarm.time"
            >
              {{ alarm.alarmInfo }}
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </el-card>
    <el-card shadow="never" class="cards_card">
      <div class="card_title">
        <span>数据分析</span>
      </div>
      <div style="display: flex; justify-content: space-between">
        <div style="width: 30%; height: 350px">
          <FormMap
            v-model="location"
            :disabled="true"
            :readonly="true"
          ></FormMap>
        </div>
        <div style="width: 68%; height: 350px">
          <Search
            ref="refSearch"
            :config="cardSearchConfig"
            style="margin-left: -20px"
          ></Search>
          <div class="echarts">
            <VChart ref="refChart" :option="data.detailChart"></VChart>
          </div>
        </div>
      </div>
    </el-card>
    <el-card shadow="never" class="cards_card">
      <div class="flex">
        <div class="item">
          <div class="card_title">
            <span>联动监测点</span>
          </div>
          <div style="width: 100%; height: 350px">
            <Search
              ref="refSearchLinkage"
              :config="cardSearchConfigLinkage"
              style="margin-left: -20px"
            ></Search>
            <div class="echarts">
              <VChart ref="refChart" :option="data.detailChartLinkage"></VChart>
            </div>
          </div>
        </div>
        <div class="item">
          <div class="card_title">
            <span>联动监测点</span>
          </div>
          <div class="videoDiv">
            <IndependentVideo :video="base.videoConfig"></IndependentVideo>
          </div>
        </div>
      </div>
    </el-card>

    <!-- <el-card shadow="never" class="cards_card">
      <div class="card_title">
        <span>处理结果</span>
      </div>
      <div>{{ props.config.processStatus === '3' ? '已处理' : '未完成' }}</div>
    </el-card> -->
  </div>
</template>

<script lang="ts" setup>
import { dayjs } from 'element-plus';
import { formatDate } from '@/utils/DateFormatter';
import Search from '@/components/Form/Search.vue';
import IndependentVideo from '@/components/IndependentVideo/IndependentVideo.vue';
import { detailChart } from './chart';
import { IECharts } from '@/plugins/echart';
import { getRemoteMeterDataList } from '@/api/operatingCharges/meterReading';
import {
  getStationAttrDataQueryGroupByDay,
  GetStationAttrs,
  getAlarmRulesById,
  getAlarmCenter
} from '@/api/shuiwureports/zhandian';
import { traverse, objectLookup } from '@/utils/GlobalHelper';
import { getVideoUrlById } from '@/api/video';
const alarmList = ref<any>([]);
const alarmLevel = [
  { label: '提醒报警', value: '1' },
  { label: '重要报警', value: '2' },
  { label: '紧急报警', value: '3' }
];
const alarmType = [
  { label: '液位异常', value: '1' },
  { label: '水质异常', value: '2' },
  { label: '设备故障', value: '3' },
  { label: '通讯异常', value: '4' },
  { label: '流量异常', value: '5' },
  { label: '控制异常', value: '6' },
  { label: '设备健康', value: '7' },
  { label: '其他', value: '8' }
];
const processStatus = [
  { label: '未处理', value: '1' },
  { label: '处理中', value: '2' },
  { label: '已处理', value: '3' }
];

const props = defineProps<{ config: any }>();
const location = ref<any>(
  props.config.location?.split(',').map((data) => +data)
);
const refSearch = ref<InstanceType<typeof Search>>();
const refSearchLinkage = ref<InstanceType<typeof Search>>();
const refHistorySearch = ref<InstanceType<typeof Search>>();
const refChart = ref<IECharts>();

const state = ref([
  {
    label: '报警类型',
    value: 'alarmType',
    format: (row) => alarmType.find((item) => item.value === row)?.label || '-'
  },
  {
    label: '触发时间',
    value: 'time',
    format: (row) => {
      return formatDate(row, 'YYYY-MM-DD HH:mm:ss') || '-';
    }
  },
  {
    label: '持续时间',
    value: 'time',
    format: (row) => {
      if (props.config.endTime) {
        const time = dayjs(props.config.endTime).diff(row, 'date');
        return timeLeft(time);
      }
      return '-';
    }
  },
  {
    label: '报警等级',
    value: 'alarmLevel',
    format: (row) => alarmLevel.find((item) => item.value === row)?.label || '-'
  },
  {
    label: '处理状态',
    value: 'processStatus',
    format: (row) =>
      processStatus.find((item) => item.value === row)?.label || '-'
  },
  {
    label: '处理建议',
    value: 'processMethod',
    format: (row) => row || '-'
  },
  {
    label: '处理结果',
    value: 'processStatus',
    format: (row) => (row === '3' ? '已处理' : '未完成')
  }
]);

const cardSearchConfig = ref<ISearch>({
  defaultParams: {
    time: [
      dayjs().add(-1, 'd').startOf('day').valueOf(),
      dayjs().add(2, 'd').startOf('day').valueOf()
    ],
    queryType: '30m',
    attributeId: props.config.stationAttrId
  },
  filters: [
    { label: '', field: 'time', type: 'datetimerange', format: 'x' },
    {
      hidden:
        window.SITE_CONFIG.SITENAME === 'yanting' &&
        (props.config.alarmType === '日用量报警' ||
          props.config.alarmType === '月用量报警'),
      label: '选择变量',
      field: 'attributeId',
      type: 'select-tree',
      options: computed(() => data.stationTree) as any,
      onChange() {
        refreshData();
      }
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          type: 'primary',
          perm: true,
          text: '查询',
          click: () => {
            refreshData();
          }
        }
      ]
    }
  ]
});

const cardSearchConfigLinkage = ref<ISearch>({
  defaultParams: {
    time: [
      dayjs().add(-1, 'd').startOf('day').valueOf(),
      dayjs().add(2, 'd').startOf('day').valueOf()
    ],
    queryType: '30m',
    attributeId: ''
  },
  filters: [
    { label: '', field: 'time', type: 'datetimerange', format: 'x' },
    {
      hidden:
        window.SITE_CONFIG.SITENAME === 'yanting' &&
        (props.config.alarmType === '日用量报警' ||
          props.config.alarmType === '月用量报警'),
      label: '选择变量',
      field: 'attributeId',
      type: 'select-tree',
      options: computed(() => data.stationTreeLinkage) as any,
      onChange() {
        refreshData();
      }
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          type: 'primary',
          perm: true,
          text: '查询',
          click: () => {
            refreshData();
          }
        }
      ]
    }
  ]
});

const searchConfig = ref<ISearch>({
  defaultParams: {
    time: [
      dayjs().add(-1, 'y').format('YYYY-MM-DD HH:mm:ss'),
      dayjs().format('YYYY-MM-DD HH:mm:ss')
    ]
  },
  filters: [
    {
      label: '',
      field: 'time',
      type: 'datetimerange',
      format: 'YYYY-MM-DD HH:mm:ss'
    }
  ],
  operations: [
    {
      type: 'btn-group',
      btns: [
        {
          type: 'primary',
          perm: true,
          text: '查询',
          click: () => {
            getAlramList();
          }
        }
      ]
    }
  ]
});

function timeLeft(val: number) {
  const timetype = [
    { label: '天', key: 86400000 },
    { label: '小时', key: 3600000 },
    { label: '分钟', key: 60000 },
    { label: '秒', key: 1000 }
  ];
  let time = '';
  for (const i in timetype) {
    if (val >= timetype[i].key) {
      //
      time += parseInt(val / timetype[i].key + '') + timetype[i].label;
      val %= timetype[i].key;
    }
  }
  return time;
}

const refreshData = async () => {
  const params: any = {
    stationId: props.config.stationId,
    attributeId: props.config.stationAttrId,
    filterStart: 0,
    filterEnd: 24,
    ...(refSearch.value?.queryParams || {})
  };
  if (params.time) {
    params.start = params.time[0];
    params.end = params.time[1];
    delete params.time;
  }
  const val = objectLookup(
    data.stationTree,
    'children',
    'id',
    params.attributeId
  );

  if (
    window.SITE_CONFIG.SITENAME === 'yanting' &&
    (props.config.alarmType === '日用量报警' ||
      props.config.alarmType === '月用量报警')
  ) {
    params.beginYm = dayjs(params.start).format('YYYY-MM-DD');
    params.endYm = dayjs(params.end).format('YYYY-MM-DD');
    params.remoteMeterCode = props.config?.deviceId;
    getRemoteMeterDataList(params).then((res) => {
      data.detailChart = detailChart(res.data.data.data || [], '表底', '1');
    });
  } else {
    getStationAttrDataQueryGroupByDay(params).then((res) => {
      data.detailChart = detailChart(res.data.data || [], val.label);
    });
  }
};

function initqueryParams() {
  GetStationAttrs({ stationId: props.config.stationId }).then((res) => {
    data.stationTree = traverse(res.data || [], 'children', {
      children: 'attrList',
      label: 'name',
      value: 'id'
    });
    data.stationTree = traverseDisabled(data.stationTree);
    refreshData();
  });
}

function initqueryParamsLinkage() {
  GetStationAttrs({ stationId: base.config.stationId }).then((res) => {
    data.stationTreeLinkage = traverse(res.data || [], 'children', {
      children: 'attrList',
      label: 'name',
      value: 'id'
    });
    data.stationTreeLinkage = traverseDisabled(data.stationTreeLinkage);
    cardSearchConfigLinkage.value.defaultParams = {
      time: [
        dayjs().add(-1, 'd').startOf('day').valueOf(),
        dayjs().add(2, 'd').startOf('day').valueOf()
      ],
      queryType: '30m',
      attributeId: base.config.remoteStationAttrId
    };
    debugger;
    refSearchLinkage.value?.resetForm();
    refreshData();
  });
}

function traverseDisabled(val: any) {
  const k = val.map((item) => {
    if (item.children && item.children.length !== 0) {
      item.label = item.type;
      item.value = '';
      item.disabled = true;
      traverseDisabled(item.children);
    } else {
      item.disabled = false;
    }
    return item;
  });
  return k;
}

const data = reactive({
  stationTree: [],
  detailChart: detailChart(),
  stationTreeLinkage: [],
  detailChartLinkage: detailChart()
});

//获取历史告警
const getAlramList = () => {
  const query = refHistorySearch.value?.queryParams || {};
  getAlarmCenter({
    stationId: props.config.stationId,
    page: 1,
    size: 999,
    startTime: query.time[0],
    endTime: query.time[1],
    attr: props.config.attr
  }).then((res) => {
    alarmList.value = res.data.data?.data.map((item) => {
      return {
        ...item,
        time: formatDate(item.time, 'YYYY-MM-DD HH:mm:ss')
      };
    });
  });
};

const base = reactive<{
  config: any;
  videoConfig: any;
}>({
  config: {},
  videoConfig: {
    status: false,
    url: '',
    talkurl: '',
    type: 'customHls',
    key: null
  }
});

// 获取配置信息
const getAlarmConfig = () => {
  getAlarmRulesById(props.config.alarmRuleId).then((res) => {
    debugger;
    base.config = res.data.data.data || {};
    initqueryParamsLinkage();
    getPlaySourceData(base.config.remoteVideoId);
  });
};
// 获取视频数据
const getPlaySourceData = (id) => {
  getVideoUrlById(id).then((res) => {
    base.videoConfig = {
      status: true,
      url: res.data.data.data ?? '',
      talkurl: '',
      type: setVideoType(res.data.data.data),
      key: id.id
    };
  });
};

// 视频类型
const setVideoType = (url) => {
  if (url.indexOf('ws') !== -1) {
    return 'ws';
  } else {
    return 'customHls';
  }
};

onMounted(() => {
  setTimeout(() => {
    refChart.value?.resize();
  }, 400);
  initqueryParams();
  console.log('props.config', props.config);
  getAlramList();
  getAlarmConfig();
});
</script>

<style lang="scss" scoped>
.cards {
}
.title {
  display: flex;
  align-items: center;
  padding-top: 10px;
  width: 100%;
  justify-content: flex-start;
  span {
    line-height: 30px;
  }
}

.title_item {
  font-size: 18px;
  font-weight: 700;
  padding-left: 10px;
}
.cards_card {
  margin-bottom: 20px;
}

.card_title {
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 10px;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  span {
    border-left: 5px solid #4e6fbf;
    padding-left: 5px;
  }
}
.card {
  padding: 5px;
}

.echarts {
  width: 100%;
  height: 300px;
  margin-top: 15px;
}

.flex {
  display: flex;
  align-items: top;
  justify-content: space-between;

  .item {
    flex: 1;
  }
}

.videoDiv {
  width: 100%;
  height: 340px;
}
</style>
