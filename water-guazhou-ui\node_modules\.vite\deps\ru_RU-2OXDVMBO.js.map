{"version": 3, "sources": ["../../@arcgis/core/chunks/ru_RU.js"], "sourcesContent": ["/*\nAll material copyright ESRI, All Rights Reserved, unless otherwise specified.\nSee https://js.arcgis.com/4.26/esri/copyright.txt for details.\n*/\nimport{g as e}from\"./_commonjsHelpers.js\";import{c as o}from\"./_commonjs-dynamic-modules.js\";function r(e,o){for(var r=0;r<o.length;r++){const t=o[r];if(\"string\"!=typeof t&&!Array.isArray(t))for(const o in t)if(\"default\"!==o&&!(o in e)){const r=Object.getOwnPropertyDescriptor(t,o);r&&Object.defineProperty(e,o,r.get?r:{enumerable:!0,get:()=>t[o]})}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:\"Module\"}))}var t,a,n={},s={get exports(){return n},set exports(e){n=e}};t=s,void 0!==(a=function(e,o){Object.defineProperty(o,\"__esModule\",{value:!0}),o.default={_decimalSeparator:\",\",_thousandSeparator:\" \",_percentPrefix:null,_percentSuffix:\"%\",_date_millisecond:\"mm:ss SSS\",_date_second:\"HH:mm:ss\",_date_minute:\"HH:mm\",_date_hour:\"HH:mm\",_date_day:\"dd MMM\",_date_week:\"ww\",_date_month:\"MMM\",_date_year:\"yyyy\",_duration_millisecond:\"SSS\",_duration_second:\"ss\",_duration_minute:\"mm\",_duration_hour:\"hh\",_duration_day:\"dd\",_duration_week:\"ww\",_duration_month:\"MM\",_duration_year:\"yyyy\",_era_ad:\"н.э.\",_era_bc:\"до н.э.\",A:\"У\",P:\"В\",AM:\"утра\",PM:\"вечера\",\"A.M.\":\"до полудня\",\"P.M.\":\"после полудня\",January:\"января\",February:\"февраля\",March:\"марта\",April:\"апреля\",May:\"мая\",June:\"июня\",July:\"июля\",August:\"августа\",September:\"сентября\",October:\"октября\",November:\"ноября\",December:\"декабря\",Jan:\"янв.\",Feb:\"февр.\",Mar:\"март\",Apr:\"апр.\",\"May(short)\":\"май\",Jun:\"июнь\",Jul:\"июль\",Aug:\"авг.\",Sep:\"сент.\",Oct:\"окт.\",Nov:\"нояб.\",Dec:\"дек.\",Sunday:\"воскресенье\",Monday:\"понедельник\",Tuesday:\"вторник\",Wednesday:\"среда\",Thursday:\"четверг\",Friday:\"пятница\",Saturday:\"суббота\",Sun:\"вс.\",Mon:\"пн.\",Tue:\"вт.\",Wed:\"ср.\",Thu:\"чт.\",Fri:\"пт.\",Sat:\"сб.\",_dateOrd:function(e){return\"-ое\"},\"Zoom Out\":\"Уменьшить\",Play:\"Старт\",Stop:\"Стоп\",Legend:\"Легенда\",\"Click, tap or press ENTER to toggle\":\"Щелкните, коснитесь или нажмите ВВОД, чтобы переключить\",Loading:\"Идет загрузка\",Home:\"Начало\",Chart:\"График\",\"Serial chart\":\"Серийная диаграмма\",\"X/Y chart\":\"Диаграмма X/Y\",\"Pie chart\":\"Круговая диаграмма\",\"Gauge chart\":\"Датчик-диаграмма\",\"Radar chart\":\"Лепестковая диаграмма\",\"Sankey diagram\":\"Диаграмма Сэнки\",\"Chord diagram\":\"Диаграмма Chord\",\"Flow diagram\":\"Диаграмма флоу\",\"TreeMap chart\":\"Иерархическая диаграмма\",Series:\"Серия\",\"Candlestick Series\":\"Серия-подсвечник\",\"Column Series\":\"Столбчатая серия\",\"Line Series\":\"Линейная серия\",\"Pie Slice Series\":\"Круговая серия\",\"X/Y Series\":\"X/Y серия\",Map:\"Карта\",\"Press ENTER to zoom in\":\"Нажмите ВВОД чтобу увеличить\",\"Press ENTER to zoom out\":\"Нажмите ВВОД чтобы уменьшить\",\"Use arrow keys to zoom in and out\":\"Используйте клавиши-стрелки чтобы увеличить и уменьшить\",\"Use plus and minus keys on your keyboard to zoom in and out\":\"Используйте клавиши плюс и минус на клавиатуре чтобы увеличить и уменьшить\",Export:\"Экспортировать\",Image:\"Изображение\",Data:\"Данные\",Print:\"Печатать\",\"Click, tap or press ENTER to open\":\"Щелкните, коснитесь или нажмите ВВОД чтобы открыть\",\"Click, tap or press ENTER to print.\":\"Щелкните, коснитесь или нажмите ВВОД чтобы распечатать\",\"Click, tap or press ENTER to export as %1.\":\"Щелкните, коснитесь или нажмите ВВОД чтобы экспортировать как %1\",'To save the image, right-click this link and choose \"Save picture as...\"':'Чтобы сохранить изображение, щелкните правой кнопкой на ссылке и выберите \"Сохранить изображение как...\"','To save the image, right-click thumbnail on the left and choose \"Save picture as...\"':'Чтобы сохранить изображение, щелкните правой кнопкой на картинке слева и выберите \"Сохранить изображение как...\"',\"(Press ESC to close this message)\":\"(Нажмите ESC чтобы закрыть это сообщение)\",\"Image Export Complete\":\"Экспорт изображения завершен\",\"Export operation took longer than expected. Something might have gone wrong.\":\"Экспортирование заняло дольше, чем планировалось. Возможно что-то пошло не так.\",\"Saved from\":\"Сохранено из\",PNG:\"PNG\",JPG:\"JPG\",GIF:\"GIF\",SVG:\"SVG\",PDF:\"PDF\",JSON:\"JSON\",CSV:\"CSV\",XLSX:\"XLSX\",\"Use TAB to select grip buttons or left and right arrows to change selection\":\"Используйте клавишу TAB, чтобы выбрать рукоятки или клавиши стрелок влево и вправо, чтобы изменить выделение\",\"Use left and right arrows to move selection\":\"Используйте стрелки влево-вправо, чтобы передвинуть выделение\",\"Use left and right arrows to move left selection\":\"Используйте стрелки влево-вправо, чтобы передвинуть левое выделение\",\"Use left and right arrows to move right selection\":\"Используйте стрелки влево-вправо, чтобы передвинуть правое выделение\",\"Use TAB select grip buttons or up and down arrows to change selection\":\"Используйте TAB, чтобы выбрать рукоятки или клавиши вверх-вниз, чтобы изменить выделение\",\"Use up and down arrows to move selection\":\"Используйте стрелки вверх-вниз, чтобы передвинуть выделение\",\"Use up and down arrows to move lower selection\":\"Используйте стрелки вверх-вниз, чтобы передвинуть нижнее выделение\",\"Use up and down arrows to move upper selection\":\"Используйте стрелки вверх-вниз, чтобы передвинуть верхнее выделение\",\"From %1 to %2\":\"От %1 до %2\",\"From %1\":\"От %1\",\"To %1\":\"До %1\",\"No parser available for file: %1\":\"Нет анализатора для файла: %1\",\"Error parsing file: %1\":\"Ошибка при разборе файла: %1\",\"Unable to load file: %1\":\"Не удалось загрузить файл: %1\",\"Invalid date\":\"Некорректная дата\"}}(o,n))&&(t.exports=a);const i=r({__proto__:null,default:e(n)},[n]);export{i as r};\n"], "mappings": ";;;;;;;;;AAI6F,SAASA,GAAE,GAAEC,IAAE;AAAC,WAAQD,KAAE,GAAEA,KAAEC,GAAE,QAAOD,MAAI;AAAC,UAAME,KAAED,GAAED,EAAC;AAAE,QAAG,YAAU,OAAOE,MAAG,CAAC,MAAM,QAAQA,EAAC;AAAE,iBAAUD,MAAKC,GAAE,KAAG,cAAYD,MAAG,EAAEA,MAAK,IAAG;AAAC,cAAMD,KAAE,OAAO,yBAAyBE,IAAED,EAAC;AAAE,QAAAD,MAAG,OAAO,eAAe,GAAEC,IAAED,GAAE,MAAIA,KAAE,EAAC,YAAW,MAAG,KAAI,MAAIE,GAAED,EAAC,EAAC,CAAC;AAAA,MAAC;AAAA;AAAA,EAAC;AAAC,SAAO,OAAO,OAAO,OAAO,eAAe,GAAE,OAAO,aAAY,EAAC,OAAM,SAAQ,CAAC,CAAC;AAAC;AAAC,IAAI;AAAJ,IAAM;AAAN,IAAQ,IAAE,CAAC;AAAX,IAAa,IAAE,EAAC,IAAI,UAAS;AAAC,SAAO;AAAC,GAAE,IAAI,QAAQ,GAAE;AAAC,MAAE;AAAC,EAAC;AAAE,IAAE,GAAE,YAAU,IAAE,SAAS,GAAEA,IAAE;AAAC,SAAO,eAAeA,IAAE,cAAa,EAAC,OAAM,KAAE,CAAC,GAAEA,GAAE,UAAQ,EAAC,mBAAkB,KAAI,oBAAmB,KAAI,gBAAe,MAAK,gBAAe,KAAI,mBAAkB,aAAY,cAAa,YAAW,cAAa,SAAQ,YAAW,SAAQ,WAAU,UAAS,YAAW,MAAK,aAAY,OAAM,YAAW,QAAO,uBAAsB,OAAM,kBAAiB,MAAK,kBAAiB,MAAK,gBAAe,MAAK,eAAc,MAAK,gBAAe,MAAK,iBAAgB,MAAK,gBAAe,QAAO,SAAQ,QAAO,SAAQ,WAAU,GAAE,KAAI,GAAE,KAAI,IAAG,QAAO,IAAG,UAAS,QAAO,cAAa,QAAO,iBAAgB,SAAQ,UAAS,UAAS,WAAU,OAAM,SAAQ,OAAM,UAAS,KAAI,OAAM,MAAK,QAAO,MAAK,QAAO,QAAO,WAAU,WAAU,YAAW,SAAQ,WAAU,UAAS,UAAS,UAAS,WAAU,KAAI,QAAO,KAAI,SAAQ,KAAI,QAAO,KAAI,QAAO,cAAa,OAAM,KAAI,QAAO,KAAI,QAAO,KAAI,QAAO,KAAI,SAAQ,KAAI,QAAO,KAAI,SAAQ,KAAI,QAAO,QAAO,eAAc,QAAO,eAAc,SAAQ,WAAU,WAAU,SAAQ,UAAS,WAAU,QAAO,WAAU,UAAS,WAAU,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,UAAS,SAASE,IAAE;AAAC,WAAM;AAAA,EAAK,GAAE,YAAW,aAAY,MAAK,SAAQ,MAAK,QAAO,QAAO,WAAU,uCAAsC,2DAA0D,SAAQ,iBAAgB,MAAK,UAAS,OAAM,UAAS,gBAAe,sBAAqB,aAAY,iBAAgB,aAAY,sBAAqB,eAAc,oBAAmB,eAAc,yBAAwB,kBAAiB,mBAAkB,iBAAgB,mBAAkB,gBAAe,kBAAiB,iBAAgB,2BAA0B,QAAO,SAAQ,sBAAqB,oBAAmB,iBAAgB,oBAAmB,eAAc,kBAAiB,oBAAmB,kBAAiB,cAAa,aAAY,KAAI,SAAQ,0BAAyB,gCAA+B,2BAA0B,gCAA+B,qCAAoC,2DAA0D,+DAA8D,8EAA6E,QAAO,kBAAiB,OAAM,eAAc,MAAK,UAAS,OAAM,YAAW,qCAAoC,sDAAqD,uCAAsC,0DAAyD,8CAA6C,oEAAmE,4EAA2E,4GAA2G,wFAAuF,oHAAmH,qCAAoC,6CAA4C,yBAAwB,gCAA+B,gFAA+E,mFAAkF,cAAa,gBAAe,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,KAAI,OAAM,MAAK,QAAO,KAAI,OAAM,MAAK,QAAO,+EAA8E,gHAA+G,+CAA8C,iEAAgE,oDAAmD,uEAAsE,qDAAoD,wEAAuE,yEAAwE,4FAA2F,4CAA2C,+DAA8D,kDAAiD,sEAAqE,kDAAiD,uEAAsE,iBAAgB,eAAc,WAAU,SAAQ,SAAQ,SAAQ,oCAAmC,iCAAgC,0BAAyB,gCAA+B,2BAA0B,iCAAgC,gBAAe,oBAAmB;AAAC,EAAE,GAAE,CAAC,OAAK,EAAE,UAAQ;AAAG,IAAM,IAAEH,GAAE,EAAC,WAAU,MAAK,SAAQ,EAAE,CAAC,EAAC,GAAE,CAAC,CAAC,CAAC;", "names": ["r", "o", "t", "e"]}