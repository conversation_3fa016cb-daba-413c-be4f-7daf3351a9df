import {
  L
} from "./chunk-JFOWKXSG.js";
import {
  j,
  p
} from "./chunk-IEIKQ72S.js";
import "./chunk-3IDKVHSA.js";
import "./chunk-WJWRKQWS.js";
import "./chunk-JV6TBH5W.js";
import "./chunk-TV6WKLTU.js";
import "./chunk-5VDORDNW.js";
import "./chunk-FHKOFAQ2.js";
import "./chunk-XGD5S6QR.js";
import "./chunk-AVXSZSGJ.js";
import "./chunk-6OHGIAG7.js";
import "./chunk-PTIRBOGQ.js";
import "./chunk-P37TUI4J.js";
import "./chunk-LAEW33J6.js";
import "./chunk-NGPCXWDX.js";
import "./chunk-IXMTA2RC.js";
import "./chunk-65QKNYBL.js";
import "./chunk-ST7DNJJS.js";
import "./chunk-EPJSBV4J.js";
import "./chunk-YEODPCXQ.js";
import "./chunk-67MHB3E3.js";
import {
  x
} from "./chunk-MKG56XBH.js";
import "./chunk-FZ7BG3VX.js";
import "./chunk-YJWWP4AU.js";
import "./chunk-EQYLMJEJ.js";
import "./chunk-3WUI7ZKG.js";
import "./chunk-VTOVDEW2.js";
import "./chunk-3WEGNHPY.js";
import "./chunk-N7ADFPOO.js";
import "./chunk-2KFL4KXW.js";
import "./chunk-CHUBHVZP.js";
import "./chunk-I7WHRVHF.js";
import "./chunk-22GGEXM2.js";
import "./chunk-NVZMGX2J.js";
import "./chunk-ZZ5SHLHN.js";
import "./chunk-JZWZKWMU.js";
import "./chunk-22HLMDJ5.js";
import "./chunk-37DYRJVQ.js";
import "./chunk-JOV46W3N.js";
import "./chunk-UVNYHPLJ.js";
import "./chunk-7OAX5UZS.js";
import "./chunk-5GX2JMCX.js";
import "./chunk-EDS4WCRT.js";
import "./chunk-SGIJIEHB.js";
import "./chunk-IJYAOLHM.js";
import "./chunk-VUZIUAG2.js";
import "./chunk-2HIKCLEI.js";
import "./chunk-7CPUVZNS.js";
import "./chunk-2NIWKSZM.js";
import "./chunk-3WCHZJQK.js";
import "./chunk-X7FOCGBC.js";
import "./chunk-V5GIYRXW.js";
import "./chunk-SRBBUKOI.js";
import "./chunk-M6X55NI4.js";
import {
  w as w2
} from "./chunk-XTO3XXZ3.js";
import "./chunk-XVA5SA7P.js";
import "./chunk-U4SVMKOQ.js";
import {
  w
} from "./chunk-63M4K32A.js";
import "./chunk-R5MYQRRS.js";
import {
  f2 as f
} from "./chunk-JXLVNWKF.js";
import "./chunk-LJHVXLBF.js";
import "./chunk-G5KX4JSG.js";
import "./chunk-74XRRMG4.js";
import "./chunk-EIGTETCG.js";
import "./chunk-MQAXMQFG.js";
import "./chunk-36FLFRUE.js";
import "./chunk-RQXGVG3K.js";
import "./chunk-EGHLQERQ.js";
import "./chunk-7SWS36OI.js";
import "./chunk-6HCWK637.js";
import "./chunk-H4S5JNVJ.js";
import "./chunk-UOKTNY52.js";
import "./chunk-KUPAGB4V.js";
import {
  e
} from "./chunk-NDCSRZLO.js";
import {
  a2 as a,
  y
} from "./chunk-JN4FSB7Y.js";
import "./chunk-HP475EI3.js";
import "./chunk-C5VMWMBD.js";
import "./chunk-JEDE7445.js";
import "./chunk-TUM6KUQZ.js";
import "./chunk-2CM7MIII.js";
import "./chunk-EKX3LLYN.js";
import "./chunk-4RZONHOY.js";
import "./chunk-RV4I37UI.js";
import "./chunk-LTKA6OKA.js";
import "./chunk-XOI5RUBC.js";
import "./chunk-REW33H3I.js";
import "./chunk-GZGAQUSK.js";
import "./chunk-BVTIFMBM.js";
import "./chunk-H3AJBOWU.js";

// node_modules/@arcgis/core/layers/OpenStreetMapLayer.js
var c = class extends L {
  constructor(...e2) {
    super(...e2), this.portalItem = null, this.isReference = null, this.tileInfo = new j({ size: [256, 256], dpi: 96, format: "png8", compressionQuality: 0, origin: new w({ x: -20037508342787e-6, y: 20037508342787e-6, spatialReference: f.WebMercator }), spatialReference: f.WebMercator, lods: [new p({ level: 0, scale: 591657527591555e-6, resolution: 156543.033928 }), new p({ level: 1, scale: 295828763795777e-6, resolution: 78271.5169639999 }), new p({ level: 2, scale: 147914381897889e-6, resolution: 39135.7584820001 }), new p({ level: 3, scale: 73957190948944e-6, resolution: 19567.8792409999 }), new p({ level: 4, scale: 36978595474472e-6, resolution: 9783.93962049996 }), new p({ level: 5, scale: 18489297737236e-6, resolution: 4891.96981024998 }), new p({ level: 6, scale: 9244648868618e-6, resolution: 2445.98490512499 }), new p({ level: 7, scale: 4622324434309e-6, resolution: 1222.99245256249 }), new p({ level: 8, scale: 2311162217155e-6, resolution: 611.49622628138 }), new p({ level: 9, scale: 1155581108577e-6, resolution: 305.748113140558 }), new p({ level: 10, scale: 577790.554289, resolution: 152.874056570411 }), new p({ level: 11, scale: 288895.277144, resolution: 76.4370282850732 }), new p({ level: 12, scale: 144447.638572, resolution: 38.2185141425366 }), new p({ level: 13, scale: 72223.819286, resolution: 19.1092570712683 }), new p({ level: 14, scale: 36111.909643, resolution: 9.55462853563415 }), new p({ level: 15, scale: 18055.954822, resolution: 4.77731426794937 }), new p({ level: 16, scale: 9027.977411, resolution: 2.38865713397468 }), new p({ level: 17, scale: 4513.988705, resolution: 1.19432856685505 }), new p({ level: 18, scale: 2256.994353, resolution: 0.597164283559817 }), new p({ level: 19, scale: 1128.497176, resolution: 0.298582141647617 })] }), this.subDomains = ["a", "b", "c"], this.fullExtent = new w2(-20037508342787e-6, -2003750834278e-5, 2003750834278e-5, 20037508342787e-6, f.WebMercator), this.urlTemplate = "https://{subDomain}.tile.openstreetmap.org/{level}/{col}/{row}.png", this.operationalLayerType = "OpenStreetMap", this.type = "open-street-map", this.copyright = "Map data &copy; OpenStreetMap contributors, CC-BY-SA";
  }
  get refreshInterval() {
    return 0;
  }
};
e([y({ type: x, json: { read: false, write: false, origins: { "web-document": { read: false, write: false } } } })], c.prototype, "portalItem", void 0), e([y({ type: Boolean, json: { read: false, write: false } })], c.prototype, "isReference", void 0), e([y({ type: Number, readOnly: true, json: { read: false, write: false, origins: { "web-document": { read: false, write: false } } } })], c.prototype, "refreshInterval", null), e([y({ type: j, json: { write: false } })], c.prototype, "tileInfo", void 0), e([y({ type: ["show", "hide"] })], c.prototype, "listMode", void 0), e([y({ readOnly: true, json: { read: false, write: false } })], c.prototype, "subDomains", void 0), e([y({ readOnly: true, json: { read: false, write: false }, nonNullable: true })], c.prototype, "fullExtent", void 0), e([y({ readOnly: true, json: { read: false, write: false } })], c.prototype, "urlTemplate", void 0), e([y({ type: ["OpenStreetMap"] })], c.prototype, "operationalLayerType", void 0), e([y({ json: { read: false } })], c.prototype, "type", void 0), e([y({ json: { read: false, write: false } })], c.prototype, "copyright", void 0), e([y({ json: { read: false, write: false } })], c.prototype, "wmtsInfo", void 0), c = e([a("esri.layers.OpenStreetMapLayer")], c);
var u = c;
export {
  u as default
};
//# sourceMappingURL=OpenStreetMapLayer-FWKGJ2QE.js.map
